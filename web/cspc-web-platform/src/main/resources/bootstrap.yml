server:
  port: 8082
  servlet:
    context-path: /platformApi

spring:
  application:
    name: cspc-web-platform
  profiles:
    active: ${ENV:dev}
  main:
    allow-bean-definition-overriding: true

  cloud:
    nacos:
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
      config:
        profile: ${spring.application.active:dev}
        namespace: ${NACOS_NAMESPACE:cnoocshell-dev}
        server-addr: ${NACOS_SERVER_ADDR:27.40.98.108:8848} # Nacos 服务器地址
        file-extension: yaml          # 配置文件扩展名
        file—prefix: ${spring.application.name:dev}
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}  # Nacos 服务器地址

logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    com.netflix.discovery.DiscoveryClient: INFO
    com.cnoocshell.web.base.controller: INFO
    com.alibaba.cloud.nacos: DEBUG
swagger:
  author: cnoocshell
  title: cnoocshell
  basePackage: com.cnoocshell.web.platform.controller

management:
  security:
    enabled: false
  endpoints:
    health:
      sensitive: false
    web:
      exposure:
        include: "*"
  health:
    kafka:
      enabled: true