package com.cnoocshell.web.platform;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.session.config.annotation.web.http.EnableSpringHttpSession;

import com.cnoocshell.common.annotation.ExcludeFromComponetScan;
import com.cnoocshell.common.config.mybatis.DatasourceConfig;
import com.cnoocshell.common.config.mybatis.MybatisConfiguration;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
@EnableFeignClients({
		"com.cnoocshell.member.api",
		"com.cnoocshell.base.api",
		"com.cnoocshell.goods.api",
		"com.cnoocshell.information.api",
		"com.cnoocshell.order.api",
})
@EnableSpringHttpSession
@ComponentScan(value = {
		"com.cnoocshell.web.platform",
		"com.cnoocshell.common.config",
		"com.cnoocshell.common.service",
		"com.cnoocshell.web.common"
}, excludeFilters = {
		@ComponentScan.Filter(type = FilterType.ANNOTATION, value = ExcludeFromComponetScan.class),
		@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = MybatisConfiguration.class),
		@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = DatasourceConfig.class)
})
@EnableDiscoveryClient
@Slf4j
public class CspcWebPlatformApplication {
	public static void main(String[] args) {
		SpringApplication.run(CspcWebPlatformApplication.class, args);
		log.info("----------------CspcWebPlatformApplication is start-----------------");
	}
}
