package com.cnoocshell.web.platform.controller;

import com.cnoocshell.base.api.dto.*;
import com.cnoocshell.base.api.service.IValueSetService;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * @Description:: 值集服务
 */

@Api(tags={"ValueSet"},description = ": 值集服务")
@RestController
@RequiredArgsConstructor
@RequestMapping("/valueSet")
public class ValueSetController {

   private final IValueSetService iValueSetService;

   @ApiOperation("根据值集id得到值集")
   @PostMapping(value="/getValueSet")
   public ItemResult<ValueSetDTO> getValueSet(@RequestParam("valueSetId") String valueSetId)throws Exception{
      return new ItemResult<>(iValueSetService.getValueSet(valueSetId));
   }


   @ApiOperation("通过值集id删除值集")
   @PostMapping(value="/deleteValueSet")
   public ItemResult deleteValueSet(LoginInfo loginInfo,@RequestParam("valueSetId") String valueSetId)throws Exception{
      iValueSetService.deleteValueSet(valueSetId,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @ApiOperation("值集分页查询")
   @PostMapping(value="/pageValueSet")
   public ItemResult<PageInfo<ValueSetDTO>> pageValueSet(@RequestBody ValueQueryDTO valueQueryDTO)throws Exception{
      return new ItemResult<>(iValueSetService.pageValueSet(valueQueryDTO));
   }


   @ApiOperation("创建值集")
   @PostMapping(value="/createValueSet")
   public ItemResult createValueSet(LoginInfo loginInfo,@RequestBody ValueSetDTO valueSet)throws Exception{
      iValueSetService.createValueSet(valueSet,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @ApiOperation("该值集是否可修改")
   @PostMapping(value="/canValueSetEdit")
   public ItemResult<Boolean> canValueSetEdit(@RequestParam("valueSetId") String valueSetId)throws Exception{
      return new ItemResult<>(iValueSetService.canValueSetEdit(valueSetId));
   }


   @ApiOperation("修改值集")
   @PostMapping(value="/updateValueSet")
   public ItemResult updateValueSet(LoginInfo loginInfo,@RequestBody ValueSetDTO valueSet)throws Exception{
      iValueSetService.updateValueSet(valueSet,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @ApiOperation("根据code获取值集子节点树")
   @PostMapping(value="/getValueSetByRefrenceCode")
   public ItemResult<ValueSetTreeDTO> getValueSetByRefrenceCode(@RequestParam("refrenceCode") String refrenceCode)throws Exception{
      return new ItemResult<>(iValueSetService.getValueSetByReferenceCode(refrenceCode));
   }


   @ApiOperation("创建值集项")
   @PostMapping(value="/createValueSetOption")
   public ItemResult createValueSetOption(@RequestBody ValueSetOptionDTO valueSet,@RequestParam("operator") String operator)throws Exception{
      iValueSetService.createValueSetOption(valueSet,operator);
      return new ItemResult<>(new Object());

   }


   @ApiOperation("创建值集和值集项")
   @PostMapping(value="/createValueAndOption")
   public ItemResult createValueAndOption(LoginInfo logininfo,@Valid @RequestBody ValueAndOptionDTO valueAndOptionDTO)throws Exception{
      iValueSetService.createValueAndOption(valueAndOptionDTO,logininfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @ApiOperation("修改值集项")
   @PostMapping(value="/updateValueSetOptions")
   public ItemResult updateValueSetOptions(LoginInfo loginInfo,@RequestBody List<ValueSetOptionDTO> valueSetOptions)throws Exception{
      iValueSetService.updateValueSetOptions(valueSetOptions,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @ApiOperation("更新值集和值集项")
   @PostMapping(value="/updateValueAndOption")
   public ItemResult updateValueAndOption(LoginInfo loginInfo,@Valid @RequestBody ValueAndOptionDTO valueAndOptionDTO)throws Exception{
      iValueSetService.updateValueAndOption(valueAndOptionDTO,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @ApiOperation("通过值集项id删除值集项")
   @PostMapping(value="/deleteValueSetOption")
   public ItemResult deleteValueSetOption(LoginInfo loginInfo,@RequestParam("valueSetId") String valueSetId)throws Exception{
      iValueSetService.deleteValueSetOption(valueSetId,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @ApiOperation("根据父节点id获取子节点树")
   @PostMapping(value="/getChildValueSetOption")
   public ItemResult<ValueSetTreeDTO> getChildValueSetOption(@RequestParam("parentOptionId") String parentOptionId)throws Exception{
      return new ItemResult<>(iValueSetService.getChildValueSetOption(parentOptionId));
   }


   @ApiOperation("根据值集项id得到值集项")
   @PostMapping(value="/getValueSetOption")
   public ItemResult<ValueSetOptionDTO> getValueSetOption(@RequestParam("valueSetOptionId") String valueSetOptionId)throws Exception{
      return new ItemResult<>(iValueSetService.getValueSetOption(valueSetOptionId));
   }


   @ApiOperation("根据值集id查询值集项")
   @PostMapping(value="/listValueSetOption")
   public ItemResult<List<ValueSetOptionDTO>> listValueSetOption(@RequestParam("valueSetId") String valueSetId)throws Exception{
      return new ItemResult<>(iValueSetService.listValueSetOption(valueSetId));
   }

   @ApiOperation("通过key查询值集合")
   @PostMapping(value="/findOptionByKey")
   public ItemResult<List<ValueSetOptionDTO>> findOptionByKey(@RequestParam("key") String key)throws Exception{
      return new ItemResult<>(iValueSetService.findOptionByKey(key));
   }

   @ApiOperation("通过key的集合查询值集合")
   @PostMapping(value="/batchFindOption")
   public ItemResult<List<ValueSetOptionDTO>> batchFindOption(@RequestBody List<String> keys)throws Exception{
      return new ItemResult<>(iValueSetService.batchFindOption(keys));
   }

   @ApiOperation("重新加载缓存")
   @PostMapping(value="/reloadRedisCache")
   public ItemResult<Boolean> reloadRedisCache(){
      iValueSetService.reloadRedisCache();
      return new ItemResult<>(true);
   }

}
