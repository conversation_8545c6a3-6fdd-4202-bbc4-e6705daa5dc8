package com.cnoocshell.web.platform.controller;

import com.alibaba.fastjson.JSON;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.result.PageData;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.dto.base.PageQuery;
import com.cnoocshell.goods.api.service.IBaseGoodAuditService;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.goods.api.service.IGoodsService;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Description Web端控制器，平台商品类目相关接口
 * @date 2018年8月20日 上午10:29:31
 */
@Slf4j
@RestController
@RequestMapping("/goods")
@Api(tags = {"GoodsController"}, description = "Web端控制器，平台商品类目相关接口")
public class GoodsController {

    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private IGoodsCategoryService goodsCategoryService;
    @Autowired
    private IBaseGoodAuditService baseGoodAuditService;

    @ApiOperation(value = "进行审批", tags = "审批")
    @PostMapping("audit")
    public ItemResult<Boolean> audit(@ApiIgnore LoginInfo loginInfo, @RequestParam("goodId") String goodId, @RequestParam("pass") boolean pass, @RequestParam(required = false) String msg) {
        return baseGoodAuditService.doAudit(goodId, pass, msg, loginInfo.getAccountId());
    }

    @ApiOperation("分页查看标准商品列表")
    @PostMapping(value = "/pageBaseGoods")
    public ItemResult<PageInfo<BaseGoodsDTO>> pageBaseGoods(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody PageBaseGoodsDTO pageBaseGoodsDTO) {
        checkLogin(loginInfo);
        log.info("/pageBaseGoods,req:{}", pageBaseGoodsDTO);
        return goodsService.pageBaseGoods(pageBaseGoodsDTO);
    }


    @ApiOperation(value = "获取标准商品详细", tags = "商品")
    @GetMapping("{goodId}")
    public ItemResult<BaseGoodDetailAuditDto> details(@PathVariable String goodId) {
        return baseGoodAuditService.baseGoodDetails(goodId);
    }

    @ApiOperation(value = "商品通用查询")
    @PostMapping(value = "/goodsCommonQuery")
    public ItemResult<PageData<GoodsDTO>> goodsCommonQuery(@RequestBody PageQuery<GoodsQueryCondDTO> pageQuery) throws Exception {
        return goodsService.goodsCommonQuery(pageQuery);
    }

    @ApiOperation(value = "取得商品详情")
    @GetMapping(value = "/getGoodsInfo")
    public ItemResult<GoodsDTO> getGoodsInfo(String goodsId) {
        log.info("/getGoodsInfo-goodsId_{}", goodsId);
        return goodsService.getGoodsInfo(goodsId);
    }

    @ApiOperation(value = "搜索商品")
    @GetMapping(value = "/findGoods")
    public ItemResult<PageInfo<GoodsDTO>> findGoods(@ApiIgnore LoginInfo loginInfo, String name, Integer goodsStatus, Integer categoryType, Integer goodsType,
                                                    String category, String sellernm, String keywords, Integer pageSize, Integer pageNum) {
        log.info("/findGoods-{},{},{},{},{},{},{},{},{}",
                name, goodsStatus, categoryType, goodsType, category, sellernm, keywords, pageSize, pageNum);
        //sellernm -> sellerId
        String sellerId = "";
        return goodsService.findGoods(name, goodsStatus, categoryType, goodsType, category, sellerId, keywords, pageSize, pageNum);
    }

    @ApiOperation(value = "选择品类（平台商品）")
    @GetMapping(value = "/findBaseGoodsLikeName")
    public ItemResult<List<GoodsDTO>> findBaseGoodsLikeName(@ApiIgnore LoginInfo loginInfo, String name, Integer categoryType) {
        log.info("/findBaseGoodsLikeName-name_{} categoryType_{}", name, categoryType);
        return goodsService.findBaseGoodsLikeName(name, categoryType);
    }

    @ApiOperation(value = "查询有效的卖家商品")
    @GetMapping(value = "/findUserGoods")
    public ItemResult<List<GoodsDTO>> findUserGoods(@ApiIgnore LoginInfo loginInfo, String name, String sellerId, Integer categoryType) {
        log.info("/findUserGoods-name_{} categoryType_{}", name, categoryType);
        return goodsService.findUserGoods(name, null, sellerId, categoryType);
    }

    @ApiOperation(value = "模糊查询有效的卖家商品")
    @GetMapping(value = "/findUserGoodsLikeName")
    public ItemResult<List<GoodsDTO>> findUserGoodsLikeName(@ApiIgnore LoginInfo loginInfo, String name, String sellerId, Integer categoryType) {
        log.info("/findUserGoodsLikeName-name_{} categoryType_{}", name, categoryType);
        return goodsService.findUserGoodsLikeName(name, sellerId, categoryType);
    }

    @ApiOperation(value = "创建商品")
    @PostMapping(value = "/createGoods")
    public ItemResult<GoodsDTO> createGoods(@ApiIgnore LoginInfo loginInfo, @RequestBody GoodsDTO goodsDTO) {
        log.info("/createGoods-goodsDTO_{} operator_{}", JSON.toJSONString(goodsDTO), null);
        goodsDTO.setSellerId(loginInfo.getMemberId());
        if (goodsDTO.getGoodsType() == null) {
            goodsDTO.setGoodsType(2);
        }
        return goodsService.create(goodsDTO, loginInfo.getAccountId());
    }

    @ApiOperation(value = "更新商品")
    @PostMapping(value = "/updateGoods")
    public ItemResult<GoodsDTO> updateGoods(@ApiIgnore LoginInfo loginInfo, @RequestBody GoodsDTO goodsDTO) {
        log.info("/updateGoods-goodsDTO_{} operator_{}", JSON.toJSONString(goodsDTO), loginInfo.getAccountId());
        return goodsService.update(goodsDTO, loginInfo.getAccountId());
    }

    @ApiOperation(value = "更新商品状态")
    @PostMapping(value = "/updateGoodsStatus")
    public ItemResult<String> updateStatus(@ApiIgnore LoginInfo loginInfo, String goodsId, Integer goodsStatus) {
        log.info("/updateGoodsStatus-goodsId_{} goodsStatus_{} operator_{}", goodsId, goodsStatus, loginInfo.getAccountId());
        return goodsService.updateStatus(goodsId, goodsStatus, loginInfo.getAccountId());
    }

    @ApiOperation(value = "删除商品（逻辑删除，支持批量删除）")
    @PostMapping(value = "/deleteGoods")
    public ItemResult<Boolean> deleteGoods(@ApiIgnore LoginInfo loginInfo, String goodsIds) {
        log.info("/deleteGoods-goodsIds_{} operator_{}", goodsIds, loginInfo.getAccountId());
        return goodsService.delete(goodsIds, loginInfo.getAccountId());
    }

    @ApiOperation(value = "查询全部商品分类List")
    @GetMapping(value = "/getAllCategoryList")
    public ItemResult<List<GoodsCategoryDTO>> getAllCategoryList() {
        ItemResult<List<GoodsCategoryDTO>> result = goodsCategoryService.getAllList();
        result.setSuccess(true);
        return result;
    }

    @ApiOperation(value = "查询全部商品分类Tree")
    @GetMapping(value = "/getAllCategoryTree")
    public ItemResult<List<GoodsCategoryDTO>> getAllCategoryTree() {
        return goodsCategoryService.getAllTree();
    }

    @ApiOperation(value = "模糊查询商品分类ByName")
    @GetMapping(value = "/getCategoryByLikeName")
    public ItemResult<List<GoodsCategoryDTO>> getCategoryByLikeName(@ApiIgnore LoginInfo loginInfo, String name) {
        log.info("/getCategoryByLikeName-name_{} goodsStatus{} operator_{}", name);
        ItemResult<List<GoodsCategoryDTO>> result = goodsCategoryService.getByLikeName(name);
        return result;
    }

    @ApiOperation(value = "创建商品分类")
    @PostMapping(value = "/createCategory")
    public ItemResult<GoodsCategoryDTO> createCategory(@ApiIgnore LoginInfo loginInfo, String categoryName, String parentId) {
        log.info("/createCategory-categoryName_{} parentId_{} operator_{}", categoryName, parentId);
        ItemResult<GoodsCategoryDTO> result = goodsCategoryService.create(null, categoryName, parentId, loginInfo.getAccountId());
        return result;
    }

    @ApiOperation(value = "更新商品分类")
    @PostMapping(value = "/updateCategory")
    public ItemResult<GoodsCategoryDTO> updateCategory(@ApiIgnore LoginInfo loginInfo, String categoryId, String categoryName) {
        log.info("/updateCategory-categoryId_{} categoryName_{} operator_{}", categoryId, categoryName, loginInfo.getAccountId());
        ItemResult<GoodsCategoryDTO> result = goodsCategoryService.update(categoryId, null, categoryName, loginInfo.getAccountId());
        return result;
    }

    @ApiOperation(value = "删除商品分类")
    @PostMapping(value = "/deleteCategory")
    public ItemResult<Boolean> deleteCategory(@ApiIgnore LoginInfo loginInfo, String categoryId) {
        log.info("/deleteCategory-categoryId_{} operator_{}", categoryId, loginInfo.getAccountId());
        ItemResult<Boolean> result = goodsCategoryService.delete(categoryId, loginInfo.getAccountId());
        return result;
    }

    @ApiOperation(value = "获取商品属性")
    @GetMapping(value = "/getAttrValByGoodsId")
    public ItemResult<List<GoodsAttributeDTO>> getAttrValByGoodsId(@ApiIgnore LoginInfo loginInfo, String goodsId, Integer attriType) {
        log.info("/getAttrValByGoodsId-goodsId_{} attriType_{} operator_{}", goodsId, attriType);
        return goodsService.getAttrValByGoodsId(goodsId, attriType);
    }

    @ApiOperation(value = "根据属性名称查询属性值")
    @GetMapping(value = "/getAttrValByName")
    public ItemResult<List<GoodsAttributeDTO>> getAttrValByName(@ApiIgnore LoginInfo loginInfo, String attrName, Integer categoryType) {
        log.info("/getAttrValByName-attrName_{} categoryType_{}", attrName, categoryType);
        return goodsService.getAttrValByName(attrName, categoryType);
    }

    @ApiOperation(value = "查询商品单位换算信息")
    @GetMapping(value = "/getUnitConverInfo")
    public ItemResult<List<UnitConverDTO>> getUnitConverInfo(@ApiIgnore LoginInfo loginInfo, String goodsId) {
        log.info("/getUnitConverInfo-goodsId_{}", goodsId);
        ItemResult<List<UnitConverDTO>> result = goodsService.getUnitConverInfo(goodsId);
        return result;
    }

    @ApiOperation(value = "获取分类关联属性信息")
    @GetMapping(value = "/getCategoryAttrVals")
    public ItemResult<Map<String, List<GoodsAttributeDTO>>> getCategoryAttrVals(@ApiIgnore LoginInfo loginInfo, String categoryId, Integer categoryType) {
        log.info("/getCategoryAttrVals-categoryId_{} categoryType_{}", categoryId, categoryType);
        ItemResult<Map<String, List<GoodsAttributeDTO>>> result = goodsService.getCategoryAttrVals(categoryId, categoryType);
        return result;
    }

    @ApiOperation("查看标准商品详情")
    @PostMapping(value = "/getBaseGoodsDetail")
    public ItemResult<BaseGoodDetailAuditDto> getBaseGoodsDetail(@ApiIgnore LoginInfo loginInfo, @Valid @RequestParam("baseGoodsId") String baseGoodsId) {
        checkLogin(loginInfo);
        log.info("/getBaseGoodsDetail,req:{}", baseGoodsId);
        return baseGoodAuditService.baseGoodDetails(baseGoodsId);
    }

    @ApiOperation("启用标准商品")
    @PostMapping(value = "/enableBaseGoods")
    public ItemResult<List<String>> enableBaseGoods(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody List<String> baseGoodsIds) {
        checkLogin(loginInfo);
        log.info("/enableBaseGoods,req:{}", baseGoodsIds);
        return goodsService.enableBaseGoods(baseGoodsIds, loginInfo.getAccountId());
    }

    @ApiOperation("禁用标准商品")
    @PostMapping(value = "/disableBaseGoods")
    public ItemResult<List<String>> disableBaseGoods(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody List<String> baseGoodsIds) {
        checkLogin(loginInfo);
        log.info("/disableBaseGoods,req:{}", baseGoodsIds);
        return goodsService.disableBaseGoods(baseGoodsIds, loginInfo.getAccountId());
    }


    @ApiOperation("查看平台商品详情")
    @PostMapping(value = "/getGoodsDetail")
    public ItemResult<GoodsPlatformDTO> getGoodsDetail(@Valid @RequestParam("goodsId") String goodsId) {
        log.info("/getGoodsDetail,req:{}", goodsId);
        return goodsService.getGoodsDetail(goodsId);
    }

    @ApiOperation("删除平台商品")
    @PostMapping(value = "/deleteGoodsPlatform")
    public ItemResult<List<String>> deleteGoodsPlatform(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody List<String> goodsIds) {
        checkLogin(loginInfo);
        log.info("/deleteGoodsPlatform,req:{}", goodsIds);
        return goodsService.deleteGoodsPlatform(goodsIds, loginInfo.getAccountId());
    }

    @ApiOperation("启用平台商品")
    @PostMapping(value = "/enableGoodsPlatform")
    public ItemResult<List<String>> enableGoodsPlatform(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody List<String> goodsIds) {
        checkLogin(loginInfo);
        log.info("/enableGoodsPlatform,req:{}", goodsIds);
        return goodsService.enableGoodsPlatform(goodsIds, loginInfo.getAccountId());
    }

    @ApiOperation("禁用平台商品")
    @PostMapping(value = "/disableGoodsPlatform")
    public ItemResult<List<String>> disableGoodsPlatform(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody List<String> goodsIds) {
        checkLogin(loginInfo);
        log.info("/disableGoodsPlatform,req:{}", goodsIds);
        return goodsService.disableGoodsPlatform(goodsIds, loginInfo.getAccountId());
    }

    @ApiOperation("平台商品列表")
    @PostMapping(value = "/pageGoodsPlatform")
    public ItemResult<PageInfo<GoodsPlatformDTO>> pageGoodsPlatform(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody PageGoodsPlatformDTO pageGoodsPlatformDTO) {
        checkLogin(loginInfo);
        log.info("/pageGoodsPlatform,req:{}", pageGoodsPlatformDTO);
        return goodsService.pageGoodsPlatform(pageGoodsPlatformDTO);
    }

    @ApiOperation("查询标准商品属性")
    @PostMapping(value = "/findBaseGoodsAttribute")
    public ItemResult<List<CategoryAttributeDTO>> findBaseGoodsAttribute(@ApiIgnore LoginInfo loginInfo, @Valid @RequestParam("goodsId") String goodsId) {
        checkLogin(loginInfo);
        log.info("/findBaseGoodsAttribute,req:{}", goodsId);
        return goodsService.findBaseGoodsAttribute(goodsId);
    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }
}