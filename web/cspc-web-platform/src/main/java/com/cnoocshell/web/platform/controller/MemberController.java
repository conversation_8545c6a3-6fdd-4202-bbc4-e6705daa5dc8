package com.cnoocshell.web.platform.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.base.api.dto.ValueSetTreeDTO;
import com.cnoocshell.base.api.service.IRegionService;
import com.cnoocshell.base.api.service.IValueSetService;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.constant.MemberNumberConstant;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.service.IMemberService;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.web.platform.vo.member.MemberApprovalRequestPageDTO;
import com.cnoocshell.web.platform.vo.member.MemberQueryPageDTO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = {"member"}, description = "会员服务")
@RestController
@RequiredArgsConstructor
@RequestMapping("/member")
public class MemberController {
    private final IMemberService iMemberService;
    private final IRegionService regionService;
    private final IValueSetService valueSetService;


    @ApiOperation("分页获取会员请求")
    @PostMapping(value = "/pageRegisterMemberApprovalRequests")
    public ItemResult<PageInfo<MemberApprovalRequestDTO>> pageRegisterMemberApprovalRequests(@RequestBody MemberApprovalRequestPageDTO query) {
        query.defaultPageIfNull();

        PageInfo<MemberApprovalRequestDTO> pageInfo = iMemberService.pageRegisterMemberApprovalRequests(query.getMemberApprovalRequestQueryDTO(), query.getPageNum(), query.getPageSize());
        updateAreaNameAndCarrierTypeName(pageInfo != null ? pageInfo.getList() : null);
        return new ItemResult<>(pageInfo);
    }

    @ApiOperation("审批通过（列表页面是用）")
    @PostMapping(value = "/approveRequestList")
    public ItemResult approveRequestList(@ApiIgnore LoginInfo loginInfo, @RequestBody ApproveRequestDTO dto) {
        checkLogin(loginInfo);
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        dto.setUpdateInfoFlag(false);
        iMemberService.approveRequest(dto);
        return new ItemResult<>(new Object());
    }

    @ApiOperation("拒绝请求")
    @PostMapping(value = "/rejectRequest")
    public ItemResult rejectRequest(@ApiIgnore LoginInfo loginInfo, @RequestBody RejectRequestDTO dto) {
        checkLogin(loginInfo);
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        iMemberService.rejectRequest(dto);
        return new ItemResult<>(new Object());
    }

    @ApiOperation("分页查询用户")
    @PostMapping(value = "/pageMemberListView")
    public ItemResult<PageInfo<MemberListViewDTO>> pageMemberListView(@RequestBody MemberQueryPageDTO query) {
        query.defaultPageIfNull();

        log.info("query:{}", query);
        PageInfo<MemberListViewDTO> pageInfo = iMemberService.pageMemberListView(query.getMemberQueryDTO(), query.getPageNum(), query.getPageSize());
        if (pageInfo != null && pageInfo.getList() != null && !pageInfo.getList().isEmpty()) {
            Set<String> adCodeSet = Sets.newHashSet();
            adCodeSet(pageInfo, adCodeSet);
            Map<String, String> map = regionService.findNameByAdCodeList(adCodeSet);
            setAreaName(pageInfo, map);
        }
        return new ItemResult<>(pageInfo);
    }

    private static void setAreaName(PageInfo<MemberListViewDTO> pageInfo, Map<String, String> map) {
        pageInfo.getList().forEach(item -> {
            if (StringUtils.isBlank(item.getProvinceName()) && StringUtils.isNotBlank(item.getProvinceCode())) {
                item.setProvinceName(map.getOrDefault(item.getProvinceCode(), ""));
            }
            if (StringUtils.isBlank(item.getCityName()) && StringUtils.isNotBlank(item.getCityCode())) {
                item.setCityName(map.getOrDefault(item.getCityCode(), ""));
            }
            if (StringUtils.isBlank(item.getAreaName()) && StringUtils.isNotBlank(item.getAreaCode())) {
                item.setAreaName(map.getOrDefault(item.getAreaCode(), ""));
            }
        });
    }

    private static void adCodeSet(PageInfo<MemberListViewDTO> pageInfo, Set<String> adCodeSet) {
        for (MemberListViewDTO item : pageInfo.getList()) {
            if (StringUtils.isBlank(item.getProvinceName()) && StringUtils.isNotBlank(item.getProvinceCode())) {
                adCodeSet.add(item.getProvinceCode());
            }
            if (StringUtils.isBlank(item.getCityName()) && StringUtils.isNotBlank(item.getCityCode())) {
                adCodeSet.add(item.getCityCode());
            }
            if (StringUtils.isBlank(item.getAreaName()) && StringUtils.isNotBlank(item.getAreaCode())) {
                adCodeSet.add(item.getAreaCode());
            }
            if (item.getCarrierFlg() != null && item.getCarrierFlg().intValue() == 1) {
            }
        }
    }

    @ApiOperation("根据ID查找用户详情（包括经营、资质、意向及审批信息）")
    @PostMapping(value = "/findMemberDetailById")
    public ItemResult<MemberDTO> findMemberDetailById(@ApiIgnore LoginInfo loginInfo, @RequestParam("memberId") String memberId) {
        checkLogin(loginInfo);
        MemberDTO memberDetailById = iMemberService.findMemberDetailById(memberId);
        return new ItemResult<>(memberDetailById);
    }

    @ApiOperation("禁用用户")
    @PostMapping(value = "/disableMember")
    public ItemResult disableMember(@ApiIgnore LoginInfo loginInfo, @RequestParam("memberId") String memberId) {
        checkLogin(loginInfo);
        iMemberService.disableMember(memberId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @ApiOperation("启用用户")
    @PostMapping(value = "/enableMember")
    public ItemResult enableMember(@ApiIgnore LoginInfo loginInfo, @RequestParam("memberId") String memberId) {
        checkLogin(loginInfo);
        iMemberService.enableMember(memberId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @ApiOperation("分页获取会员请求")
    @PostMapping(value="/pageMemberApprovalRequests")
    public ItemResult<PageInfo<MemberApprovalRequestDTO>> pageMemberApprovalRequests(@RequestBody MemberApprovalRequestPageDTO query){
        query.defaultPageIfNull();
        PageInfo<MemberApprovalRequestDTO> pageInfo = iMemberService.pageMemberApprovalRequests(query.getMemberApprovalRequestQueryDTO(), query.getPageNum(), query.getPageSize());
        updateAreaNameAndCarrierTypeName(pageInfo != null ? pageInfo.getList() : null);
        return new ItemResult<>(pageInfo);
    }

    @ApiOperation("获取一条变更记录")
    @PostMapping(value="/findMemberApprovalRequest")
    public ItemResult<MemberApprovalRequestDTO> findMemberApprovalRequest(@RequestParam("requestId") String requestId)throws Exception{
        MemberApprovalRequestDTO request = iMemberService.findMemberApprovalRequest(requestId);
        updateAreaNameAndCarrierTypeName(request == null ? null : Lists.newArrayList(request));
        return new ItemResult<>(request);
    }

    @ApiOperation("获取经营信息审批详情")
    @PostMapping(value="/getMemberApprovalBusinessDetails")
    public ItemResult getMemberApprovalBusinessDetails(@RequestParam("requestId") String requestId) {
        MemberBusinessRequestDetailDTO details = iMemberService.getMemberApprovalBusinessDetails(requestId);
        return new ItemResult<>(details);
    }

    @ApiOperation("获取会员商品购买意向")
    @GetMapping("/getIntentionsByMemberId")
    public ItemResult<List<MemberPurchaseGoodsIntentionDTO>> getIntentionsByMemberId(@RequestParam("memberId") String memberId) {
        return ItemResult.success(iMemberService.getIntentionsByMemberId(memberId));
    }

    @ApiOperation("获取会员变更商品购买意向明细")
    @GetMapping("/member/getIntentionsByRequestId")
    public ItemResult<List<MemberPurchaseGoodsIntentionDTO>> getIntentionsByRequestId(@RequestParam("requestId") String requestId){
        return ItemResult.success(iMemberService.getIntentionsByRequestId(requestId));
    }

    @ApiOperation("维护会员购买商品意向销售信息")
    @PostMapping("/maintainMemberGoodsIntention")
    public ItemResult<Boolean> maintainMemberGoodsIntention(@ApiIgnore LoginInfo loginInfo,
                                                            @Valid @RequestBody SubmitMemberPurchaseGoodsIntentionDTO param) {
        checkLogin(loginInfo);
        if(CharSequenceUtil.isBlank(param.getCrmCode()))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR,"请维护客户编码");
        verifyIntention(param.getIntentions());

        param.setOperatorId(loginInfo.getAccountId());
        param.setOperatorName(loginInfo.getRealName());
        return iMemberService.maintainMemberGoodsIntention(param);
    }

    @ApiOperation("审核通过会员商品意向信息变更")
    @PostMapping("/passMemberGoodsIntention")
    public ItemResult<Boolean> passMemberGoodsIntention(@ApiIgnore LoginInfo loginInfo,
                                                        @Valid @RequestBody SubmitMemberPurchaseGoodsIntentionDTO param) {
        checkLogin(loginInfo);
        if(CharSequenceUtil.isBlank(param.getRequestId()))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR,"请求参数不能为空");
        verifyIntention(param.getIntentions());

        param.setOperatorId(loginInfo.getAccountId());
        param.setOperatorName(loginInfo.getRealName());
        return iMemberService.passMemberGoodsIntention(param);
    }

    @ApiOperation("买家审批通过")
    @PostMapping(value = "/approveRequestByBuyerRegister")
    public ItemResult<Boolean> approveRequestByBuyerRegister(@ApiIgnore LoginInfo loginInfo, @RequestBody ApproveRequestDTO param) {
        checkLogin(loginInfo);
        if(CharSequenceUtil.isBlank(param.getMemberId()))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR,"会员ID不能为空");
        if(CharSequenceUtil.isBlank(param.getCrmCode()))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR,"请维护客户编码");
        verifyIntention(param.getIntentions());

        param.setOperatorId(loginInfo.getAccountId());
        param.setOperatorName(loginInfo.getRealName());
        param.setOperator(loginInfo.getOperator());
        param.setUpdateInfoFlag(false);

        return iMemberService.approveRequestByBuyerRegister(param);
    }


    private void updateAreaNameAndCarrierTypeName(List<MemberApprovalRequestDTO> list) {
        if (CollUtil.isEmpty(list))
            return;

        Set<String> adCodeSet = Sets.newHashSet();
        boolean search = false;
        search = adCodeSetAndIsSearch(list, adCodeSet, search);
        ValueSetTreeDTO valueSetTreeDTO = search ? valueSetService.getValueSetByReferenceCode("CARRIER_TYPE") : null;
        boolean bl = valueSetTreeDTO != null && valueSetTreeDTO.getTreeList() != null && !valueSetTreeDTO.getTreeList().isEmpty();
        Map<String, String> map = regionService.findNameByAdCodeList(adCodeSet);

        setAreaName(list, map, bl, valueSetTreeDTO);

    }

    private static void setAreaName(List<MemberApprovalRequestDTO> list, Map<String, String> map, boolean bl, ValueSetTreeDTO valueSetTreeDTO) {
        list.forEach(item -> {
            if (StringUtils.isBlank(item.getProvinceName()) && StringUtils.isNotBlank(item.getProvinceCode())) {
                item.setProvinceName(map.getOrDefault(item.getProvinceCode(), ""));
            }
            if (StringUtils.isBlank(item.getCityName()) && StringUtils.isNotBlank(item.getCityCode())) {
                item.setCityName(map.getOrDefault(item.getCityCode(), ""));
            }
            if (StringUtils.isBlank(item.getAreaName()) && StringUtils.isNotBlank(item.getAreaCode())) {
                item.setAreaName(map.getOrDefault(item.getAreaCode(), ""));
            }
            if (bl && Objects.equals(item.getCarrierFlg(), MemberNumberConstant.ONE) && StringUtils.isNotBlank(item.getCarrierType())) {
                valueSetTreeDTO.getTreeList().stream()
                        .filter(item2 -> StringUtils.equals(item2.getOptionKey(), item.getCarrierType()))
                        .forEach(item3 -> item.setCarrierTypeName(item3.getOptionValue()));
            }
        });
    }

    private static boolean adCodeSetAndIsSearch(List<MemberApprovalRequestDTO> list, Set<String> adCodeSet, boolean search) {
        for (MemberApprovalRequestDTO item : list) {
            if (StringUtils.isBlank(item.getProvinceName()) && StringUtils.isNotBlank(item.getProvinceCode())) {
                adCodeSet.add(item.getProvinceCode());
            }
            if (StringUtils.isBlank(item.getCityName()) && StringUtils.isNotBlank(item.getCityCode())) {
                adCodeSet.add(item.getCityCode());
            }
            if (StringUtils.isBlank(item.getAreaName()) && StringUtils.isNotBlank(item.getAreaCode())) {
                adCodeSet.add(item.getAreaCode());
            }
            if (item.getCarrierFlg() != null && item.getCarrierFlg().intValue() == 1) {
                search = true;
            }
        }
        return search;
    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }

    private static void verifyIntention(List<MemberPurchaseGoodsIntentionDTO> list) {
        if (CollUtil.isEmpty(list))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "意向购买信息不能为空");
        for (MemberPurchaseGoodsIntentionDTO v : list) {
            if (CharSequenceUtil.isBlank(v.getSaleChannel()))
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "未维护销售渠道");
            if (CharSequenceUtil.isBlank(v.getSaleUserId()))
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "未维护销售人员信息");
        }
        if (list.stream()
                .collect(Collectors
                        .groupingBy(v ->
                                        CharSequenceUtil.format("{}_{}", v.getGoodsCategoryId(), v.getGoodsId()),
                                Collectors.counting()))
                .values().stream()
                .anyMatch(v -> v > 1L)) {
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "意向购买信息中存在相同的商品类目和商品");
        }

    }
}