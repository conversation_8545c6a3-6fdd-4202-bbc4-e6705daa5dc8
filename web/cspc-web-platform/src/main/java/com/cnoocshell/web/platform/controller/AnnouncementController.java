package com.cnoocshell.web.platform.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.information.api.dto.announcement.*;
import com.cnoocshell.information.api.service.IAnnouncementService;
import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.service.IAccountService;
import com.cnoocshell.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @Created：Wed Aug 29 16:12:44 CST 2018
 * @Author: <EMAIL>
 * @Version:2
 * @Description:公告服务接口
*/

@Api(tags={"Announcement"},description = "公告服务")
@RestController
@RequestMapping("/announcement")
public class AnnouncementController {

   @Autowired 
   private IAnnouncementService iAnnouncementService;
   @Autowired
   private IAccountService accountService;

   @ApiOperation("修改一个公告")
   @PostMapping(value="/update")
   public ItemResult update(@ApiIgnore LoginInfo loginInfo,@Valid @RequestBody AnnouncementUpdateDTO announcementUpdateDTO)throws Exception{
      announcementUpdateDTO.setOperator(loginInfo.getAccountId());
      iAnnouncementService.update(announcementUpdateDTO);
      return new ItemResult(new Object());
   }


   @ApiOperation("删除一个公告")
   @PostMapping(value="/delete")
   public ItemResult delete(@ApiIgnore LoginInfo loginInfo, @RequestParam("id") String id)throws Exception{
      iAnnouncementService.delete(id,loginInfo.getAccountId());
      return new ItemResult(new Object());
   }


   @ApiOperation("添加一个公告")
   @PostMapping(value="/create")
   public ItemResult create(@ApiIgnore LoginInfo loginInfo,@Valid @RequestBody AnnouncementCreateDTO announcementCreateDTO)throws Exception{
      announcementCreateDTO.setOperator(loginInfo.getAccountId());
      announcementCreateDTO.setCreateUserName(loginInfo.getRealName());
      iAnnouncementService.create(announcementCreateDTO);
      return new ItemResult(new Object());
   }

   @ApiOperation("按条件翻页查询公告")
   @PostMapping(value="/findAll")
   public ItemResult<PageInfo<AnnouncementDTO>> findAll(@RequestBody AnnouncementQueryDTO query)throws Exception{
      return new ItemResult<>(iAnnouncementService.findAll(query));
   }

   @ApiOperation("查询单条公告详情(不含审批记录)")
   @PostMapping(value="/findById")
   public ItemResult<AnnouncementDTO> findById(@RequestParam("id") String id)throws Exception{
      return new ItemResult<>(iAnnouncementService.findById(id));
   }


   @ApiOperation("查询单条公告详情(含审批记录)")
   @PostMapping(value="/findDetailById")
   public ItemResult<AnnouncementDetailDTO> findDetailById(@RequestParam("id") String id)throws Exception{
      AnnouncementDetailDTO announcementDetailDTO = iAnnouncementService.findDetailById(id);
      List<AnnouncementApprovalDTO> list = announcementDetailDTO.getList();
      List<AnnouncementApprovalDTO> newList = new ArrayList<>();
      if(list != null && !list.isEmpty() ){
         Set<String> ids = list.stream().map(AnnouncementApprovalDTO::getCreateUser).collect(Collectors.toSet());
         List<AccountDTO> accountList = Lists.newArrayList();
         if( ids != null && !ids.isEmpty() ) {
            accountList = accountService.findByIds(Lists.newArrayList(ids));
         }
         Map<String,String> accountMap = accountList.stream().collect(Collectors.toMap(AccountDTO::getAccountId,account -> account.getAccountName() != null ? account.getAccountName() : ""));
         newList = list.stream().map(item -> {
            if(accountMap.containsKey(item.getUpdateUser())){
               item.setCreateUserName(accountMap.get(item.getUpdateUser()));
            }
            return item;
         }).collect(Collectors.toList());
      }
      announcementDetailDTO.setList(newList);
      return new ItemResult<>(announcementDetailDTO);
   }


   @ApiOperation("禁止（暂停）公告")
   @PostMapping(value="/pause")
   public ItemResult pause(@ApiIgnore LoginInfo loginInfo, @RequestParam("id") String id)throws Exception{
      iAnnouncementService.pause(id,loginInfo.getAccountId());
      return new ItemResult(new Object());
   }


   @ApiOperation("启用已暂停的公告(如果当前时间已经过期，当前公告也不会显示)")
   @PostMapping(value="/goon")
   public ItemResult<Boolean> goon(@ApiIgnore LoginInfo loginInfo, @RequestParam("id") String id)throws Exception{
      iAnnouncementService.goon(id,loginInfo.getAccountId());
      return new ItemResult<>(Boolean.TRUE);
   }


   @ApiOperation("公告审批")
   @PostMapping(value="/approval")
   public ItemResult<Boolean> approval(@ApiIgnore LoginInfo loginInfo, @RequestBody AnnouncementApprovalDTO announcementApprovalDTO)throws Exception{
      announcementApprovalDTO.setUpdateUser(loginInfo.getAccountId());
      iAnnouncementService.approval(announcementApprovalDTO);
      return new ItemResult<>(Boolean.TRUE);
   }


   @ApiOperation("查询主页显示的公共，查询对象可以为空")
   @PostMapping(value="/findForIndex")
   public ItemResult<List<AnnouncementDTO>> findForIndex(@RequestBody AnnouncementIndexQueryDTO query){
      return new ItemResult<>(iAnnouncementService.findForIndex(query));
   }

}
