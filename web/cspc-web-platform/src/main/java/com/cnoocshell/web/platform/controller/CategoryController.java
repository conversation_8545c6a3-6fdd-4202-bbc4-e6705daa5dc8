package com.cnoocshell.web.platform.controller;

import com.cnoocshell.common.constant.HeaderConstant;
import com.cnoocshell.common.enums.ApplicationType;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.CategoryDTO;
import com.cnoocshell.goods.api.dto.CategoryTreeDTO;
import com.cnoocshell.goods.api.dto.GoodsCategorySimpleDTO;
import com.cnoocshell.goods.api.dto.GoodsSimpleDTO;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.cnoocshell.member.api.session.LoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Description  Web端控制器，平台商品分类相关接口
 * @date   2019年1月28日 上午10:29:31
 */
@Slf4j
@RestController
@RequestMapping("/category")
@Api(tags={"CategoryController"},description = "Web端控制器，平台商品分类相关接口")
public class CategoryController {

    @Autowired
    private IGoodsCategoryService goodsCategoryService;

    @ApiOperation("查询商品分类树--新接口")
    @PostMapping(value="/anon/findCategoryTree")
    public ItemResult<List<CategoryTreeDTO>> findCategoryTree(String categoryCode, HttpServletRequest request){
        log.info("CategoryController findCategoryTree,req:{}", categoryCode);
        String type = request.getHeader(HeaderConstant.APPLICATION_TYPE);
        if (StringUtils.isNotEmpty(type) && type.equals(ApplicationType.MP.getType())) {
            return  new ItemResult<>(goodsCategoryService.findCategoryTreeByMp(categoryCode));
        }
        return  new ItemResult<>(goodsCategoryService.findCategoryTree(categoryCode));
    }

    @ApiOperation("查询商品分类树--小程序")
    @PostMapping(value="/anon/findCategoryTreeByMp")
    public ItemResult<List<CategoryTreeDTO>> findCategoryTreeByMp(String categoryCode){
        log.info("CategoryController findCategoryTree,req:{}", categoryCode);
        return  new ItemResult<>(goodsCategoryService.findCategoryTreeByMp(categoryCode));
    }

    @ApiOperation("查询商品分类树和商品--新接口")
    @PostMapping(value="/anon/findCategoryTreeGoods")
    public ItemResult<List<CategoryTreeDTO>> findCategoryTreeGoods(String categoryCode){
        log.info("CategoryController findCategoryTreeGoods,req:{}", categoryCode);
        return  new ItemResult<>(goodsCategoryService.findCategoryTreeGoods(categoryCode));
    }

    @ApiOperation("添加商品分类--新接口")
    @PostMapping(value="/addCategory")
    public ItemResult<Boolean> addCategory(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody CategoryDTO categoryDTO){
        checkLogin(loginInfo);
        log.info("CategoryController addCategory,req:{}", categoryDTO);
        goodsCategoryService.addCategory(categoryDTO,loginInfo.getAccountId());
        return  new ItemResult<>(true);
    }

    @ApiOperation("修改商品分类--新接口")
    @PostMapping(value="/uptCategory")
    public ItemResult<Boolean> uptCategory(@ApiIgnore LoginInfo loginInfo, @Valid @RequestBody CategoryDTO categoryDTO){
        checkLogin(loginInfo);
        log.info("CategoryController uptCategory,req:{}", categoryDTO);
        goodsCategoryService.uptCategory(categoryDTO,loginInfo.getAccountId());
        return  new ItemResult<>(true);
    }

    /**
     *
     * @param loginInfo
     * @param spuType  1标品 0单一商品
     * @return
     */
    @ApiOperation("获取标准商品属性配置--新接口")
    @PostMapping(value="/anon/getCategory")
    public ItemResult<CategoryDTO> getCategory(@ApiIgnore LoginInfo loginInfo, @RequestParam("spuType") String spuType){
        log.info("CategoryController getCategory}");
        checkLogin(loginInfo);
        return  new ItemResult<>(goodsCategoryService.getCategory(spuType));
    }

    @ApiOperation("删除商品分类--新接口")
    @PostMapping(value="/delCategory")
    public ItemResult<Boolean> delCategory(@ApiIgnore LoginInfo loginInfo, @Valid @RequestParam("categoryId") String categoryId){
        checkLogin(loginInfo);
        log.info("CategoryController delCategory,req:{}", categoryId);
        goodsCategoryService.delCategory(categoryId,loginInfo.getAccountId());
        return  new ItemResult<>(true);
    }


    @ApiOperation("根据查询商品分类树--新接口")
    @PostMapping(value="/anon/findCategoryTreeByCode")
    public ItemResult<List<CategoryTreeDTO>> findCategoryTreeByCode(@Valid @RequestParam("categoryCode") String categoryCode){
        log.info("CategoryController findCategoryTreeByCode,req:{}", categoryCode);
        return  new ItemResult<>(goodsCategoryService.findCategoryTreeByCode(categoryCode));
    }

    @ApiOperation("根据accountId查询分类绑定的经理")
    @PostMapping(value = "/findCategoryByCountId")
    ItemResult<List<GoodsCategorySimpleDTO>> findCategoryByCountId(@RequestBody GoodsCategorySimpleDTO categorySimpleDTO){
        return new ItemResult<>(goodsCategoryService.findCategoryByCountId(categorySimpleDTO));
    }

    @ApiOperation("根据分类id获取商品信息")
    @GetMapping(value= "/findGoodsSimpleByCategoryId")
    ItemResult<List<GoodsSimpleDTO>> findGoodsSimpleByCategoryId(@RequestParam("categoryId") String categoryId){
        return goodsCategoryService.findGoodsSimpleByCategoryId(categoryId);
    }

    private void checkLogin(LoginInfo loginInfo){
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId())){
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
    }

}