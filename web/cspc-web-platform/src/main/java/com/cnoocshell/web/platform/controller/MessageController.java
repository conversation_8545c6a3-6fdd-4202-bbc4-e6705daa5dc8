package com.cnoocshell.web.platform.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.information.api.dto.internalmessage.*;
import com.cnoocshell.information.api.service.IInternalMessageService;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.web.common.utils.LoginUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags={"InternalMessage"},description = ": 站内信服务")
@RestController
@RequiredArgsConstructor
@RequestMapping("/internalMessage")
public class MessageController {

    @Autowired
    private IInternalMessageService internalMessageService;

    @ApiOperation("发送消息")
    @PostMapping(value="/sendMessage")
    public ItemResult<Boolean> sendMessage(@RequestBody InternalMessageCreateDTO internalMessageCreateDTO) {
        return new ItemResult<>(internalMessageService.sendMessage(internalMessageCreateDTO));
    }

    @ApiOperation("删除消息")
    @PostMapping(value="/delete")
    public ItemResult<Boolean> delete(@RequestBody InternalMessageDeleteDTO internalMessageDeleteDTO) {
        return new ItemResult<>(internalMessageService.delete(internalMessageDeleteDTO));
    }

    @ApiOperation("更新已读状态消息")
    @PostMapping(value="/updateReadStatus")
    public ItemResult<Boolean> updateReadStatus(@ApiIgnore LoginInfo loginInfo,@RequestBody InternalMessageReadStatusUpdateDTO param) {
        LoginUtil.checkLogin(loginInfo);
        param.setAccountId(loginInfo.getAccountId());
        param.setMemberId(loginInfo.getMemberId());
        param.setOperator(loginInfo.getAccountId());
        return new ItemResult<>(internalMessageService.updateReadStatus(param));
    }

    @ApiOperation("分页")
    @PostMapping(value="/findAll")
    public ItemResult<PageInfo<InternalMessageDTO>> findAll(@ApiIgnore LoginInfo loginInfo,@RequestBody InternalMessageQueryDTO internalMessageQueryDTO) {
        internalMessageQueryDTO.setMemberId(loginInfo.getMemberId());
        internalMessageQueryDTO.setAccountId(loginInfo.getAccountId());
        return new ItemResult<>(internalMessageService.findAll(internalMessageQueryDTO));
    }

    @ApiOperation("根据账号查询未读消息数量")
    @PostMapping(value="/count")
    public ItemResult<Integer> count(@RequestParam("accountId") String accountId) {
        return new ItemResult<>(internalMessageService.count(accountId));
    }

    @ApiOperation("根据消息id查询消息详情")
    @PostMapping(value="/findById")
    public ItemResult<InternalMessageDTO> findById(@RequestParam("id") String id) {
        return new ItemResult<>(internalMessageService.findById(id));
    }
}
