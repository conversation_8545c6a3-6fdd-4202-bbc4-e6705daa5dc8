<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>web</artifactId>
		<groupId>com.cnoocshell</groupId>
		<version>2.1.4-RELEASE</version>
	</parent>
	<artifactId>cspc-web-platform</artifactId>
	<packaging>jar</packaging>
	<name>cspc-web-platform</name>
	<version>2.1.4-RELEASE</version>
	<description>Web Platform Microservice</description>
	
	<properties>
		<docker.deploy.version>${project.version}</docker.deploy.version>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
		</dependency>

		<!--poi-->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
		</dependency>

		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>cspc-web-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>cspc-goods-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>cspc-order-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>cspc-information-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.cuisongliu</groupId>
			<artifactId>kaptcha-spring-boot-autoconfigure</artifactId>
			<version>1.3</version>
		</dependency>

		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
			<version>1.5.10</version>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>4.2.2</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>cspc-web-platform</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<dependencies>
					<!-- spring热部署-->
					<dependency>
						<groupId>org.springframework</groupId>
						<artifactId>springloaded</artifactId>
						<version>1.2.8.RELEASE</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>
</project>
