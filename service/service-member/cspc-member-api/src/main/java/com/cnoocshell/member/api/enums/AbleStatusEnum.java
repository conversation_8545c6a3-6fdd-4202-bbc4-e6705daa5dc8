package com.cnoocshell.member.api.enums;

import com.cnoocshell.member.api.dto.member.enums.MemberStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;


/**
 * 企业启用禁用状态
 * 通过member中status=201(禁用) 状态转换得到
 * 参考 {@link MemberStatusEnum}
 */
@Getter
@AllArgsConstructor
public enum AbleStatusEnum {
    ENABLE(1, "禁用"),
    DISABLE(0, "启用");

    private Integer status;
    private String name;


    public static AbleStatusEnum getByStatus(Integer status) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getStatus(), status))
                .findFirst()
                .orElse(null);
    }

    public static String getNameByStatus(Integer status) {
        AbleStatusEnum ableStatusEnum = getByStatus(status);
        return Objects.nonNull(ableStatusEnum) ? ableStatusEnum.getName() : null;
    }
}
