package com.cnoocshell.member.api.service;

import com.cnoocshell.member.api.dto.accountRelation.AccountRelationInfoDTO;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationPage;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationPageRequestDTO;
import com.cnoocshell.member.api.dto.accountRelation.QueryAccountRelationDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "cspc-service-member")
public interface IAccountRelationService {

    @ApiOperation("分页查询")
    @PostMapping(value = "/accountRelation/findAccountRelationByCondition")
    public PageInfo<AccountRelationPage> findAccountRelationByCondition(@RequestBody AccountRelationPageRequestDTO requestDTO);

    @ApiOperation("新增")
    @PostMapping(value = "/accountRelation/addAccountRelation")
    Boolean addAccountRelation(@RequestBody AccountRelationInfoDTO requestDTO);

    @ApiOperation("删除")
    @PostMapping(value = "/accountRelation/deleteAccountRelation")
    public String deleteAccountRelation(@RequestParam String id);

    @ApiOperation("查询账户关联账户信息")
    @PostMapping("/accountRelation/queryAccountRelation")
    List<AccountRelationInfoDTO> queryAccountRelation(@RequestBody QueryAccountRelationDTO param);
}
