package com.cnoocshell.member.api.dto.account;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 微信小程序订阅记录表对应的实体类。
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MbAccountSubscribeWechatDTO {

    /**
     * 主键ID，使用自增策略。
     */
    private Long id;

    /**
     * 账号ID，不能为空。
     */
    private String accountId;

    /**
     * 账户真实姓名。
     */
    private String accountRealName;

    /**
     * 订阅业务编号。
     */
    private String businessNo;

    /**
     * 订阅业务类型：ENQUIRY-询价，BIDDING-竞价。
     */
    private String businessType;

    /**
     * 微信用户的openid。
     */
    private String openId;

    /**
     * 模板ID。
     */
    private String templateId;

    /**
     * 模板名称。
     */
    private String templateName;

    /**
     * 推送状态：0未推送，1已推送。
     */
    private String sendStatus;

    /**
     * 删除标志，默认值为0（false）。
     */
    private Boolean delFlg;

    /**
     * 创建人。
     */
    private String createUser;

    /**
     * 创建时间。
     */
    private Date createTime;

    /**
     * 修改人。
     */
    private String updateUser;

    /**
     * 修改时间。
     */
    private Date updateTime;

}