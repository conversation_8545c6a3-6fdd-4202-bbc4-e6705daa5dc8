package com.cnoocshell.member.api.dto.accountRelation;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryAccountRelationDTO {
    @ApiModelProperty("账户ID")
    private List<String> accountIds;

    @ApiModelProperty("账户角色")
    private List<String> accountRoles;

    @ApiModelProperty("账户关联账户角色")
    private List<String> linkAccountRoles;
}
