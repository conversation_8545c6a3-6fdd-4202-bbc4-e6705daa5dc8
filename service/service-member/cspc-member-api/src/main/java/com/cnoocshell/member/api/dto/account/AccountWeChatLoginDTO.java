package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @DESCRIPTION:
 */
@ApiModel("微信登陆DTO")
@Data
public class AccountWeChatLoginDTO implements Serializable {
    private static final long serialVersionUID = -4480299318274802408L;

    /**
     * 微信码
     */
    @NotBlank
    @ApiModelProperty("微信码")
    private String weChatCode;

    /**
     * 手机
     */
    @NotBlank
    @ApiModelProperty("手机")
    private String mobile;

    /**
     * 短信验证码
     */
    @ApiModelProperty("短信验证码")
    private String smsCode;

    /**
     * 短信验证码凭证
     */
    @ApiModelProperty("短信验证码凭证")
    private String certificate;

    /**
     * 客户端ip
     */
    @ApiModelProperty("客户端ip")
    private String ip;

    /**
     * 会话id
     */
    @ApiModelProperty("会话id")
    private String sessionId;

    /**
     * app名称
     */
    @ApiModelProperty("app名称")
    private String loginType;

    /**
     * 客户端网卡物理地址
     */
    @ApiModelProperty("客户端网卡物理地址")
    private String mac;

    /**
     * 终端
     */
    @ApiModelProperty("终端")
    private String terminal;

    /**
     * 系统
     */
    @ApiModelProperty("系统")
    private String os;

    /**
     * 系统版本
     */
    @ApiModelProperty("系统版本")
    private String osVersion;

    /**
     * 机器品牌
     */
    @ApiModelProperty("机器品牌")
    private String deviceBrand;

    /**
     *安卓手机唯一识别码
     */
    @ApiModelProperty("安卓手机唯一识别码")
    private String imei;

    /**
     * 浏览器信息
     */
    @ApiModelProperty("浏览器信息")
    private String userAgent;
}
