package com.cnoocshell.member.api.service;

import com.cnoocshell.member.api.dto.account.AccountChangeHistoryDTO;
import com.cnoocshell.member.api.dto.account.PageAccountChangeHistoryDTO;
import com.cnoocshell.member.api.dto.account.UpdateAccountChangeHistoryDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description::null
*/
@Tag(name = "IAccountChangeHistoryService", description = "员工授权，启用，禁用历史")
@FeignClient(name ="cspc-service-member")
public interface IAccountChangeHistoryService {


   @ApiOperation("新增员工授权，启用，禁用历史")
   @PostMapping(path = "/accountChangeHistory/add", consumes = "application/json")
   void add(@RequestBody AccountChangeHistoryDTO accountChangeHistoryDTO, @RequestParam("operator") String operator);

   @ApiOperation("根据DTO分页查询员工授权，启用，禁用历史")
   @PostMapping(path = "/accountChangeHistory/pageHistoryInfoList", consumes = "application/json")
   PageInfo<AccountChangeHistoryDTO> pageHistoryInfoList(@RequestBody PageAccountChangeHistoryDTO pageAccountChangeHistoryDTO);

   @ApiOperation("根据DTO查询员工授权，启用，禁用历史")
   @PostMapping(path = "/accountChangeHistory/findByQuery", consumes = "application/json")
   List<AccountChangeHistoryDTO> findByQuery(@RequestBody AccountChangeHistoryDTO accountChangeHistoryDTO);

   @ApiOperation("删除账号变更记录")
   @PostMapping(path = "/accountChangeHistory/removeHistoryByIds", consumes = "application/json")
   void removeHistoryByIds(@RequestBody UpdateAccountChangeHistoryDTO param);
}
