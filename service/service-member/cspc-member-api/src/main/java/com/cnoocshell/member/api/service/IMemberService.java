package com.cnoocshell.member.api.service;

import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.dto.member.intention.MemberIntentionSimpleDTO;
import com.cnoocshell.member.api.dto.report.MemberAccountReportQueryDTO;
import com.cnoocshell.member.api.dto.report.MemberAccountReportResultDTO;
import com.cnoocshell.member.api.dto.report.MemberReportQueryDTO;
import com.cnoocshell.member.api.dto.report.MemberReportResultDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description::会员对外服务接口
 */
@Tag(name = "IMemberService", description = "会员对外服务接口")
@FeignClient(name = "cspc-service-member")
//@FeignClient(name = "cspc-service-member", url = "http://***********:8089")
public interface IMemberService {
    @ApiOperation("根据ID查看会员简要信息")
    @PostMapping(value = "/member/findMemberSimpleById", consumes = "application/json")
    MemberSimpleDTO findMemberSimpleById(@RequestParam("memberId") String memberId);

    @ApiOperation("分页获取会员请求")
    @PostMapping(path = "/member/pageRegisterMemberApprovalRequests", consumes = "application/json")
    PageInfo<MemberApprovalRequestDTO> pageRegisterMemberApprovalRequests(@RequestBody MemberApprovalRequestQueryDTO query,
                                                                          @RequestParam("pageNum") Integer pageNum,
                                                                          @RequestParam("pageSize") Integer pageSize);

    @ApiOperation("获取会员请求详情")
    @PostMapping(value = "/member/getMemberApprovalDetails", consumes = "application/json")
    MemberDTO getMemberApprovalDetails(@RequestParam("memberApprovalRequestId") String memberApprovalRequestId);

    @ApiOperation("按照id查询会员（只包括已审批的资质）")
    @PostMapping(path = "/member/findRealMemberById", consumes = "application/json")
    MemberDTO findRealMemberById(@RequestParam("id") String id);

    @ApiOperation("根据ID查找会员详情（没有资质信息）")
    @PostMapping(value = "/member/findMemberDetailById", consumes = "application/json")
    MemberDTO findMemberDetailById(@RequestParam("memberId") String memberId);

    @ApiOperation("根据账户ID查找实名认证详情")
    @PostMapping(value = "/member/findRealNameCertByAccountId", consumes = "application/json")
    MemberCertDTO findRealNameCertByAccountId(@RequestParam("accountId") String accountId);

    @ApiOperation("提交企业注册（买家）")
    @PostMapping(path = "/member/registerBuyer", consumes = "application/json")
    ItemResult<String> registerBuyer(@RequestBody MemberRequestDTO memberRequestDTO);

    @ApiOperation("分页获取会员请求")
    @PostMapping(path = "/member/pageMemberApprovalRequests", consumes = "application/json")
    PageInfo<MemberApprovalRequestDTO> pageMemberApprovalRequests(@RequestBody MemberApprovalRequestQueryDTO query, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize);

    @ApiOperation("审批请求")
    @PostMapping(path = "/member/approveRequest", consumes = "application/json")
    void approveRequest(@RequestBody ApproveRequestDTO dto);

    @ApiOperation("审批拒绝")
    @PostMapping(path = "/member/rejectRequest", consumes = "application/json")
    void rejectRequest(@RequestBody RejectRequestDTO dto);

    @ApiOperation("更新会员基本信息")
    @PostMapping(value = "/member/updateBaseInfo", consumes = "application/json")
    void updateBaseInfo(@RequestBody MemberBaseInfoDTO memberBaseInfoDTO);

    @ApiOperation("分页查询会员")
    @PostMapping(value = "/member/pageMemberListView", consumes = "application/json")
    PageInfo<MemberListViewDTO> pageMemberListView(@RequestBody MemberQueryDTO query,
                                                   @RequestParam("pageNum") Integer pageNum,
                                                   @RequestParam("pageSize") Integer pageSize);

    @ApiOperation("禁用会员")
    @PostMapping(path = "/member/disableMember", consumes = "application/json")
    void disableMember(@RequestParam("memberId") String memberId, @RequestParam("operatorId") String operatorId);

    @ApiOperation("启用会员")
    @PostMapping(path = "/member/enableMember", consumes = "application/json")
    void enableMember(@RequestParam("memberId") String memberId, @RequestParam("operatorId") String operatorId);

    @ApiOperation("按照id查询会员（包括资质）")
    @GetMapping(path = "/member/findMemberById", consumes = "application/json")
    MemberDetailDTO findMemberById(@RequestParam("id") String id);

    @ApiOperation("变更企业经营信息")
    @PostMapping(path = "/member/updateBusinessInfo", consumes = "application/json")
    String updateBusinessInfo(@RequestBody MemberBusinessInfoDTO memberBusinessInfoDTO);

    @ApiOperation("一般资质变更,必须写明资质类型 certType、CertId")
    @PostMapping(path = "/member/updateCert", consumes = "application/json")
    String updateCert(@RequestBody UpdateCertDTO dto);

    @ApiOperation("获取一条变更记录")
    @PostMapping(value = "/member/findMemberApprovalRequest", consumes = "application/json")
    MemberApprovalRequestDTO findMemberApprovalRequest(@RequestParam("requestId") String requestId);

    @ApiOperation("获取经营信息审批详情")
    @PostMapping(path = "/member/getMemberApprovalBusinessDetails", consumes = "application/json")
    MemberBusinessRequestDetailDTO getMemberApprovalBusinessDetails(@RequestParam("requestId") String requestId);

    @ApiOperation("获取会员商品购买意向")
    @GetMapping("/member/getIntentionsByMemberId")
    List<MemberPurchaseGoodsIntentionDTO> getIntentionsByMemberId(@RequestParam("memberId") String memberId);

    @ApiOperation("获取会员变更商品购买意向明细")
    @GetMapping("/member/getIntentionsByRequestId")
    List<MemberPurchaseGoodsIntentionDTO> getIntentionsByRequestId(@RequestParam("requestId") String requestId);

    @ApiOperation("维护会员购买商品意向销售信息")
    @PostMapping("/member/maintainMemberGoodsIntention")
    ItemResult<Boolean> maintainMemberGoodsIntention(@RequestBody SubmitMemberPurchaseGoodsIntentionDTO param);

    @ApiOperation("审核通过会员商品意向信息变更")
    @PostMapping("/member/passMemberGoodsIntention")
    ItemResult<Boolean> passMemberGoodsIntention(@RequestBody SubmitMemberPurchaseGoodsIntentionDTO param);

    @ApiOperation("买家审批通过")
    @PostMapping(value = "/member/approveRequestByBuyerRegister")
    ItemResult<Boolean> approveRequestByBuyerRegister(@RequestBody ApproveRequestDTO param);

    @ApiOperation("提交会员商品意向数据变更请求")
    @PostMapping(value = "/member/submitMemberIntentionChange")
    ItemResult<Boolean> submitMemberIntentionChange(@RequestBody SubmitMemberPurchaseGoodsIntentionDTO param);

    @ApiOperation("根据会员ID查找会员代码")
    @GetMapping(value = "/member/findMemberCodeByMemberId", consumes = "application/json")
    String findMemberCodeByMemberId(@RequestParam("memberId") String memberId);

    @ApiOperation("根据意向商品ids获取用户信息")
    @PostMapping(value = "/member/findMemberInfoByGoodsIds")
    List<MemberPurchaseGoodsIntentionDTO> findMemberInfoByGoodsIds(@RequestBody List<String> goodsIds);

    @ApiOperation("根据memberids获取信息")
    @PostMapping(value = "/member/findMemberByIds")
    List<MemberDetailDTO> findMemberByIds(@RequestBody List<String> memberIds);

    @ApiOperation("根据会员编码获取会员下账户消息")
    @PostMapping(value = "/member/listAccountsByMemberCodes")
    List<AccountSimpleDTO> listAccountsByMemberCodes(@RequestBody QueryMemberAccountDTO param);

    @ApiOperation("根据会员编码获取会员信息")
    @PostMapping(value = "/member/listSimpleMemberByCodes")
    List<MemberSimpleDTO> listSimpleMemberByCodes(@RequestBody List<String> memberCodes);

    @ApiOperation("根据意向商品ids获取用户信息")
    @GetMapping(value = "/member/findMembersBySalesUserIdAndGoodsCode")
    List<MemberPurchaseGoodsIntentionDTO> findMembersBySalesUserIdAndGoodsCode(@RequestParam String goodsId, @RequestParam String salesUserId);

    @ApiOperation("根据商品code找到人")
    @GetMapping(value = "/member/queryMemberInfo")
    List<MemberDataInfoDTO> queryMemberInfo(@RequestParam String goodsCode);

    @ApiOperation("根据会员code查找意向商品")
    @GetMapping(value = "/member/queryGoodsByMemberCode")
    List<MemberGoodsInfoDTO> queryGoodsByMemberCode(@RequestParam List<String> memberCodeList);

    @ApiOperation("根据商品和销售人员ID查询匹配会员意向信息")
    @PostMapping(value = "/member/queryMemberBySaleInfo")
    List<MemberPurchaseGoodsIntentionDTO> queryMemberBySaleInfo(@RequestBody QueryIntentionInfoDTO param);

    @ApiModelProperty("根据商品code查找会员信息")
    @PostMapping(path = "/member/queryMemberInfoByGoodsCode", consumes = "application/json")
    List<MemberInfoForGoodsDTO> queryMemberInfoByGoodsCode(@RequestBody MemberQueryByGoodsDTO memberQueryByGoodsDTO);

    @ApiModelProperty("查询企业用户(报表使用)")
    @PostMapping(path = "/member/queryMemberByMemberNameAndCrmCode", consumes = "application/json")
    PageInfo<MemberSimpleDataDTO> queryMemberByMemberNameAndCrmCode(@RequestBody MemberSimpleDataDTO memberSimpleDataDTO);


    @ApiOperation("企业保证金状态列表查询接口")
    @PostMapping(path = "/member/queryMemberDepositStatus", consumes = "application/json")
    PageInfo<MemberDepositStatusDataDTO> queryMemberDepositStatus(@RequestBody MemberDepositStatusDTO dto);

    @ApiOperation("企业保证金状态保存接口")
    @PostMapping(path = "/member/saveMemberDepositStatus", consumes = "application/json")
    Boolean saveMemberDepositStatus(@RequestBody MemberSaveDepositStatusDTO dto) ;

    @ApiOperation("企业保证金状态列表导出接口")
    @PostMapping(path = "/member/exportMemberDepositStatus", consumes = "application/json")
    ExportExcelDTO exportMemberDepositStatus(@RequestBody MemberDepositStatusDTO dto);

    @ApiOperation("企业保证金状态列表导出接口数据")
    @PostMapping(path = "/member/exportMemberDepositStatusDataList", consumes = "application/json")
    List<MemberDepositStatusExportDTO> exportMemberDepositStatusDataList(@RequestBody MemberDepositStatusDTO dto);

    @ApiOperation("企业管理员申请退款接口")
    @PostMapping(path = "/member/requestRefund", consumes = "application/json")
    Boolean requestRefund(@RequestBody MemberRefundDepositStatusDTO dto) ;

    @ApiOperation("企业保证金状态导入接口")
    @PostMapping(path = "/member/import/excel", consumes = "application/json")
    ItemResult<MemberDepositStatusImportResultDTO>  importExcel( @RequestBody MemberDepositStatusImportDataDTO dto) ;

    @ApiOperation("查询企业保证金状态接口")
    @GetMapping(value = "/member/query/deposit/status")
    String  queryDepositStatus(@RequestParam("memberCode") String memberCode);

    @ApiOperation("查询goodsCode")
    @GetMapping(value = "/member/query/queryGoodsCodeByMemberId")
    List<String>  queryGoodsCodeByMemberId(@RequestParam("memberId") String memberId);

    @ApiOperation("会员是否存在企业注册记录")
    @GetMapping(value = "/member/existEnterpriseRegistrationRequest")
    Boolean existEnterpriseRegistrationRequest(@RequestParam("memberId") String memberId,
                                               @RequestParam(value = "status",required = false) String status);

    @ApiOperation("查询可参与竞价其它客户")
    @PostMapping("/member/queryBiddingMemberList")
    List<BiddingMemberDTO> queryBiddingMemberList(@RequestBody QueryBiddingMemberDTO param);

    @GetMapping("/member/queryMemberByLikeCrmCode")
    List<MemberSimpleDTO> queryMemberByLikeCrmCode(@RequestParam String crmCode);

    @ApiOperation("更加memberCode查询会员信息 根据goodsCode查询会员意向信息")
    @PostMapping("/member/queryMemberIntentionInfo")
    List<MemberIntentionInfoDTO> queryMemberIntentionInfo(@RequestBody QueryMemberIntentionInfoDTO param);

    @ApiOperation("客户报表 列表查询")
    @PostMapping("/member/memberReport")
    PageInfo<MemberReportResultDTO> memberReport(@RequestBody MemberReportQueryDTO param);

    @ApiOperation("客户账号报表 列表查询")
    @PostMapping("/member/memberAccountReport")
    PageInfo<MemberAccountReportResultDTO> memberAccountReport(@RequestBody MemberAccountReportQueryDTO param);

    @ApiOperation("根据销售渠道编码查询匹配的意向信息")
    @PostMapping("/member/queryIntentionBySaleChannel")
    List<MemberIntentionSimpleDTO> queryIntentionBySaleChannel(@RequestBody List<String> saleChannels);
}