package com.cnoocshell.member.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum IntentionTypeEnum {
    MAIN(1, "主要意向"),
    SECONDARY(2, "次要意向");


    private Integer type;
    private String name;

    public static IntentionTypeEnum getByType(Integer type) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(type, v.getType()))
                .findFirst()
                .orElse(null);
    }

    public static String getNameByType(Integer type) {
        IntentionTypeEnum intentionTypeEnum = getByType(type);
        return Objects.isNull(intentionTypeEnum) ? null : intentionTypeEnum.getName();
    }
}
