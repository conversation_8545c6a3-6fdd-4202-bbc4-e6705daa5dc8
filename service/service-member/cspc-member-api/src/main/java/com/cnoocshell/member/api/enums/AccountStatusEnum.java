package com.cnoocshell.member.api.enums;

import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.member.api.constant.MemberNumberConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;


/**
 *账户状态
 */
@Getter
@AllArgsConstructor
public enum AccountStatusEnum {
    ENABLE(1, "禁用"),
    DISABLE(0, "启用");

    private Integer status;
    private String name;


    public static AccountStatusEnum getByStatus(Integer status) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getStatus(), status))
                .findFirst()
                .orElse(null);
    }

    public static String getNameByStatus(Integer status) {
        AccountStatusEnum ableStatusEnum = getByStatus(status);
        return Objects.nonNull(ableStatusEnum) ? ableStatusEnum.getName() : null;
    }
    public static String getNameByStatus(Boolean status) {
        Integer statusByInt = null;
        if(BooleanUtil.isTrue(status))
            statusByInt = MemberNumberConstant.ONE;
        if(BooleanUtil.isFalse(status))
            statusByInt = MemberNumberConstant.ZERO;

        AccountStatusEnum ableStatusEnum = getByStatus(statusByInt);
        return Objects.nonNull(ableStatusEnum) ? ableStatusEnum.getName() : null;
    }

}
