package com.cnoocshell.member.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 协议业务单号类型
 */
@Getter
@AllArgsConstructor
public enum AgreementBusinessTypeEnum {
    ACCOUNT_ID("VO_NO_TYPE_ACCOUNT_ID", "账号ID"),
    ENQUIRY_BUYER_DETAIL_ID("VO_NO_TYPE_ENQUIRY_BUYER_DETAIL_ID", "询价报量明细ID"),
    BIDDING_BUYER_DETAIL_ID("VO_NO_TYPE_BIDDING_BUYER_DETAIL_ID", "竞价报量明细ID");

    private String type;
    private String name;
    }
