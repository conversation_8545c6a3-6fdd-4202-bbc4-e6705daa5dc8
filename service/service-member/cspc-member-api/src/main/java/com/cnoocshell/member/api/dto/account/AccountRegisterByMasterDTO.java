package com.cnoocshell.member.api.dto.account;

import com.cnoocshell.member.api.dto.base.RoleDTO;
import com.cnoocshell.member.api.enums.RegexPatterns;
import com.cnoocshell.member.api.enums.RegisterTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 07/08/2018 17:24
 * @DESCRIPTION:
 */
@Data
public class AccountRegisterByMasterDTO implements Serializable {
    /**
     * 用户名格式
     */
    @ApiModelProperty("用户名格式")
    public static final String USERNAME_PATTERN = "^$|^[a-zA-Z]\\w{5,19}$";
    /**
     * 电话号码格式
     */
    @ApiModelProperty("电话号码格式")
    public static final String MOBILE_PHONE_NUMBER_PATTERN = "^0?(1\\d{2})\\d{8}$";

    private static final long serialVersionUID = -5713316270210262182L;

    /**
     * 账户名称
     */
    @ApiModelProperty("账户名称(6到20个字母、数字或下划线组成，且必须以字母开头)")
//    @NotBlank
    @Pattern(regexp = USERNAME_PATTERN, message = "6到20个字母、数字或下划线组成，且必须以字母开头")
    private String accountName;

    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    @NotBlank(message = "ID不能为空")
    private String memberId;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;

    /**
     * 真实名称
     */
    @ApiModelProperty("真实名称")
    @NotBlank(message ="姓名不能为空")
    private String realName;

    /**
     * 员工id
     */
    @ApiModelProperty("员工id")
    @Size(max = 10,message = "工号限制输入10位字符")
    private String employeeId;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("职务")
    private String position;

    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 电话号码
     */
    @ApiModelProperty("电话号码")
    @NotBlank(message = "电话号码不能为空")
    private String mobile;

    /**
     * 是否业务员
     */
    @ApiModelProperty("是否业务员")
    private Boolean salesman;

    /**
     * 销售区域id列表
     */
    @ApiModelProperty("销售区域id列表")
    private List<String> saleRegionInfoIdList;
    /**
     * 角色
     */
    @ApiModelProperty("角色")
    private List<RoleDTO> roleList;
    /**
     * 角色id集合
     */
    @ApiModelProperty("角色id集合")
    private List<Integer> roleIdList;

    /**
     * 操作人id
     */
    @ApiModelProperty("操作人id")
    private String operatorId;

    /**
     * 注册时使用的应用名称
     */
    @ApiModelProperty("注册类型")
    private Integer registerType = RegisterTypeEnum.ADMIN_CREATE.getType();

    /**
     * 注册时使用的应用名称
     */
    @ApiModelProperty("注册时使用的应用名称")
    private String registerApp;

    /**
     * 短信签名
     */
    @ApiModelProperty("短信签名")
    private String registerSign;

    /**
     * 注册客户端ip
     */
    @ApiModelProperty("注册客户端ip")
    private String registerIP;

    @ApiModelProperty("是否平台")
    private Boolean isPlatform = false;
}
