package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @DESCRIPTION:
 */
@ApiModel("微信小程序登录并绑定微信openId")
@Data
public class AccountWeChatLoginAndBindingDTO implements Serializable {

    private static final long serialVersionUID = 338094842058803427L;

    /**
     * 登录名
     */
    @NotBlank
    @ApiModelProperty("登录名（账户名或手机）")
    private String loginName;

    /**
     * 密码
     */
    @NotBlank
    @ApiModelProperty("密码")
    private String password;

    /**
     * 微信码
     */
    @NotBlank
    @ApiModelProperty("微信码")
    private String weChatCode;

    /**
     * 客户端ip
     */
    @ApiModelProperty("客户端ip")
    private String ip;

    /**
     * 会话id
     */
    @ApiModelProperty("会话id")
    private String sessionId;

    /**
     * app名称
     */
    @ApiModelProperty("app名称")
    private String loginType;

    /**
     * 客户端网卡物理地址
     */
    @ApiModelProperty("客户端网卡物理地址")
    private String mac;

    /**
     * 终端
     */
    @ApiModelProperty("终端")
    private String terminal;

    /**
     * 系统
     */
    @ApiModelProperty("系统")
    private String os;

    /**
     * 系统版本
     */
    @ApiModelProperty("系统版本")
    private String osVersion;

    /**
     * 手机品牌
     */
    @ApiModelProperty("手机品牌")
    private String deviceBrand;

    /**
     * 安卓手机唯一识别码
     */
    @ApiModelProperty("安卓手机唯一识别码")
    private String imei;

    /**
     * 浏览器信息
     */
    @ApiModelProperty("浏览器信息")
    private String userAgent;
}
