package com.cnoocshell.member.api.redis;

/**
 * @Author: <EMAIL>
 * @Date: 23/08/2018 11:09
 * @DESCRIPTION:
 */
public class MemberRedisKeys {

    // 添加私有构造函数，防止实例化
    private MemberRedisKeys() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated.");
    }

    public static final String MEMBER_INCR_CODE_MEMBER="member:incrCode:member";
    public static final String MEMBER_INCR_CODE_SUBACCOUNT="member:incrCode:subAccount:";

    public static final String USER_LOGIN_RETRY_COUNT="member:user_login_retry_count:";

    public static final String USER_FORCE_OFF_LINE="member:login:force_offline:";

    public static final String VERIFICATION_CODE="member:verificationCode:";

    public static final String SMSCODE="member:verificationCode:sms:";

    public static final String EMAILCODE="member:verificationCode:email:";

    public static final String CAPTCHA="member:verificationCode:captcha:";

    public static final String CACHE_ACCOUNT = "member:cache:account:";

    public static final String CACHE_MEMBER = "member:cache:member:";

    public static final String CACHE_MEMBER_HISTORY = "member:cache:memberHistory:";

    public static final String CACHE_MEMBER_DTO = "member:cache:member:dto:";

    public static final String CACHE_MEMBER_DETAIL = "member:cache:member:detail:";

    public static final String CACHE_MEMBER_CONFIG = "member:cache:memberConfig:";

    public static final String CACHE_MEMBER_STD_CONFIG = "member:cache:stdConfig";

    public static final String CACHE_PLATFORM_CAPTAIN = "member:cache:captain:platform";

    /**
     * 需要刷新session的账号
     */
    public static final String REFRESH_SESSION = "member:refresh_session:";

}
