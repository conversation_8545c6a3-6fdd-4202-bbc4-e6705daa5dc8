package com.cnoocshell.member.api.dto.accountRelation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AccountRelationPage implements Serializable {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("人员ID")
    private String accountId;

    @ApiModelProperty("人员姓名")
    private String accountRealName;

    @ApiModelProperty("人员角色")
    private String accountRole;

    @ApiModelProperty("关联账户ID")
    private String linkAccountId;

    @ApiModelProperty("关联账户姓名")
    private String linkAccountRealName;

    @ApiModelProperty("关联账户角色")
    private String linkAccountRole;
}
