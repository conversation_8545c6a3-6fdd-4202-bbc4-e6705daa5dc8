package com.cnoocshell.member.api.session;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 09/08/2018 10:05
 * @DESCRIPTION:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Operator implements Serializable {
    private static final long serialVersionUID = -3174138670542114588L;

    private String accountId;
    private String accountName;
    private Integer accountType;
    private Boolean status;
    private String realName;

    private String mobile;
    private String accountCode;

    private String memberId;
    private String memberName;
    private String memberShortName;
    private String memberCode;

    private String sessionId;
    private String clientIP;
    private String userAgent;
    private String driverToken;
}
