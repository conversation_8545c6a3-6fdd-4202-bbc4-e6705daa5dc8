package com.cnoocshell.member.api.dto.exception;

import com.alibaba.fastjson.JSON;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.exception.CodeMeta;
import com.google.common.collect.ImmutableMap;

/**
 * @Author: <EMAIL>
 * @Date: 07/10/2018 16:58
 * @DESCRIPTION:
 */
public class MemberBizException extends BizException {

    public MemberBizException(CodeMeta errorCode) {
        super(errorCode);
    }

    public MemberBizException(CodeMeta errorCode, Object... args) {
        super(errorCode,args);
    }

    @Override
    public String getMessage() {
        return super.getMessage();
    }

    public String getOnlyMessage(){
        return super.getMessage();
    }
}
