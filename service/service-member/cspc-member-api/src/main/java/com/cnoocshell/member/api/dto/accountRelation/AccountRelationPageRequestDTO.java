package com.cnoocshell.member.api.dto.accountRelation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AccountRelationPageRequestDTO {

    @ApiModelProperty("人员姓名")
    private String accountRealName;

    @ApiModelProperty("人员角色")
    private String accountRole;

    @ApiModelProperty("关联账户姓名")
    private String linkAccountRealName;

    @ApiModelProperty("关联账户角色")
    private String linkAccountRole;

    @ApiModelProperty("页码")
    private int pageNum;

    @ApiModelProperty("单页条数")
    private int pageSize;
}
