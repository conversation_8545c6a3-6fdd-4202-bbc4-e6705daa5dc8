package com.cnoocshell.member.api.dto.exception;

import com.cnoocshell.common.exception.CodeMeta;

/**
 * @Author: <EMAIL>
 * @Date: 07/08/2018 18:33
 * @DESCRIPTION: member模块异常代码定义，code以02开头
 */
public class MemberCode {

    // 添加私有构造函数，防止实例化
    private MemberCode() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public static final CodeMeta LOGIN_ERROR = new CodeMeta("020001", "LOGIN_ERROR", "{}", "{}");
    public static final CodeMeta LOGIN_LOCKED = new CodeMeta("020002", "LOGIN_LOCKED", "账户已被锁定:{}", "LOGIN_LOCKED error, see: {}");
    public static final CodeMeta LOGIN_ERROR_CAPTCHA_NEED = new CodeMeta("020003", "LOGIN_ERROR_CAPTCHA_NEED", "登录错误:{}", "LOGIN_ERROR_CAPTCHA_NEED error, see: {}");
    public static final CodeMeta REGISTER_ERROR = new CodeMeta("020004", "REGISTER_ERROR", "{}", "{}");
    public static final CodeMeta VALIDATION_ERROR = new CodeMeta("020005", "VALIDATION_ERROR", "{}", "{}");
    public static final CodeMeta PASSWD_DECRYPT_ERROR = new CodeMeta("020007", "PASSWD_DECRYPT_ERROR", "{}", "{}");
    public static final CodeMeta LOGIN_DISABLE_ERROR = new CodeMeta("020009", "LOGIN_DISABLE_ERROR", "{}", "{}");
    public static final CodeMeta IS_BLANK = new CodeMeta("020010", "IS_BLANK", "参数:{}不可为空", "args is null error, see: {}");

    public static final CodeMeta MEMBER_NOT_ALLOW_NEW_REQUEST = new CodeMeta("020011", "MEMBER_NOT_ALLOW_NEW_REQUEST", "当前有未完结的申请，不允许提交新请求。编号：{}，申请类型：{}", "MEMBER_NOT_ALLOW_NEW_REQUEST error, see: {}");
    public static final CodeMeta APPROVE_REQUEST_STATUS_ERROR = new CodeMeta("020012", "APPROVE_REQUEST_STATUS_ERROR", "当前变更请求已经被处理,不能重复处理 {}", "APPROVE_REQUEST_STATUS_ERROR error, see: {}");
    public static final CodeMeta MEMBER_REQUEST_APPROVAL_ERROR = new CodeMeta("020013", "MEMBER_REQUEST_APPROVAL_ERROR", "审批错误:{}", "approval error: {}");
    public static final CodeMeta MEMBER_NOT_EXIST = new CodeMeta("020022", "MEMBER_NOT_EXIST", "用户不存在", "MEMBER_NOT_EXIST error, see: {}");
    public static final CodeMeta CERT_IS_OVER_TIME = new CodeMeta("020026", "CERT_IS_OVER_TIME", "证件({})已过期", "CERT_IS_OVER_TIME error, see: {}");
    public static final CodeMeta CREDIT_CODE_IS_EXIST = new CodeMeta("020027", "CREDIT_CODE_IS_EXIST", "工商号码({})已存在", "CREDIT_CODE_IS_EXIST error, see: {}");
    public static final CodeMeta MEMBER_IS_NOT_ENTERPRISE = new CodeMeta("020028", "MEMBER_IS_NOT_ENTERPRISE", "个人用户不允许此操作", "MEMBER_IS_NOT_ENTERPRISE error, see: {}");
    public static final CodeMeta MEMBER_ALREADY_ENTERPRISE = new CodeMeta("020030", "MEMBER_ALREADY_ENTERPRISE", "该用户已经是企业类型，若要申请({})类型，请到企业类型更新菜单操作", "MEMBER_ALREADY_ENTERPRISE error, see: {}");
    public static final CodeMeta UNKNOWN_CERTIFICATES = new CodeMeta("020034", "UNKNOWN_CERTIFICATES", "未知的证件类型", "UNKNOWN_CERTIFICATES error, see: {}");

    public static final CodeMeta MEMBER_DATA_NOT_EXIST = new CodeMeta("020049", "MEMBER_DATA_NOT_EXIST", "{}", "{}");
    public static final CodeMeta PASSWORD_ERROR_SAME = new CodeMeta("020051", "PASSWORD_ERROR_SAME", "密码不能和之前的一样", "The password cannot be the same as before");

    public static final CodeMeta IMAGE_IS_EMPTY = new CodeMeta("020052", "IMAGE_IS_EMPTY", "未上传图片", "IMAGE_IS_EMPTY error");
    public static final CodeMeta MEMBER_NAME_IS_EXIST = new CodeMeta("020053", "MEMBER_NAME_IS_EXIST", "该公司名已经注册, {}", "MEMBER_NAME_IS_EXIST error, see: {}");


    public static final CodeMeta SMSCODE_SEND_FREQUENCY_LIMIT = new CodeMeta("020071", "SMSCODE_SEND_FREQUENCY_LIMIT", "短信验证码发送过于频繁，请1分钟后重试", "smscode send frequency limit error:{}");

    public static final CodeMeta SMSCODE_CHECK_ERROR = new CodeMeta("020072", "SMSCODE_CHECK_ERROR", "{}", "{}");
    public static final CodeMeta CAPTCHA_SEND_ERROR = new CodeMeta("020073", "CAPTCHA_SEND_ERROR", "{}", "{}");
    public static final CodeMeta CAPTCHA_CHECK_ERROR = new CodeMeta("020074", "CAPTCHA_CHECK_ERROR", "{}", "{}");


    public static final CodeMeta MEMBER_OTHER_ERROR = new CodeMeta("020099", "MEMBER_OTHER_ERROR", "{}", "{}");


}
