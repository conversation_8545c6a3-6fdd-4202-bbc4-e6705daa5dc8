package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: <EMAIL>
 * @Date: 17/08/2018 15:48
 * @DESCRIPTION:
 */
@Data
public class AccountBaseInfoUpdateDTO implements Serializable {
    private static final long serialVersionUID = 5951562768248085232L;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号不能为空!")
    private String mobile;


    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    @NotBlank(message = "会员ID不能为空!")
    private String accountId;
    /**
     * 性别
     */
    @ApiModelProperty("性别")
    private Integer sex;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String headPic;

    /**
     * 生日
     */
    @ApiModelProperty("生日")
    private Date birthDay;

    /**
     * 会员QQ
     */
    @ApiModelProperty("会员QQ")
    private String memberQq;

    @ApiModelProperty("微信id")
    private String wechatId;

    /**
     * 账户昵称
     */
    @ApiModelProperty("账户昵称")
    private String accountNickname;

    /**
     * 微信名称
     */
    @ApiModelProperty("微信名称")
    private String wechatName;

    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 微信头像url
     */
    @ApiModelProperty("微信头像url")
    private String wechatAvatarUrl;
    /**
     * 操作员id
     */
    @ApiModelProperty("操作员id")
    private String operator;

    /**
     * 操作员ip
     */
    @ApiModelProperty("操作员ip")
    private String operatorIP;

    @ApiModelProperty("用户真实姓名")
    @Size(max = 32,message = "用户姓名限制长度32位")
    private String realName;
}
