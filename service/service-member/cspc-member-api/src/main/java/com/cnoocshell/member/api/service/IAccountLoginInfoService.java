package com.cnoocshell.member.api.service;

import com.cnoocshell.member.api.dto.account.AccountLoginInfoDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description: 用户登陆日志查询服务
 */
@Tag(name = "IAccountLoginInfoService", description = "用户登陆日志查询服务")
@FeignClient(name = "cspc-service-member")
public interface IAccountLoginInfoService {

    @ApiOperation("下线时更新登录日志")
    @PostMapping("/accountLoginInfo/offline")
    void offline(@RequestParam("sessionId") String sessionId, @RequestParam("reason") String reason, @RequestParam("operator") String operator);

    @ApiOperation("新增登录记录")
    @PostMapping(value = "/accountLoginInfo/insertLoginInfo")
    void insertLoginInfo(@RequestBody AccountLoginInfoDTO param);
}
