package com.cnoocshell.member.api.session;

import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.MemberDTO;
import com.cnoocshell.member.api.dto.member.MemberSimpleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 09/08/2018 10:04
 * @DESCRIPTION:
 */
@Data
public class LoginInfo implements Serializable {
    private static final long serialVersionUID = -3174138670542114580L;
    public static final String SESSION_NAME = "_loginInfo";

    private String sessionId;

    private String accountId;
    private String accountCode;

    /**
     * 账户名(登陆使用，唯一)
     */
    private String accountName;

    /**
     * 真实姓名
     */
    private String realName;
    //昵称
    private String accountNickname;
    private String headPic;

    /**
     * 0个人/1企业主账号/2企业子账号
     */
    private Integer accountType;

    /**
     * 会员id
     */
    private String memberId;

    /**
     * 会员名称
     */
    private String memberName;
    private String memberShortName;
    private String memberCode;

    @ApiModelProperty("是否买家")
    private Integer buyerFlg;
    @ApiModelProperty("是否卖家")
    private Integer sellerFlg;
    @ApiModelProperty("是否承运商")
    private Integer carrierFlg;
    @ApiModelProperty("是否供应商")
    private Integer supplierFlg;
    /**
     * 2020.11.2 新增承运商注册字段 业务范围（或者叫业务类型），
     */
    @ApiModelProperty("承运商业务范围")
    private String businessScope;

    @ApiModelProperty("买家类型")
    private String buyerType;
    @ApiModelProperty("卖家类型")
    private String sellerType;
    @ApiModelProperty("承运商类型")
    private String carrierType;
    @ApiModelProperty("供应商类型")
    private String supplierType;
    /**
     * 工号
     */
    private String employeeId;

    /**
     * 默认登陆账号（如果有多个绑定在一起的账号，如果有一个是默认登陆账号，则自动登陆该账号）
     */
    private Boolean defaultAccount;

    /**
     * 是否个人司机
     */
    private Boolean personDriver;

    /**
     * 是否承运商司机
     */
    private Boolean entDriver;

    /**
     * 是否业务员
     */
    private Boolean salesman;

    /**
     * 是否需要修改密码
     */
    private Boolean needUpdatePassword;
    /**
     * 手机号
     */
    private String mobile;

    private String email;

    /**
     * 微信免登id
     */
    private String wechatOpenId;

    /**
     * 登录日期
     */
    private Date lastLoginDate;

    /**
     * 上次登录地址ip
     */
    private String lastLoginIp;

    private Boolean status;

    private String userCode;

    private String clientIP;
    private String userAgent;
    private String driverToken;
    /**
     * 是否接入erp
     */
    private Boolean hasErp;

    /**
     * 角色
     */
    private List<String> roleList;
    private List<String> roleNameList;
    /**
     * 角色对应的平台类型
     */
    private List<String> roleTypeList;
    @ApiModelProperty("数据权限-账号对应的销售区域id")
    private List<String> saleRegionIdList;
    @ApiModelProperty("数据权限-账号对应的行政区域adCode")
    private List<String> accountRegionAdCodeList;
    @ApiModelProperty("数据权限-账号对应的仓库id")
    private List<String> accountStoreIdList;
    @ApiModelProperty("数据权限-账号对应的erp账号")
    private List<ERPAccountInfo> erpAccountList;

    @ApiModelProperty("会员类型")
    private String memberType;


    private Operator operator;

    public LoginInfo() {
    }
    public LoginInfo(AccountDTO accountDTO) {
        this(accountDTO, (MemberDTO) null);
    }
    public LoginInfo(AccountDTO accountDTO, MemberSimpleDTO memberDTO) {
        this.accountId = accountDTO.getAccountId();
        this.accountCode = accountDTO.getAccountCode();
        this.accountName = accountDTO.getAccountName();
        this.realName = accountDTO.getRealName();
        this.accountNickname = accountDTO.getAccountNickname();
        this.accountType = accountDTO.getAccountType();
        this.headPic = accountDTO.getHeadPic();
        this.memberId = accountDTO.getMemberId();
        this.memberName = accountDTO.getMemberName();
        this.memberShortName =accountDTO.getMemberShortName();
        this.memberCode = accountDTO.getMemberCode();

        this.employeeId = accountDTO.getEmployeeId();
        this.defaultAccount = accountDTO.getDefaultAccount();
        this.personDriver = accountDTO.getPersonDriver();
        this.entDriver = accountDTO.getEntDriver();
        this.salesman = accountDTO.getSalesman();
        this.needUpdatePassword = accountDTO.getNeedUpdatePassword();
        this.mobile = accountDTO.getMobile();
        this.wechatOpenId = accountDTO.getWechatOpenId();
        this.lastLoginDate = accountDTO.getLastLoginDate();
        this.lastLoginIp = accountDTO.getLastLoginIp();
        this.status = accountDTO.getStatus();
        if(memberDTO != null){
            this.sellerFlg=memberDTO.getSellerFlg();
            this.carrierFlg=memberDTO.getCarrierFlg();
            this.supplierFlg=memberDTO.getSupplierFlg();
            this.sellerFlg=memberDTO.getSellerFlg();
            this.sellerType = memberDTO.getSellerType();
            this.carrierType = memberDTO.getCarrierType();
            this.supplierType = memberDTO.getSupplierType();
            this.buyerType = memberDTO.getBuyerType();
            this.memberCode = memberDTO.getMemberCode();
            this.memberShortName = memberDTO.getMemberShortName();
            this.businessScope = memberDTO.getBusinessScope();
            this.memberType = memberDTO.getMemberType();
        }
    }

    public LoginInfo(AccountDTO accountDTO, MemberDTO memberDTO) {
        this.accountId = accountDTO.getAccountId();
        this.accountCode = accountDTO.getAccountCode();
        this.accountName = accountDTO.getAccountName();
        this.realName = accountDTO.getRealName();
        this.accountNickname = accountDTO.getAccountNickname();
        this.accountType = accountDTO.getAccountType();
        this.headPic = accountDTO.getHeadPic();
        this.memberId = accountDTO.getMemberId();
        this.memberName = accountDTO.getMemberName();
        this.memberShortName =accountDTO.getMemberShortName();
        this.memberCode = accountDTO.getMemberCode();

        this.employeeId = accountDTO.getEmployeeId();
        this.defaultAccount = accountDTO.getDefaultAccount();
        this.personDriver = accountDTO.getPersonDriver();
        this.entDriver = accountDTO.getEntDriver();
        this.salesman = accountDTO.getSalesman();
        this.needUpdatePassword = accountDTO.getNeedUpdatePassword();
        this.mobile = accountDTO.getMobile();
        this.wechatOpenId = accountDTO.getWechatOpenId();
        this.lastLoginDate = accountDTO.getLastLoginDate();
        this.lastLoginIp = accountDTO.getLastLoginIp();
        this.status = accountDTO.getStatus();
        if(memberDTO != null){
            this.sellerFlg=memberDTO.getSellerFlg();
            this.memberCode = memberDTO.getMemberCode();
            this.memberShortName = memberDTO.getMemberShortName();
            this.memberType = memberDTO.getMemberType();
        }
    }

    public String getAccountId() {
        if(StringUtils.isBlank(accountId)){
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getMemberId() {
        if(StringUtils.isBlank(accountId)){
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public Operator getOperator() {
        return new Operator(accountId,accountName,accountType,status,realName,mobile,accountCode,
                memberId,memberName,memberShortName,memberCode,sessionId,clientIP,userAgent,driverToken);
    }

    @Deprecated(since = "2.1.4-RELEASE")
    public void setOperator(Operator operator) {
        //do nothing
    }
}