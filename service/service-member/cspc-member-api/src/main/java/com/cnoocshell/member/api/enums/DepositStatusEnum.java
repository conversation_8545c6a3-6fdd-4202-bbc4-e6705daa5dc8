package com.cnoocshell.member.api.enums;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 保证金状态 根据字典 VS_DEPOSIT_STATUS进行枚举定义
 */
@Getter
@AllArgsConstructor
public enum DepositStatusEnum {
    PAID("VO_DEPOSIT_STATUS_PAYED", "已缴纳"),

    UNPAID("VO_DEPOSIT_STATUS_UNPAY", "未缴纳"),

    REFUNDING("VO_DEPOSIT_STATUS_REFUNDING", "退款中");

    private String code;

    private String name;

    public static String findNameByCode(String code) {
        if (CharSequenceUtil.isEmpty(code)) {
            return null;
        }
        for (DepositStatusEnum value : DepositStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }
}
