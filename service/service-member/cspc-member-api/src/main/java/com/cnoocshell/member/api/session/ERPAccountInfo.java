package com.cnoocshell.member.api.session;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class ERPAccountInfo implements Serializable {

    @ApiModelProperty("卖家会员下的erp账号名称")
    private String erpAccountName;

    @ApiModelProperty("卖家会员下的erp账号")
    private String mdmCode;

    @ApiModelProperty("卖家会员id")
    private String sellerMemberId;

    @ApiModelProperty("卖家会员名称")
    private String sellerMemberName;
}
