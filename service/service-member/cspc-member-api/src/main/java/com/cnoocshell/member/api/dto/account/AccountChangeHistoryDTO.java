package com.cnoocshell.member.api.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel("员工授权，启用，禁用历史DTO")
@Data
public class AccountChangeHistoryDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private String accountHistoryId;

    /**
     * 变更会员ID
     */
    @ApiModelProperty("变更会员ID")
    private String accountId;

    /**
     * 变更会员真实姓名
     */
    @ApiModelProperty("变更会员真实姓名")
    private String realName;

    /**
     * 操作人真实姓名
     */
    @ApiModelProperty("操作人真实姓名")
    private String operatroName;

    /**
     * 变更方式（授权，启用，禁用）
     */
    @ApiModelProperty("变更方式（授权，启用，禁用）")
    private String way;

    /**
     * 账号角色ID集合
     */
    @ApiModelProperty("账号角色ID集合")
    private List<Integer> roleIdList;

    /**
     * 修改原因
     */
    @ApiModelProperty("修改原因")
    private String reason;

    /**
     * 是否生效
     */
    @ApiModelProperty("是否生效")
    private Integer isEffect;

    /**
     * 是否立即生效
     */
    @ApiModelProperty("是否立即生效")
    private Integer isImmediateEffect;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    private Date effectTime;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    private Integer delFlg;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;
}
