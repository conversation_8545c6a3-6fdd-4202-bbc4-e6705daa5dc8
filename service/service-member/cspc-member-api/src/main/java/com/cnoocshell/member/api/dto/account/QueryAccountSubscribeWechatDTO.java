package com.cnoocshell.member.api.dto.account;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryAccountSubscribeWechatDTO {
    private List<String> accountId;
    private List<String> businessNo;
    @NotBlank
    private String businessType;
}
