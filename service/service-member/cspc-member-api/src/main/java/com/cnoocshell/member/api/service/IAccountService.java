package com.cnoocshell.member.api.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.account.*;
import com.cnoocshell.member.api.dto.account.checkCode.CheckEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.CheckSMSCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetSMSCodeDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description 账户
 */
@Api(tags = {"IAccountService"}, description = "账户服务")
@FeignClient(name = "cspc-service-member")
public interface IAccountService {

    @ApiOperation("根据手机号和密码，本地数据库登陆校验")
    @PostMapping("/account/loginVerification")
    AccountDTO loginVerification(@RequestBody AccountLoginDTO accountLoginDTO);

    @ApiOperation("校验手机号是否已被个人账号使用")
    @PostMapping("/account/checkMobilePhoneExistsByPersonl")
    boolean checkMobilePhoneExistsByPersonl(@RequestParam("phoneNumber") String phoneNumber);

    @ApiOperation("校验新手机号是否已被企业主账号使用")
    @PostMapping("/account/checkMobilePhoneExistsByMasterAccount")
    boolean checkMobilePhoneExistsByMasterAccount(@RequestParam("phoneNumber") String phoneNumber);

    @ApiOperation("手机动态码登陆")
    @PostMapping("/account/loginByPhoneVerificationCode")
    AccountDTO loginByPhoneVerificationCode(@RequestBody PhoneCodeLoginDTO phoneCodeLoginDTO);

    @ApiOperation("校验短信验证码")
    @PostMapping("/account/checkSMSCode")
    Boolean checkSMSCode(@RequestBody CheckSMSCodeDTO dto);

    @ApiOperation("校验新用户名是否已被占用")
    @PostMapping("/account/checkAccountNameExists")
    boolean checkAccountNameExists(@RequestParam("newAccountName") String newAccountName);

    @ApiOperation("个人PC注册账户")
    @PostMapping("/account/registerAccountByPC")
    ItemResult<AccountDTO> registerAccountByPC(@RequestBody AccountPCRegisterDTO accountRegisterDTO);

    @ApiOperation("根据id查询单个账户信息")
    @PostMapping("/account/findById")
    AccountDTO findById(@RequestParam("accountId") String accountId);

    @ApiOperation("根据id查询单个账户信息")
    @GetMapping("/account/findByEmail")
    AccountDTO findByEmail(@RequestParam("email") String email);

    @ApiOperation("根据id查询账户简单信息")
    @PostMapping("/account/findSimpleById")
    AccountSimpleDTO findSimpleById(@RequestParam("accountId") String accountId);

    @ApiOperation("根据ids查询账户信息(仅含账户表信息)")
    @PostMapping("/account/findByIds")
    List<AccountDTO> findByIds(@RequestBody List<String> ids);

    @ApiOperation("根据手机号查找账号，并根据登陆类型（即登陆所在app）过滤账号")
    @PostMapping("/account/findByMobilePhoneAndLoginType")
    List<AccountDTO> findByMobilePhoneAndLoginType(@RequestParam("mobilePhoneNumber") String mobilePhoneNumber,
                                                   @RequestParam(value = "loginType", required = false) String loginType);

    @ApiOperation("校验邮件验证码")
    @PostMapping("/account/checkEmailCode")
    ItemResult<Boolean> checkEmailCode(@RequestBody CheckEmailCodeDTO dto);

    @ApiOperation("校验手机号是否已被占用")
    @PostMapping("/account/checkMobilePhoneExists")
    boolean checkMobilePhoneExists(@RequestParam("phoneNumber") String phoneNumber);

    @ApiOperation("获取短信验证码")
    @PostMapping("/account/getSMSCode")
    String getSMSCode(@RequestBody GetSMSCodeDTO dto);

    @ApiOperation("更新手机号")
    @PostMapping("/account/updateMobilePhone")
    void updateMobilePhone(@RequestParam("accountId") String accountId, @RequestParam("phoneNumber") String phoneNumber, @RequestParam("operator") String operator, @RequestParam("ip") String ip);

    @ApiOperation("获取邮件验证码")
    @PostMapping("/account/getEmailCode")
    String getEmailCode(@RequestBody GetEmailCodeDTO dto);

    @ApiOperation("更新账户基本信息")
    @PostMapping("/account/updateBaseInfo")
    void updateBaseInfo(@RequestBody AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO);

    @ApiOperation("更新用户名")
    @PostMapping("/account/updateAccountName")
    void updateAccountName(@RequestParam("accountId") String accountId, @RequestParam("newAccountName") String newAccountName, @RequestParam("operator") String operator, @RequestParam("ip") String ip);

    @ApiOperation("创建子账号")
    @PostMapping("/account/registerSubAccountByMaster")
    ItemResult<AccountDTO> registerSubAccountByMaster(@RequestBody AccountRegisterByMasterDTO accountRegisterByMasterDTO);

    @ApiOperation("根据条件分页查询账号")
    @PostMapping("/account/findAll")
    PageInfo<AccountDTO> findAll(@RequestBody AccountSearchDTO accountSearchDTO);


    @ApiOperation("禁用账号")
    @PostMapping("/account/disabled")
    void disabled(@RequestParam("accountId") String accountId,
                  @RequestParam("reason") String reason,
                  @RequestParam("operator") String operator);

    @ApiOperation("启用账号")
    @PostMapping("/account/enabled")
    void enabled(@RequestParam("accountId") String accountId,
                 @RequestParam("operator") String operator);

    @ApiOperation("根据账户名查询单个账户信息")
    @PostMapping("/account/findByAccountName")
    AccountDTO findByAccountName(@RequestParam("accountName") String accountName);

    @ApiOperation("根据id查询单个账户信息(含角色、权限、销售区域、组织机构、数据权限)")
    @PostMapping("/account/findDetailById")
    AccountDTO findDetailById(@RequestParam("accountId") String accountId);

    @ApiOperation("更新子账账户信息")
    @PostMapping("/account/updateSubAccountInfo")
    ItemResult<Boolean> updateSubAccountInfo(@RequestBody SubAccountUpdateDTO subAccountUpdateDTO);

    @ApiOperation("切换主账户")
    @PostMapping("/account/changeAccountType")
    void changeAccountType(@RequestBody ChangeAccountTypeDTO changeAccountTypeDTO);


    @ApiOperation("刷新在线用户角色、权限、销售区域信息")
    @PostMapping("/account/refreshLoginInfo")
    void refreshLoginInfo(@RequestBody RefreshLoginInfoDTO dto);

    @ApiOperation("解绑公司子账号")
    @PostMapping("/account/unBindingAccount")
    void unBindingAccount(@RequestParam("currUserAccountId") String currUserAccountId,
                          @RequestParam("unBindingAccountId") String unBindingAccountId);

    @ApiOperation("根据角色code获取用户名称")
    @PostMapping("/account/findAccountNameByRoleCode")
    List<AccountNameDTO> findAccountNameByRoleCode(@RequestParam("roleCode") String roleCode);

    @ApiOperation("根据用户ID查询用户信息")
    @PostMapping("/account/findAccountByAccountId")
    List<AccountInfoReturnDTO> findAccountByAccountId(@RequestBody List<String> ids);

    @ApiOperation("根据角色code获取用户名称")
    @PostMapping(value = "/account/listSimpleByIds")
    List<AccountSimpleDTO> listSimpleByIds(@RequestBody List<String> accountIds);

    @GetMapping("/account/existsAccountByEmail")
    Boolean existsAccountByEmail(@RequestParam String email,
                                 @RequestParam(required = false) String exceptAccountId);

    @PostMapping("/accountSubscribeWechat/createSubscribeRecord")
    ItemResult<String> createSubscribeRecord(@RequestBody MbAccountSubscribeWechatDTO accountSubscribeWechatDTO);

    @PostMapping(value = "/accountSubscribeWechat/checkAccountSubscribe")
    ItemResult<Boolean> checkAccountSubscribe(@RequestBody MbAccountSubscribeWechatDTO accountSubscribeWechatDTO);

    @ApiOperation("根据条件查询微信通知订阅记录")
    @PostMapping(value = "/accountSubscribeWechat/querySubscribeByCondition")
    List<AccountSubscribeWechatDTO> querySubscribeByCondition(@RequestBody QueryAccountSubscribeWechatDTO param);

    @PostMapping(value = "/accountSubscribeWechat/updateSendStatus")
    ItemResult<String> updateSendStatus(@RequestBody MbAccountSubscribeWechatDTO accountSubscribeWechatDTO);

}
