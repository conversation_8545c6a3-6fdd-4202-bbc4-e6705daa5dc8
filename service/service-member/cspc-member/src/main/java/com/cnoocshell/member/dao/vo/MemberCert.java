package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "mb_member_cert")
@EqualsAndHashCode(callSuper = true)
public class MemberCert extends BaseEntity {
    /**
     * 资质id
     */
    @Id
    @Column(name = "cert_id")
    private String certId;

    /**
     * 会员id
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 账户id
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 资质名称
     */
    @Column(name = "cert_name")
    private String certName;

    /**
     * 资质类型
     */
    @Column(name = "cert_type")
    private String certType;

    /**
     * 资质状态
     */
    private String status;

    /**
     * 附件id
     */
    @Column(name = "attachment_id")
    private String attachmentId;

    /**
     * 真实姓名
     */
    @Column(name = "real_name")
    private String realName;

    /**
     * 证件号码(身份证,驾驶证,从业资格证等)
     */
    @Column(name = "id_number")
    private String idNumber;
    /**
     * 生效时间
     */
    @Column(name = "effective_time")
    private Date effectiveTime;

    /**
     *排序码
     */
    @Column(name = "order_num")
    private Integer orderNum;
}