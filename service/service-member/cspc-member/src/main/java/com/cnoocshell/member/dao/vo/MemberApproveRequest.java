package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "mb_member_approve_request")
@EqualsAndHashCode(callSuper = true)
public class MemberApproveRequest extends BaseEntity {
    /**
     * 变更请求id
     */
    @Id
    @Column(name = "request_id")
    private String requestId;

    /**
     * 变更请求id（方便前端查询使用）
     */
    @Column(name = "request_num")
    private String requestNum;

    /**
     * 会员id
     */
    @Column(name = "member_id")
    private String memberId;
    /**
     * 会员名称
     */
    @Column(name = "member_name")
    private String memberName;
    /**
     * 会员代码
     */
    @Column(name = "member_code")
    private String memberCode;

    /**
     * 实名认证账号id
     */
    @Column(name = "account_id")
    private String accountId;
    /**
     * 会员名称
     */
    @Column(name = "account_name")
    private String accountName;

    /**
     * 个人会员/企业会员
     */
    @Column(name = "member_type")
    private String memberType;

    @Column(name = "contact_name")
    private String contactName;
    /**
     * 紧急联系方式
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    @Column(name = "request_type")
    private String requestType;
    /**
     * 变更状态
     */
    private String status;

    /**
     * 变更摘要
     */
    @Column(name = "change_message")
    private String changeMessage;

    /**
     * 申请人Id
     */
    @Column(name = "request_member_id")
    private String requestMemberId;

    /**
     * 审批人Id
     */
    @Column(name = "approve_id")
    private String approveId;

    /**
     * 审批人姓名
     */
    @Column(name = "approve_name")
    private String approveName;

    /**
     * 申请提交时间
     */
    @Column(name = "request_time")
    private Date requestTime;

    /**
     * 审批时间
     */
    @Column(name = "approve_time")
    private Date approveTime;

    /**
     * 审批意见
     */
    @Column(name = "approve_text")
    private String approveText;
}