package com.cnoocshell.member.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.member.api.dto.member.MemberApprovalRequestQueryDTO;
import com.cnoocshell.member.api.dto.member.enums.AdvertStatusEnum;
import com.cnoocshell.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.cnoocshell.member.dao.vo.MemberApproveRequest;
import com.cnoocshell.member.exception.DuplicateString;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface MemberApproveRequestMapper extends IBaseMapper<MemberApproveRequest> {
    /**
     * 查找会员最近一次未完成的变更
     *
     * @param memberId 会员id
     * @return 变更记录
     */
    @Select("select * from mb_member_approve_request where member_id =#{memberId} and del_flg = 0 and status in('100','200','300') order by request_time desc limit 1")
    MemberApproveRequest findLastChangeRequest(@Param(DuplicateString.MEMBER_ID) String memberId);

    /**
     * 查找会员最近一次变更
     *
     * @param memberId 会员id
     * @return 变更记录
     */
    @Select("select * from mb_member_approve_request where member_id = #{memberId} and del_flg = 0 order by request_time desc limit 1")
    MemberApproveRequest findLastChangeRequest2(@Param(DuplicateString.MEMBER_ID) String memberId);

    @Select("select * from mb_member_approve_request where member_id =#{memberId} and request_type=#{requestType} and del_flg = 0  order by request_time desc limit 1")
    MemberApproveRequest findLastChangeRequestByType(@Param(DuplicateString.MEMBER_ID) String memberId,
                                                     @Param(DuplicateString.REQUEST_TYPE)String requestType);

    List<MemberApproveRequest> pageByCondition(@Param("param")MemberApprovalRequestQueryDTO param);

    @Select("select * from mb_member_approve_request where member_id =#{memberId} and request_type=#{requestType} and status=#{status} and del_flg = 0  order by request_time desc limit 1")
    MemberApproveRequest findLastChangeRequestByCondition(@Param(DuplicateString.MEMBER_ID) String memberId,
                                                     @Param(DuplicateString.REQUEST_TYPE)String requestType,
                                                     @Param(DuplicateString.STATUS)String status);

    MemberApproveRequest findLastRequest(@Param(DuplicateString.MEMBER_ID) String memberId,
                                                          @Param("requestTypeList")List<String> requestTypeList,
                                                          @Param("statusList")List<String> statusList);

    List<MemberApproveRequest> pageMemberChangeRequest(@Param("param")MemberApprovalRequestQueryDTO param);

    MemberApproveRequest findLatestRequest(@Param("memberId") String memberId, @Param("requestType") String requestType,
                                           @Param("status") String status, @Param("approvalOrCreateTime") Date approvalOrCreateTime);
}