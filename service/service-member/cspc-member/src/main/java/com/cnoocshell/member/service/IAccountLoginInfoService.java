package com.cnoocshell.member.service;


import com.cnoocshell.member.api.dto.account.AccountLoginInfoDTO;

/**
 * @DESCRIPTION:用户登陆日志查询服务
 */
public interface IAccountLoginInfoService {

    /**
     * 下线时更新登录日志
     * @param sessionId 会话id
     * @param reason 原因
     * @param operator 操作人
     */
    void offline(String sessionId,String reason,String operator);

    /**
     * 强制下线时更新登录日志
     * @param accountId 用户id
     * @param reason 原因
     * @param operator 操作人
     */
    void forceOffline(String accountId,String reason,String operator);

    void insertLoginInfo(AccountLoginInfoDTO param);
}
