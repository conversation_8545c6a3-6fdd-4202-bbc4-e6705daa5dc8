package com.cnoocshell.member.controller;

import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationInfoDTO;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationPage;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationPageRequestDTO;
import com.cnoocshell.member.api.dto.accountRelation.QueryAccountRelationDTO;
import com.cnoocshell.member.exception.DuplicateString;
import com.cnoocshell.member.service.IAccountRelationService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/accountRelation")
@RequiredArgsConstructor
public class AccountRelationController {

    @Autowired
    private IAccountRelationService iAccountRelationService;

    @ApiOperation("分页查询")
    @PostMapping(value = "/findAccountRelationByCondition")
    public PageInfo<AccountRelationPage> findAccountRelationByCondition(@RequestBody AccountRelationPageRequestDTO requestDTO) {
        return iAccountRelationService.findAccountRelationByCondition(requestDTO);
    }

    @ApiOperation("新增")
    @PostMapping(value = "/addAccountRelation")
    public Boolean addAccountRelation(@RequestBody AccountRelationInfoDTO requestDTO) {
        return iAccountRelationService.addAccountRelation(requestDTO);
    }

    @ApiOperation("删除")
    @PostMapping(value = "/deleteAccountRelation")
    public String deleteAccountRelation(@RequestParam String id) {
        return "删除" + iAccountRelationService.deleteAccountRelation(id) + "条数据";
    }

    @ApiOperation("查询账户关联账户信息")
    @PostMapping("/queryAccountRelation")
    public List<AccountRelationInfoDTO> queryAccountRelation(@RequestBody QueryAccountRelationDTO param){
        return iAccountRelationService.queryAccountRelation(param);
    }
}
