package com.cnoocshell.member.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.member.api.dto.member.MemberCertDTO;
import com.cnoocshell.member.api.dto.member.enums.MemberCertTypeEnum;
import com.cnoocshell.member.dao.vo.MemberCertHistory;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;

public interface IMemberCertHistoryBiz extends IBaseBiz<MemberCertHistory> {
    void saveCertHistoryList(List<MemberCertDTO> memberCertDTOList, String memberId, String accountId, String requestId, String operatorId);

    List<MemberCertHistory> findByRequestId(String requestId);

    void updateByConditionSelective(MemberCertHistory memberCertHistory, Condition condition);

    /**
     * 审批时更新一版资质
     * @param certList
     */
    void update(List<MemberCertDTO> certList, String requestId,String operatorId);

    MemberCertHistory findByCertIdAndRequestId(String certId, String requestId);

    /**
     * 审批时更新经营信息
     * @param certDTO
     */
    void updateCert(MemberCertDTO certDTO,String memberId, String requestId, String operatorId, MemberCertTypeEnum certType);

    MemberCertHistory findByCertId(String certId);
}
