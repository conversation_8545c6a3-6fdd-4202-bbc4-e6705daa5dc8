package com.cnoocshell.member.controller;

import com.cnoocshell.member.api.dto.account.AccountChangeHistoryDTO;
import com.cnoocshell.member.api.dto.account.PageAccountChangeHistoryDTO;
import com.cnoocshell.member.service.IAccountChangeHistoryService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Description:null
*/

@Api(tags={"AccountChangeHistory"})
@RestController
@RequestMapping("/accountChangeHistory")
@RequiredArgsConstructor
public class AccountChangeHistoryController {

   private final IAccountChangeHistoryService iAccountChangeHistoryService;

   @ApiOperation("新增员工授权，启用，禁用历史")
   @PostMapping(value="/add")
   public void add(@RequestBody AccountChangeHistoryDTO accountChangeHistoryDTO,@RequestParam("operator") String operator)throws Exception{
      iAccountChangeHistoryService.add(accountChangeHistoryDTO,operator);
   }

   @ApiOperation("根据DTO查询员工授权，启用，禁用历史")
   @PostMapping(value="/findByQuery")
   public List<AccountChangeHistoryDTO> findByQuery(@RequestBody AccountChangeHistoryDTO accountChangeHistoryDTO)throws Exception{
      return iAccountChangeHistoryService.findByQuery(accountChangeHistoryDTO);
   }


   @ApiOperation("根据DTO分页查询员工授权，启用，禁用历史")
   @PostMapping(value="/pageHistoryInfoList")
   public PageInfo<AccountChangeHistoryDTO> pageHistoryInfoList(@RequestBody PageAccountChangeHistoryDTO pageAccountChangeHistoryDTO)throws Exception{
      return iAccountChangeHistoryService.pageHistoryInfoList(pageAccountChangeHistoryDTO);
   }



}
