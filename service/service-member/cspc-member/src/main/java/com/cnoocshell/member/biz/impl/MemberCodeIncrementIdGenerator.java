package com.cnoocshell.member.biz.impl;

import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.service.common.bsid.IBusinessIdGenerator;
import com.cnoocshell.member.api.redis.MemberRedisKeys;
import com.cnoocshell.member.biz.IMemberBiz;
import com.cnoocshell.member.dao.mapper.MemberMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * @Author: <EMAIL>
 * @Date: 13/09/2018 17:00
 * @DESCRIPTION:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemberCodeIncrementIdGenerator implements IBusinessIdGenerator {

    private final BizRedisService bizRedisService;
    private final MemberMapper memberMapper;
    //1296 ==> 100
    private static final Long INIT = 1296L;

    private static final String LENGTH1="A0000";
    private static final String LENGTH2="A000";
    private static final String LENGTH3="A00";
    private static final String LENGTH4="A0";
    private static final String LENGTH5="A";

    public String incrementCode(Long init2) {
        init2 = init2 == null || init2 < INIT ? INIT : init2;
        long s = System.currentTimeMillis();
        long num = bizRedisService.incr(MemberRedisKeys.MEMBER_INCR_CODE_MEMBER,1);
        if(num < init2 ){
            bizRedisService.set(MemberRedisKeys.MEMBER_INCR_CODE_MEMBER,init2);
            num = bizRedisService.incr(MemberRedisKeys.MEMBER_INCR_CODE_MEMBER,1);
        }
        String memberCode = Long.toString(num,36).toUpperCase();
        log.info("get memberCode incrementCode : cost time : {}",(System.currentTimeMillis() - s) );
        switch (memberCode.length()){
            case 1: return LENGTH1 + memberCode;
            case 2: return LENGTH2 + memberCode;
            case 3: return LENGTH3 + memberCode;
            case 4: return LENGTH4 + memberCode;
            default:
                return LENGTH5 + memberCode;
        }
    }

    @Override
    public String incrementCode() {
        if( !bizRedisService.hasKey(MemberRedisKeys.MEMBER_INCR_CODE_MEMBER) ){
            String maxMemberCode = memberMapper.findMaxMemberCode();
            if (maxMemberCode != null) {
                return incrementCode(Long.valueOf(maxMemberCode.substring(1),36));
            }
        }
        return incrementCode(INIT);
    }

    public String getNewMemberCode(){
        String memberCode = incrementCode();
        while( memberMapper.countByMemberCode(memberCode) > 0) {
            log.info("memberCode: {} already exists",memberCode);
            memberCode = incrementCode();
        }
        return memberCode;
    }

    @Override
    public String businessCodePrefix() {
        return null;
    }

    @Override
    public Long gain() {
        return null;
    }

    @Override
    public String gainString() {
        return null;
    }

    @Override
    public String gainDateString() {
        return null;
    }
}
