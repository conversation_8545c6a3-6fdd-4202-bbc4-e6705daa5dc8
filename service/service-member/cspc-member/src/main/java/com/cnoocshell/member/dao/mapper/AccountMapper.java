package com.cnoocshell.member.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.member.api.dto.account.AccountInfoReturnDTO;
import com.cnoocshell.member.dao.vo.Account;
import com.cnoocshell.member.exception.DuplicateString;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AccountMapper extends IBaseMapper<Account> {
    /**
     * 解决大小写不敏感的问题
     *
     * @param accountName
     * @return
     */
    @Select("select * from mb_account where del_flg = 0 and account_name = #{accountName}")
    Account findByName(@Param(DuplicateString.ACCOUNT_NAME) String accountName);

    @Select("select max(account_code) from mb_account where member_code = #{memberCode}")
    String findMaxAccountCode(@Param(DuplicateString.MEMBER_CODE) String memberCode);

    List<AccountInfoReturnDTO> findAccountByAccountId(List<String> accountIds);
    List<String> listAccountIdByMemberCodes(@Param("memberCodes")List<String> memberCodes);

    List<Account> selectByAccountIds(@Param("ids")List<String> ids);
}
