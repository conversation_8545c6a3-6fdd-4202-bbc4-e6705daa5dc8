package com.cnoocshell.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cnoocshell.base.api.dto.*;
import com.cnoocshell.base.api.dto.cloud.AttachmentinfoDTO;
import com.cnoocshell.base.api.dto.role.AccountRoleDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.service.IAttachmentService;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.base.api.service.IValueSetService;
import com.cnoocshell.common.annotation.AddLog;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.dto.EmailDTO;
import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.dto.ExportSheetDTO;
import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.enums.AppNames;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.IEmailSendService;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.common.BaseService;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.goods.api.dto.CategoryAndParentCategoryDTO;
import com.cnoocshell.goods.api.dto.GoodsCategorySimpleDTO;
import com.cnoocshell.goods.api.dto.GoodsSimpleDTO;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.goods.api.service.IGoodsService;
import com.cnoocshell.integration.enums.EmailTemplateEnum;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.member.api.constant.MemberNumberConstant;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.dto.member.enums.*;
import com.cnoocshell.member.api.dto.member.enums.AdvertStatusEnum;
import com.cnoocshell.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.cnoocshell.member.api.dto.member.enums.MemberCertTypeEnum;
import com.cnoocshell.member.api.dto.member.enums.MemberTypeEnum;
import com.cnoocshell.member.api.dto.member.intention.MemberIntentionSimpleDTO;
import com.cnoocshell.member.api.dto.report.*;
import com.cnoocshell.member.api.enums.AbleStatusEnum;
import com.cnoocshell.member.api.enums.AccountStatusEnum;
import com.cnoocshell.member.api.enums.DepositStatusEnum;
import com.cnoocshell.member.api.enums.IntentionTypeEnum;
import com.cnoocshell.member.biz.*;
import com.cnoocshell.member.biz.impl.MemberPurchaseGoodsIntentionBiz;
import com.cnoocshell.member.common.EnumUtil;
import com.cnoocshell.member.dao.mapper.MemberApproveRequestMapper;
import com.cnoocshell.member.dao.mapper.MemberMapper;
import com.cnoocshell.member.dao.mapper.MemberPurchaseGoodsIntentionMapper;
import com.cnoocshell.member.dao.vo.*;
import com.cnoocshell.member.exception.DuplicateString;
import com.cnoocshell.member.service.IMemberService;
import com.cnoocshell.mq.core.MQMessage;
import com.cnoocshell.mq.core.service.IMQProducer;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @DESCRIPTION:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemberService extends BaseService<Member> implements IMemberService {

    public static final String CERT_CAN_NOT_NULL = "资质不可为空";
    public static final String MEMBER_ID_CAN_NOT_NULL = "会员id不可为空";
    public static final String MEMBER_ID_MUST_BE_UNIQUE = "会员id不可有多个";
    private static final String TEMPLATE1 = "{}_{}";
    private static final String INTENTION_NOT_NULL = "意向数据不能为空";

    private final MemberPurchaseGoodsIntentionMapper purchaseGoodsIntentionMapper;
    private final IMemberBiz memberBiz;
    private final IMemberHistoryBiz memberHistoryBiz;
    private final IMemberCertHistoryBiz memberCertHistoryBiz;
    private final IAttachmentService attachmentService;
    private final IAccountBiz accountBiz;
    private final IMemberApproveRequestBiz memberApproveRequestBiz;
    private final IMemberPurchaseGoodsIntentionBiz iMemberPurchaseGoodsIntentionBiz;
    private final IGoodsService iGoodsService;
    private final IGoodsCategoryService iGoodsCategoryService;
    private final IMemberPurchaseGoodsIntentionHistoryBiz intentionHistoryBiz;
    private final MemberApproveRequestMapper memberApproveRequestMapper;
    private final MemberMapper memberMapper;
    private final ISmsSendService iSmsSendService;
    private final IEmailSendService emailSendService;
    private final IRoleService iRoleService;
    @Qualifier("myTaskAsyncPool")
    private final Executor executor;
    private final IMQProducer mqProducer;
    private final IValueSetService iValueSetService;

    @Value("${spring.profiles.active:dev}")
    private String profile;

    @Override
    public MemberSimpleDTO findMemberSimpleById(String memberId) {
        return memberBiz.findMemberSimpleById(memberId);
    }

    @Override
    public PageInfo<MemberApprovalRequestDTO> pageRegisterMemberApprovalRequests(MemberApprovalRequestQueryDTO query, Integer pageNum, Integer pageSize) {
        PageInfo<MemberApprovalRequestDTO> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        query.setRegisterFlg(true);
        if ((AppNames.WEB_SERVICE_SELLER.getPlatform().equals(query.getAppName()))
                && checkRequestType(query)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "卖家只能审批企业会员注册信息");
        }

        Page<MemberApprovalRequestDTO> dtoPage = memberBiz.pageMemberApprovalRequests(query, pageInfo);
        updateMemberInfo(dtoPage);
        return new PageInfo<>(dtoPage);
    }

    @Override
    public MemberDTO getMemberApprovalDetails(String memberApprovalRequestId) {
        MemberDTO dto = memberBiz.getMemberApprovalDetails(memberApprovalRequestId);
        if (dto != null) {
            sortCertList(dto.getMemberCertDTOList());
            //处理商品意向数据
            iMemberPurchaseGoodsIntentionBiz.handleNameByIntentions(dto.getIntentionList());
        }
        return dto;
    }

    @Override
    public MemberDTO findRealMemberById(String id) {
        MemberDTO dto = memberBiz.findRealMemberById(id);
        if (dto != null) {
            sortCertList(dto.getMemberCertDTOList());
        }
        return dto;
    }

    @Override
    public MemberDTO findMemberDetailById(String id) {
        MemberDTO memberDTO = memberBiz.findMemberDetailById(id);
        if (memberDTO != null) {
            sortCertList(memberDTO.getMemberCertDTOList());
            memberDTO.setIntentionList(sortAscIntentionByIntentionType(memberDTO.getIntentionList()));
        }
        return memberDTO;
    }

    @Override
    public MemberCertDTO findRealNameCertByAccountId(String accountId) {
        return memberBiz.findRealNameCertByAccountId(accountId);
    }

    @AddLog(operatorFieldName = "operatorId")
    @Override
    public String registerBuyer(MemberRequestDTO memberRequestDTO) {
        checkMemberIdIsEmptyThrowable(memberRequestDTO.getMemberId());
        memberRequestDTO.setSellerFlg(MemberDTO.FLG_FALSE);
        memberRequestDTO.setCarrierFlg(MemberDTO.FLG_FALSE);
        memberRequestDTO.setSupplierFlg(MemberDTO.FLG_FALSE);
        addressAnalysis(memberRequestDTO);
        memberRequestDTO.setMemberType(MemberTypeEnum.ENTERPRISE_BUYER.getCode());
        return memberBiz.submitRegister(
                memberRequestDTO,
                ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER.getCode(),
                true,
                memberRequestDTO.getProxy() == null ? 0 : memberRequestDTO.getProxy(),
                memberRequestDTO.getOperatorId());
    }

    @Override
    public PageInfo<MemberApprovalRequestDTO> pageMemberApprovalRequests(MemberApprovalRequestQueryDTO query, Integer pageNum, Integer pageSize) {
        PageInfo<MemberApprovalRequestDTO> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        query.setRegisterFlg(false);
        PageInfo<MemberApprovalRequestDTO> dtoPage = memberBiz.pageMemberChangeRequest(query, pageInfo);
        //处理企业变更请求中的企业相关信息
        this.updateMemberInfo(dtoPage.getList());

        return dtoPage;
    }

    @Transactional(rollbackFor = Exception.class)
    @AddLog(operatorFieldName = "operatorId")
    @Override
    public void approveRequest(ApproveRequestDTO dto) {
        log.info("approveRequest: {}", JSON.toJSONString(dto));
        MemberApproveRequest request = memberApproveRequestBiz.get(dto.getApproveRequestId());
        if (request == null) {
            throw new MemberBizException(BasicCode.INVALID_PARAM, ":变更请求不存在");
        }
        String memberId = request.getMemberId();
        //变更请求
        Member member = memberBiz.findById(memberId);
        if (member == null) {
            throw new MemberBizException(BasicCode.INVALID_PARAM, ":会员不存在");
        }
        dto.setMemberId(member.getMemberId());
        dto.setMemberCode(member.getMemberCode());
        String requestType = request.getRequestType();
        String requestId = dto.getApproveRequestId();
        String operatorId = dto.getOperatorId();
        ApproveRequestTypeEnum requestTypeEnum = EnumUtil.getEnumByCode(requestType, ApproveRequestTypeEnum.class);

        if (Objects.nonNull(requestTypeEnum) && BooleanUtil.isTrue(dto.getUpdateInfoFlag())) {
            switch (requestTypeEnum) {
                case REGISTER_ENTERPRISE_BUYER: {
                    addressAnalysis(dto.getRegisterDTO());
                    //设置CRM code
                    if(StringUtils.isNotBlank(dto.getCrmCode()))
                        member.setCrmCode(dto.getCrmCode());
                    memberBiz.updateRegisterInfo(dto.getRegisterDTO(), member, request, operatorId);
                    break;
                }
                case CHANGE_BUSINESS: {
                    memberBiz.updateBusinessInfo(dto.getMemberBusinessInfoDTO(), memberId, requestId, operatorId);
                    break;
                }
                case CHANGE_NORMAL_CERT: {
                    memberCertHistoryBiz.update(dto.getMemberCertDTOList(), requestId, operatorId);
                    break;
                }
                default: {
                    throw new BizException(BasicCode.CUSTOM_ERROR, "变更类型不正确:" + requestTypeEnum.getMsg());
                }
            }
        }
        memberBiz.approveOrRejectRequest(dto.getApproveRequestId(), dto.getRegisterDTO() == null ? "" : dto.getRegisterDTO().getCarrierType(), dto.getMessage(), true, dto.getOperator(), dto.getAppName());
    }

    @AddLog(operatorFieldName = "operatorId")
    @Override
    public void rejectRequest(RejectRequestDTO dto) {
        log.info("rejectRequest: {}", JSON.toJSONString(dto));
        memberBiz.approveOrRejectRequest(dto.getApproveRequestId(), "", dto.getMessage(), false, dto.getOperator(), dto.getAppName());
    }

    @AddLog(operatorFieldName = "operatorId")
    @Override
    public void updateBaseInfo(MemberBaseInfoDTO memberBaseInfoDTO) {
        checkMemberIdIsEmptyThrowable(memberBaseInfoDTO.getMemberId());
        String codeArr = memberBaseInfoDTO.getProvinceCode();
        if (!StringUtils.isEmpty(codeArr)) {
            //1000,1100,1101
            String[] split = codeArr.split(",");
            if (split.length > 0) {
                memberBaseInfoDTO.setProvinceCode(split[0]);
                if (split.length > 1) {
                    memberBaseInfoDTO.setCityCode(split[1]);
                    if (split.length > 2) {
                        memberBaseInfoDTO.setAreaCode(split[2]);
                    }
                }
            }
        }
        memberBiz.updateBaseInfo(memberBaseInfoDTO, memberBaseInfoDTO.getOperatorId());
    }

    @Override
    public PageInfo<MemberListViewDTO> pageMemberListView(MemberQueryDTO query, Integer pageNum, Integer pageSize) {
        PageInfo<MemberListViewDTO> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageNum(pageNum);
        Page<MemberListViewDTO> dtos = memberBiz.pageMemberListView(query, pageInfo);
        return new PageInfo<>(dtos);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void enableMember(String memberId, String operatorId) {
        memberBiz.enableMember(memberId, operatorId);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void disableMember(String memberId, String operatorId) {
        memberBiz.disableMember(memberId, operatorId);
    }

    @Override
    public MemberDetailDTO findMemberById(String memberId) {
        return memberBiz.findMemberById(memberId);
    }

    @Override
    public List<MemberDetailDTO> findMemberByIds(List<String> ids) {
        Condition condition = memberBiz.newCondition();
        condition.createCriteria().andIn("memberId", ids)
                .andEqualTo("delFlg", 0);
        List<Member> byCondition = memberBiz.findByCondition(condition);
        return BeanUtil.copyToList(byCondition, MemberDetailDTO.class);
    }

    @AddLog(operatorFieldName = "operatorId")
    @Override
    public String updateBusinessInfo(MemberBusinessInfoDTO memberBusinessInfoDTO) {
        checkMemberIdIsEmptyThrowable(memberBusinessInfoDTO.getMemberId());
        return memberBiz.updateBusinessInfo(memberBusinessInfoDTO, true, memberBusinessInfoDTO.getOperatorId());
    }

    @Override
    public String updateCert(UpdateCertDTO dto) {
        return memberBiz.updateCert(dto.getMemberCertDTOList(), true, dto.getOperatorId(), dto.getMemberId());
    }

    @Override
    public MemberApprovalRequestDTO findMemberApprovalRequest(String requestId) {
        MemberApprovalRequestDTO item = memberBiz.findMemberApprovalRequest(requestId);
        if (StringUtils.isBlank(item.getMemberCode())) {
            Member member = memberBiz.findById(item.getMemberId());
            if (member != null) {
                item.setMemberCode(member.getMemberCode());
            }
        }
        return item;
    }

    @Override
    public MemberBusinessRequestDetailDTO getMemberApprovalBusinessDetails(String memberApprovalRequestId) {
        checkParamThrowable(memberApprovalRequestId, "变更ID");
        MemberBusinessRequestDetailDTO result = memberBiz.getMemberApprovalBusinessDetails(memberApprovalRequestId);
        if(Objects.nonNull(result)){
            result.setOldIntentions(sortAscIntentionByIntentionType(result.getOldIntentions()));
            result.setNewIntentions(sortAscIntentionByIntentionType(result.getNewIntentions()));
        }
        return result;
    }

    @Override
    public List<MemberPurchaseGoodsIntentionDTO> getIntentionsByMemberId(String memberId) {
        if(CharSequenceUtil.isBlank(memberId))
            return Collections.emptyList();
        List<MemberPurchaseGoodsIntentionDTO> result = BeanUtil.copyToList(iMemberPurchaseGoodsIntentionBiz.listByMemberId(memberId),MemberPurchaseGoodsIntentionDTO.class);
        if(CollUtil.isEmpty(result))
            return Collections.emptyList();

        this.handleIntentions(result);

        return result;
    }

    @Override
    public List<MemberPurchaseGoodsIntentionDTO> getIntentionsByRequestId(String requestId) {
        if(CharSequenceUtil.isBlank(requestId))
            return Collections.emptyList();

        MemberApproveRequest request = memberApproveRequestBiz.get(requestId);
        if(Objects.isNull(request))
            return Collections.emptyList();

        List<MemberPurchaseGoodsIntentionHistory> histories = intentionHistoryBiz.listByRequestId(requestId);
        if(CollUtil.isEmpty(histories))
            return Collections.emptyList();

        if(AdvertStatusEnum.NEW_REQUEST.getCode().equals(request.getStatus())){
            histories.forEach(v->{
                v.setSaleChannel(null);
                v.setSaleUserId(null);
                v.setSaleUserName(null);
                v.setMaintainUserId(null);
                v.setMaintainUserName(null);
                v.setMaintainTime(null);
            });
        }

        String memberId = CollUtil.getFirst(histories).getMemberId();
        List<MemberPurchaseGoodsIntention> intentions = iMemberPurchaseGoodsIntentionBiz.listByMemberId(memberId);
        if(CollUtil.isNotEmpty(intentions)){
            //K:categoryId_goodsId
            Map<String,MemberPurchaseGoodsIntention> intentionMap = intentions.stream()
                    .collect(Collectors.
                            toMap(v->CharSequenceUtil.format(TEMPLATE1,v.getGoodsCategoryId(),v.getGoodsId()),
                                    Function.identity()));
            histories.forEach(v->{
                String key = CharSequenceUtil.format(TEMPLATE1,v.getGoodsCategoryId(),v.getGoodsId());
                MemberPurchaseGoodsIntention intention = CommonUtils.getByKey(intentionMap,key);
                if(Objects.nonNull(intention)){
                    v.setSaleChannel(intention.getSaleChannel());
                    v.setSaleUserId(intention.getSaleUserId());
                    v.setSaleUserName(intention.getSaleUserName());
                    v.setMaintainUserId(intention.getMaintainUserId());
                    v.setMaintainUserName(intention.getMaintainUserName());
                    v.setMaintainTime(intention.getMaintainTime());
                }
            });
        }

        List<MemberPurchaseGoodsIntentionDTO> result = BeanUtil.copyToList(histories,MemberPurchaseGoodsIntentionDTO.class);
        this.handleIntentions(result);
        return result;
    }

    @Override
    @AddLog(operatorFieldName = DuplicateString.OPERATOR_ID)
    @Transactional(rollbackFor = Exception.class)
    public Boolean maintainMemberGoodsIntention(SubmitMemberPurchaseGoodsIntentionDTO param) {
        if (CharSequenceUtil.isBlank(param.getCrmCode()))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "未维护客户编码");
        if (CollUtil.isEmpty(param.getIntentions()))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, INTENTION_NOT_NULL);
        List<MemberPurchaseGoodsIntention> intentions = iMemberPurchaseGoodsIntentionBiz.listByMemberId(param.getMemberId());
        if (CollUtil.isEmpty(intentions))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "购买商品意向数据不存在");
        if (param.getIntentions().stream().anyMatch(v -> CharSequenceUtil.isBlank(v.getId())))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "购买商品意向数据ID不能为空");

        Map<String, MemberPurchaseGoodsIntention> intentionMap = intentions.stream()
                .collect(Collectors.toMap(v -> v.getId(), Function.identity(), (v1, v2) -> v1));

        for (MemberPurchaseGoodsIntentionDTO v : param.getIntentions()) {
            if (CharSequenceUtil.isBlank(v.getSaleChannel()))
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "销售渠道未维护");
            if (CharSequenceUtil.isBlank(v.getSaleUserId()))
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "销售人员未维护");

            MemberPurchaseGoodsIntention intention = CommonUtils.getByKey(intentionMap, v.getId());
            if (Objects.isNull(intention))
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "购买商品意向数据不存在");
            if (handleChange(v, intention)) {
                intention.setMaintainUserId(param.getOperatorId());
                intention.setMaintainUserName(param.getOperatorName());
                intention.setMaintainTime(new Date());
                CommonUtils.setOperator(intention, param.getOperatorId(), false);

                iMemberPurchaseGoodsIntentionBiz.updateSelective(intention);
            }
        }

        Member member = memberBiz.findById(param.getMemberId());
        if (Objects.isNull(member))
            throw new MemberBizException(MemberCode.MEMBER_NOT_EXIST);

        if (!CharSequenceUtil.equals(member.getCrmCode(), param.getCrmCode())) {
            member.setCrmCode(param.getCrmCode());
            CommonUtils.setOperator(member, param.getOperatorId(), false);
            memberBiz.updateSelective(member);
        }


        return Boolean.TRUE;
    }

    @SneakyThrows
    @Override
    @AddLog(operatorFieldName = DuplicateString.OPERATOR_ID,businessNumberFieldName = DuplicateString.REQUEST_ID)
    @Transactional(rollbackFor = Exception.class)
    public Boolean passMemberGoodsIntention(SubmitMemberPurchaseGoodsIntentionDTO param) {
        //校验参数
        verifyPassMemberGoodsIntentionParam(param);

        MemberApproveRequest request = memberApproveRequestBiz.get(param.getRequestId());
        if (Objects.isNull(request)
                || !CharSequenceUtil.equals(request.getRequestType(), ApproveRequestTypeEnum.CHANGE_GOODS_INTENTION.getCode()))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "购买商品意向变更请求不存在");

        List<MemberPurchaseGoodsIntentionHistory> histories = intentionHistoryBiz.listByRequestId(param.getRequestId());
        if(CollUtil.isEmpty(histories) || !CommonUtils.equalsSize(param.getIntentions(),histories))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR,"购买商品意向变更请求明细不存在");

        Map<String,MemberPurchaseGoodsIntentionDTO> intentionMap = CommonUtils.getMap(param.getIntentions(),MemberPurchaseGoodsIntentionDTO::getId);

        request.setStatus(AdvertStatusEnum.APPROVED.getCode());
        request.setApproveId(param.getOperatorId());
        request.setApproveName(param.getOperatorName());
        request.setApproveTime(new Date());
        CommonUtils.setOperator(request,param.getOperatorId(),false);

        param.getIntentions().forEach(v->{
            v.setMaintainUserId(param.getOperatorId());
            v.setMaintainUserName(param.getOperatorName());
            v.setMaintainTime(new Date());
        });

        histories.forEach(v->{
            CommonUtils.setOperator(v,param.getOperatorId(),false);
            MemberPurchaseGoodsIntentionDTO intention = CommonUtils.getByKey(intentionMap,v.getId());
            if(Objects.isNull(intention))
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR,"购买商品意向变更请求明细不存在");

            v.setSaleChannel(intention.getSaleChannel());
            v.setSaleUserId(intention.getSaleUserId());
            v.setSaleUserName(intention.getSaleUserName());
            v.setMaintainUserId(param.getOperatorId());
            v.setMaintainUserName(param.getOperatorName());
            v.setMaintainTime(new Date());

            intentionHistoryBiz.updateSelective(v);
        });

        memberApproveRequestBiz.updateSelective(request);
        iMemberPurchaseGoodsIntentionBiz.saveIntentionList(param.getIntentions(),request.getMemberId(),request.getMemberCode(),param.getOperatorId());

        //发送最新变更意向数据消息
        MQMessage<MemberIntentionChangeDTO> mq = new MQMessage<>();
        mq.setExchange(MqTopicConstant.MEMBER_INTENTION_CHANGE_TOPIC);
        MemberIntentionChangeDTO intentionChange = new MemberIntentionChangeDTO(request.getMemberId(),request.getMemberCode(),param.getOperatorId(),new Date(),null);
        intentionChange.setNewestIntentions(BeanUtil.copyToList(iMemberPurchaseGoodsIntentionBiz.listByMemberId(request.getMemberId()),MemberPurchaseGoodsIntentionDTO.class));
        mq.setData(intentionChange);
        mqProducer.send(mq);

        // 发送短信通知客户管理员 告知审批结果
        List<AccountSimpleDTO> buyerAdmins = this.listAccountsByMemberCodes(new QueryMemberAccountDTO(Arrays.asList(request.getMemberCode()),Arrays.asList(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode())));
        if(CollUtil.isNotEmpty(buyerAdmins)) {
            for (AccountSimpleDTO buyerAdmin : buyerAdmins) {
                SmsDTO sms = new SmsDTO(SmsTemplateEnum.ENTERPRISE_PRODUCT_CHANGE_APPROVED_CODE.getCode(), Arrays.asList(buyerAdmin.getMobile()),Arrays.asList(buyerAdmin.getRealName()));
                iSmsSendService.sendSms(sms);
            }
        }
        return true;
    }

    @Override
    @AddLog(operatorFieldName = DuplicateString.OPERATOR_ID,businessNumberFieldName = DuplicateString.REQUEST_ID)
    @Transactional(rollbackFor = Exception.class)
    public Boolean approveRequestByBuyerRegister(ApproveRequestDTO param) {
        this.approveRequest(param);
        //更新CRM code
        if(CharSequenceUtil.isNotBlank(param.getCrmCode())) {
            log.info("注册审批更新CRM CODE memberId:{} crmCode:{}", param.getMemberId(), param.getCrmCode());
            memberBiz.updateCrmCode(param.getCrmCode(), param.getMemberId());
        }
        List<MemberPurchaseGoodsIntentionHistory> histories = intentionHistoryBiz.listByRequestId(param.getApproveRequestId());
        if(CollUtil.isNotEmpty(histories)){
            Map<String,MemberPurchaseGoodsIntentionDTO> intentionMap = param.getIntentions().stream()
                    .collect(Collectors.toMap(v->v.getId(),Function.identity(),(v1,v2)->v1));
            for (MemberPurchaseGoodsIntentionHistory v : histories) {
                CommonUtils.setOperator(v,param.getOperatorId(),false);
                MemberPurchaseGoodsIntentionDTO intention = CommonUtils.getByKey(intentionMap,v.getId());
                if(Objects.isNull(intention))
                    continue;

                v.setSaleChannel(intention.getSaleChannel());
                v.setSaleUserId(intention.getSaleUserId());
                v.setSaleUserName(intention.getSaleUserName());
                v.setMaintainUserId(param.getOperatorId());
                v.setMaintainUserName(param.getOperatorName());
                v.setMaintainTime(new Date());
                intentionHistoryBiz.updateSelective(v);
            }
            iMemberPurchaseGoodsIntentionBiz.saveIntentionList(param.getIntentions(),param.getMemberId(),param.getMemberCode(),param.getOperatorId());
        }

        // 申请企业入驻审核流程 平台运营人员通过审批 邮件 销售人员 【审批结果通知】企业入驻申请
        executor.execute(()->this.notifyEnterPriseRegisterSuccess(param));

        return Boolean.TRUE;
    }

    @Override
    public Boolean existRequest(String memberId,ApproveRequestTypeEnum requestType, List<AdvertStatusEnum> status) {
        Condition condition = new Condition(MemberApproveRequest.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.MEMBER_ID,memberId)
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(DuplicateString.REQUEST_TYPE,requestType.getCode())
                .andIn(DuplicateString.STATUS, status.stream().map(AdvertStatusEnum::getCode).collect(Collectors.toSet()));

        return memberApproveRequestMapper.selectCountByCondition(condition) > 0;
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitMemberIntentionChange(SubmitMemberPurchaseGoodsIntentionDTO param) {
        Member member = memberBiz.get(param.getMemberId());
        List<MemberPurchaseGoodsIntention> intentions = iMemberPurchaseGoodsIntentionBiz.listByMemberId(param.getMemberId());
        //是否需要审批
        Boolean needApprove = Boolean.TRUE;
        if (CollUtil.isEmpty(param.getIntentions()))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, INTENTION_NOT_NULL);

        Map<String, MemberPurchaseGoodsIntention> intentionMap = null;
        List<String> removeIds = new ArrayList<>();
        List<MemberPurchaseGoodsIntentionDTO> newIntentions = new ArrayList<>();
        //需要更新意向类型的产品
        List<MemberPurchaseGoodsIntention> updateIntentions = null;

        if (CollUtil.isNotEmpty(intentions)) {
            List<String> categoryIds = intentions.stream()
                    .map(v->v.getGoodsCategoryId())
                    .distinct().collect(Collectors.toList());
            needApprove = param.getIntentions().stream()
                    .anyMatch(v -> !CollUtil.contains(categoryIds, v.getGoodsCategoryId()));
            //不需要审批
           if(!BooleanUtil.isTrue(needApprove)) {
               //需要删除的数据
               removeIds = intentions.stream()
                       .filter(v -> param.getIntentions().stream()
                               .noneMatch(v1 -> CharSequenceUtil.equals(v1.getGoodsCategoryId(), v.getGoodsCategoryId())
                                       && CharSequenceUtil.equals(v1.getGoodsId(), v.getGoodsId())))
                       .map(v -> v.getId()).collect(Collectors.toList());
               //需要新增的数据
               newIntentions = param.getIntentions().stream().filter(v->
                       intentions.stream().noneMatch(v1->
                               CharSequenceUtil.equals(v1.getGoodsCategoryId(), v.getGoodsCategoryId())
                               && CharSequenceUtil.equals(v1.getGoodsId(), v.getGoodsId())))
                       .collect(Collectors.toList());

               updateIntentions = getChangeIntentionTypeList(param, intentions);
           }

            intentionMap = intentions.stream()
                    .collect(Collectors.toMap(v -> CharSequenceUtil.format(TEMPLATE1, v.getGoodsCategoryId(), v.getGoodsId()), Function.identity(), (v1, v2) -> v1));
        }
        Map<String, MemberPurchaseGoodsIntention> finalIntentionMap = intentionMap;
        param.getIntentions().forEach(v -> {
            String key = CharSequenceUtil.format(TEMPLATE1, v.getGoodsCategoryId(), v.getGoodsId());
            MemberPurchaseGoodsIntention intention = CommonUtils.getByKey(finalIntentionMap, key);
            v.defaultNull();

            if (Objects.nonNull(intention)) {
                v.setSaleUserId(intention.getSaleUserId());
                v.setSaleUserName(intention.getSaleUserName());
                v.setMaintainUserId(intention.getMaintainUserId());
                v.setMaintainUserName(intention.getMaintainUserName());
                v.setMaintainTime(intention.getMaintainTime());
            }
        });
        if (BooleanUtil.isFalse(needApprove)) {
            //删除数据
            iMemberPurchaseGoodsIntentionBiz.removeByIds(removeIds);
            //新增数据
            iMemberPurchaseGoodsIntentionBiz.saveNewIntentionList(newIntentions,member.getMemberId(),member.getMemberCode(),param.getOperatorId());
            //主换次 次换主调整
            iMemberPurchaseGoodsIntentionBiz.updateIntentionType(updateIntentions,param.getOperatorId());

            //发送最新变更意向数据消息
            MQMessage<MemberIntentionChangeDTO> mq = new MQMessage<>();
            mq.setExchange(MqTopicConstant.MEMBER_INTENTION_CHANGE_TOPIC);
            MemberIntentionChangeDTO intentionChange = new MemberIntentionChangeDTO(member.getMemberId(),member.getMemberCode(),param.getOperatorId(),new Date(),null);
            intentionChange.setNewestIntentions(BeanUtil.copyToList(iMemberPurchaseGoodsIntentionBiz.listByMemberId(member.getMemberId()),MemberPurchaseGoodsIntentionDTO.class));
            mq.setData(intentionChange);
            mqProducer.send(mq);
        }


        String requestId = memberApproveRequestBiz.newRequest(member, needApprove, ApproveRequestTypeEnum.CHANGE_GOODS_INTENTION, param.getOperatorId());
        intentionHistoryBiz.saveIntentionHistoryList(param.getIntentions(), param.getMemberId(),member.getMemberCode(), requestId, param.getOperatorId());

        // 申请企业入驻审核流程 游客/个人用户提交企业入驻申请 发送邮件 to 主产品销售经理
        // 对意向商品分主次
        notifyBySubmitMemberIntentionChange(param, needApprove, member);
        return Boolean.TRUE;
    }

    private void notifyBySubmitMemberIntentionChange(SubmitMemberPurchaseGoodsIntentionDTO param, Boolean needApprove, Member member) {
        List<MemberPurchaseGoodsIntentionDTO> intentionTypeOneList = param.getIntentions().stream()
                .filter(e -> e.getIntentionType() == 1).collect(Collectors.toList());
        MemberPurchaseGoodsIntentionDTO mainGood = intentionTypeOneList.get(0);

        // 根据主意向商品分类查询 销售经理信息
        String goodsCode = mainGood.getGoodsCode();
        DataPermissionGoodsCodeDTO dto = new DataPermissionGoodsCodeDTO();
        dto.setRoleCode(BaseRoleTypeEnum.SELLER_SALES_MANAGER.getRoleCode());
        dto.setGoodsCodeList(Arrays.asList(goodsCode));
        List<DataPermissionAccountInfoDTO> accountByCategoryCode = iRoleService.findAccountByGoodsCode(dto);

        for (DataPermissionAccountInfoDTO accountInfo : accountByCategoryCode) {
            // 在分类对应的商品分类信息中 查询第一个（只会有一个分类）包含的人员信息即销售经理信息
            if (Objects.equals(accountInfo.getGoodsCode(), goodsCode)) {
                List<AccountInfoDTO> accountList = accountInfo.getAccountList();
                // 对分类下所包含的销售经理发送邮件
                for (AccountInfoDTO accountInfoDTO : accountList) {
                    // 发送邮件
                    if(BooleanUtil.isTrue(needApprove)){
                        // 企业意向购买产品变更流程（企业产品跨二级分类变更） 企业用户提交产品变更申请（二级分类变更） 发送邮件 to 主产品销售经理
                        EmailDTO emailDTO = new EmailDTO();
                        emailDTO.setTos(Arrays.asList(accountInfoDTO.getEmail()));
                        emailDTO.setEmailTemplateCode(EmailTemplateEnum.GOOD_CHANGE_APPLICATION.getCode());
                        emailDTO.setTemplateParam(this.getEmailTemplateParam(accountInfoDTO, member));
                        emailSendService.sendEmail(emailDTO);
                    }
                    if(BooleanUtil.isFalse(needApprove)){
                        // 企业意向购买产品变更流程（企业产品跨二级分类变更） 企业用户提交产品变更申请（二级分类变更） 发送邮件 to 主产品销售经理
                        EmailDTO emailDTO = new EmailDTO();
                        emailDTO.setTos(Arrays.asList(accountInfoDTO.getEmail()));
                        emailDTO.setEmailTemplateCode(EmailTemplateEnum.UNASSIGNED_SALESPERSON.getCode());
                        emailDTO.setTemplateParam(this.getEmailTemplateParam(accountInfoDTO, member));
                        emailSendService.sendEmail(emailDTO);
                    }
                }
                break;
            }
        }
    }

    private Map<String,Object> getEmailTemplateParam(AccountInfoDTO accountInfoDTO, Member member){
        Map<String,Object> emailTemplate = new HashMap<>();
        emailTemplate.put("mainProductSalesManagerName",accountInfoDTO.getRealName());
        emailTemplate.put("customerName",member.getMemberName());
        emailTemplate.put("contactPersonName",member.getContactName());
        emailTemplate.put("contactPhone",member.getContactPhone());
        return emailTemplate;
    }

    @Override
    public String findMemberCodeByMemberId(String memberId) {
        Member member = memberBiz.get(memberId);
        return member == null ? null : member.getMemberCode();
    }

    @Override
    public List<MemberPurchaseGoodsIntentionDTO> findMemberInfoByGoodsIds(List<String> goodsIds) {
        Condition condition = iMemberPurchaseGoodsIntentionBiz.newCondition();
        condition.createCriteria().andIn(DuplicateString.GOODS_ID, goodsIds);
        List<MemberPurchaseGoodsIntention> byCondition = iMemberPurchaseGoodsIntentionBiz.findByCondition(condition);
        return BeanUtil.copyToList(byCondition, MemberPurchaseGoodsIntentionDTO.class);
    }

    @Override
    public List<AccountSimpleDTO> listAccountsByMemberCodes(QueryMemberAccountDTO param) {
        List<AccountSimpleDTO> accounts =  memberBiz.listAccountsByMemberCodes(param);
        memberBiz.handleAccountMemberName(accounts);
        return accounts;
    }

    @Override
    public List<MemberSimpleDTO> listSimpleMemberByCodes(List<String> memberCodes) {
        return BeanUtil.copyToList(memberBiz.listByCodes(memberCodes),MemberSimpleDTO.class);
    }

    @Override
    public List<MemberPurchaseGoodsIntentionDTO> findMembersBySalesUserIdAndGoodsCode(String goodsId, String salesUserId) {
        Condition condition = iMemberPurchaseGoodsIntentionBiz.newCondition();
        condition.createCriteria().andEqualTo(DuplicateString.GOODS_ID, goodsId).
                andEqualTo("saleUserId", salesUserId).
                andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE);
        List<MemberPurchaseGoodsIntention> byCondition = iMemberPurchaseGoodsIntentionBiz.findByCondition(condition);
        return BeanUtil.copyToList(byCondition, MemberPurchaseGoodsIntentionDTO.class);
    }

    @Override
    public List<MemberDataInfoDTO> queryMemberInfo(String goodsCode) {
        return memberMapper.queryMemberDataInfo(goodsCode);
    }
    @Override
    public List<MemberPurchaseGoodsIntentionDTO> queryMemberBySaleInfo(QueryIntentionInfoDTO param) {
        Condition condition = iMemberPurchaseGoodsIntentionBiz.newCondition();
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE);
        CommonUtils.andInIfNotEmpty(criteria,DuplicateString.GOODS_ID,param.getGoodsIds());
        CommonUtils.andInIfNotEmpty(criteria,"goodsCode",param.getGoodsCodes());
        CommonUtils.andInIfNotEmpty(criteria,DuplicateString.MEMBER_CODE,param.getMemberCodes());
        CommonUtils.andInIfNotEmpty(criteria,"saleUserId",param.getSaleUserIds());

        List<MemberPurchaseGoodsIntention> list = iMemberPurchaseGoodsIntentionBiz.findByCondition(condition);
        List<MemberPurchaseGoodsIntentionDTO> result = BeanUtil.copyToList(list,MemberPurchaseGoodsIntentionDTO.class);
        if(CollUtil.isNotEmpty(result)){
            List<String> saleUserIds = result.stream().map(v->v.getSaleUserId()).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
            Map<String,Account> accountMap = CommonUtils.getMap(accountBiz.listByIds(saleUserIds),Account::getAccountId);
            result.forEach(v->{
                Account account = CommonUtils.getByKey(accountMap,v.getSaleUserId());
                if(Objects.nonNull(account))
                    v.setSaleUserEmployeeId(account.getEmployeeId());
            });
        }

        return result;
    }

    @Override
    public List<MemberInfoForGoodsDTO> queryMemberInfoByGoodsCode(MemberQueryByGoodsDTO memberQueryByGoodsDTO) {
        return memberMapper.queryMemberInfoByGoodsCode(memberQueryByGoodsDTO);
    }

    @Override
    public List<MemberGoodsInfoDTO> queryGoodsByMemberCode(List<String> memberCodeList) {
       return memberMapper.queryGoodsByMemberCode(memberCodeList);
    }

    @Override
    public PageInfo<MemberSimpleDataDTO> queryMemberByMemberNameAndCrmCode(MemberSimpleDataDTO memberSimpleDataDTO) {
        if (Objects.nonNull(memberSimpleDataDTO) && Objects.nonNull(memberSimpleDataDTO.getPageNum()) && Objects.nonNull(memberSimpleDataDTO.getPageSize())) {
            PageMethod.startPage(memberSimpleDataDTO.getPageNum(), memberSimpleDataDTO.getPageSize());
        }
        return new PageInfo<>(memberMapper.queryMemberByMemberNameAndCrmCode(memberSimpleDataDTO));
    }


    @Override
    public PageInfo<MemberDepositStatusDataDTO> queryMemberDepositStatus(MemberDepositStatusDTO dto) {
        AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
        accountRoleDTO.setAccountId(dto.getAccountId());
        List<DataPermissionDTO> goodsByCategoryAccountId;
        try {
            goodsByCategoryAccountId = iRoleService.getDataPermissionList(accountRoleDTO);
        } catch (Exception e) {
            log.error("企业保证金状态列表查询没有数据权限：{}", e.getMessage());
            return null;
        }
        if (CollUtil.isEmpty(goodsByCategoryAccountId)) {
            log.debug("企业保证金状态列表查询没有数据权限");
            return null;
        }
        List<String> goodsCodeList = goodsByCategoryAccountId.stream().map(DataPermissionDTO::getGoodsCode).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        if (CollUtil.isEmpty(goodsCodeList)) {
            log.debug("企业保证金状态列表查询没有数据权限");
            return null;
        }
        dto.setGoodsCodeList(goodsCodeList);
        dto.setMemberType(MemberTypeEnum.ENTERPRISE_BUYER.getCode());
        if (dto.getPageSize() != -1) {
            PageMethod.startPage(dto.getPageNum(), dto.getPageSize());
        }
        List<MemberDepositStatusDataDTO> memberDepositStatusDataDTOS = memberMapper.queryMemberDepositStatus(dto);
        return new PageInfo<>(memberDepositStatusDataDTOS);
    }

    @Override
    public Boolean saveMemberDepositStatus(MemberSaveDepositStatusDTO dto) {
        Member member = BeanUtil.copyProperties(dto, Member.class);
        member.setDepositStatusUpdateUser(dto.getAccountId());
        member.setDepositStatusUpdateTime(new Date());
        MemberDepositStatusDTO depositStatusDTO = new MemberDepositStatusDTO();
        depositStatusDTO.setPageSize(-1);
        depositStatusDTO.setAccountId(dto.getAccountId());
        PageInfo<MemberDepositStatusDataDTO> dtoPageInfo = queryMemberDepositStatus(depositStatusDTO);
        List<MemberDepositStatusDataDTO> list = dtoPageInfo.getList();
        if (CollUtil.isEmpty(list)) {
            return false;
        }
        MemberDepositStatusDataDTO memberDepositStatusDataDTO = list.stream().filter(f -> f.getMemberId().equals(dto.getMemberId())).findFirst().orElse(null);
        if (BeanUtil.isEmpty(memberDepositStatusDataDTO)) {
            return false;
        }
        try {
            memberMapper.updateByPrimaryKeySelective(member);
            return true;
        } catch (Exception e) {
            log.error("企业保证金状态保存失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean requestRefund(MemberRefundDepositStatusDTO dto) {
        try {
           String memberId =  memberMapper.queryMemberDepositStatusByAccountId(dto.getAccountId());
            if (StringUtils.isEmpty(memberId)|| !memberId.equals(dto.getMemberId())){
                return false;
            }
            dto.setDepositStatusUpdateTime(new Date());
            memberMapper.requestRefund(dto);
            // 向该客户主产品对应的销售经理发送邮件通知
            String goodsCode = memberMapper.queryMainGoodsCode(dto.getMemberId());
            DataPermissionGoodsCodeDTO dataPermissionGoodsCodeDTO = new DataPermissionGoodsCodeDTO();
            dataPermissionGoodsCodeDTO.setRoleCode(BaseRoleTypeEnum.SELLER_SALES_MANAGER.getRoleCode());
            dataPermissionGoodsCodeDTO.setGoodsCodeList(Collections.singletonList(goodsCode));
            List<DataPermissionAccountInfoDTO> accountByCategoryCode = iRoleService.findAccountByGoodsCode(dataPermissionGoodsCodeDTO);
            if (CollUtil.isNotEmpty(accountByCategoryCode)){
                for (DataPermissionAccountInfoDTO dataPermissionAccountInfoDTO : accountByCategoryCode) {
                    List<AccountInfoDTO> accountList = dataPermissionAccountInfoDTO.getAccountList();
                    if (CollUtil.isNotEmpty(accountList)){
                        for (AccountInfoDTO accountInfoDTO : accountList) {
                            EmailDTO emailDTO = getEmailDTO(dto, accountInfoDTO);
                            //发送邮件
                            emailSendService.sendEmail(emailDTO);
                        }
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("企业管理员申请退款失败：{}", e.getMessage());
            return false;
        }
    }

    @NotNull
    private static EmailDTO getEmailDTO(MemberRefundDepositStatusDTO dto, AccountInfoDTO accountInfoDTO) {
        EmailDTO emailDTO = new EmailDTO();
        EmailTemplateEnum biddingSubmitTemplate = EmailTemplateEnum.DEPOSIT_STATUS_REFUND;
        emailDTO.setEmailTemplateCode(biddingSubmitTemplate.getCode());
        //tos
        emailDTO.setTos(List.of(accountInfoDTO.getEmail()));
        Map<String, Object> templateParam = new HashMap<>();
        templateParam.put("realName", accountInfoDTO.getRealName());
        templateParam.put("userName", dto.getMemberName());
        emailDTO.setTemplateParam(templateParam);
        return emailDTO;
    }

    @Override
    public ExportExcelDTO exportMemberDepositStatus(MemberDepositStatusDTO dto) {
        dto.setPageSize(-1);
        PageInfo<MemberDepositStatusDataDTO> memberDepositStatusDataDTOPageInfo = queryMemberDepositStatus(dto);
        if (Objects.nonNull(memberDepositStatusDataDTOPageInfo)) {
            List<MemberDepositStatusDataDTO> list = memberDepositStatusDataDTOPageInfo.getList();
            if (CollUtil.isNotEmpty(list)) {
                list.forEach(data -> data.setDepositStatus(DepositStatusEnum.findNameByCode(data.getDepositStatus())));
                List<MemberDepositStatusExportDTO> memberDepositStatusExportDTOS = BeanUtil.copyToList(list, MemberDepositStatusExportDTO.class);
                String fileName = CharSequenceUtil.format("线上销售平台_客户保证金状态导出_" + DateUtil.today() + ".xlsx");
                return new ExportExcelDTO(fileName, List.of(new ExportSheetDTO("客户保证金更新清单", MemberDepositStatusExportDTO.class, memberDepositStatusExportDTOS)));
            }
        }
        return null;
    }

    @Override
    public ItemResult<MemberDepositStatusImportResultDTO> importExcel(MemberDepositStatusImportDataDTO dto) {
        MemberDepositStatusDTO memberDepositStatusDTO = new MemberDepositStatusDTO();
        memberDepositStatusDTO.setPageSize(-1);
        memberDepositStatusDTO.setAccountId(dto.getAccountId());
        PageInfo<MemberDepositStatusDataDTO> pageInfo = queryMemberDepositStatus(memberDepositStatusDTO);
        if (Objects.isNull(pageInfo)) {
            return ItemResult.fail(null, "没有符合的数据");
        }
        List<MemberDepositStatusDataDTO> list = pageInfo.getList();
        if (CollUtil.isEmpty(list)) {
            return ItemResult.fail(null, "没有符合的数据");
        }
        List<String> crmCodeList = list.stream().map(MemberDepositStatusDataDTO::getCrmCode).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        List<String> importDataList = dto.getList();
        List<String> importList = new ArrayList<>(importDataList);
        importList.removeAll(crmCodeList);
        LinkedHashMap<String, Integer> map = dto.getMap();

        MemberDepositStatusImportResultDTO result = new MemberDepositStatusImportResultDTO();

        if (CollUtil.isNotEmpty(importList)) {
            String lastMessage = "行数据填写有问题，请上传符合要求的客户保证金更新清单";
            List<String> failRowLineNumList = importList.stream()
                    .map(v->StrUtil.toStringOrNull(CommonUtils.getByKey(map,v)))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            String failMessage = String.join(",", failRowLineNumList) + lastMessage;

            result.setFailCount(CollUtil.size(failRowLineNumList));
            result.setFailRowLineNumList(failRowLineNumList);
            result.setFailMessage(failMessage);
        }

        //a. 先根据excel表中的客户数据反向找到线上销售平台中的剩余客户，更新此剩余客户的保证金状态为【未缴纳】。
        List<String> unPayUserList = new ArrayList<>();

        //b. 对于存在于excel表中的客户，【已缴纳】和【退款中】状态的客户，在线上销售平台中状态保持不变

        // 【未缴纳】状态的客户状态统一更新为【已缴纳】。
        List<String> payedUserList = new ArrayList<>();

        for (MemberDepositStatusDataDTO data : list) {
            String crmCode = data.getCrmCode();
            if (CharSequenceUtil.isBlank(crmCode))
                continue;
            String depositStatus = data.getDepositStatus();
            String dataFilter = importDataList.stream().filter(f -> f.equals(crmCode)).findFirst().orElse(null);
            if (CharSequenceUtil.isEmpty(dataFilter)) {
                unPayUserList.add(crmCode);
            } else {
                if (DepositStatusEnum.UNPAID.getCode().equals(depositStatus)) {
                    payedUserList.add(dataFilter);
                }
            }
        }
        int unPay = 0;
        if (CollUtil.isNotEmpty(unPayUserList)) {
            memberMapper.updateDepositStatus(unPayUserList, DepositStatusEnum.UNPAID.getCode(), dto.getAccountId(), new Date());
            unPay = unPayUserList.size();
        }
        int payed = 0;
        if (CollUtil.isNotEmpty(payedUserList)) {
            memberMapper.updateDepositStatus(payedUserList, DepositStatusEnum.PAID.getCode(), dto.getAccountId(), new Date());
            payed = payedUserList.size();
        }

        result.setPayedCount(payed);
        result.setUnPayCount(unPay);

        return ItemResult.success(result);
    }

    private static void handleErrorMessage(List<String> importList, LinkedHashMap<String, Integer> map, List<String> headerMessageList) {
        for (String value : importList) {
            Integer rowIndex = map.get(value);
            headerMessageList.add(String.valueOf(rowIndex));
        }
    }

    @Override
    public String queryDepositStatus(String memberCode) {
       return memberMapper.queryDepositStatus(memberCode);
    }

    @Override
    public List<String> queryGoodsCodeByMemberId(String memberId) {
        Condition condition = new Condition(MemberPurchaseGoodsIntention.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", false)
                .andEqualTo("memberId",memberId);
        List<MemberPurchaseGoodsIntention> memberPurchaseGoodsIntentions = purchaseGoodsIntentionMapper.selectByCondition(condition);
        return memberPurchaseGoodsIntentions.stream().map(MemberPurchaseGoodsIntention::getGoodsCode).collect(Collectors.toList());
    }


    @Override
    public Boolean existEnterpriseRegistrationRequest(String memberId,String status) {
        Condition condition = new Condition(MemberApproveRequest.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(DuplicateString.MEMBER_ID, memberId)
                .andIn(DuplicateString.REQUEST_TYPE, Arrays.asList(ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER.getCode(),
                        ApproveRequestTypeEnum.REGISTER_ENTERPRISE_SELLER.getCode()));
        if(CharSequenceUtil.isNotBlank(status))
            criteria.andEqualTo(DuplicateString.STATUS,status);

        return memberApproveRequestMapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public List<MemberDepositStatusExportDTO> exportMemberDepositStatusDataList(MemberDepositStatusDTO dto) {
        dto.setPageSize(-1);
        PageInfo<MemberDepositStatusDataDTO> memberDepositStatusDataDTOPageInfo = queryMemberDepositStatus(dto);
        if (Objects.nonNull(memberDepositStatusDataDTOPageInfo)) {
            List<MemberDepositStatusDataDTO> list = memberDepositStatusDataDTOPageInfo.getList();
            if (CollUtil.isNotEmpty(list)) {
                list.forEach(data -> data.setDepositStatus(DepositStatusEnum.findNameByCode(data.getDepositStatus())));
                List<MemberDepositStatusExportDTO> memberDepositStatusExportDTOS = BeanUtil.copyToList(list, MemberDepositStatusExportDTO.class);
               return memberDepositStatusExportDTOS;
            }
        }
        return null;
    }

    @Override
    public List<BiddingMemberDTO> queryBiddingMemberList(QueryBiddingMemberDTO param) {
        List<BiddingMemberDTO> result = purchaseGoodsIntentionMapper.queryBiddingMemberList(param);
        if (CollUtil.isEmpty(result))
            return Collections.emptyList();
        String separator = ",";
        Set<String> goodsCodes = new HashSet<>();
        for (BiddingMemberDTO v : result) {
            if (CharSequenceUtil.isNotBlank(v.getMainGoodsCodes()))
                goodsCodes.addAll(CharSequenceUtil.split(v.getMainGoodsCodes(), separator));
            if (CharSequenceUtil.isNotBlank(v.getMinorGoodsCodes()))
                goodsCodes.addAll(CharSequenceUtil.split(v.getMinorGoodsCodes(), separator));
        }
        if (CollUtil.isNotEmpty(goodsCodes)) {
            List<String> goodsCodeList = goodsCodes.stream().filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(goodsCodeList)) {
                Map<String, GoodsSimpleDTO> goodsMap = CommonUtils.getMap(iGoodsService.findGoodsSimpleByCodes(goodsCodeList), GoodsSimpleDTO::getGoodsCode);
                result.forEach(v -> {
                    List<GoodsSimpleDTO> mainGoods = CommonUtils.getListByKey(goodsMap, CharSequenceUtil.split(v.getMainGoodsCodes(), separator));
                    v.setMainGoods(CollUtil.join(CommonUtils.getListValueByDistinctAndFilterBank(mainGoods, GoodsSimpleDTO::getGoodsName), separator));

                    List<GoodsSimpleDTO> minorGoods = CommonUtils.getListByKey(goodsMap, CharSequenceUtil.split(v.getMinorGoodsCodes(), separator));
                    v.setMinorGoods(CollUtil.join(CommonUtils.getListValueByDistinctAndFilterBank(minorGoods, GoodsSimpleDTO::getGoodsName), separator));
                });
            }
        }

        //处理意向商品名称模糊匹配数据
        return result;
    }

    @Override
    public List<MemberSimpleDTO> queryMemberByLikeCrmCode(String crmCode) {
        Condition condition = new Condition(Member.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        CommonUtils.andLikeIfNotBank(criteria, DuplicateString.CRM_CODE, crmCode);

        return BeanUtil.copyToList(memberBiz.findByCondition(condition), MemberSimpleDTO.class);
    }

    @Override
    public List<MemberIntentionInfoDTO> queryMemberIntentionInfo(QueryMemberIntentionInfoDTO param) {
        if(CollUtil.isEmpty(param.getMemberCodes()))
            return Collections.emptyList();
        List<Member> members = memberBiz.listByCodes(param.getMemberCodes());
        if(CollUtil.isEmpty(members))
            return Collections.emptyList();
        List<MemberIntentionInfoDTO> memberResult = BeanUtil.copyToList(members, MemberIntentionInfoDTO.class);
        if(CollUtil.isEmpty(param.getGoodsCodes()))
            return memberResult;

        Map<String,MemberIntentionInfoDTO> memberMap = CommonUtils.getMap(memberResult, MemberIntentionInfoDTO::getMemberCode);
        Condition condition = new Condition(MemberPurchaseGoodsIntention.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andIn(DuplicateString.MEMBER_CODE, param.getMemberCodes())
                .andIn(DuplicateString.GOODS_CODE, param.getGoodsCodes());
        List<MemberPurchaseGoodsIntention> intentions = iMemberPurchaseGoodsIntentionBiz.findByCondition(condition);
        if(CollUtil.isEmpty(intentions))
            return memberResult;
        List<MemberIntentionInfoDTO> intentionResult = BeanUtil.copyToList(intentions, MemberIntentionInfoDTO.class);
        for (MemberIntentionInfoDTO intention : intentionResult) {
            MemberIntentionInfoDTO member = CommonUtils.getByKey(memberMap,intention.getMemberCode());
            if(Objects.nonNull(member)){
                intention.setMemberId(member.getMemberId());
                intention.setMemberName(member.getMemberName());
                intention.setCrmCode(member.getCrmCode());
            }
        }
        List<String> intentionMemberCodes = CommonUtils.getListValueByDistinctAndFilterBank(intentionResult, MemberIntentionInfoDTO::getMemberCode);

        List<MemberIntentionInfoDTO> otherMemberResult = memberResult.stream()
                .filter(v -> !CollUtil.contains(intentionMemberCodes, v.getMemberCode()))
                .collect(Collectors.toList());

        return CommonUtils.union(otherMemberResult,intentionResult);
    }

    @Override
    public PageInfo<MemberReportResultDTO> memberReport(MemberReportQueryDTO param) {
        PageInfo<MemberReportResultDTO> pageResult = memberBiz.memberReport(param);
        if(CollUtil.isEmpty(pageResult.getList()))
            return pageResult;
        //分类信息
        List<String> categoryIds = CommonUtils.getListValueByDistinctAndFilterBank(pageResult.getList(), MemberReportResultDTO::getCategoryIdLevelTwo);
        //K:categoryIdLevelTwo
        Map<String,CategoryAndParentCategoryDTO> categoryMap = null;
        if(CollUtil.isNotEmpty(categoryIds)){
            categoryMap = CommonUtils.getMap(iGoodsCategoryService.queryCategoryAndParentByCategoryId(categoryIds),CategoryAndParentCategoryDTO::getCategoryIdLevelTwo);
        }

        //商品信息
        List<String> goodsCodes = CommonUtils.getListValueByDistinctAndFilterBank(pageResult.getList(), MemberReportResultDTO::getGoodsCode);
        //K:goodsCode
        Map<String,GoodsSimpleDTO> goodsMap = null;
        if(CollUtil.isNotEmpty(goodsCodes)){
            goodsMap = CommonUtils.getMap(iGoodsService.findGoodsSimpleByCodes(goodsCodes),GoodsSimpleDTO::getGoodsCode);
        }

        //查询销售渠道字典
        ValueSetTreeDTO salesChannelDict = iValueSetService.getValueSetByReferenceCode("VS_SALES_CHANNEL");
        Map<String, ValueSetTreeDTO> salesChannelMap = null;
        if (Objects.nonNull(salesChannelDict)) {
            salesChannelMap = CommonUtils.getMap(salesChannelDict.getTreeList(), ValueSetTreeDTO::getOptionKey);
        }

        for (MemberReportResultDTO v : pageResult.getList()) {
            //分类
            CategoryAndParentCategoryDTO categoryInfo = CommonUtils.getByKey(categoryMap,v.getCategoryIdLevelTwo());
            if(Objects.nonNull(categoryInfo)){
                v.setCategoryIdLevelOne(categoryInfo.getCategoryIdLevelOne());
                v.setCategoryCodeLevelOne(categoryInfo.getCategoryCodeLevelOne());
                v.setCategoryNameLevelOne(categoryInfo.getCategoryNameLevelOne());
                v.setCategoryCodeLevelTwo(categoryInfo.getCategoryCodeLevelTwo());
                v.setCategoryNameLevelTwo(categoryInfo.getCategoryNameLevelTwo());
            }
            //产品
            GoodsSimpleDTO goodsInfo = CommonUtils.getByKey(goodsMap,v.getGoodsCode());
            if(Objects.nonNull(goodsInfo)){
                v.setSapMaterialCode(goodsInfo.getSapMaterialCode());
                v.setGoodsName(goodsInfo.getGoodsName());
            }
            //销售渠道
            ValueSetTreeDTO saleChannelValue = CommonUtils.getByKey(salesChannelMap,v.getSaleChannel());
            if(Objects.nonNull(saleChannelValue)){
                v.setSaleChannelName(saleChannelValue.getOptionValue());
            }
            //客户状态
            v.setAbleStatusName(AbleStatusEnum.getNameByStatus(v.getAbleStatus()));
            //意向类型
            v.setIntentionTypeName(IntentionTypeEnum.getNameByType(v.getIntentionType()));
            //保证金状态
            v.setDepositStatusName(DepositStatusEnum.findNameByCode(v.getDepositStatus()));
        }

        return pageResult;
    }

    @Override
    public PageInfo<MemberAccountReportResultDTO> memberAccountReport(MemberAccountReportQueryDTO param) {
        PageInfo<MemberAccountReportResultDTO> pageResult = memberBiz.memberAccountReport(param);
        if (CollUtil.isEmpty(pageResult.getList()))
            return pageResult;

        List<String> accountIds = CommonUtils.getListValueByDistinctAndFilterBank(pageResult.getList(), MemberAccountReportResultDTO::getAccountId);
        //账号及会员企业信息
        List<MemberAccountReportResultDTO> memberAccountSimpleList = memberMapper.queryAccountReportInfo(accountIds);
        //K:accountId
        Map<String, MemberAccountReportResultDTO> accountMap = CommonUtils.getMap(memberAccountSimpleList, MemberAccountReportResultDTO::getAccountId);
        //查询意向信息
        List<String> memberIds = CommonUtils.getListValueByDistinctAndFilterBank(memberAccountSimpleList, MemberAccountReportResultDTO::getMemberId);
        List<String> goodsCodes = CommonUtils.getListValueByDistinctAndFilterBank(pageResult.getList(), MemberAccountReportResultDTO::getGoodsCode);
        //K:memberId_goodsCode
        Map<String, MemberIntentionReportResultDTO> intentionMap = CommonUtils.getMap(iMemberPurchaseGoodsIntentionBiz.queryMemberIntentionReport(memberIds, goodsCodes),
                v -> CharSequenceUtil.format(TEMPLATE1, v.getMemberId(), v.getGoodsCode()));
        //商品信息 K:goodsCode
        Map<String, GoodsSimpleDTO> goodsMap = CommonUtils.getMap(iGoodsService.findGoodsSimpleByCodes(goodsCodes), GoodsSimpleDTO::getGoodsCode);
        //产品分类信息
        List<String> categoryIds = CommonUtils.getListValueByDistinctAndFilterBank(pageResult.getList(), MemberAccountReportResultDTO::getCategoryIdLevelTwo);
        //K:categoryIdLevelTwo
        Map<String, CategoryAndParentCategoryDTO> categoryMap = CommonUtils.getMap(iGoodsCategoryService
                .queryCategoryAndParentByCategoryId(categoryIds), CategoryAndParentCategoryDTO::getCategoryIdLevelTwo);
        //销售渠道
        //查询销售渠道字典
        ValueSetTreeDTO salesChannelDict = iValueSetService.getValueSetByReferenceCode("VS_SALES_CHANNEL");
        Map<String, ValueSetTreeDTO> salesChannelMap = null;
        if (Objects.nonNull(salesChannelDict)) {
            salesChannelMap = CommonUtils.getMap(salesChannelDict.getTreeList(), ValueSetTreeDTO::getOptionKey);
        }

        CopyOptions copyOptions = CopyOptions.create().setIgnoreNullValue(true);

        for (MemberAccountReportResultDTO v : pageResult.getList()) {
            //账户及会员企业信息
            MemberAccountReportResultDTO accountInfo = CommonUtils.getByKey(accountMap, v.getAccountId());
            if (Objects.nonNull(accountInfo)) {
                BeanUtil.copyProperties(accountInfo, v, copyOptions);
                v.setAbleStatusName(AbleStatusEnum.getNameByStatus(accountInfo.getAbleStatus()));
                v.setAccountStatusName(AccountStatusEnum.getNameByStatus(accountInfo.getStatus()));
                v.setAccountTypeName(AccountTypeEnum.getNameByType(accountInfo.getAccountType()));
            }

            //意向信息
            String goodsKey = CharSequenceUtil.format(TEMPLATE1, v.getMemberId(), v.getGoodsCode());
            MemberIntentionReportResultDTO intentionInfo = CommonUtils.getByKey(intentionMap, goodsKey);
            if (Objects.nonNull(intentionInfo)) {
                v.setSaleChannel(intentionInfo.getSaleChannel());
                v.setIntentionType(intentionInfo.getIntentionType());
                ValueSetTreeDTO saleChannelValue = CommonUtils.getByKey(salesChannelMap, v.getSaleChannel());
                if (Objects.nonNull(saleChannelValue)) {
                    v.setSaleChannelName(saleChannelValue.getOptionValue());
                }
            }
            //商品信息
            GoodsSimpleDTO goodsInfo = CommonUtils.getByKey(goodsMap, v.getGoodsCode());
            if (Objects.nonNull(goodsInfo)) {
                v.setSapMaterialCode(goodsInfo.getSapMaterialCode());
                v.setGoodsName(goodsInfo.getGoodsName());
            }

            //产品分类信息
            CategoryAndParentCategoryDTO categoryInfo = CommonUtils.getByKey(categoryMap, v.getCategoryIdLevelTwo());
            if (Objects.nonNull(categoryInfo)) {
                v.setCategoryCodeLevelTwo(categoryInfo.getCategoryCodeLevelTwo());
                v.setCategoryNameLevelTwo(categoryInfo.getCategoryNameLevelTwo());
                v.setCategoryCodeLevelOne(categoryInfo.getCategoryCodeLevelOne());
                v.setCategoryNameLevelOne(categoryInfo.getCategoryNameLevelOne());
                v.setCategoryIdLevelOne(categoryInfo.getCategoryIdLevelOne());
            }
        }

        return pageResult;
    }

    @Override
    public List<MemberIntentionSimpleDTO> queryIntentionBySaleChannel(List<String> saleChannels) {
        return iMemberPurchaseGoodsIntentionBiz.queryIntentionBySaleChannel(saleChannels);
    }

    /**
     *是否发生变更
     * 若变更 更新数据
     */
    private static Boolean handleChange(MemberPurchaseGoodsIntentionDTO change,MemberPurchaseGoodsIntention intention){
        Boolean result = Boolean.FALSE;
        if(!CharSequenceUtil.equals(change.getSaleChannel(),intention.getSaleChannel())){
            result = Boolean.TRUE;
            intention.setSaleChannel(change.getSaleChannel());
        }
        if(!CharSequenceUtil.equals(change.getSaleUserId(),intention.getSaleUserId())
                || !CharSequenceUtil.equals(change.getSaleUserName(),intention.getSaleUserName())){
            result = Boolean.TRUE;
            intention.setSaleUserId(change.getSaleUserId());
            intention.setSaleUserName(change.getSaleUserName());
        }
        return result;
    }

    private void handleIntentions(List<MemberPurchaseGoodsIntentionDTO> result) {
        Map<String,GoodsSimpleDTO> goodsMap = null;
        List<String> goodsIds = result.stream()
                .map(v->v.getGoodsId())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(goodsIds)){
            goodsMap = CommonUtils.getMap(iGoodsService.findGoodsSimpleByIds(goodsIds),GoodsSimpleDTO::getGoodsId);
        }
        List<String> goodsCategoryIds = result.stream()
                .map(v->v.getGoodsCategoryId())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, GoodsCategorySimpleDTO> categoryMap = null;
        if(CollUtil.isNotEmpty(goodsCategoryIds)){
            categoryMap = CommonUtils.getMap(iGoodsCategoryService.findCategorySimpleByIds(goodsCategoryIds),GoodsCategorySimpleDTO::getCategoryId);
        }

        List<String> maintainUserIds = result.stream()
                .map(v->v.getMaintainUserId())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        Map<String, Account> maintainUserMap = null;
        if(CollUtil.isNotEmpty(maintainUserIds)){
            maintainUserMap = CommonUtils.getMap(accountBiz.findByIds(maintainUserIds),Account::getAccountId);
        }

        for (MemberPurchaseGoodsIntentionDTO v : result) {
            GoodsSimpleDTO goods = CommonUtils.getByKey(goodsMap, v.getGoodsId());
            if (Objects.nonNull(goods))
                v.setGoodsName(goods.getGoodsName());
            GoodsCategorySimpleDTO category = CommonUtils.getByKey(categoryMap, v.getGoodsCategoryId());
            if (Objects.nonNull(category))
                v.setGoodsCategoryName(category.getCategoryName());
            Account maintainUser = CommonUtils.getByKey(maintainUserMap, v.getMaintainUserId());
            if (Objects.nonNull(maintainUser))
                v.setMaintainUserName(maintainUser.getAccountName());
        }
    }


    private void setRealNameInfo(MemberApprovalRequestDTO item, List<MemberCertHistory> certHistoryList) {
        if (!CollectionUtils.isEmpty(certHistoryList)) {
            MemberCertHistory memberCertHistory = certHistoryList.stream().filter(certHistory -> MemberCertTypeEnum.IDENTITY_LICENSE.getCode().equals(certHistory.getCertType())).findFirst().orElse(null);
            if (memberCertHistory != null) {
                item.setRealName(memberCertHistory.getRealName());
                item.setRealNameIdNumber(memberCertHistory.getIdNumber());
                item.setRealNameEffectiveTime(memberCertHistory.getEffectiveTime());
                if (StringUtils.isNotBlank(memberCertHistory.getAttachmentId())) {
                    try {
                        List<AttachmentinfoDTO> attachmentList = attachmentService.getByIds(memberCertHistory.getAttachmentId());
                        if (!CollectionUtils.isEmpty(attachmentList)) {
                            item.setRealNameIdPic(attachmentList.stream().map(it -> it.getAttcPath()).collect(Collectors.toList()));
                        }
                    } catch (Exception e) {
                        log.error("根据附件id:" + memberCertHistory.getAttachmentId() + "获取附件信息错误：" + e.getMessage(), e);
                    }
                }
            }
        }
    }

    private void updateMemberInfo(List<MemberApprovalRequestDTO> list) {
        if (CollUtil.isEmpty(list))
            return;
        List<String> memberIds = list.stream()
                .map(MemberApprovalRequestDTO::getMemberId)
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        List<String> requestIds = list.stream()
                .map(MemberApprovalRequestDTO::getRequestId)
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Member> memberMap = CommonUtils.getMap(memberBiz.listByIds(memberIds), Member::getMemberId);
        Map<String, MemberHistory> memberHistoryMap = CommonUtils.getMap(memberHistoryBiz.listByRequestIds(requestIds), MemberHistory::getRequestId);
        for (MemberApprovalRequestDTO item : list) {
            this.handleMemberInfo(item, memberMap);
            this.handleMemberInfoByHistory(item, CommonUtils.getByKey(memberHistoryMap, item.getRequestId()));
        }
    }

    private void handleMemberInfo(MemberApprovalRequestDTO item, Map<String, Member> memberMap) {
        Member member = CommonUtils.getByKey(memberMap, item.getMemberId());
        if (Objects.isNull(member))
            return;

        item.setMemberCode(member.getMemberCode());
        item.setProvinceCode(member.getProvinceCode());
        item.setCityCode(member.getCityCode());
        item.setAreaCode(member.getAreaCode());
        item.setSellerFlg(member.getSellerFlg());
        item.setMainProducts(member.getMainProducts());
    }

    private void handleMemberInfoByHistory(MemberApprovalRequestDTO item, MemberHistory memberHistory) {
        if(Objects.isNull(memberHistory))
            return;

        if (StringUtils.isNotBlank(memberHistory.getProvinceCode())) {
            item.setProvinceCode(memberHistory.getProvinceCode());
        }
        if (StringUtils.isNotBlank(memberHistory.getCityCode())) {
            item.setCityCode(memberHistory.getCityCode());
        }
        if (StringUtils.isNotBlank(memberHistory.getAreaCode())) {
            item.setAreaCode(memberHistory.getAreaCode());
        }
        if (Objects.equals(memberHistory.getSellerFlg(), MemberNumberConstant.ONE)) {
            item.setSellerFlg(memberHistory.getSellerFlg());
        }
        if (StringUtils.isNotBlank(memberHistory.getMainProducts())) {
            item.setMainProducts(memberHistory.getMainProducts());
        }
    }

    private static boolean checkRequestType(MemberApprovalRequestQueryDTO query) {
        return !(ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER.getCode().equals(query.getRequestType()) ||
                ApproveRequestTypeEnum.REGISTER_ENTERPRISE_SELLER.getCode().equals(query.getRequestType()));
    }

    private void sortCertList(List<MemberCertDTO> certDTOList) {
        if (CollUtil.isNotEmpty(certDTOList)) {
            try {
                certDTOList.sort(Comparator.comparing(MemberCertDTO::getCreateTime).reversed());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    private void checkMemberIdIsEmptyThrowable(String str) {
        if (StringUtils.isEmpty(str)) {
            throw new BizException(MemberCode.MEMBER_NOT_EXIST, str);
        }
    }

    private void addressAnalysis(MemberRequestDTO memberRequestDTO) {
        //1000,1100,1101
        String codeArr = memberRequestDTO.getProvinceCode();
        if (!StringUtils.isEmpty(codeArr)) {
            String[] split = codeArr.split(",");
            if (split.length > 0) {
                memberRequestDTO.setProvinceCode(split[0]);
                if (split.length > 1) {
                    memberRequestDTO.setCityCode(split[1]);
                    if (split.length > 2) {
                        memberRequestDTO.setAreaCode(split[2]);
                    }
                }
            }
        }
    }

    private void checkParamThrowable(String str, String name) {
        if (StringUtils.isEmpty(str)) {
            throw new BizException(BasicCode.PARAM_NULL, name);
        }
    }

    private static void verifyPassMemberGoodsIntentionParam(SubmitMemberPurchaseGoodsIntentionDTO param) {
        if(CollUtil.isEmpty(param.getIntentions()))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR,INTENTION_NOT_NULL);
        if(CharSequenceUtil.isBlank(param.getRequestId()))
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR,"变更请求参数不能为空");
        param.getIntentions().forEach(v->{
            if(CharSequenceUtil.isBlank(v.getId()))
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "购买商品意向数据ID不能为空");
            if (CharSequenceUtil.isBlank(v.getSaleChannel()))
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "销售渠道未维护");
            if (CharSequenceUtil.isBlank(v.getSaleUserId()))
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "销售人员未维护");
        });
    }


    private void notifyEnterPriseRegisterSuccess(ApproveRequestDTO param) {
        log.info("企业注册审批通过开始通知客户与销售 requestId:{}",param.getApproveRequestId());
        // 企业关联销售人员信息 邮件通知
        MemberHistory memberHistory = memberHistoryBiz.getByRequestId(param.getApproveRequestId());
        List<String> saleUserIdList = param.getIntentions().stream()
                .map(MemberPurchaseGoodsIntentionDTO::getSaleUserId)
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        List<Account> smsMoblieList = accountBiz.findByIds(saleUserIdList);
        log.info("企业注册审批通过开始通知销售 saleUserId:{}",saleUserIdList);
        if(CollUtil.isNotEmpty(smsMoblieList)) {
            for (Account saleUser : smsMoblieList) {
                EmailDTO emailDTO = new EmailDTO();
                emailDTO.setTos(Arrays.asList(saleUser.getEmail()));
                emailDTO.setEmailTemplateCode(EmailTemplateEnum.ENTERPRISE_ENROLLMENT_APPROVE.getCode());
                emailDTO.setTemplateParam(this.getEmailParamByEnterpriseRegisterPass(memberHistory, saleUser));
                log.info("企业注册审批通过 通知销售 param:{}",emailDTO);
                emailSendService.sendEmail(emailDTO);
            }
        }else{
            log.info("企业注册审批通过 销售账户为空");
        }

        Member member = memberBiz.findById(param.getMemberId());
        // 申请企业入驻审核流程 平台运营人员通过审批 发送短信
        Account account = accountBiz.findById(member.getMainAccountId());
        List<String> smsParam = new ArrayList<>();
        List<String> contactPhones = new ArrayList<>();
        if(Objects.nonNull(account)){
            smsParam.add(account.getRealName());
            contactPhones.add(account.getMobile());
        }
        SmsDTO sms = new SmsDTO(SmsTemplateEnum.ENTERPRISE_SETTLEMENT_APPROVED_CODE.getCode(), contactPhones, smsParam);
        iSmsSendService.sendSms(sms);
    }

    /**
     *获取企业注册森普通过 通知销售人员邮件模板参数
     */
    private Map<String,Object> getEmailParamByEnterpriseRegisterPass(MemberHistory memberHistory, Account saleUserAccount){
        Map<String,Object> emailTemplate = new HashMap<>();
        emailTemplate.put("saleRealName",saleUserAccount.getRealName());
        emailTemplate.put("memberName",Objects.nonNull(memberHistory) ? memberHistory.getMemberName() : "");
        emailTemplate.put("realName",Objects.nonNull(memberHistory) ? memberHistory.getContactName() : "");
        emailTemplate.put("mobile",Objects.nonNull(memberHistory) ? memberHistory.getContactPhone() : "");
        return emailTemplate;
    }


    /**
     *获取产品不变但是意向类型发生变化的变更数据
     */
    private static List<MemberPurchaseGoodsIntention> getChangeIntentionTypeList(SubmitMemberPurchaseGoodsIntentionDTO param,
                                                                                 List<MemberPurchaseGoodsIntention> intentions) {
        List<MemberPurchaseGoodsIntention> updateIntentions = new ArrayList<>();
        //K:categoryId_goodsId
        Map<String, MemberPurchaseGoodsIntentionDTO> changeIntentionMap = param.getIntentions().stream()
                .collect(Collectors.toMap(v -> CharSequenceUtil.format(TEMPLATE1, v.getGoodsCategoryId(), v.getGoodsId()), Function.identity(), (v1, v2) -> v1));
        for (MemberPurchaseGoodsIntention intention : intentions) {
            String key = CharSequenceUtil.format(TEMPLATE1, intention.getGoodsCategoryId(), intention.getGoodsId());
            MemberPurchaseGoodsIntentionDTO changeIntention = CommonUtils.getByKey(changeIntentionMap, key);
            if (Objects.nonNull(changeIntention) && !Objects.equals(changeIntention.getIntentionType(), intention.getIntentionType())) {
                intention.setIntentionType(changeIntention.getIntentionType());
                updateIntentions.add(intention);
            }
        }
        return updateIntentions;
    }

    /**
     * 对意向列表按照意向类型进行升序排序。
     *
     * @param intentions 意向列表
     * @return 排序后的意向列表
     */
    private static List<MemberPurchaseGoodsIntentionDTO> sortAscIntentionByIntentionType(List<MemberPurchaseGoodsIntentionDTO> intentions) {
        if (CollUtil.isEmpty(intentions))
            return intentions;
        intentions = intentions.stream()
                .sorted(Comparator.comparing(MemberPurchaseGoodsIntentionDTO::getIntentionType))
                .collect(Collectors.toList());
        return intentions;
    }
}