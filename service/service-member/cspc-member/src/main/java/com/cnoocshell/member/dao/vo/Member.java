package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "mb_member")
@EqualsAndHashCode(callSuper = true)
public class Member extends BaseEntity {
    /**
     * 会员id
     */
    @Id
    @Column(name = "member_id")
    private String memberId;

    /**
     * 会员名称
     */
    @Column(name = "member_name")
    private String memberName;
    /**
     * 会员简称
     */
    @Column(name = "member_short_name")
    private String memberShortName;

    @Column(name = "main_account_id")
    private String mainAccountId;

    /**
     * 会员代码
     */
    @Column(name = "member_code")
    private String memberCode;

    /**
     * CRM 客户代码
     */
    @Column(name = "crm_code")
    private String crmCode;

    /**
     * 个人会员/企业会员
     */
    @Column(name = "member_type")
    private String memberType;

    @Column(name = "seller_flg")
    private Integer sellerFlg;

    /**
     * 账户冻结标识
     */
    private String status;

    @Column(name = "contact_name")
    private String contactName;

    /**
     * 紧急联系方式
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 所在国家编码
     */
    @Column(name = "country_code")
    private String countryCode;

    /**
     * 所在省编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 所在城市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 所在地区编码
     */
    @Column(name = "area_code")
    private String areaCode;

    /**
     * 详细地址
     */
    @Column(name = "address_detail")
    private String addressDetail;

    /**
     * 详细地址
     */
    @Column(name = "rigist_address_detail")
    private String rigistAddressDetail;

    /**
     * 邮编
     */
    @Column(name = "zip_code")
    private String zipCode;

    /**
     * 备注
     */
    private String memo;

    /**
     * 传真
     */
    private String fax;

    /**
     * 网址
     */
    private String website;

    /**
     * 企业信用号
     */
    @Column(name = "credit_code")
    private String creditCode;

    /**
     * 营业范围
     */
    @Column(name = "main_products")
    private String mainProducts;

    @ApiModelProperty("商标URL")
    @Column(name = "trademark_url")
    private String trademarkUrl;

    /**
     * 业务省份（多个用,隔开）
     */
    @Column(name = "province")
    private String province;

    /**
     * 商品类别（多个用,隔开）
     */
    @Column(name = "goods_category")
    private String goodsCategory;

    /**
     * 是否中海壳牌股东关联方
     */
    @Column(name = "shareholder_relation_party")
    private Boolean shareholderRelationParty;

    @Column(name = "deposit_status")
    private String depositStatus;

    @Column(name = "deposit_status_update_user")
    private String depositStatusUpdateUser;

    @Column(name = "deposit_status_update_time")
    private Date depositStatusUpdateTime;
}