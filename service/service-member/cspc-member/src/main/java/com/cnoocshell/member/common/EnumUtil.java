package com.cnoocshell.member.common;


import com.cnoocshell.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.cnoocshell.member.api.dto.member.enums.CodeEnum;
import com.cnoocshell.member.api.dto.member.enums.MemberTypeEnum;
import com.cnoocshell.member.api.dto.member.enums.StrEnum;

/**
 * @Description 枚举工具类
 */
public class EnumUtil {

    // 添加私有构造函数，防止实例化
    private EnumUtil() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }

    public static <T extends CodeEnum> String getMessageByCode(Integer code, Class<T> enumClass) {
        for (T item : enumClass.getEnumConstants()) {
            if (item.getCode().equals(code)) {
                return item.getMsg();
            }
        }
        return null;
    }

    public static <T extends StrEnum> String getMessageByCode(String code, Class<T> enumClass) {
        for (T item : enumClass.getEnumConstants()) {
            if (item.getCode().equals(code)) {
                return item.getMsg();
            }
        }
        return null;
    }

    public static <T extends CodeEnum> T getEnumByCode(Integer code, Class<T> enumClass) {
        for (T item : enumClass.getEnumConstants()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static <T extends StrEnum> T getEnumByCode(String code, Class<T> enumClass) {
        for (T item : enumClass.getEnumConstants()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static String getMemberTypeCodeByRequestCode(String code) {
        int temp = Integer.parseInt(code);
        int i = temp - Integer.parseInt(ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER.getCode());
        if (i > 3 || i < 0) {
            return null;
        }
        temp = i + Integer.parseInt(MemberTypeEnum.ENTERPRISE_BUYER.getCode());
        return String.valueOf(temp);
    }
}
