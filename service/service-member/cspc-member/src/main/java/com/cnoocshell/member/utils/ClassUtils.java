package com.cnoocshell.member.utils;

import com.cnoocshell.member.api.dto.member.MemberBusinessRequestDetailDTO;
import com.cnoocshell.member.api.dto.member.MemberCertDTO;
import com.cnoocshell.member.exception.DuplicateString;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Field;
import java.util.Set;

@Slf4j
public class ClassUtils {

    private ClassUtils(){}

    public static void findChangeFields(MemberBusinessRequestDetailDTO dto){
        if( dto == null ){
            return;
        }
        Set<String> changeFieldSet = Sets.newHashSet();
        Set<String> ignoreField = Sets.newHashSet("requestId","requestNum", DuplicateString.MEMBER_ID,"attachmentId","memberCertAttachmentDTOS","oldInfo","changeFields",DuplicateString.STATUS,"serialVersionUID","certId");
        findChangeFields(dto.getNewInfo(),dto.getOldInfo(),changeFieldSet,ignoreField);
        dto.setChangeFields(changeFieldSet);
    }

    private static void findChangeFields(Object objA,Object objB,Set<String> diffSet,Set<String> ignoreField){
        if( objA == null && objB == null ){
            return;
        }
        if(objA == null){
            findChangeFields(objB, null,diffSet,ignoreField);
            return;
        }
        //下方objA不可能为空
        Field[] fields = objA.getClass().getDeclaredFields();

        for (Field field : fields) {
            String fieldName = field.getName();
            if(ignoreField.contains(fieldName)){
                continue;
            }
            field.setAccessible(true);

            Object aFieldValue = getFieldValue(field,objA);
            Object bFieldValue = getFieldValue(field,objB);

            if( aFieldValue == null && bFieldValue == null ){
                continue;
            }

            if( field.getType().isAssignableFrom(MemberCertDTO.class) ){
                findChangeFields(aFieldValue,bFieldValue,diffSet,ignoreField);
            }else{
                checkValue(diffSet, field.getType(), fieldName, aFieldValue, bFieldValue);
            }
        }
    }

    private static void checkValue(Set<String> diffSet, Class<?> type, String fieldName, Object aFieldValue, Object bFieldValue) {
        if( type.isAssignableFrom(String.class) ){
            String str1 = aFieldValue == null ? "" : (String)aFieldValue;
            String str2 = bFieldValue == null ? "" : (String)bFieldValue;
            if(!StringUtils.equals(str1,str2)) {
                diffSet.add(fieldName);
            }
        }else if( aFieldValue != null && bFieldValue == null || aFieldValue == null || !aFieldValue.equals(bFieldValue)){
            diffSet.add(fieldName);
        }
    }

    private static Object getFieldValue(Field field,Object obj){
        if( obj == null ){
            return null;
        }
        try {
            return field.get(obj);
        }catch(IllegalArgumentException|IllegalAccessException e){
            log.warn(e.getMessage(),e);
            return null;
        }
    }
}
