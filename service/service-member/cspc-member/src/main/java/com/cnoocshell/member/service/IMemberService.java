package com.cnoocshell.member.service;

import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.dto.member.enums.AdvertStatusEnum;
import com.cnoocshell.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.cnoocshell.member.api.dto.member.intention.MemberIntentionSimpleDTO;
import com.cnoocshell.member.api.dto.report.MemberAccountReportQueryDTO;
import com.cnoocshell.member.api.dto.report.MemberAccountReportResultDTO;
import com.cnoocshell.member.api.dto.report.MemberReportQueryDTO;
import com.cnoocshell.member.api.dto.report.MemberReportResultDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * @Description 会员对外服务接口
 */
public interface IMemberService {
    /**
     * 根据ID查看用户简要信息
     *
     * @param memberId 会员id
     * @return 会员信息DTO对象
     */
    MemberSimpleDTO findMemberSimpleById(String memberId);

    /**
     * 分页获取会员请求
     *
     * @param query    查询对象
     * @param pageNum  页数
     * @param pageSize 一页显示数量
     * @return 会员请求DTO对象集合
     */
    PageInfo<MemberApprovalRequestDTO> pageRegisterMemberApprovalRequests(MemberApprovalRequestQueryDTO query, Integer pageNum, Integer pageSize);

    /**
     * 获取会员请求详情
     *
     * @param memberApprovalRequestId 会员请求Id
     * @return 会员请求DTO对象
     */
    MemberDTO getMemberApprovalDetails(String memberApprovalRequestId);

    /**
     * 根据ID查找会员（只有已审批的信息）
     *
     * @param id 会员id
     * @return 会员DTO对象
     */
    MemberDTO findRealMemberById(String id);

    /**
     * 根据ID查找用户详情（没有资质信息）
     *
     * @param memberId 会员id
     * @return 用户信息DTO对象
     */
    MemberDTO findMemberDetailById(String memberId);

    /**
     * 根据账户ID查找实名认证详情
     *
     * @param accountId 账户id
     * @return 实名认证DTO对象
     */
    MemberCertDTO findRealNameCertByAccountId(String accountId);

    /**
     * 提交企业注册（买家）
     * requestType - 申请类型
     * 必须指明每个资质的资质类型，是经营资质、卖家资质、承运商资质。。。
     *
     * @param memberRequestDTO 企业注册DTO对象
     * @return 申请id
     */
    String registerBuyer(MemberRequestDTO memberRequestDTO);

    /**
     * 分页获取会员请求
     *
     * @param query    查询对象
     * @param pageNum  页数
     * @param pageSize 一页显示数量
     * @return 会员请求DTO对象集合
     */
    PageInfo<MemberApprovalRequestDTO> pageMemberApprovalRequests(MemberApprovalRequestQueryDTO query,
                                                                  Integer pageNum,
                                                                  Integer pageSize);

    /**
     * 审批请求
     *
     * @param dto
     */
    void approveRequest(ApproveRequestDTO dto);

    /**
     * 拒绝请求
     *
     * @param dto
     */
    void rejectRequest(RejectRequestDTO dto);

    /**
     * 更新会员基本信息
     *
     * @param memberBaseInfoDTO 会员基本信息DTO对象
     */
    void updateBaseInfo(MemberBaseInfoDTO memberBaseInfoDTO);


    /**
     * 分页查询用户 (列表使用)
     *
     * @param query 查询对象
     * @return 用户信息（列表使用）DTO对象集合
     */
    PageInfo<MemberListViewDTO> pageMemberListView(MemberQueryDTO query, Integer pageNum, Integer pageSize);

    /**
     * 启用用户
     *
     * @param memberId   用户id
     * @param operatorId 操作人AccountId
     */
    void enableMember(String memberId, String operatorId);

    /**
     * 禁用用户
     *
     * @param memberId   用户id
     * @param operatorId 操作人AccountId
     */
    void disableMember(String memberId, String operatorId);

    /**
     * 按照id查询用户（包括资质）
     *
     * @param id 会员id
     * @return 用户（包括资质）
     */
    MemberDetailDTO findMemberById(String id);

    /**
     * 按照id查询用户（包括资质）
     *
     * @param ids 会员ids
     * @return 用户（包括资质）
     */
    List<MemberDetailDTO> findMemberByIds(List<String> ids);

    /**
     * 变更企业经营信息
     * @param memberBusinessInfoDTO 业经营信息DTO对象
     * @return 请求变更id
     */
    String updateBusinessInfo(MemberBusinessInfoDTO memberBusinessInfoDTO);

    /**
     * 一般资质更新变更
     * 必须写明资质类型 certType、CertId
     *
     * @param dto
     * @return 请求id
     */
    String updateCert(UpdateCertDTO dto);

    /**
     * 获取一条变更记录
     * @param requestId 申请id
     * @return 变更记录DTO对象
     */
    MemberApprovalRequestDTO findMemberApprovalRequest(String requestId);

    /**
     * 获取经营信息审批详情
     * @param memberApprovalRequestId 会员请求Id
     * @return 经营信息审批详情
     */
    MemberBusinessRequestDetailDTO getMemberApprovalBusinessDetails(String memberApprovalRequestId);

    List<MemberPurchaseGoodsIntentionDTO> getIntentionsByMemberId(String memberId);

    List<MemberPurchaseGoodsIntentionDTO> getIntentionsByRequestId(String requestId);


    Boolean maintainMemberGoodsIntention(SubmitMemberPurchaseGoodsIntentionDTO param);

    Boolean passMemberGoodsIntention(SubmitMemberPurchaseGoodsIntentionDTO param);


    Boolean approveRequestByBuyerRegister(ApproveRequestDTO param);

    Boolean existRequest(String memberId, ApproveRequestTypeEnum requestType, List<AdvertStatusEnum> status);

    Boolean submitMemberIntentionChange(SubmitMemberPurchaseGoodsIntentionDTO param);

    String findMemberCodeByMemberId(String memberId);

    List<MemberPurchaseGoodsIntentionDTO> findMemberInfoByGoodsIds(List<String> goodsIds);

    List<AccountSimpleDTO> listAccountsByMemberCodes(QueryMemberAccountDTO param);

    List<MemberSimpleDTO> listSimpleMemberByCodes(List<String> memberCodes);

    List<MemberPurchaseGoodsIntentionDTO> findMembersBySalesUserIdAndGoodsCode(String goodsId, String salesUserId);

    List<MemberDataInfoDTO> queryMemberInfo(String goodsCode);

    List<MemberPurchaseGoodsIntentionDTO> queryMemberBySaleInfo(QueryIntentionInfoDTO param);

    List<MemberInfoForGoodsDTO> queryMemberInfoByGoodsCode(MemberQueryByGoodsDTO memberQueryByGoodsDTO);

    List<MemberGoodsInfoDTO> queryGoodsByMemberCode(List<String> memberCodeList);

    PageInfo<MemberSimpleDataDTO> queryMemberByMemberNameAndCrmCode(MemberSimpleDataDTO memberSimpleDataDTO);

    PageInfo<MemberDepositStatusDataDTO> queryMemberDepositStatus(MemberDepositStatusDTO dto);

    Boolean saveMemberDepositStatus(MemberSaveDepositStatusDTO dto);

    Boolean requestRefund(MemberRefundDepositStatusDTO dto);

    ExportExcelDTO exportMemberDepositStatus(MemberDepositStatusDTO dto);

    ItemResult<MemberDepositStatusImportResultDTO> importExcel(MemberDepositStatusImportDataDTO dto);

    String queryDepositStatus(String memberCode);

    List<String> queryGoodsCodeByMemberId(String memberId);

    Boolean existEnterpriseRegistrationRequest(String memberId,String status);

    List<MemberDepositStatusExportDTO> exportMemberDepositStatusDataList(MemberDepositStatusDTO dto);

    List<BiddingMemberDTO> queryBiddingMemberList(QueryBiddingMemberDTO param);

    List<MemberSimpleDTO> queryMemberByLikeCrmCode(String crmCode);

    List<MemberIntentionInfoDTO> queryMemberIntentionInfo(QueryMemberIntentionInfoDTO param);

    /**
     * 客户报表
     */
    PageInfo<MemberReportResultDTO> memberReport(MemberReportQueryDTO param);

    /**
     * 客户账号报表
     */
    PageInfo<MemberAccountReportResultDTO> memberAccountReport(MemberAccountReportQueryDTO param);

    List<MemberIntentionSimpleDTO> queryIntentionBySaleChannel(List<String> saleChannels);
}