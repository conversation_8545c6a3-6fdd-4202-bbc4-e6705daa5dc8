package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "mb_member_cert_history")
@EqualsAndHashCode(callSuper = true)
public class MemberCertHistory extends BaseEntity {
    /**
     * id
     */
    @Id
    private String id;

    /**
     * 变更请求id
     */
    @Column(name = "request_id")
    private String requestId;

    /**
     * 会员id
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 账户id
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 资质id
     */
    @Column(name = "cert_id")
    private String certId;

    /**
     * 资质名称
     */
    @Column(name = "cert_name")
    private String certName;

    /**
     * 资质类型
     */
    @Column(name = "cert_type")
    private String certType;

    /**
     * 资质状态
     */
    private String status;

    /**
     * 真实姓名
     */
    @Column(name = "real_name")
    private String realName;

    /**
     * 证件号码(身份证,驾驶证,从业资格证等)
     */
    @Column(name = "id_number")
    private String idNumber;

    /**
     * 生效时间
     */
    @Column(name = "effective_time")
    private Date effectiveTime;

    /**
     * 附件id
     */
    @Column(name = "attachment_id")
    private String attachmentId;

    /**
     *排序码
     */
    @Column(name = "order_num")
    private Integer orderNum;
}