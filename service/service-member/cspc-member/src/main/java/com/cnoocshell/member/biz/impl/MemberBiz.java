package com.cnoocshell.member.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.base.api.dto.AccountInfoDTO;
import com.cnoocshell.base.api.dto.DataPermissionAccountInfoDTO;
import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.base.api.dto.DataPermissionGoodsCodeDTO;
import com.cnoocshell.base.api.dto.report.AccountReportQueryDTO;
import com.cnoocshell.base.api.dto.report.AccountReportResultDTO;
import com.cnoocshell.base.api.dto.role.AccountRoleDTO;
import com.cnoocshell.base.api.dto.role.QueryAccountDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.service.IDataPermissionService;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.annotation.RedisLock;
import com.cnoocshell.common.dto.EmailDTO;
import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.enums.AppNames;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.exception.DistributeLockException;
import com.cnoocshell.common.redis.UserInfoKeys;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.IEmailSendService;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.service.lock.RedisLockService;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.goods.api.dto.GoodsCategorySimpleDTO;
import com.cnoocshell.goods.api.dto.GoodsSimpleDTO;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.goods.api.service.IGoodsService;
import com.cnoocshell.integration.enums.EmailTemplateEnum;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.member.api.constant.MemberNumberConstant;
import com.cnoocshell.member.api.dto.KeyValueDTO;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.dto.member.enums.*;
import com.cnoocshell.member.api.dto.report.*;
import com.cnoocshell.member.api.session.Operator;
import com.cnoocshell.member.biz.*;
import com.cnoocshell.member.common.EnumUtil;
import com.cnoocshell.member.dao.mapper.AccountMapper;
import com.cnoocshell.member.dao.mapper.MemberApproveRequestMapper;
import com.cnoocshell.member.dao.mapper.MemberCertMapper;
import com.cnoocshell.member.dao.mapper.MemberMapper;
import com.cnoocshell.member.dao.vo.*;
import com.cnoocshell.member.exception.DuplicateString;
import com.cnoocshell.member.service.handler.SynchronizeMemberHandler;
import com.cnoocshell.member.utils.ClassUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class MemberBiz extends BaseCacheBiz<Member> implements IMemberBiz {

    private final IGoodsService iGoodsService;
    private final IGoodsCategoryService iGoodsCategoryService;
    private final AccountMapper accountMapper;
    @Value("${spring.profiles.active:dev}")
    private String profile;

    private final MemberMapper memberMapper;
    private final IAccountBiz accountBiz;
    private final IMemberApproveRequestBiz iMemberApproveRequestBiz;
    private final IMemberCertBiz iMemberCertBiz;
    private final IMemberCertHistoryBiz iMemberCertHistoryBiz;
    private final IMemberHistoryBiz memberHistoryBiz;
    private final IAccountSessionBiz accountSessionBiz;
    private final UUIDGenerator uuidGenerator;
    private final IMemberPurchaseGoodsIntentionBiz memberIntentionBiz;
    private final SynchronizeMemberHandler synchronizeMemberHandler;
    private final IRoleService iRoleService;
    private final RedisLockService redisLockService;
    private final IMemberPurchaseGoodsIntentionHistoryBiz intentionHistoryBiz;
    private final ISmsSendService iSmsSendService;
    private final IEmailSendService emailSendService;
    private final MemberCertMapper memberCertMapper;
    private final MemberApproveRequestMapper memberApproveRequestMapper;
    private final BizRedisService bizRedisService;
    @Qualifier("myTaskAsyncPool")
    private final Executor executor;
    private final IDataPermissionService iDataPermissionService;

    @PostConstruct
    public void init() {
        initCache(null, DuplicateString.MEMBER_ID, Lists.newArrayList(DuplicateString.MEMBER_CODE));
        cleanRedisCache(null);
    }

    @Override
    public void cleanRedisCache(String operator) {
        cacheClear();
        log.info("{} clear member cache", operator);
    }

    @Override
    public String findMaxMemberCode() {
        return memberMapper.findMaxMemberCode();
    }

    @Override
    public boolean countMemberCode(String memberCode) {
        Condition condition = new Condition(Member.class);
        condition.createCriteria().andEqualTo(DuplicateString.MEMBER_CODE, memberCode);
        return memberMapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public MemberSimpleDTO findMemberSimpleById(String memberId) {
        Member member = findById(memberId);
        if (member != null && !BooleanUtil.isTrue(member.getDelFlg())) {
            MemberSimpleDTO memberSimpleDTO = new MemberSimpleDTO();
            BeanUtils.copyProperties(member, memberSimpleDTO);
            return memberSimpleDTO;
        }
        return null;
    }

    @Override
    public Member findById(String id) {
        return memberMapper.findById(id);
    }

    @Override
    public void insertSelective(Member member) {
        memberMapper.insertSelective(member);
    }

    @Override
    public Page<MemberApprovalRequestDTO> pageMemberApprovalRequests(MemberApprovalRequestQueryDTO query, PageInfo pageInfo) {

        Page<MemberApproveRequest> page = PageMethod.startPage(pageInfo.getPageNum(),pageInfo.getPageSize()).doSelectPage(()->{
            memberApproveRequestMapper.pageByCondition(query);
        });
        Page<MemberApprovalRequestDTO> result = page.stream()
                .map(this::request2dto)
                .collect(Collectors.toCollection(Page::new));
        result.setPageNum(page.getPageNum());
        result.setPageSize(page.getPageSize());
        result.setTotal(page.getTotal());
        return result;
    }

    /**
     * 获取审批详情
     *
     * @param memberApprovalRequestId
     * @return
     */
    @Override
    public MemberDTO getMemberApprovalDetails(String memberApprovalRequestId) {
        log.info("getMemberApprovalDetails requestId: {}", memberApprovalRequestId);
        MemberApproveRequest request = iMemberApproveRequestBiz.get(memberApprovalRequestId);
        if (request == null || BooleanUtil.isTrue(request.getDelFlg())) {
            return null;
        }
        if (request.getStatus().equals(AdvertStatusEnum.NEW_REQUEST.getCode()) ||
                request.getStatus().equals(AdvertStatusEnum.REJECT.getCode())) {
            Member member = get(request.getMemberId());
            //企业注册
            boolean typeChange = ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER.getCode().equals(request.getRequestType())
                    || ApproveRequestTypeEnum.REGISTER_ENTERPRISE_SELLER.getCode().equals(request.getRequestType());
            //第一次注册
            boolean firstRegister = typeChange && MemberTypeEnum.PERSON_BUYER.getCode().equals(member.getMemberType());
            log.info("getMemberApprovalDetails firstRegister:{}", firstRegister);
            if (firstRegister) {
                return getMemberApprovalDetails1(memberApprovalRequestId, request, member);
            } else if (typeChange) {
                return getMemberApprovalDetails2(memberApprovalRequestId, request, member);
            }
        }
        MemberDTO memberDTO = findRealMemberById(request.getMemberId());
        if (StringUtils.isNotBlank(memberDTO.getMainAccountId())) {
            memberDTO.setMainAccountName(accountBiz.findById(memberDTO.getMainAccountId()).getAccountName());
        }
        return memberDTO;
    }

    /**
     * 获取会员已审批的信息和资质
     *
     * @param id
     * @return
     */
    @Override
    public MemberDTO findRealMemberById(String id) {
        Member member = findById(id);
        if (member == null) {
            return null;
        }
        MemberDTO memberDTO = new MemberDTO();
        //基本信息
        BeanUtils.copyProperties(member, memberDTO);
        //处理商品意向
        //处理商品意向
        this.handeMemberIntention(member, memberDTO);

        //资质
        List<MemberCertDTO> memberCertDTOList = addCertList(memberDTO);

        memberDTO.setMemberCertDTOList(memberCertDTOList);

        // 申请人
        memberDTO.setCreateUserId(member.getCreateUser());
        Account account = accountBiz.findById(member.getCreateUser());
        if (account != null) {
            memberDTO.setCreateUserName(account.getAccountName());
        }
        //查询最近一次经营信息、一般资质请求信息
        this.handleLastRequestInfo(id, memberDTO);

        return memberDTO;
    }

    @Override
    public MemberDTO findMemberDetailById(String id) {
        Member member = findById(id);
        if (member == null) {
            return null;
        }
        MemberDTO memberDTO = new MemberDTO();
        //基本信息
        BeanUtils.copyProperties(member, memberDTO);
        //处理商品意向
        this.handeMemberIntention(member, memberDTO);

        //创建人
        setCreateUser(member, memberDTO);
        //资质
        //原本信息
        List<MemberCertDTO> memberCertDTOList = addCertList(memberDTO);

        // 经营信息
        MemberApproveRequest request = iMemberApproveRequestBiz.findLastChangeRequest(id);
        String requestType = null;
        if (Objects.isNull(request)) {
            memberDTO.setMemberCertDTOList(memberCertDTOList);
            setMemberExtInfo(memberDTO, member);
            return memberDTO;
        } else {
            requestType = request.getRequestType();
            memberDTO.setLastTimeRequestId(request.getRequestId());
            memberDTO.setLastTimeRequestType(requestType);
            memberDTO.setLastTimeRequestRejectReason(request.getApproveText());
            memberDTO.setLastTimeRequestTime(request.getCreateTime());
            memberDTO.setLastTimeRequestStatus(request.getStatus());

            //查询最近一次经营信息、一般资质请求信息
            this.handleLastRequestInfo(id, memberDTO);

        }
        // 企业类型是否有变更
        if (!member.getStatus().equals(MemberStatusEnum.CHANGE.getCode()) &&
                !member.getStatus().equals(MemberStatusEnum.AUTH_ING.getCode())) {
            memberDTO.setMemberCertDTOList(memberCertDTOList);
            setMemberExtInfo(memberDTO, member);
            return memberDTO;
        }
        String requestStatus = request.getStatus();
        String requestId = request.getRequestId();
        //证件 仅查询待审批的数据
        List<MemberCertHistory> certHistories = getMemberCertHistories(requestStatus, requestId);
        setHistory(requestStatus, requestType, requestId, memberDTO, member, certHistories);
        setBusinessStatus(request, memberDTO, member, requestStatus, requestType);
        setMemberCertAttachment(certHistories, requestStatus, memberCertDTOList);
        memberDTO.setMemberCertDTOList(memberCertDTOList);
        setMemberExtInfo(memberDTO, member);
        return memberDTO;
    }

    private void handeMemberIntention(Member member, MemberDTO memberDTO) {
        MemberApproveRequest request = iMemberApproveRequestBiz.findLastChangeRequest(member.getMemberId(),ApproveRequestTypeEnum.CHANGE_GOODS_INTENTION);
        if(Objects.nonNull(request)) {
            if(CharSequenceUtil.equals(request.getStatus(),AdvertStatusEnum.NEW_REQUEST.getCode())){
                memberDTO.setIntentionChangeStatus(request.getStatus());
                memberDTO.setIntentionList(BeanUtil.copyToList(intentionHistoryBiz.listByRequestId(request.getRequestId()),MemberPurchaseGoodsIntentionDTO.class));
            }else if(CharSequenceUtil.equals(request.getStatus(),AdvertStatusEnum.REJECT.getCode())){
                memberDTO.setIntentionChangeStatus(request.getStatus());
                List<String> allowMemberType = Arrays.asList(MemberTypeEnum.ENTERPRISE_BUYER.getCode(),MemberTypeEnum.ENTERPRISE_SELLER.getCode());
                if(CollUtil.contains(allowMemberType,member.getMemberType())){
                    //企业会员被驳回展示原意向内容
                    memberDTO.setIntentionList(BeanUtil.copyToList(memberIntentionBiz.listByMemberId(member.getMemberId()), MemberPurchaseGoodsIntentionDTO.class));
                }else{
                    memberDTO.setIntentionList(BeanUtil.copyToList(intentionHistoryBiz.listByRequestId(request.getRequestId()),MemberPurchaseGoodsIntentionDTO.class));
                }
            }else{
                memberDTO.setIntentionList(BeanUtil.copyToList(memberIntentionBiz.listByMemberId(member.getMemberId()), MemberPurchaseGoodsIntentionDTO.class));
            }
        }else{
            memberDTO.setIntentionList(BeanUtil.copyToList(memberIntentionBiz.listByMemberId(member.getMemberId()), MemberPurchaseGoodsIntentionDTO.class));
        }
        memberIntentionBiz.handleNameByIntentions(memberDTO.getIntentionList());
    }

    @Override
    public MemberCertDTO findRealNameCertByAccountId(String accountId) {
        Condition condition = new Condition(MemberCert.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.ACCOUNT_ID, accountId)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(DuplicateString.CERT_TYPE, MemberCertTypeEnum.IDENTITY_LICENSE.getCode());
        condition.orderBy(DuplicateString.UPDATE_TIME).desc();
        List<MemberCert> byCondition = iMemberCertBiz.findByCondition(condition);
        if (byCondition != null && !byCondition.isEmpty()) {
            MemberCert cert = byCondition.get(0);
            MemberCertDTO dto = new MemberCertDTO();
            BeanUtils.copyProperties(cert, dto);
            iMemberCertBiz.setMemberCertAttachment(dto.getAttachmentId(), dto);

            //更新前
            Condition c = new Condition(MemberCertHistory.class);
            c.createCriteria()
                    .andEqualTo(DuplicateString.ACCOUNT_ID, accountId)
                    .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                    .andEqualTo(DuplicateString.CERT_TYPE, MemberCertTypeEnum.IDENTITY_LICENSE.getCode())
                    .andLessThan(DuplicateString.CREATE_TIME, cert.getCreateTime());
            c.orderBy(DuplicateString.CREATE_TIME).desc();
            List<MemberCertHistory> certHistories = iMemberCertHistoryBiz.findByCondition(c);
            if (certHistories != null && !certHistories.isEmpty()) {
                MemberCertHistory history = certHistories.get(0);
                MemberCertDTO oldInfo = new MemberCertDTO();
                BeanUtils.copyProperties(history, oldInfo);
                iMemberCertBiz.setMemberCertAttachment(dto.getAttachmentId(), oldInfo);
                dto.setOldInfo(oldInfo);
            }

            return dto;
        }
        // 申请中
        Condition c = new Condition(MemberCertHistory.class);
        c.createCriteria()
                .andEqualTo(DuplicateString.ACCOUNT_ID, accountId)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(DuplicateString.CERT_TYPE, MemberCertTypeEnum.IDENTITY_LICENSE.getCode());
        c.orderBy(DuplicateString.CREATE_TIME).desc();
        List<MemberCertHistory> certHistories = iMemberCertHistoryBiz.findByCondition(c);
        if (certHistories != null && !certHistories.isEmpty()) {
            MemberCertHistory history = certHistories.get(0);
            MemberCertDTO dto = new MemberCertDTO();
            BeanUtils.copyProperties(history, dto);
            iMemberCertBiz.setMemberCertAttachment(dto.getAttachmentId(), dto);
            return dto;
        }
        return new MemberCertDTO();
    }

    @RedisLock(index = 0, fieldName = DuplicateString.MEMBER_ID)
    @Override
    public String submitRegister(MemberRequestDTO memberRequestDTO, String requestType, boolean needApprove, int proxy, String operatorId) {
        log.info("memberRequestDTO:{} ", memberRequestDTO);
        Member byId = findById(memberRequestDTO.getMemberId());
        checkMemberForSubmitRegister(memberRequestDTO, byId);
        //工商执照是否重复
        checkTemp(memberRequestDTO);

        //是否可以变更
        checkApprove(memberRequestDTO, needApprove);


        memberRequestDTO.setAccountId(byId.getMainAccountId());
        String memberName = memberRequestDTO.getMemberName();
        String contactName = memberRequestDTO.getContactName();
        String contactPhone = memberRequestDTO.getContactPhone();
        Account account = accountBiz.findById(memberRequestDTO.getAccountId());
        if (account != null) {
            contactPhone = StringUtils.isBlank(contactPhone) ? account.getMobile() : contactPhone;
            contactName = StringUtils.isBlank(contactName) ? account.getAccountName() : contactName;
        }
        if (memberRequestDTO.getMemberCertDTOList() != null && !memberRequestDTO.getMemberCertDTOList().isEmpty()) {
            contactName = updateSessionLoginInfoAccountInfo(memberRequestDTO, contactName, account);
        }

        //变更请求表
        String requestId = iMemberApproveRequestBiz.newRequestByContactName(
                memberRequestDTO.getMemberId(), byId.getMemberCode(),
                requestType,
                byId.getMemberType(),
                memberName,
                contactName,
                contactPhone,
                "注册企业类型:" + EnumUtil.getMessageByCode(requestType, ApproveRequestTypeEnum.class),
                needApprove,
                operatorId);

        List<MemberCertDTO> memberCertDTOList = null;
        //三证合一
        updateCertHistoryForSyncretic(memberRequestDTO, needApprove, operatorId, requestId);

        saveCertHistoryListForApprove(memberRequestDTO, needApprove, operatorId, requestId);

        //会员表
        MemberHistory memberHistory = new MemberHistory();
        BeanUtils.copyProperties(memberRequestDTO, memberHistory);
        memberHistory.setRequestId(requestId);
        memberHistory.setMemberId(memberRequestDTO.getMemberId());
        setOperInfo(memberHistory, operatorId, true);
        if (needApprove) {
            memberHistory.setStatus(MemberStatusEnum.AUTH_ING.getCode());
        } else {
            memberHistory.setStatus(MemberStatusEnum.NORMAL.getCode());
        }
        if(CollUtil.isNotEmpty(memberRequestDTO.getProvinceList())){
            memberHistory.setProvince(memberRequestDTO.getProvinceList().stream()
                    .map(v->v.getKey())
                    .filter(CharSequenceUtil::isNotBlank).collect(Collectors.joining(",")));
        }
        memberHistoryBiz.insertSelective(memberHistory);

        updateSelectiveForApprove(memberRequestDTO, needApprove, operatorId);
        List<MemberPurchaseGoodsIntentionDTO> intentionList = memberRequestDTO.getIntentionList();
        //保存意向记录进行审核
        intentionHistoryBiz.saveIntentionHistoryList(intentionList, memberRequestDTO.getMemberId(),byId.getMemberCode(),requestId,operatorId);

        //通知用户
        notifyBySubmitRegister(needApprove, account, intentionList, memberRequestDTO);
        //返回变更ID
        return requestId;
    }

    private void notifyBySubmitRegister(boolean needApprove, Account account, List<MemberPurchaseGoodsIntentionDTO> intentionList, MemberRequestDTO param) {
        if (!(needApprove && account != null))
            return;
        //处理商品名称数据
        memberIntentionBiz.handleNameByIntentions(intentionList);
        // 申请企业入驻审核流程 游客/个人用户提交企业入驻申请 发送邮件 to 主产品销售经理
        // 对意向商品分主次
        List<MemberPurchaseGoodsIntentionDTO> intentionTypeOneList = intentionList.stream()
                .filter(e -> e.getIntentionType() == 1).collect(Collectors.toList());
        List<String> minorGoodNameIntentionList = intentionList.stream()
                .filter(e -> e.getIntentionType() == 2)
                .map(MemberPurchaseGoodsIntentionDTO::getGoodsName)
                .distinct().collect(Collectors.toList());
        MemberPurchaseGoodsIntentionDTO mainGood = CollUtil.getFirst(intentionTypeOneList);
        if(Objects.isNull(mainGood))
            return;

        // 根据主意向商品分类查询 销售经理信息
        String goodsCode = mainGood.getGoodsCode();
        DataPermissionGoodsCodeDTO dto = new DataPermissionGoodsCodeDTO();
        dto.setRoleCode(BaseRoleTypeEnum.SELLER_SALES_MANAGER.getRoleCode());
        dto.setGoodsCodeList(Arrays.asList(goodsCode));
        List<DataPermissionAccountInfoDTO> accountByCategoryCode = iRoleService.findAccountByGoodsCode(dto);

        for (DataPermissionAccountInfoDTO accountInfo : accountByCategoryCode) {
            // 在分类对应的商品分类信息中 查询第一个（只会有一个分类）包含的人员信息即销售经理信息
            if (Objects.equals(accountInfo.getGoodsCode(), goodsCode)) {
                List<AccountInfoDTO> accountList = accountInfo.getAccountList();
                // 对分类下所包含的销售经理发送邮件
                for (AccountInfoDTO accountInfoDTO : accountList) {
                    // 申请企业入驻审核流程 游客/个人用户提交企业入驻申请 发送邮件 to 主产品销售经理
                    EmailDTO emailDTO = new EmailDTO();
                    emailDTO.setTos(Collections.singletonList(accountInfoDTO.getEmail()));
                    // 模板
                    emailDTO.setEmailTemplateCode(EmailTemplateEnum.ENTERPRISE_ENROLLMENT_APPLICATION.getCode());
                    emailDTO.setTemplateParam(this.getEmailTemplateParam(accountInfoDTO.getRealName(), param, mainGood, minorGoodNameIntentionList));
                    emailSendService.sendEmail(emailDTO);
                }
            }
        }
    }

    private Map<String,Object> getEmailTemplateParam(String realName, MemberRequestDTO request
            ,MemberPurchaseGoodsIntentionDTO mainGood, List<String> minorGoodNameIntentionList){
        Map<String,Object> emailTemplate = new HashMap<>();
        emailTemplate.put("saleMasterName",realName);
        emailTemplate.put("memberName",request.getMemberName());
        emailTemplate.put("mobile",request.getContactPhone());
        emailTemplate.put("realName",request.getContactName());
        emailTemplate.put("mainGoods",mainGood.getGoodsName());
        emailTemplate.put("minorGoods",minorGoodNameIntentionList);
        return emailTemplate;
    }

    @Override
    public boolean checkMemberNameUsed(String memberId, String memberName) {
        Condition condition = new Condition(Member.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(DuplicateString.MEMBER_NAME, memberName)
                .andNotEqualTo(DuplicateString.MEMBER_TYPE,MemberTypeEnum.PERSON_BUYER.getCode());
        if (StringUtils.isNotBlank(memberId)) {
            criteria.andNotEqualTo(DuplicateString.MEMBER_ID, memberId);
        }
        return mapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public void approveOrRejectRequest(String approveRequestId, String newCarrierType, String message, boolean approve, Operator operator, String appName) {
        String operatorId = operator.getAccountId();
        //检查状态
        MemberApproveRequest request = iMemberApproveRequestBiz.get(approveRequestId);
        checkMemberApproveRequest(approveRequestId, operator, appName, request);

        updateApproveMemberRequest(approveRequestId, message, approve, operatorId);
        String memberId = request.getRequestMemberId();
        Account account = accountBiz.findById(StringUtils.isBlank(request.getAccountId()) ? get(request.getMemberId()).getMainAccountId() : request.getAccountId());

        // 检查member是否存在
        checkMemberExistException(memberId);

        // 审批拒绝
        if (!approve) {
            sendApproveReject(approveRequestId, message, memberId, request, operatorId, account);
            return;
        }
        // 审批通过

        // 注册时同步一次经营信息变更
        String businessRequestId = null;
        // 发短信使用
        String mainAccountId = null;
        // 会员信息
        MemberHistory memberHistory = memberHistoryBiz.get(approveRequestId);
        if (memberHistory != null && !BooleanUtil.isTrue(memberHistory.getDelFlg())) {

            Member member = get(memberId);
            List<String> requestTypeList = Arrays.asList(ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER.getCode()
                    , ApproveRequestTypeEnum.REGISTER_ENTERPRISE_SELLER.getCode());
            // 请求变更企业类型
            boolean typeChange = CollUtil.contains(requestTypeList, request.getRequestType());
            //会员注册
            boolean firstRegister = typeChange && member.getMemberType().startsWith(MemberDTO.PERSON_TYPE_PRE);
            account = updateSelective(firstRegister, member, memberHistory, account, operatorId);
            // 企业类型变更
            log.info("是否企业类型变更:{}", typeChange);
            setApproveRequestType(typeChange, request, member, operatorId, memberHistory);
            // 避免刷掉申请人，将此属性置为NULL
            memberHistory.setCreateUser(null);
            memberHistory.setCreateTime(null);
            memberHistory.setMemberCode(null);
            memberHistory.setMainAccountId(null);
            memberHistory.setSellerFlg(Objects.equals(member.getSellerFlg(), MemberNumberConstant.ONE) ? member.getSellerFlg() : memberHistory.getSellerFlg());

            log.info("member的数据是=============================>1{}", member);
            log.info("memberHistory的数据是=============================>1{}", memberHistory);
            BeanUtils.copyProperties(memberHistory, member);

            // 经营信息变更
            updateSessionLoginInfoMemberName(request, memberHistory, member, memberId);
            member.setStatus(MemberStatusEnum.NORMAL.getCode());

            // 更新会员
            log.info("member的数据是=============================>2{}", member);
            updateSelective(member, false);
            Member m = get(memberId);
            log.info("member的数据是=============================>3{}", m);
            mainAccountId = m.getMainAccountId();
            // 注册同步经营信息
            businessRequestId = insertSelective(firstRegister, m, account, memberId, operatorId, memberHistory, businessRequestId);
            //向消息队列发送变更后的会员信息
            log.info("member的数据是=============================>5{}", m);
            synchronizeMemberHandler.syncMemberInfo(m);

        } else {
            // 更新会员状态
            Member member = new Member();
            member.setStatus(MemberStatusEnum.NORMAL.getCode());
            member.setMemberId(memberId);
            updateSelective(member, false);
        }

        //资质
        MemberCertHistory certHistory = new MemberCertHistory();
        certHistory.setRequestId(approveRequestId);
        certHistory.setMemberId(memberId);
        certHistory.setDelFlg(Boolean.FALSE);

        List<MemberCertHistory> certHistoryList = iMemberCertHistoryBiz.find(certHistory);
        //一般资质变更
        if(ApproveRequestTypeEnum.CHANGE_NORMAL_CERT.getCode().equals(request.getRequestType())){
            this.saveNormalCert(certHistoryList,memberId);
        }else{
            mainAccountId = updateHistory(certHistoryList, businessRequestId, operatorId, mainAccountId, account, request, memberId);
        }

        // 更新资质历史表的资质状态
        certHistory.setStatus(AdvertStatusEnum.APPROVED.getCode());
        Condition condition = new Condition(MemberCertHistory.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.REQUEST_ID, approveRequestId)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        iMemberCertHistoryBiz.updateByConditionSelective(certHistory, condition);
        // 审批通过 发送通知短信
        executor.execute(()->{
            sendApproveSuccess(memberId, request);
        });

        cleanRedisCache(null);
        accountBiz.cleanRedisCache(null);
        //删除缓存的用户信息
        if(Objects.nonNull(account))
            bizRedisService.del(UserInfoKeys.USER_INFO_KEY + account.getAccountId());
    }


    @Override
    public void updateRegisterInfo(MemberRequestDTO memberRequestDTO, Member member, MemberApproveRequest request, String operatorId) {
        //工商执照是否重复
        String temp = getTemp(memberRequestDTO);
        checkUpdateException(memberRequestDTO, temp);

        Date now = new Date();
        String contactName = memberRequestDTO.getContactName();
        String contactPhone = memberRequestDTO.getContactPhone();
        Account account = accountBiz.findById(memberRequestDTO.getAccountId());
        if (account != null) {
            contactPhone = StringUtils.isBlank(contactPhone) ? account.getMobile() : contactPhone;
            contactName = StringUtils.isBlank(contactName) ? account.getAccountName() : contactName;
        }
        if (!CollectionUtils.isEmpty(memberRequestDTO.getMemberCertDTOList())) {
            contactName = getContactName(memberRequestDTO, contactName, account);
        }

        //变更请求表
        String requestId = request.getRequestId();
        request.setMemberName(member.getMemberName());
        request.setContactName(contactName);
        request.setContactPhone(contactPhone);
        request.setUpdateTime(now);
        request.setUpdateUser(operatorId);
        iMemberApproveRequestBiz.updateSelective(request);

        //经营信息更新
        MemberBusinessInfoDTO memberBusinessInfoDTO = new MemberBusinessInfoDTO();
        BeanUtils.copyProperties(memberRequestDTO, memberBusinessInfoDTO);
        memberBusinessInfoDTO.setBusinessCert(memberRequestDTO.getBusinessCert());
        memberBusinessInfoDTO.setTaxCert(memberBusinessInfoDTO.getTaxCert());
        memberBusinessInfoDTO.setOrganizationCert(memberBusinessInfoDTO.getOrganizationCert());
        updateBusinessInfo(memberBusinessInfoDTO, member.getMemberId(), requestId, operatorId);

        /** 一般资质 */
        //会员资质变更表
        iMemberCertHistoryBiz.update(memberRequestDTO.getMemberCertDTOList(), request.getRequestId(), operatorId);

        //更新会员历史表
        MemberHistory memberHistory = memberHistoryBiz.get(request.getRequestId());
        BeanUtils.copyProperties(memberRequestDTO, memberHistory);
        memberHistory.setRequestId(request.getRequestId());
        memberHistory.setMemberId(member.getMemberId());
        memberHistory.setUpdateTime(now);
        memberHistory.setUpdateUser(operatorId);
        memberHistory.setStatus(MemberStatusEnum.AUTH_ING.getCode());
        memberHistoryBiz.updateSelective(memberHistory);

        //更新会员状态
        member.setUpdateTime(now);
        member.setUpdateUser(operatorId);
        member.setStatus(MemberStatusEnum.AUTH_ING.getCode());
        this.updateSelective(member);
    }


    @Override
    public void updateBusinessInfo(MemberBusinessInfoDTO memberBusinessInfoDTO, String memberId, String requestId, String operatorId) {
        //工商执照是否重复
        String temp;
        if (memberBusinessInfoDTO.getIsSyncretic() == 0) {
            temp = memberBusinessInfoDTO.getBusinessLicenseCode();
        } else {
            temp = memberBusinessInfoDTO.getCreditCode();
        }
        if (StringUtils.isEmpty(temp)) {
            throw new MemberBizException(BasicCode.PARAM_NULL, "营业执照号码");
        }
        if (checkCreditCode(temp, memberBusinessInfoDTO.getMemberId())) {
            throw new MemberBizException(MemberCode.CREDIT_CODE_IS_EXIST, temp);
        }
        // 公司名是否重复
        if (checkMemberNameUsed(memberBusinessInfoDTO.getMemberId(), memberBusinessInfoDTO.getMemberName())) {
            throw new MemberBizException(MemberCode.MEMBER_NAME_IS_EXIST, memberBusinessInfoDTO.getMemberName());
        }

        if (memberBusinessInfoDTO.getIsSyncretic().equals(1) && StringUtils.isNotBlank(memberBusinessInfoDTO.getCreditCode())) {
            String code = memberBusinessInfoDTO.getCreditCode();
            memberBusinessInfoDTO.setOrganizationCode(code);
            memberBusinessInfoDTO.setBusinessLicenseCode(code);
            memberBusinessInfoDTO.setTaxCode(code);
        }

        MemberHistory memberHistory = memberHistoryBiz.get(requestId);
        if (memberHistory == null) {
            return;
        }
        BeanUtils.copyProperties(memberBusinessInfoDTO, memberHistory);
        memberHistory.setRequestId(requestId);
        memberHistory.setMemberId(memberId);
        memberHistory.setUpdateTime(new Date());
        memberHistory.setUpdateUser(operatorId);
        memberHistoryBiz.updateSelective(memberHistory);

        iMemberCertHistoryBiz.updateCert(memberBusinessInfoDTO.getBusinessCert(), memberId, requestId, operatorId, MemberCertTypeEnum.BUSINESS_LICENSE);
    }

    @RedisLock(index = 0, fieldName = DuplicateString.MEMBER_ID)
    @Override
    public void updateBaseInfo(MemberBaseInfoDTO memberBaseInfoDTO, String operatorId) {
        Member member = findById(memberBaseInfoDTO.getMemberId());
        if (member == null || BooleanUtil.isTrue(member.getDelFlg())) {
            throw new BizException(MemberCode.MEMBER_NOT_EXIST, memberBaseInfoDTO.getMemberId());
        }
        if (StringUtils.isEmpty(memberBaseInfoDTO.getSellerType())) {
            iMemberApproveRequestBiz.newRequest(
                    memberBaseInfoDTO.getMemberId(), member.getMemberCode(),
                    member.getMemberName(),
                    ApproveRequestTypeEnum.CHANGE_NORMAL_INFO.getCode(),
                    member.getMemberType(),
                    "修改基本信息",
                    false,
                    operatorId);
        }
        memberBaseInfoDTO.setMemberName(null);
        memberBaseInfoDTO.setMemberCode(null);
        Member m = new Member();
        BeanUtils.copyProperties(memberBaseInfoDTO, m);
        m.setUpdateUser(operatorId);
        m.setUpdateTime(new Date());
        updateSelective(m, false);

        //通知会员信息变更
        synchronizeMemberHandler.syncMemberInfo(this.findById(memberBaseInfoDTO.getMemberId()));
    }

    @Override
    public Page<MemberListViewDTO> pageMemberListView(MemberQueryDTO query, PageInfo pageInfo) {
        Page<Member> members = pageMember(query, pageInfo);
        Page<MemberListViewDTO> collect = members.stream()
                .map(item -> {
                    MemberListViewDTO memberListViewDTO = new MemberListViewDTO();
                    BeanUtils.copyProperties(item, memberListViewDTO);
                    memberListViewDTO.setStatusText(EnumUtil.getMessageByCode(item.getStatus(), MemberStatusEnum.class));
                    return memberListViewDTO;
                })
                .collect(Collectors.toCollection(Page::new));
        BeanUtils.copyProperties(members, collect);
        return collect;
    }

    /**
     * 启用会员
     *
     * @param memberId
     * @param operatorId
     */
    @Override
    public void enableMember(String memberId, String operatorId) {
        Member member = new Member();
        member.setMemberId(memberId);
        member.setStatus(MemberStatusEnum.NORMAL.getCode());
        updateSelective(member, false);
        Account account = accountBiz.findById(operatorId);
        if (account != null) {
            log.info("[ENABLE_MEMBER] 启用用户, 用户ID: {}, 用户名: {}, 操作人ID: {}, 操作人名: {}",
                    memberId, member.getMemberName(), account.getAccountId(),
                    StringUtils.isEmpty(account.getRealName()) ? account.getAccountName() : account.getRealName());
        } else {
            log.info("[ENABLE_MEMBER] 启用用户, 用户ID: {}, 用户名: {}, 操作人ID: 未知, 操作人名: 未知",
                    memberId, member.getMemberName());
        }
    }

    /**
     * 禁用会员
     *
     * @param memberId
     * @param operatorId
     */
    @Override
    public void disableMember(String memberId, String operatorId) {
        Member member = findById(memberId);
        if (member == null) {
            throw new BizException(MemberCode.MEMBER_NOT_EXIST, memberId);
        }
        if (member.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            member.setStatus(MemberStatusEnum.CANCELLATION.getCode());
            updateSelective(member);

            Account account = accountBiz.findById(operatorId);
            if (account != null) {
                log.info("[DISABLE_MEMBER] 禁用用户, 用户ID: {}, 用户名: {}, 操作人ID: {}, 操作人名: {}",
                        memberId, member.getMemberName(), account.getAccountId(),
                        StringUtils.isEmpty(account.getRealName()) ? account.getAccountName() : account.getRealName());
            } else {
                log.info("[DISABLE_MEMBER] 禁用用户, 用户ID: {}, 用户名: {}, 操作人ID: 未知, 操作人名: 未知",
                        memberId, member.getMemberName());
            }
        }
    }

    @Override
    public MemberDetailDTO findMemberById(String memberId) {
        Member member = findById(memberId);
        if (member != null && !BooleanUtil.isTrue(member.getDelFlg())) {
            MemberDetailDTO memberDetailDTO = new MemberDetailDTO();
            BeanUtils.copyProperties(member, memberDetailDTO);
            return memberDetailDTO;
        }
        return null;
    }

    @Override
    public String updateBusinessInfo(MemberBusinessInfoDTO memberBusinessInfoDTO, boolean needApprove, String operatorId) {
        checkUpdateBusinessInfo(memberBusinessInfoDTO);

        String key = "updateBusinessInfo_2530_" + memberBusinessInfoDTO.getMemberId();
        String lock = null;
        try {
            lock = redisLockService.lockFast(key);

            //工商执照是否重复
            List<MemberCertDTO> certDTOList = getMemberCertDTOS(memberBusinessInfoDTO);

            //是否可以提交新变更请求
            checkTempRequest(memberBusinessInfoDTO, needApprove);

            //变更请求
            Member byId = findById(memberBusinessInfoDTO.getMemberId());
            checkDelFlagForById(memberBusinessInfoDTO, byId);
            certDTOList = filterUnChangeCert(certDTOList, memberBusinessInfoDTO.getMemberId());
            //如果证件附件没有变化  如果经营信息录入字段也没有变化
            checkCertDtoList(memberBusinessInfoDTO, certDTOList, byId);
            String requestId = iMemberApproveRequestBiz.newRequest(
                    memberBusinessInfoDTO.getMemberId(), byId.getMemberCode(),
                    byId.getMemberName(),
                    ApproveRequestTypeEnum.CHANGE_BUSINESS.getCode(),
                    byId.getMemberType(),
                    "经营信息更新",
                    needApprove,
                    operatorId);

            if (memberBusinessInfoDTO.getIsSyncretic().equals(1) && StringUtils.isNotBlank(memberBusinessInfoDTO.getCreditCode())) {
                String code = memberBusinessInfoDTO.getCreditCode();
                memberBusinessInfoDTO.setOrganizationCode(code);
                memberBusinessInfoDTO.setBusinessLicenseCode(code);
                memberBusinessInfoDTO.setTaxCode(code);
            }

            MemberHistory memberHistory = new MemberHistory();
            BeanUtils.copyProperties(memberBusinessInfoDTO, memberHistory);
            memberHistory.setRequestId(requestId);
            memberHistory.setMemberId(memberBusinessInfoDTO.getMemberId());
            setOperInfo(memberHistory, operatorId, true);
            memberHistoryBiz.insertSelective(memberHistory);

            saveCertHistoryList(memberBusinessInfoDTO, needApprove, operatorId, requestId);

            if (!needApprove) {
                Member member = new Member();
                BeanUtils.copyProperties(memberBusinessInfoDTO, member);
                setOperInfo(member, operatorId, false);
                updateSelective(member, false);
            } else {
                Member member = new Member();
                member.setMemberId(memberBusinessInfoDTO.getMemberId());
                member.setStatus(MemberStatusEnum.CHANGE.getCode());
                updateSelective(member, false);
            }

            return requestId;
        } catch (DistributeLockException e) {
            log.error("redis加锁失败");
            throw new BizException(MemberCode.APPROVE_REQUEST_STATUS_ERROR);
        } finally {
            if (lock != null) {
                redisLockService.unlock(key, lock);
            }
        }
    }

    @RedisLock(index = 3)
    @Override
    public String updateCert(List<MemberCertDTO> memberCertDtoList, boolean needApproval, String operatorId, String memberId) {
        Set<String> certNames = Sets.newHashSet();
        setMemberCertDTO(memberCertDtoList, certNames);

        Member byId = this.findById(memberId);
        if (Objects.isNull(byId))
            throw new BizException(MemberCode.MEMBER_NOT_EXIST, memberId);
        List<String> enterpriseType = Arrays.asList(MemberTypeEnum.ENTERPRISE_BUYER.getCode(),
                MemberTypeEnum.ENTERPRISE_SELLER.getCode());
        if (!CollUtil.contains(enterpriseType, byId.getMemberType()))
            throw new BizException(MemberCode.MEMBER_IS_NOT_ENTERPRISE);

        //是否可以提交新变更请求
        if (needApproval) {
            MemberApproveRequest tempRequest = iMemberApproveRequestBiz.findLastChangeRequest(memberId,ApproveRequestTypeEnum.CHANGE_NORMAL_CERT,AdvertStatusEnum.NEW_REQUEST);
            if (tempRequest != null) {
                throw new BizException(MemberCode.MEMBER_NOT_ALLOW_NEW_REQUEST,
                        tempRequest.getRequestNum(), EnumUtil.getMessageByCode(tempRequest.getRequestType(), ApproveRequestTypeEnum.class));
            }
        }
        //变更请求表
        String requestId = iMemberApproveRequestBiz.newRequest(
                memberId, byId.getMemberCode(),
                byId.getMemberName(),
                ApproveRequestTypeEnum.CHANGE_NORMAL_CERT.getCode(),
                byId.getMemberType(),
                "更新资质：" + StringUtils.join(certNames, ","),
                needApproval,
                operatorId);
        //更新状态
        byId.setStatus(MemberStatusEnum.CHANGE.getCode());
        this.updateSelective(byId);
        //资质
        if(CollUtil.isNotEmpty(memberCertDtoList)){
            List<MemberCertHistory> certHistory = new ArrayList<>();
            Integer orderNum = 1;

            for (MemberCertDTO v : memberCertDtoList) {
                MemberCertHistory target = new MemberCertHistory();
                BeanUtils.copyProperties(v, target);
                target.setOrderNum(orderNum);
                target.setId(this.getUuidGeneratorGain());
                target.setRequestId(requestId);
                target.setCertId(this.getUuidGeneratorGain());
                target.setStatus(AdvertStatusEnum.NEW_REQUEST.getCode());
                target.handleUser(operatorId,true);

                certHistory.add(target);
                orderNum++;
            }
            iMemberCertHistoryBiz.batchInsert(certHistory);
        }

        return requestId;
    }

    @Override
    public MemberApprovalRequestDTO findMemberApprovalRequest(String requestId) {
        MemberApprovalRequestDTO dto = new MemberApprovalRequestDTO();
        MemberApproveRequest request = iMemberApproveRequestBiz.get(requestId);
        if (request == null) {
            return dto;
        }
        BeanUtils.copyProperties(request, dto);
        dto.setRequestTypeText(EnumUtil.getMessageByCode(request.getRequestType(), ApproveRequestTypeEnum.class));
        dto.setStatusText(EnumUtil.getMessageByCode(request.getStatus(), AdvertStatusEnum.class));
        return dto;
    }

    /**
     * 获取企业变更信息审批详情
     *
     * @param memberApprovalRequestId
     * @return
     */
    @Override
    public MemberBusinessRequestDetailDTO getMemberApprovalBusinessDetails(String memberApprovalRequestId) {
        MemberApproveRequest request = iMemberApproveRequestBiz.get(memberApprovalRequestId);
        if (request == null || BooleanUtil.isTrue(request.getDelFlg())) {
            return null;
        }

        String memberId = request.getMemberId();
        Member member = findById(memberId);
        if (member == null || BooleanUtil.isTrue(member.getDelFlg())) {
            return null;
        }
        MemberBusinessRequestDetailDTO detailDTO = new MemberBusinessRequestDetailDTO();
        detailDTO.setStatus(request.getStatus());
        detailDTO.setRequestId(request.getRequestId());
        detailDTO.setRequestNum(request.getRequestNum());
        MemberBaseInfoDTO infoDTO = new MemberBaseInfoDTO();
        BeanUtils.copyProperties(member, infoDTO);
        Account account = accountBiz.findById(member.getCreateUser());
        if (account != null) {
            infoDTO.setCreateUserId(account.getAccountId());
            infoDTO.setCreateUserName(account.getAccountName());
        }
        infoDTO.setMemberName(member.getMemberName());
        detailDTO.setBaseInfo(infoDTO);
        detailDTO.setSellerFlg(member.getSellerFlg());
        //处理经营信息
        this.handleBusinessInfo(memberApprovalRequestId, detailDTO, request, member);
        //处理意向数据
        this.handleIntentions(detailDTO,request,member);
        //处理一般资质数据
        this.handleNormalCert(detailDTO,request,member);

        ClassUtils.findChangeFields(detailDTO);
        return detailDTO;
    }

    private void handleNormalCert(MemberBusinessRequestDetailDTO detailDTO, MemberApproveRequest request, Member member) {
        List<MemberCertDTO> nowCerts = BeanUtil.copyToList(
                iMemberCertBiz.findByMemberId(request.getMemberId(),
                        Arrays.asList(MemberCertTypeEnum.OTHER_LICENSE)),
                MemberCertDTO.class);
        List<MemberCertHistory> requestCerts = null;
        //非经营信息变更不设置经营信息历史信息
        if (!ApproveRequestTypeEnum.CHANGE_NORMAL_CERT.getCode().equals(request.getRequestType())) {
            detailDTO.setNewNormalCerts(nowCerts);
        } else {
            requestCerts = iMemberCertHistoryBiz.findByRequestId(request.getRequestId());

            detailDTO.setNewNormalCerts(BeanUtil.copyToList(requestCerts, MemberCertDTO.class));
            if (CharSequenceUtil.equals(request.getStatus(), AdvertStatusEnum.NEW_REQUEST.getCode())) {
                // 待审批 old为当前信息
                detailDTO.setOldNormalCerts(nowCerts);
            } else {
                MemberApproveRequest lastedRequest = iMemberApproveRequestBiz.findLatestRequest(member.getMemberId(),
                        ApproveRequestTypeEnum.CHANGE_NORMAL_CERT,
                        AdvertStatusEnum.APPROVED,
                        getAprrovalOrCreateTime(request));
                if (Objects.isNull(lastedRequest)) {
                    detailDTO.setOldNormalCerts(nowCerts);
                } else {
                    detailDTO.setOldNormalCerts(BeanUtil.copyToList(iMemberCertHistoryBiz.findByRequestId(lastedRequest.getRequestId()), MemberCertDTO.class));
                }
            }
        }
    }

    private void handleIntentions(MemberBusinessRequestDetailDTO detailDTO, MemberApproveRequest request, Member member) {
        List<MemberPurchaseGoodsIntentionDTO> nowIntentions =  BeanUtil.copyToList(memberIntentionBiz.listByMemberId(member.getMemberId()),MemberPurchaseGoodsIntentionDTO.class);
        List<MemberPurchaseGoodsIntentionDTO> requestIntentions = null;

        //处理商品名称数据
        this.handleGoodsAndCategoryName(nowIntentions);
        //非经营信息变更不设置经营信息历史信息
        if(!ApproveRequestTypeEnum.CHANGE_GOODS_INTENTION.getCode().equals(request.getRequestType())){
            detailDTO.setNewIntentions(nowIntentions);
        }else{
            requestIntentions = intentionHistoryBiz.getByRequestId(request.getRequestId());
            detailDTO.setNewIntentions(requestIntentions);

            if (CharSequenceUtil.equals(request.getStatus(),AdvertStatusEnum.NEW_REQUEST.getCode())) {
                // 待审批 old为当前信息
                detailDTO.setOldIntentions(nowIntentions);
            } else{
                MemberApproveRequest lastedRequest = iMemberApproveRequestBiz.findLatestRequest(member.getMemberId(),
                        ApproveRequestTypeEnum.CHANGE_GOODS_INTENTION,
                        AdvertStatusEnum.APPROVED,
                        getAprrovalOrCreateTime(request));
                if(Objects.isNull(lastedRequest)){
                    detailDTO.setOldIntentions(nowIntentions);
                }else{
                    detailDTO.setOldIntentions(intentionHistoryBiz.getByRequestId(lastedRequest.getRequestId()));
                }
            }
        }

    }

    private void handleGoodsAndCategoryName(List<MemberPurchaseGoodsIntentionDTO> intentions){
        if(CollUtil.isEmpty(intentions))
            return;
        List<String> goodsIds = intentions.stream().map(v->v.getGoodsId()).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> categoryIds = intentions.stream().map(v->v.getGoodsCategoryId()).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());

        Map<String,GoodsSimpleDTO> goodsMap = null;
        if(CollUtil.isNotEmpty(goodsIds))
            goodsMap = CommonUtils.getMap(iGoodsService.findGoodsSimpleByIds(goodsIds), GoodsSimpleDTO::getGoodsId);

        Map<String, GoodsCategorySimpleDTO> goodsCategoryMap = null;
        if(CollUtil.isNotEmpty(categoryIds))
            goodsCategoryMap = CommonUtils.getMap(iGoodsCategoryService.findCategorySimpleByIds(categoryIds),GoodsCategorySimpleDTO::getCategoryId);
        for (MemberPurchaseGoodsIntentionDTO intention : intentions) {
            GoodsSimpleDTO goods = CommonUtils.getByKey(goodsMap,intention.getGoodsId());
            if(Objects.nonNull(goods))
                intention.setGoodsName(goods.getGoodsName());
            GoodsCategorySimpleDTO category = CommonUtils.getByKey(goodsCategoryMap, intention.getGoodsCategoryId());
            if(Objects.nonNull(category))
                intention.setGoodsCategoryName(category.getCategoryName());
        }
    }


    private void handleBusinessInfo(String memberApprovalRequestId, MemberBusinessRequestDetailDTO detailDTO, MemberApproveRequest request, Member member) {
        //非经营信息变更不设置经营信息历史信息
        if(!ApproveRequestTypeEnum.CHANGE_BUSINESS.getCode().equals(request.getRequestType())){
            detailDTO.setNewInfo(this.getNowBusinessInfoByMemberId(member));
        }else {
            // newInfo
            MemberBusinessInfoDTO newInfo = getBusinessInfoByRequestId(memberApprovalRequestId);
            detailDTO.setNewInfo(newInfo);
            if (CharSequenceUtil.equals(request.getStatus(),AdvertStatusEnum.NEW_REQUEST.getCode())) {
                // 待审批 old为当前信息
                detailDTO.setOldInfo(this.getNowBusinessInfoByMemberId(member));
            } else{
                //非待审批 经营信息审批小于当前审批时间或创建时间最近一次审批通过记录的数据
                MemberApproveRequest lastedRequest = iMemberApproveRequestBiz.findLatestRequest(member.getMemberId(),
                        ApproveRequestTypeEnum.CHANGE_BUSINESS,
                        AdvertStatusEnum.APPROVED,
                        getAprrovalOrCreateTime(request)
                );
                if(Objects.isNull(lastedRequest)){
                    detailDTO.setOldInfo(this.getNowBusinessInfoByMemberId(member));
                }else{
                    detailDTO.setOldInfo(this.getBusinessInfoByRequestId(lastedRequest.getRequestId()));
                }
            }
        }
    }

    private MemberBusinessInfoDTO getNowBusinessInfoByMemberId(Member member) {
        MemberBusinessInfoDTO infoDTO = new MemberBusinessInfoDTO();
        BeanUtils.copyProperties(member, infoDTO);

        MemberCert nowBusinessCert = CollUtil.getFirst(iMemberCertBiz.findByMemberId(member.getMemberId(),Arrays.asList(MemberCertTypeEnum.BUSINESS_LICENSE)));
        infoDTO.setBusinessCert(BeanUtil.toBean(nowBusinessCert,MemberCertDTO.class));
        if(Objects.nonNull(nowBusinessCert)){
            iMemberCertBiz.setMemberCertAttachment(nowBusinessCert.getAttachmentId(),infoDTO.getBusinessCert());
        }

        return infoDTO;
    }

    @Override
    public List<Member> listByCodes(List<String> memberCodes) {
        if(CollUtil.isEmpty(memberCodes))
            return Collections.emptyList();
        Condition condition = new Condition(Member.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andIn(DuplicateString.MEMBER_CODE,memberCodes);
        return this.findByCondition(condition);
    }

    @Override
    public int countByMemberCodes(List<String> memberCodes){
        if (CollUtil.isEmpty(memberCodes))
            return 0;
        Condition condition = new Condition(Member.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andIn(DuplicateString.MEMBER_CODE,memberCodes);
        return memberMapper.selectCountByCondition(condition);
    }

    @Override
    public int updateCrmCode(String crmCode, String memberId) {
        return memberMapper.updateCrmCode(crmCode,memberId);
    }

    @Override
    public List<AccountSimpleDTO> listAccountsByMemberCodes(QueryMemberAccountDTO param) {
        if (this.countByMemberCodes(param.getMemberCodes()) <= 0)
            return Collections.emptyList();
        if(CharSequenceUtil.isNotBlank(param.getDepositStatus())){
            List<String> memberCodes = memberMapper.selectMemberCodeByDepositStatus(param.getMemberCodes(),param.getDepositStatus());
            if(CollUtil.isEmpty(memberCodes))
                return null;
            param.setMemberCodes(memberCodes);
        }


        List<String> accountIds = accountBiz.listAccountIdByMemberCodes(param.getMemberCodes());
        if (CollUtil.isEmpty(accountIds))
            return Collections.emptyList();

        if (CollUtil.isEmpty(param.getRoleCodes())) {
            return BeanUtil.copyToList(accountBiz.findByIds(accountIds), AccountSimpleDTO.class);
        }
        List<String> hasRoleAccountIds = iRoleService.queryAccountIds(new QueryAccountDTO(accountIds, param.getRoleCodes()));
        if (CollUtil.isEmpty(hasRoleAccountIds))
            return Collections.emptyList();
        return BeanUtil.copyToList(accountBiz.listByIds(hasRoleAccountIds), AccountSimpleDTO.class);
    }

    @Override
    public List<Member> listByIds(List<String> memberIds) {
        if(CollUtil.isEmpty(memberIds))
            return null;
        Condition condition = new Condition(Member.class);
        condition.createCriteria()
                .andIn(DuplicateString.MEMBER_ID,memberIds)
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE);

        return this.findByCondition(condition);
    }

    @Override
    public void handleAccountMemberName(List<AccountSimpleDTO> accounts) {
        if (CollUtil.isEmpty(accounts))
            return;
        List<String> memberIds = accounts.stream()
                .map(AccountSimpleDTO::getMemberId)
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Member> memberMap = CommonUtils.getMap(this.listByIds(memberIds), Member::getMemberId);
        accounts.forEach(v -> {
            Member member = CommonUtils.getByKey(memberMap, v.getMemberId());
            if (Objects.nonNull(member)) {
                v.setMemberName(member.getMemberName());
                v.setMemberShortName(member.getMemberShortName());
            }
        });
    }


    /**
     *分页查询会员变更请求 意向变更、经营信息变更、资质变更
     * 不查询已关闭状态的数据 AdvertStatusEnum.CLOSED
     */
    @Override
    public PageInfo<MemberApprovalRequestDTO> pageMemberChangeRequest(MemberApprovalRequestQueryDTO query, PageInfo pageInfo) {
        Page<MemberApproveRequest> page = PageMethod
                .startPage(pageInfo.getPageNum(), pageInfo.getPageSize())
                .doSelectPage(() ->
                        memberApproveRequestMapper.pageMemberChangeRequest(query)
                );
        Page<MemberApprovalRequestDTO> result = page.stream()
                .map(this::request2dto)
                .collect(Collectors.toCollection(Page::new));
        result.setPageNum(page.getPageNum());
        result.setPageSize(page.getPageSize());
        result.setTotal(page.getTotal());
        return result.toPageInfo();
    }

    @Override
    public PageInfo<MemberReportResultDTO> memberReport(MemberReportQueryDTO param) {
        //数据权限查询处理
        //查询数据权限
        AccountRoleDTO queryDataPermission = new AccountRoleDTO();
        queryDataPermission.setAccountId(param.getOperatorBy());
        List<DataPermissionDTO> dataPermissionList = iRoleService.getDataPermissionList(queryDataPermission);
        if (CollUtil.isEmpty(dataPermissionList)) {
            log.info("客户报表 accountId:{} 无数据权限", param.getOperatorBy());
            return PageInfo.emptyPageInfo();
        }
        List<String> dataPermissionGoodsCodes = CommonUtils.getListValueByDistinctAndFilterBank(dataPermissionList, DataPermissionDTO::getGoodsCode);
        if (CollUtil.isEmpty(dataPermissionGoodsCodes)) {
            log.info("客户报表 accountId:{} 无数据权限", param.getOperatorBy());
            return PageInfo.emptyPageInfo();
        }
        param.setDataPermissionGoodsCodes(dataPermissionGoodsCodes);
        //产品分类编码查询
        if (CollUtil.isNotEmpty(param.getCategoryCodes())) {
            List<GoodsCategorySimpleDTO> categoryList = iGoodsCategoryService.getSimpleList(param.getCategoryCodes()
                    .stream().collect(Collectors.toSet()));
            if (CollUtil.isEmpty(categoryList)) {
                log.info("客户报表 accountId:{} categoryCode:{} 产品分类查询为空 ", param.getOperatorBy(), param.getCategoryCodes());
                return PageInfo.emptyPageInfo();
            }
            param.setCategoryIdByCategoryCodes(CommonUtils.getListValueByDistinctAndFilterBank(categoryList, GoodsCategorySimpleDTO::getCategoryId));
        }
        //产品名称查询
        if (CharSequenceUtil.isNotEmpty(param.getGoodsName())) {
            List<GoodsSimpleDTO> goodsList = iGoodsService.queryGoodsByLikeName(param.getGoodsName(), null);
            if (CollUtil.isEmpty(goodsList)) {
                log.info("客户报表 accountId:{} goodsName:{} 产品名称查询为空 ", param.getOperatorBy(), param.getGoodsName());
                return PageInfo.emptyPageInfo();
            }
            param.setGoodsCodeByGoodsName(CommonUtils.getListValueByDistinctAndFilterBank(goodsList, GoodsSimpleDTO::getGoodsCode));
        }
        //sap物料号查询
        if (CharSequenceUtil.isNotEmpty(param.getSapMaterialCode())) {
            ItemResult<List<GoodsSimpleDTO>> goodsResult = iGoodsService.findGoodsSimpleByLikeSapMaterialCode(param.getSapMaterialCode());
            if (Objects.isNull(goodsResult) || CollUtil.isEmpty(goodsResult.getData())) {
                log.info("客户报表 accountId:{} sapMaterialCode:{} SAP物料号查询为空 ", param.getOperatorBy(), param.getSapMaterialCode());
                return PageInfo.emptyPageInfo();
            }
            param.setGoodsCodeBySapMaterialCode(CommonUtils.getListValueByDistinctAndFilterBank(goodsResult.getData(), GoodsSimpleDTO::getGoodsCode));
        }

        if (BooleanUtil.isTrue(param.getNeedPage())) {
            return PageMethod.startPage(param.getPageNum(), param.getPageSize())
                    .doSelectPageInfo(() -> memberMapper.memberReport(param));
        } else {
            return PageInfo.of(memberMapper.memberReport(param));
        }
    }

    @Override
    public PageInfo<MemberAccountReportResultDTO> memberAccountReport(MemberAccountReportQueryDTO param) {
        //数据权限查询处理
        //查询数据权限
        AccountRoleDTO queryDataPermission = new AccountRoleDTO();
        queryDataPermission.setAccountId(param.getOperatorBy());
        List<DataPermissionDTO> dataPermissionList = iRoleService.getDataPermissionList(queryDataPermission);
        if (CollUtil.isEmpty(dataPermissionList)) {
            log.info("客户账号报表 accountId:{} 无数据权限", param.getOperatorBy());
            return PageInfo.emptyPageInfo();
        }
        List<String> dataPermissionGoodsCodes = CommonUtils.getListValueByDistinctAndFilterBank(dataPermissionList, DataPermissionDTO::getGoodsCode);
        if (CollUtil.isEmpty(dataPermissionGoodsCodes)) {
            log.info("客户账号报表 accountId:{} 无数据权限", param.getOperatorBy());
            return PageInfo.emptyPageInfo();
        }
        param.setDataPermissionGoodsCodes(dataPermissionGoodsCodes);

        //sap物料号查询
        if (CharSequenceUtil.isNotEmpty(param.getSapMaterialCode())) {
            ItemResult<List<GoodsSimpleDTO>> goodsResult = iGoodsService.findGoodsSimpleByLikeSapMaterialCode(param.getSapMaterialCode());
            if (Objects.isNull(goodsResult) || CollUtil.isEmpty(goodsResult.getData())) {
                log.info("客户账号报表 accountId:{} sapMaterialCode:{} SAP物料号查询为空 ", param.getOperatorBy(), param.getSapMaterialCode());
                return PageInfo.emptyPageInfo();
            }
            param.setGoodsCodeBySapMaterialCode(CommonUtils.getListValueByDistinctAndFilterBank(goodsResult.getData(), GoodsSimpleDTO::getGoodsCode));
        }

        log.info("客户账号报表入参 accountId:{} param:{}",param.getOperatorBy(),param);
        List<MemberAccountReportSimpleDTO> memberAccountList = memberMapper.memberAccountReport(param);
        if(CollUtil.isEmpty(memberAccountList)){
            log.info("客户账号报表 accountId:{} param:{} 查询未匹配", param.getOperatorBy(),param);
            return PageInfo.emptyPageInfo();
        }

        //分页查询（基于账号+产品维度）
        AccountReportQueryDTO query = new AccountReportQueryDTO();
        query.setNeedPage(param.getNeedPage());
        query.setPageNum(param.getPageNum());
        query.setPageSize(param.getPageSize());

        List<String> memberIds = CommonUtils.getListValueByDistinctAndFilterBank(memberAccountList, MemberAccountReportSimpleDTO::getMemberId);
        //查询销售渠道匹配的会员ID_商品编码
        if(CollUtil.isNotEmpty(param.getSaleChannel())){
           List<String> memberIdConcatGoodsCodeBySaleChannel = memberIntentionBiz.queryMemberIdConcatGoodsCodeBySaleChannel(memberIds,param.getSaleChannel());
           if(CollUtil.isEmpty(memberIdConcatGoodsCodeBySaleChannel)){
               log.info("客户账号报表 accountId:{} memberIds:{} saleChannel:{} 销售渠道未匹配", param.getOperatorBy(),memberIds,param.getSaleChannel());
               return PageInfo.emptyPageInfo();
           }
           query.setMemberIdConcatGoodsCodeBySaleChannel(memberIdConcatGoodsCodeBySaleChannel);
        }

        query.setMemberIds(memberIds);
        query.setAccountIds(CommonUtils.getListValueByDistinctAndFilterBank(memberAccountList, MemberAccountReportSimpleDTO::getAccountId));
        query.setRole(param.getRole());
        query.setOperatorBy(param.getOperatorBy());
        query.setOperatorName(param.getOperatorName());
        query.setOperationTime(param.getOperationTime());
        query.setCategoryCodes(param.getCategoryCodes());
        query.setGoodsName(param.getGoodsName());
        query.setGoodsCodeBySapMaterialCode(param.getGoodsCodeBySapMaterialCode());

        log.info("客户账号报表分页入参 accountId:{} query:{}",param.getOperatorBy(),query);
        PageInfo<AccountReportResultDTO> pageResult = iDataPermissionService.accountReport(query);
        if(Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getList())){
            log.info("客户账号报表 accountId:{} query:{} 账号报表查询未匹配", param.getOperatorBy(),param);
            return PageInfo.emptyPageInfo();
        }

        List<MemberAccountReportResultDTO> resultList = pageResult.getList()
                .stream()
                .map(v -> {
                    MemberAccountReportResultDTO target = new MemberAccountReportResultDTO();
                    target.setAccountId(v.getAccountId());
                    target.setMemberId(v.getMemberId());
                    target.setCategoryIdLevelTwo(v.getCategoryIdLevelTwo());
                    target.setCategoryCodeLevelTwo(v.getCategoryCodeLevelTwo());
                    target.setGoodsCode(v.getGoodsCode());
                    target.setGoodsName(v.getGoodsName());
                    target.setRoleCodes(v.getRoleCodes());
                    target.setRoleName(CollUtil.join(v.getRoleNames(),","));
                    return target;
                }).collect(Collectors.toList());

        PageInfo<MemberAccountReportResultDTO> result = new PageInfo<>();
        result.setTotal(pageResult.getTotal());
        result.setPageNum(pageResult.getPageNum());
        result.setPageSize(pageResult.getPageSize());
        result.setList(resultList);

        return result;
    }


    private MemberBusinessInfoDTO getBusinessInfoByRequestId(String memberApprovalRequestId) {
        MemberBusinessInfoDTO infoDTO = new MemberBusinessInfoDTO();
        MemberHistory memberHistory = memberHistoryBiz.get(memberApprovalRequestId);
        if (memberHistory != null && !BooleanUtil.isTrue(memberHistory.getDelFlg())) {
            BeanUtils.copyProperties(memberHistory, infoDTO);
        }
        MemberCertHistory memberCertHistory = new MemberCertHistory();
        memberCertHistory.setRequestId(memberApprovalRequestId);
        List<MemberCertHistory> memberCertHistoryList = iMemberCertHistoryBiz.find(memberCertHistory);
        if (memberCertHistoryList != null && !memberCertHistoryList.isEmpty()) {
            for (MemberCertHistory history : memberCertHistoryList) {
                MemberCertDTO memberCertDTO = new MemberCertDTO();
                BeanUtils.copyProperties(history, memberCertDTO);
                memberCertDTO.setNewCert(true);
                iMemberCertBiz.setMemberCertAttachment(history.getAttachmentId(), memberCertDTO);
                if (checkCertType(history, infoDTO, memberCertDTO)) continue;
                if (history.getCertType().equals(MemberCertTypeEnum.TAX_LICENSE.getCode())) {
                    infoDTO.setTaxCert(memberCertDTO);
                }
            }
        }
        return infoDTO;
    }

    private static boolean checkCertType(MemberCertHistory history, MemberBusinessInfoDTO infoDTO, MemberCertDTO memberCertDTO) {
        if (history.getCertType().equals(MemberCertTypeEnum.BUSINESS_LICENSE.getCode())) {
            infoDTO.setBusinessCert(memberCertDTO);
            return true;
        }
        return false;
    }


    private void saveCertHistoryList(MemberBusinessInfoDTO memberBusinessInfoDTO, boolean needApprove, String operatorId, String requestId) {
        List<MemberCertDTO> memberCertDTOList = new ArrayList<>();
        if (memberBusinessInfoDTO.getBusinessCert() != null) {
            memberBusinessInfoDTO.getBusinessCert().setCertType(MemberCertTypeEnum.BUSINESS_LICENSE.getCode());
            memberCertDTOList.add(memberBusinessInfoDTO.getBusinessCert());
        }
        iMemberCertHistoryBiz.saveCertHistoryList(memberCertDTOList,
                memberBusinessInfoDTO.getMemberId(),
                operatorId,
                requestId,
                operatorId);
        if (!needApprove) {
            iMemberCertBiz.saveCertDTOList(memberCertDTOList, memberBusinessInfoDTO.getMemberId(), operatorId, operatorId);
        }
    }

    private void checkTempRequest(MemberBusinessInfoDTO memberBusinessInfoDTO, boolean needApprove) {
        if (needApprove) {
            MemberApproveRequest tempRequest = iMemberApproveRequestBiz.findLastChangeRequest(memberBusinessInfoDTO.getMemberId(),ApproveRequestTypeEnum.CHANGE_BUSINESS,AdvertStatusEnum.NEW_REQUEST);
            if (tempRequest != null) {
                throw new BizException(MemberCode.MEMBER_NOT_ALLOW_NEW_REQUEST,
                        tempRequest.getRequestNum(), EnumUtil.getMessageByCode(tempRequest.getRequestType(), ApproveRequestTypeEnum.class));
            }
        }
    }

    private void checkCertDtoList(MemberBusinessInfoDTO memberBusinessInfoDTO, List<MemberCertDTO> certDTOList, Member byId) {
        if (CollUtil.isEmpty(certDTOList) && !checkBusinessInfo(memberBusinessInfoDTO, byId)) {
            throw new BizException(BasicCode.INVALID_PARAM, ":经营信息无变化");
        }
    }

    private boolean checkBusinessInfo(MemberBusinessInfoDTO memberBusinessInfoDTO, Member member) {
        //如果有任何一个字段不相同，则视为已修改
        return !compareString(memberBusinessInfoDTO.getRigistAddressDetail(), member.getRigistAddressDetail()) ||
                !compareString(memberBusinessInfoDTO.getCreditCode(), member.getCreditCode()) ||
                !compareString(memberBusinessInfoDTO.getMemberName(), member.getMemberName()) ||
                !compareString(memberBusinessInfoDTO.getMainProducts(), member.getMainProducts());

    }

    private void checkDelFlagForById(MemberBusinessInfoDTO memberBusinessInfoDTO, Member byId) {
        if (byId == null || BooleanUtil.isTrue(byId.getDelFlg())) {
            throw new BizException(MemberCode.MEMBER_NOT_EXIST, memberBusinessInfoDTO.getMemberId());
        }
    }

    private List<MemberCertDTO> getMemberCertDTOS(MemberBusinessInfoDTO memberBusinessInfoDTO) {
        String temp;
        List<MemberCertDTO> certDTOList = Lists.newArrayList();
        certDTOList.add(memberBusinessInfoDTO.getBusinessCert());
        if (memberBusinessInfoDTO.getIsSyncretic() == 1) {
            temp = memberBusinessInfoDTO.getCreditCode();
        } else {
            temp = memberBusinessInfoDTO.getBusinessLicenseCode();
        }
        if (StringUtils.isEmpty(temp)) {
            throw new BizException(BasicCode.PARAM_NULL, "营业执照号码");
        }

        if (StringUtils.isBlank(memberBusinessInfoDTO.getBusinessCert().getAttachmentId())) {
            throw new BizException(BasicCode.PARAM_NULL, "附件id");
        }

        if (checkCreditCode(temp, memberBusinessInfoDTO.getMemberId())) {
            throw new BizException(MemberCode.CREDIT_CODE_IS_EXIST, temp);
        }
        // 公司名是否重复
        if (checkMemberNameUsed(memberBusinessInfoDTO.getMemberId(), memberBusinessInfoDTO.getMemberName())) {
            throw new BizException(MemberCode.MEMBER_NAME_IS_EXIST, memberBusinessInfoDTO.getMemberName());
        }
        return certDTOList;
    }

    private void checkUpdateBusinessInfo(MemberBusinessInfoDTO memberBusinessInfoDTO) {
        if (!checkMemberIsEnterprise(memberBusinessInfoDTO.getMemberId())) {
            throw new BizException(MemberCode.MEMBER_IS_NOT_ENTERPRISE);
        }
        if (memberBusinessInfoDTO.getBusinessCert() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "营业执照");
        }
    }

    /**
     * 会员是否是企业
     *
     * @param memberId
     * @return
     */
    private boolean checkMemberIsEnterprise(String memberId) {
        Member member = findById(memberId);
        if (member != null && member.getMemberType() != null) {
            return MemberTypeEnum.ENTERPRISE_BUYER.getCode().equals(member.getMemberType())
                    || MemberTypeEnum.ENTERPRISE_SELLER.getCode().equals(member.getMemberType());
        }
        throw new BizException(MemberCode.MEMBER_NOT_EXIST, memberId);
    }

    private void setMemberCertDTO(List<MemberCertDTO> memberCertDtoList, Set<String> certNames) {
        for (MemberCertDTO memberCertDTO : memberCertDtoList) {
            checkException(memberCertDTO);
            if (StringUtils.isNotEmpty(memberCertDTO.getCertName())) {
                certNames.add(memberCertDTO.getCertName());
            }
        }
    }

    private void checkException(MemberCertDTO memberCertDTO) {
        if (memberCertDTO == null) {
            throw new MemberBizException(BasicCode.INVALID_PARAM, ":资质不可为空");
        }
        if (StringUtils.isBlank(memberCertDTO.getCertName())) {
            throw new MemberBizException(BasicCode.INVALID_PARAM, ":资质名称不可为空");
        }
        // 证件是否过期
        if (memberCertDTO.getEffectiveTime() != null &&
                memberCertDTO.getEffectiveTime().getTime() < System.currentTimeMillis()) {
            throw new BizException(MemberCode.CERT_IS_OVER_TIME, memberCertDTO.getCertName());
        }
    }


    private Condition findConditionByQuery(MemberQueryDTO query) {
        Condition condition = new Condition(Member.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        condition.orderBy(DuplicateString.UPDATE_TIME).desc();

        if (query.getShowEnterprise() != null && query.getShowEnterprise() &&
                query.getShowPerson() != null && query.getShowPerson()) {
            //do nothing
        } else if (query.getShowEnterprise() != null && query.getShowEnterprise()) {
            if (StringUtils.isNotBlank(query.getMemberType()) && MemberDTO.ENTERPRISE_TYPE_PRE.equals(query.getMemberType())) {
                criteria.andLike(DuplicateString.MEMBER_TYPE, MemberDTO.ENTERPRISE_TYPE_PRE + "%");
            } else {
                criteria.andEqualTo(DuplicateString.MEMBER_TYPE, query.getMemberType());
            }
        } else if (query.getShowPerson() != null && query.getShowPerson()) {
            if (StringUtils.isNotBlank(query.getMemberType()) && MemberDTO.PERSON_TYPE_PRE.equals(query.getMemberType())) {
                criteria.andLike(DuplicateString.MEMBER_TYPE, MemberDTO.PERSON_TYPE_PRE + "%");
            } else {
                criteria.andEqualTo(DuplicateString.MEMBER_TYPE, query.getMemberType());
            }
        }
        if (query.getSellerFlg() != null) {
            criteria.andEqualTo(DuplicateString.SELLER_FLG, query.getSellerFlg());
        }
        findConditionByQuery1(query, criteria);
        return condition;
    }

    private void findConditionByQuery1(MemberQueryDTO query, Example.Criteria criteria) {
        if (StringUtils.isNotBlank(query.getMainAccountId())) {
            criteria.andEqualTo("mainAccountId", query.getMainAccountId().trim());
        }
        if (StringUtils.isNotBlank(query.getMemberName())) {
            criteria.andLike(DuplicateString.MEMBER_NAME, "%" + query.getMemberName().trim() + "%");
        }
        if (StringUtils.isNotBlank(query.getMemberCode())) {
            criteria.andEqualTo(DuplicateString.MEMBER_CODE, query.getMemberCode().trim());
        }

        if (StringUtils.isNotBlank(query.getStatus())) {
            criteria.andEqualTo(DuplicateString.STATUS, query.getStatus().trim());
        }

        if (StringUtils.isNotBlank(query.getContactName())) {
            criteria.andLike("contactName", "%" + query.getContactName().trim() + "%");
        }
        if (StringUtils.isNotBlank(query.getContactPhone())) {
            criteria.andEqualTo("contactPhone", query.getContactPhone().trim());
        }
        if (StringUtils.isNotBlank(query.getCityCode())) {
            criteria.andEqualTo("cityCode", query.getCityCode().trim());
        }
    }


    private Page<Member> pageMember(MemberQueryDTO query, PageInfo<Member> pageInfo) {
        Condition condition = findConditionByQuery(query);
        return page(condition, pageInfo);
    }


    private String getContactName(MemberRequestDTO memberRequestDTO, String contactName, Account account) {
        for (MemberCertDTO dto : memberRequestDTO.getMemberCertDTOList()) {
            if (StringUtils.isEmpty(dto.getCertType())) {
                throw new MemberBizException(MemberCode.UNKNOWN_CERTIFICATES);
            }
            if (dto.getCertType().equals(MemberCertTypeEnum.IDENTITY_LICENSE.getCode())) {
                contactName = dto.getRealName();
                if (account != null) {
                    // operatorId
                    Account a = accountBiz.get(memberRequestDTO.getAccountId());
                    if(CharSequenceUtil.isNotBlank(dto.getRealName()))
                        a.setRealName(dto.getRealName());
                    accountBiz.updateSelective(a);
                    accountSessionBiz.updateSessionLoginInfoAccountInfo(a);
                }
                break;
            }
        }
        return contactName;
    }

    private void checkUpdateException(MemberRequestDTO memberRequestDTO, String temp) {
        if (StringUtils.isEmpty(temp)) {
            throw new MemberBizException(BasicCode.PARAM_NULL, temp);
        }
        if (checkCreditCode(temp, memberRequestDTO.getMemberId())) {
            throw new MemberBizException(MemberCode.CREDIT_CODE_IS_EXIST, temp);
        }
        // 公司名是否重复
        if (checkMemberNameUsed(memberRequestDTO.getMemberId(), memberRequestDTO.getMemberName())) {
            throw new MemberBizException(MemberCode.MEMBER_NAME_IS_EXIST, memberRequestDTO.getMemberName());
        }
    }

    private String insertSelective(boolean firstRegister, Member m, Account account, String memberId, String operatorId, MemberHistory memberHistory, String businessRequestId) {
        if (firstRegister) {
            MemberApproveRequest businessRequest = new MemberApproveRequest();
            businessRequest.setRequestType(ApproveRequestTypeEnum.CHANGE_BUSINESS.getCode());
            businessRequest.setChangeMessage("注册同步经营信息");
            businessRequest.setRequestMemberId(m.getMemberId());
            businessRequest.setRequestTime(new Date());
            businessRequest.setMemberType(m.getMemberType());
            businessRequest.setMemberName(m.getMemberName());
            businessRequest.setContactName(Objects.nonNull(account) ? account.getRealName() : m.getContactName());
            businessRequest.setContactPhone(Objects.nonNull(account) ? account.getMobile() : m.getContactPhone());
            businessRequest.setMemberId(memberId);
            businessRequest.setStatus(AdvertStatusEnum.CLOSED.getCode());
            setOperInfo(businessRequest, operatorId, true);
            iMemberApproveRequestBiz.save(businessRequest, operatorId);
            memberHistory.setRequestId(businessRequest.getRequestId());
            log.info("memberHistory的数据是=============================>2{}", memberHistory);
            memberHistoryBiz.insertSelective(memberHistory);
            businessRequestId = businessRequest.getRequestId();
        }
        return businessRequestId;
    }

    private String updateHistory(List<MemberCertHistory> certHistoryList, String businessRequestId, String operatorId, String mainAccountId, Account account, MemberApproveRequest request, String memberId) {
        if (certHistoryList != null && !certHistoryList.isEmpty()) {
            for (MemberCertHistory history : certHistoryList) {
                signDeleteSameCertType(history, businessRequestId, operatorId);
            }
        }
        return mainAccountId;
    }

    private void signDeleteSameCertType(MemberCertHistory history, String businessRequestId, String operatorId) {
        MemberCert cert = new MemberCert();
        BeanUtils.copyProperties(history, cert);
        cert.setStatus(AdvertStatusEnum.APPROVED.getCode());
        if (CharSequenceUtil.isBlank(cert.getCertType())) {
            log.error("证件类型不可为空,history.certId:{}", history.getCertId());
            throw new BizException(BasicCode.CUSTOM_ERROR, "证件类型不可为空");
        }
        // 删除同类型的证件并插入新数据  其它资质不删
        if (!StringUtils.equals(cert.getCertType(), MemberCertTypeEnum.OTHER_LICENSE.getCode()) &&
                !StringUtils.equals(cert.getCertType(), MemberCertTypeEnum.SHIP_OWNERSHIP_CERTIFICATE.getCode())) {
            iMemberCertBiz.signDeleteSameCertType(cert, cert.getMemberId());
        }
        if (StringUtils.isBlank(cert.getCertId()) || iMemberCertBiz.get(cert.getCertId()) == null) {
            if (StringUtils.isBlank(cert.getCertId())) {
                cert.setCertId(uuidGenerator.gain());
            }
            iMemberCertBiz.insert(cert);
        } else {
            iMemberCertBiz.updateSelective(cert);
        }
        // 注册同步经营资质信息
        if (StringUtils.isNotBlank(businessRequestId) &&
                !history.getCertType().equals(MemberCertTypeEnum.IDENTITY_LICENSE.getCode())) {
            history.setRequestId(businessRequestId);
            history.setCertId(uuidGenerator.gain());
            iMemberCertHistoryBiz.save(history, operatorId);
        }
    }


    private void sendApproveSuccess(String memberId, MemberApproveRequest request) {
        Member member = this.findById(memberId);
        if(Objects.isNull(member))
            return;
        // 企业资质变更审批流程 平台运营人员通过审批 发送短信  通知客户管理员
        if(CharSequenceUtil.equals(request.getRequestType(),ApproveRequestTypeEnum.CHANGE_NORMAL_CERT.getCode())){
            List<AccountSimpleDTO> adminAccountList = this.listAccountsByMemberCodes(new QueryMemberAccountDTO(Arrays.asList(member.getMemberCode()),Arrays.asList(BaseRoleTypeEnum.BUYER_ADMIN.getRoleCode())));
            if(CollUtil.isNotEmpty(adminAccountList)) {
                adminAccountList.forEach(v->{
                    List<String> param = Arrays.asList(v.getRealName());
                    List<String> contactPhones = Arrays.asList(v.getMobile());
                    SmsDTO sms = new SmsDTO(SmsTemplateEnum.ENTERPRISE_QUALIFICATION_CHANGE_APPROVED_CODE.getCode(), contactPhones, param);
                    iSmsSendService.sendSms(sms);
                });
            }
        }
    }

    private void updateSessionLoginInfoMemberName(MemberApproveRequest request, MemberHistory memberHistory, Member member, String memberId) {
        if (request.getRequestType().equals(ApproveRequestTypeEnum.CHANGE_BUSINESS.getCode())) {
            if ((StringUtils.isNotBlank(memberHistory.getMemberShortName()) && !StringUtils.equals(memberHistory.getMemberShortName(), member.getMemberShortName())) ||
                    (StringUtils.isNotBlank(memberHistory.getMemberName()) && !StringUtils.equals(memberHistory.getMemberName(), member.getMemberName()))) {
                // 更新账号信息
                accountBiz.updateMemberName(memberId, memberHistory.getMemberName(), memberHistory.getMemberShortName());
                accountSessionBiz.updateSessionLoginInfoMemberName(memberHistory.getMemberId(), memberHistory.getMemberName(), memberHistory.getMemberShortName());
            }
        }
    }

    private void setApproveRequestType(boolean typeChange, MemberApproveRequest request, Member member, String operatorId, MemberHistory memberHistory) {
        if (typeChange) {
            // 设置主账号权限
            ApproveRequestTypeEnum enumType = EnumUtil.getEnumByCode(request.getRequestType(), ApproveRequestTypeEnum.class);
            assert enumType != null;
            log.info("enumType:{}", enumType);
            switch (enumType) {
                case REGISTER_ENTERPRISE_BUYER: {
                    log.info("==========================1{}", member);
                    iRoleService.setEnterpriseBuyer2Account(member.getMemberId(), member.getMainAccountId(), operatorId);
                    break;
                }
                case REGISTER_ENTERPRISE_SELLER: {
                    log.info("==========================2{}", member);
                    iRoleService.setEnterpriseSeller2Account(member.getMemberId(), member.getMainAccountId(), operatorId);
                    break;
                }
                default:
                    break;
            }
        }
    }


    private Account updateSelective(boolean firstRegister, Member member, MemberHistory memberHistory, Account account, String operatorId) {
        if (firstRegister) {
            //申请人
            member.setCreateUser(memberHistory.getCreateUser());
            if (account == null) {
                account = accountBiz.findById(member.getMainAccountId());
            }
            // 更新账号信息
            if(Objects.nonNull(account)) {
                // 更新账号信息
                account.setMemberName(memberHistory.getMemberName());
                account.setMemberShortName(memberHistory.getMemberShortName());
                account.setAccountType(AccountTypeEnum.ENTERPRISE_MASTER_ACCOUNT.getAccountType());

                account.handleUser(operatorId, false);
                accountBiz.updateSelective(account);
            }

            // 设置组织机构根节点
            member.setMemberName(memberHistory.getMemberName());
        }
        return account;
    }


    private void sendApproveReject(String approveRequestId, String message, String memberId, MemberApproveRequest request, String operatorId, Account account) {
        // 更新资质历史表状态
        Condition condition = new Condition(MemberCertHistory.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.REQUEST_ID, approveRequestId)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        MemberCertHistory certHistory = new MemberCertHistory();
        certHistory.setStatus(AdvertStatusEnum.REJECT.getCode());
        iMemberCertHistoryBiz.updateByConditionSelective(certHistory, condition);
        // 更新会员状态
        Member member = new Member();
        member.setMemberId(memberId);
        member.setStatus(MemberStatusEnum.NORMAL.getCode());
        updateSelective(member, false);
        // 更新会员历史表状态
        MemberHistory history = memberHistoryBiz.get(approveRequestId);
        if (history != null) {
            MemberHistory memberHistory = new MemberHistory();
            memberHistory.setRequestId(approveRequestId);
            memberHistory.setStatus(MemberStatusEnum.NORMAL.getCode());
            memberHistoryBiz.updateSelective(memberHistory);
        }

        if(Objects.isNull(account)){
            log.info("审批拒绝 requestId:{} 主账号不存在",approveRequestId);
            return;
        }

        // 申请企业入驻审核流程 平台运营人员驳回审批 发送短信
        if (StringUtils.equals(request.getRequestType(), ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER.getCode())) {
            List<String> param = Arrays.asList(account.getRealName(),message);
            List<String> contactPhones = Arrays.asList(account.getMobile());
            SmsDTO sms = new SmsDTO(SmsTemplateEnum.ENTERPRISE_SETTLEMENT_REJECTED_CODE.getCode(), contactPhones, param);
            iSmsSendService.sendSms(sms);
            return;
        }

        // 企业意向购买产品变更审批流程（企业产品跨二级分类变更） 平台运营人员驳回审批 发送短信
        if (StringUtils.equals(request.getRequestType(), ApproveRequestTypeEnum.CHANGE_GOODS_INTENTION.getCode())) {
            List<String> param = Arrays.asList(account.getRealName(),message);
            List<String> contactPhones = Arrays.asList(account.getMobile());
            SmsDTO sms = new SmsDTO(SmsTemplateEnum.ENTERPRISE_PRODUCT_CHANGE_REJECTED_CODE.getCode(), contactPhones, param);
            iSmsSendService.sendSms(sms);
            return;
        }

        // 企业资质变更审批流程 平台运营人员驳回审批 发送短信
        if (StringUtils.equals(request.getRequestType(), ApproveRequestTypeEnum.CHANGE_BUSINESS.getCode())
            || StringUtils.equals(request.getRequestType(), ApproveRequestTypeEnum.CHANGE_NORMAL_CERT.getCode())
            || StringUtils.equals(request.getRequestType(), ApproveRequestTypeEnum.CHANGE_NORMAL_INFO.getCode())) {
            List<String> param = Arrays.asList(account.getRealName(),message);
            List<String> contactPhones = Arrays.asList(account.getMobile());
            SmsDTO sms = new SmsDTO(SmsTemplateEnum.ENTERPRISE_QUALIFICATION_CHANGE_REJECTED_CODE.getCode(), contactPhones, param);
            iSmsSendService.sendSms(sms);
        }


    }


    private void checkMemberExistException(String memberId) {
        if (!checkMemberExist(memberId)) {
            throw new BizException(MemberCode.MEMBER_NOT_EXIST);
        }
    }

    private boolean checkMemberExist(String memberId) {
        Member m = new Member();
        m.setMemberId(memberId);
        m.setDelFlg(Boolean.FALSE);
        int count = mapper.selectCount(m);
        return count > 0;
    }


    private void updateApproveMemberRequest(String approveRequestId, String message, boolean approve, String operatorId) {
        if (approve) {
            // 审批通过
            iMemberApproveRequestBiz.approveMemberRequest(approveRequestId, message, operatorId);
        } else {
            // 审批拒绝
            iMemberApproveRequestBiz.rejectMemberRequest(approveRequestId, message, operatorId);
        }
    }

    private void checkMemberApproveRequest(String approveRequestId, Operator operator, String appName, MemberApproveRequest request) {
        // 请求是否存在
        if (BooleanUtil.isTrue(request.getDelFlg())) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, approveRequestId);
        }
        if (StringUtils.equals(request.getMemberId(), operator.getMemberId()) && StringUtils.isNotBlank(appName) && !AppNames.WEB_SERVICE_PLATFORM.getPlatform().equals(appName)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "不能审批自己的申请");
        }
        // 是否是待审批状态
        if (!AdvertStatusEnum.NEW_REQUEST.getCode().equals(request.getStatus())) {
            throw new BizException(MemberCode.APPROVE_REQUEST_STATUS_ERROR, approveRequestId);
        }
    }

    private void updateSelectiveForApprove(MemberRequestDTO memberRequestDTO, boolean needApprove, String operatorId) {
        Member member = new Member();
        if (!needApprove) {
            //会员
            BeanUtils.copyProperties(memberRequestDTO, member);
        } else {
            member.setStatus(MemberStatusEnum.AUTH_ING.getCode());
        }
        member.setMemberId(memberRequestDTO.getMemberId());
        setOperInfo(member, operatorId, false);
        updateSelective(member, false);
    }

    private void saveCertHistoryListForApprove(MemberRequestDTO memberRequestDTO, boolean needApprove, String operatorId, String requestId) {
        List<MemberCertDTO> memberCertDTOList;
        memberCertDTOList = filterUnChangeCert(memberRequestDTO.getMemberCertDTOList(), memberRequestDTO.getMemberId());
        if (memberCertDTOList != null && !memberCertDTOList.isEmpty()) {
            /** 一般资质 */
            //会员资质变更表
            iMemberCertHistoryBiz.saveCertHistoryList(
                    memberCertDTOList,
                    memberRequestDTO.getMemberId(),
                    memberRequestDTO.getAccountId(),
                    requestId,
                    operatorId);

            if (!needApprove) {
                iMemberCertBiz.saveCertDTOList(
                        memberCertDTOList,
                        memberRequestDTO.getMemberId(),
                        memberRequestDTO.getAccountId(),
                        operatorId);
            }
        }
    }

    private String updateSessionLoginInfoAccountInfo(MemberRequestDTO memberRequestDTO, String contactName, Account account) {
        for (MemberCertDTO dto : memberRequestDTO.getMemberCertDTOList()) {
            if (StringUtils.isEmpty(dto.getCertType())) {
                throw new BizException(MemberCode.UNKNOWN_CERTIFICATES);
            }
            if (dto.getCertType().equals(MemberCertTypeEnum.IDENTITY_LICENSE.getCode())) {
                contactName = dto.getRealName();
                if (account != null) {
                    // operatorId
                    Account a = accountBiz.get(memberRequestDTO.getAccountId());
                    if(CharSequenceUtil.isNotBlank(dto.getRealName()))
                        a.setRealName(dto.getRealName());
                    accountBiz.updateSelective(a);
                    accountSessionBiz.updateSessionLoginInfoAccountInfo(a);
                }
                break;
            }
        }
        return contactName;
    }

    private void updateCertHistoryForSyncretic(MemberRequestDTO memberRequestDTO, boolean needApprove, String operatorId, String requestId) {
        List<MemberCertDTO> memberCertDTOList;
        if (memberRequestDTO.getIsSyncretic().equals(MemberDTO.FLG_TRUE)) {
            memberCertDTOList = filterUnChangeCert(Lists.newArrayList(memberRequestDTO.getBusinessCert()), memberRequestDTO.getMemberId());
            //如果营业执照有修改
            if (memberCertDTOList != null && !memberCertDTOList.isEmpty()) {
                updateCertHistory(memberRequestDTO, needApprove, operatorId, requestId);
            }
        } else {
            saveCertHistoryList(memberRequestDTO, needApprove, operatorId, requestId);
        }
    }

    private void updateCertHistory(MemberRequestDTO memberRequestDTO, boolean needApprove, String operatorId, String requestId) {
        if (memberRequestDTO.getBusinessCert() != null) {
            MemberCertHistory certHistory = new MemberCertHistory();
            BeanUtils.copyProperties(memberRequestDTO.getBusinessCert(), certHistory);
            certHistory.setRequestId(requestId);
            certHistory.setMemberId(memberRequestDTO.getMemberId());
            certHistory.setCertId(uuidGenerator.gain());
            certHistory.setStatus(AdvertStatusEnum.NEW_REQUEST.getCode());
            certHistory.setCertType(MemberCertTypeEnum.BUSINESS_LICENSE.getCode());//资质类型
            certHistory.setDelFlg(Boolean.FALSE);
            iMemberCertHistoryBiz.save(certHistory, operatorId);
            if (!needApprove) {
                MemberCert cert = new MemberCert();
                BeanUtils.copyProperties(certHistory, cert);
                cert.setStatus(AdvertStatusEnum.APPROVED.getCode());
                cert.setCertType(MemberCertTypeEnum.BUSINESS_LICENSE.getCode());
                cert.setMemberId(memberRequestDTO.getMemberId());
                setOperInfo(cert, operatorId, true);
                iMemberCertBiz.insert(cert);
            }
        }
        if (memberRequestDTO.getIsSyncretic().equals(MemberDTO.FLG_TRUE) && StringUtils.isNotBlank(memberRequestDTO.getCreditCode())) {
            String code = memberRequestDTO.getCreditCode();
            memberRequestDTO.setOrganizationCode(code);
            memberRequestDTO.setBusinessLicenseCode(code);
            memberRequestDTO.setTaxCode(code);
        }
    }

    private List<MemberCertDTO> filterUnChangeCert(List<MemberCertDTO> list, String memberId) {
        List<MemberCertDTO> old = iMemberCertBiz.findByMemberId(memberId);
        if (CollectionUtils.isEmpty(old)) {
            return list;
        }
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        List<MemberCertDTO> result = Lists.newArrayList();
        for (MemberCertDTO memberCertDTO : list) {
            if (StringUtils.isBlank(memberCertDTO.getCertId())) {
                log.info("certId is null，cert Name: {}", memberCertDTO.getCertName());
                result.add(memberCertDTO);
                continue;
            }
            if (isChange(old, memberCertDTO)) {
                result.add(memberCertDTO);
            }
        }
        return result;
    }

    private void saveCertHistoryList(MemberRequestDTO memberRequestDTO, boolean needApprove, String operatorId, String requestId) {
        List<MemberCertDTO> memberCertDTOList;
        memberCertDTOList = Lists.newArrayList();
        if (memberRequestDTO.getBusinessCert() != null) {
            memberRequestDTO.getBusinessCert().setCertType(MemberCertTypeEnum.BUSINESS_LICENSE.getCode());
            memberCertDTOList.add(memberRequestDTO.getBusinessCert());
        }
        if (memberRequestDTO.getOrganizationCert() != null) {
            memberRequestDTO.getOrganizationCert().setCertType(MemberCertTypeEnum.ORGANIZATION_LICENSE.getCode());
            memberCertDTOList.add(memberRequestDTO.getOrganizationCert());
        }
        if (memberRequestDTO.getTaxCert() != null) {
            memberRequestDTO.getTaxCert().setCertType(MemberCertTypeEnum.TAX_LICENSE.getCode());
            memberCertDTOList.add(memberRequestDTO.getTaxCert());
        }

        memberCertDTOList = filterUnChangeCert(memberCertDTOList, memberRequestDTO.getMemberId());
        if (memberCertDTOList != null && !memberCertDTOList.isEmpty()) {
            iMemberCertHistoryBiz.saveCertHistoryList(
                    memberCertDTOList,
                    memberRequestDTO.getMemberId(),
                    memberRequestDTO.getAccountId(),
                    requestId,
                    operatorId);

            if (!needApprove) {
                iMemberCertBiz.saveCertDTOList(
                        memberCertDTOList,
                        memberRequestDTO.getMemberId(),
                        memberRequestDTO.getAccountId(),
                        operatorId);
            }
        }
    }

    private boolean isChange(List<MemberCertDTO> old, MemberCertDTO memberCertDTO) {
        boolean change = true;
        for (MemberCertDTO certDTO : old) {
            //如果资质id相等,判断有效属性，看看是否发生变化
            if (StringUtils.equals(memberCertDTO.getCertId(), certDTO.getCertId())) {
                //如果附件id不同,则去掉前后逗号，再次校验
                //如果资质名称不同
                //如果资质类型不同
                //如果资质状态不同
                //如果资证件号码不同
                //如果资联系人名称不同
                //如果资联系人电话不同
                if (noChange(memberCertDTO, certDTO)) continue;

                change = false;
                return change;
            }
        }
        return change;
    }

    private boolean noChange(MemberCertDTO memberCertDTO, MemberCertDTO certDTO) {
        if (noChange1(memberCertDTO, certDTO)) return true;
        return noChange2(memberCertDTO, certDTO);
    }

    private boolean noChange2(MemberCertDTO memberCertDTO, MemberCertDTO certDTO) {
        if (!compareString(memberCertDTO.getContactPhone(), certDTO.getContactPhone())) {
            log.info("certId:{} 找到不同点ContactPhone", certDTO.getCertId());
            return true;
        }
        if (!compareDate(memberCertDTO.getEffectiveStartTime(), certDTO.getEffectiveStartTime())) {
            log.info("certId:{} 有效开始时间不同", certDTO.getCertId());
            return true;
        }
        if (!compareDate(memberCertDTO.getEffectiveTime(), certDTO.getEffectiveTime())) {
            log.info("certId:{} 有效结束时间不同", certDTO.getCertId());
            return true;
        }
        if (!compareString(memberCertDTO.getRoadLicenseNo(), certDTO.getRoadLicenseNo())) {
            log.info("certId:{} 道路运输经营许可证号码不同", certDTO.getCertId());
            return true;
        }
        if (!compareString(memberCertDTO.getRoadAddress(), certDTO.getRoadAddress())) {
            log.info("certId:{} 道路运输经营许可证注册地址不同", certDTO.getCertId());
            return true;
        }
        if (!compareString(memberCertDTO.getRoadBusinessScope(), certDTO.getRoadBusinessScope())) {
            log.info("certId:{} 道路运输经营许可证经营范围不同", certDTO.getCertId());
            return true;
        }
        if (!compareString(memberCertDTO.getRealName(), certDTO.getRealName())) {
            log.info("certId:{} realName不同", certDTO.getCertId());
            return true;
        }
        return false;
    }

    private boolean noChange1(MemberCertDTO memberCertDTO, MemberCertDTO certDTO) {
        if (!compareString(memberCertDTO.getAttachmentId(), certDTO.getAttachmentId())) {
            String s1 = memberCertDTO.getAttachmentId() == null ? "" : memberCertDTO.getAttachmentId();
            String s2 = certDTO.getAttachmentId() == null ? "" : certDTO.getAttachmentId();
            s1 = s1.replace(",", "");
            s2 = s2.replace(",", "");
            if (!compareString(s1, s2)) {
                log.info("certId:{} 附件id不同", certDTO.getCertId());
                return true;
            }
        }
        if (!compareString(memberCertDTO.getCertName(), certDTO.getCertName())) {
            log.info("certId:{} 资质名称不同", certDTO.getCertId());
            return true;
        }
        if (!compareString(memberCertDTO.getCertType(), certDTO.getCertType())) {
            log.info("certId:{} 资质类型不同", certDTO.getCertId());
            return true;
        }
        if (!compareString(memberCertDTO.getIdNumber(), certDTO.getIdNumber())) {
            log.info("certId:{} 身份证号", certDTO.getCertId());
            return true;
        }
        if (!compareString(memberCertDTO.getContactName(), certDTO.getContactName())) {
            log.info("certId:{} 找到不同点：ContactName", certDTO.getCertId());
            return true;
        }
        return false;
    }


    private void setBusinessStatus(MemberApproveRequest request, MemberDTO memberDTO, Member member, String requestStatus, String requestType) {
        if (AdvertStatusEnum.REJECT.getCode().equals(request.getStatus())) {
            memberDTO.setBusinessRequestId(request.getRequestId());
        }
        if (request.getStatus().equals(AdvertStatusEnum.NEW_REQUEST.getCode())) {
            // 0-未拥有 1-已拥有 2-待审批 3-已拒绝
            String t = AdvertStatusEnum.APPROVED.getCode().equals(requestStatus) ? "100" : requestStatus;
            int temp = 0;
            if (StringUtils.isNotBlank(t)) {
                temp = Integer.parseInt(t.trim().substring(0, 1));
            }
            if (ApproveRequestTypeEnum.REGISTER_ENTERPRISE_SELLER.getCode().equals(requestType)) {
                memberDTO.setSellerFlg(temp);
            }
        }
    }

    private void setMemberCertAttachment(List<MemberCertHistory> certHistories, String requestStatus, List<MemberCertDTO> memberCertDTOList) {
        if (!CollectionUtils.isEmpty(certHistories)) {
            for (MemberCertHistory ch : certHistories) {
                MemberCertDTO memberCertDTO = new MemberCertDTO();
                BeanUtils.copyProperties(ch, memberCertDTO);
                memberCertDTO.setStatus(requestStatus);
                String certType = ch.getCertType();
                if (MemberCertTypeEnum.BUSINESS_LICENSE.getCode().equals(certType) ||
                        MemberCertTypeEnum.ORGANIZATION_LICENSE.getCode().equals(certType) ||
                        MemberCertTypeEnum.TAX_LICENSE.getCode().equals(certType)) {
                    continue;
                }
                memberCertDTOList.add(memberCertDTO);
                iMemberCertBiz.setMemberCertAttachment(ch.getAttachmentId(), memberCertDTO);
            }
        }
    }

    private void setHistory(String requestStatus, String requestType, String requestId, MemberDTO memberDTO, Member member, List<MemberCertHistory> certHistories) {
        if (AdvertStatusEnum.NEW_REQUEST.getCode().equals(requestStatus)
                && ApproveRequestTypeEnum.CHANGE_BUSINESS.getCode().equals(requestType)) {
            //历史表
            MemberHistory history = memberHistoryBiz.get(requestId);
            if (history != null && !BooleanUtil.isTrue(history.getDelFlg())) {
                MemberBusinessInfoDTO businessInfoDTO = new MemberBusinessInfoDTO();
                BeanUtils.copyProperties(history, businessInfoDTO);
                BeanUtils.copyProperties(businessInfoDTO, memberDTO);
                memberDTO.setMemberCode(member.getMemberCode());
                memberDTO.setMemberShortName(member.getMemberShortName());
                memberDTO.setTrademarkUrl(member.getTrademarkUrl());
            }

            MemberDTO dto = setCertToMemberDTO(certHistories, requestStatus);
            if (dto.getBusinessCert() != null) {
                memberDTO.setBusinessCert(dto.getBusinessCert());
            }
            if (dto.getOrganizationCert() != null) {
                memberDTO.setOrganizationCert(dto.getOrganizationCert());
            }
            if (dto.getTaxCert() != null) {
                memberDTO.setTaxCert(dto.getTaxCert());
            }
        }
    }

    private MemberDTO setCertToMemberDTO(List<MemberCertHistory> certHistories, String requestStatus) {
        MemberDTO memberDTO = new MemberDTO();
        if (!CollectionUtils.isEmpty(certHistories)) {
            for (MemberCertHistory ch : certHistories) {
                MemberCertDTO memberCertDTO = new MemberCertDTO();
                BeanUtils.copyProperties(ch, memberCertDTO);
                String certType = ch.getCertType();
                memberCertDTO.setStatus(requestStatus);
                if (setMemberCertAttachment(ch, certType, memberDTO, memberCertDTO)) continue;
                if (MemberCertTypeEnum.TAX_LICENSE.getCode().equals(certType)) {
                    memberDTO.setTaxCert(memberCertDTO);
                    iMemberCertBiz.setMemberCertAttachment(ch.getAttachmentId(), memberCertDTO);
                }
            }
        }
        return memberDTO;
    }

    private boolean setMemberCertAttachment(MemberCertHistory ch, String certType, MemberDTO memberDTO, MemberCertDTO memberCertDTO) {
        if (MemberCertTypeEnum.BUSINESS_LICENSE.getCode().equals(certType)) {
            memberDTO.setBusinessCert(memberCertDTO);
            iMemberCertBiz.setMemberCertAttachment(ch.getAttachmentId(), memberCertDTO);
            return true;
        }
        if (MemberCertTypeEnum.ORGANIZATION_LICENSE.getCode().equals(certType)) {
            memberDTO.setOrganizationCert(memberCertDTO);
            iMemberCertBiz.setMemberCertAttachment(ch.getAttachmentId(), memberCertDTO);
            return true;
        }
        return false;
    }


    private void setMemberExtInfo(MemberDTO memberDTO, Member member) {
        if (memberDTO == null) {
            return;
        }
        memberDTO.setProvinceList(Lists.newArrayList());
        memberDTO.setTenantList(Lists.newArrayList());
        memberDTO.setGoodsCategoryList(Lists.newArrayList());

        this.handleProvinceAndGoodsCategoryList(memberDTO, member.getProvince(), member.getGoodsCategory());
    }

    private List<MemberCertHistory> getMemberCertHistories(String requestStatus, String requestId) {
        List<MemberCertHistory> certHistories = null;
        if (AdvertStatusEnum.NEW_REQUEST.getCode().equals(requestStatus)) {
            MemberCertHistory certHistory = new MemberCertHistory();
            certHistory.setRequestId(requestId);
            certHistory.setDelFlg(Boolean.FALSE);
            certHistories = iMemberCertHistoryBiz.find(certHistory);
        }
        return certHistories;
    }

    private List<MemberCertDTO> addCertList(MemberDTO memberDTO) {
        List<MemberCertDTO> memberCertDTOList = new ArrayList<>();
        MemberCert memberCert = new MemberCert();
        memberCert.setMemberId(memberDTO.getMemberId());
        memberCert.setDelFlg(Boolean.FALSE);
        List<MemberCert> certs = iMemberCertBiz.find(memberCert);
        if (CollUtil.isNotEmpty(certs)) {
            for (MemberCert cert : certs) {
                this.setMemberCertDTOList(memberDTO, cert, memberCertDTOList);
            }
        }
        //处理资质附件信息
        iMemberCertBiz.handleMemberCert(memberCertDTOList);

        return memberCertDTOList;
    }

    private void setMemberCertDTOList(MemberDTO memberDTO, MemberCert cert, List<MemberCertDTO> memberCertDTOList) {
        if (cert == null) {
            return;
        }
        //如果资质信息的账号Id不为空且不等于该会员的主账号Id,则不添加
        if (StringUtils.isNotBlank(cert.getAccountId()) && !StringUtils.equals(cert.getAccountId(), memberDTO.getMainAccountId())) {
            return;
        }
        MemberCertDTO memberCertDTO = new MemberCertDTO();
        BeanUtils.copyProperties(cert, memberCertDTO);
        if (cert.getCertType().equals(MemberCertTypeEnum.BUSINESS_LICENSE.getCode())) {
            iMemberCertBiz.setMemberCertAttachment(cert.getAttachmentId(), memberCertDTO);
            memberDTO.setBusinessCert(memberCertDTO);
            return;
        }
        memberCertDTOList.add(memberCertDTO);
    }


    private void setCreateUser(Member member, MemberDTO memberDTO) {
        Account account = accountBiz.findById(member.getCreateUser());
        if (account != null) {
            memberDTO.setCreateUserName(account.getAccountName());
        }
        memberDTO.setCreateUserId(member.getCreateUser());
    }


    private MemberApprovalRequestDTO request2dto(MemberApproveRequest request) {
        MemberApprovalRequestDTO memberApprovalRequestDTO = new MemberApprovalRequestDTO();
        BeanUtils.copyProperties(request, memberApprovalRequestDTO);
        memberApprovalRequestDTO.setStatusText(EnumUtil.getMessageByCode(request.getStatus(), AdvertStatusEnum.class));
        memberApprovalRequestDTO.setMemberType(EnumUtil.getMessageByCode(request.getMemberType(), MemberTypeEnum.class));
        memberApprovalRequestDTO.setRequestTypeText(EnumUtil.getMessageByCode(request.getRequestType(), ApproveRequestTypeEnum.class));
        return memberApprovalRequestDTO;
    }

    private MemberDTO getMemberApprovalDetails1(String memberApprovalRequestId, MemberApproveRequest request, Member member) {
        MemberDTO memberDTO = this.getMemberDTO(memberApprovalRequestId, request);
        if (StringUtils.isBlank(memberDTO.getMemberCode()) && StringUtils.isNotBlank(request.getMemberCode())) {
            memberDTO.setMemberCode(request.getMemberCode());
        }
        this.getMemberCertHistory(memberApprovalRequestId, memberDTO);
        memberDTO.setCreateUserId(member.getCreateUser());
        Account account = accountBiz.findById(member.getCreateUser());
        memberDTO.setCreateUserName(account == null ? null : account.getAccountName());
        memberDTO.setMainAccountName(accountBiz.findById(member.getMainAccountId()).getAccountName());
        memberDTO.setIntentionList(BeanUtil.copyToList(intentionHistoryBiz.listByRequestId(memberApprovalRequestId),MemberPurchaseGoodsIntentionDTO.class));
        return memberDTO;
    }

    private MemberDTO getMemberApprovalDetails2(String memberApprovalRequestId, MemberApproveRequest request, Member member) {
        MemberDTO realMemberById = getMemberDTO(request, member);
        MemberCertHistory memberCertHistory = new MemberCertHistory();
        memberCertHistory.setRequestId(memberApprovalRequestId);
        List<MemberCertHistory> memberCertHistoryList = iMemberCertHistoryBiz.find(memberCertHistory);
        if (realMemberById != null) {
            if (!CollectionUtils.isEmpty(memberCertHistoryList)) {
                List<MemberCertDTO> memberCertTypeDTOList = getMemberCertDTOS(memberCertHistoryList, realMemberById);
                realMemberById.getMemberCertDTOList().addAll(memberCertTypeDTOList);
            }
            realMemberById.setProvinceList(Lists.newArrayList());
            realMemberById.setTenantList(Lists.newArrayList());
            realMemberById.setGoodsCategoryList(Lists.newArrayList());


            MemberHistory memberHistory = memberHistoryBiz.get(request.getRequestId());
            if (Objects.nonNull(memberHistory)) {
                this.handleProvinceAndGoodsCategoryList(realMemberById, memberHistory.getProvince(), memberHistory.getGoodsCategory());
            }

            realMemberById.setCreateUserId(member.getCreateUser());
            Account account = accountBiz.findById(member.getCreateUser());
            realMemberById.setCreateUserName(account == null ? null : account.getAccountName());
            realMemberById.setMainAccountName(accountBiz.findById(member.getMainAccountId()).getAccountName());

            List<MemberPurchaseGoodsIntentionDTO> intentions = BeanUtil.copyToList(intentionHistoryBiz.listByRequestId(memberApprovalRequestId),MemberPurchaseGoodsIntentionDTO.class);
            if(CollUtil.isEmpty(intentions)){
                intentions = BeanUtil.copyToList(memberIntentionBiz.listByMemberId(member.getMemberId()),MemberPurchaseGoodsIntentionDTO.class);
            }
            realMemberById.setIntentionList(intentions);
        }
        return realMemberById;
    }

    private List<MemberCertDTO> getMemberCertDTOS(List<MemberCertHistory> memberCertHistoryList, MemberDTO realMemberById) {
        List<MemberCertDTO> memberCertTypeDTOList = new ArrayList<>();
        for (MemberCertHistory history : memberCertHistoryList) {
            MemberCertDTO memberCertDTO = new MemberCertDTO();
            BeanUtils.copyProperties(history, memberCertDTO);
            iMemberCertBiz.setMemberCertAttachment(history.getAttachmentId(), memberCertDTO);
            memberCertDTO.setStatus(AdvertStatusEnum.NEW_REQUEST.getCode());
            memberCertDTO.setNewCert(true);
            if (checkHistoryCertType(realMemberById, history, memberCertDTO)) continue;
            memberCertTypeDTOList.add(memberCertDTO);
        }
        return memberCertTypeDTOList;
    }

    private MemberDTO getMemberDTO(MemberApproveRequest request, Member member) {
        MemberDTO realMemberById = findRealMemberById(member.getMemberId());
        if (realMemberById != null) {
            realMemberById.setLastTimeRequestId(request.getRequestId());
            realMemberById.setLastTimeRequestType(request.getRequestType());
            realMemberById.setLastTimeRequestRejectReason(request.getApproveText());
            realMemberById.setLastTimeRequestStatus(request.getStatus());
            realMemberById.setLastTimeRequestTime(request.getCreateTime());
            if (StringUtils.isBlank(realMemberById.getMemberCode()) && StringUtils.isNotBlank(request.getMemberCode())) {
                realMemberById.setMemberCode(request.getMemberCode());
            }
        }
        return realMemberById;
    }


    private void getMemberCertHistory(String memberApprovalRequestId, MemberDTO memberDTO) {
        MemberCertHistory memberCertHistory = new MemberCertHistory();
        memberCertHistory.setRequestId(memberApprovalRequestId);
        memberCertHistory.setDelFlg(Boolean.FALSE);
        List<MemberCertDTO> memberCertHistoryList = BeanUtil.copyToList(iMemberCertHistoryBiz.find(memberCertHistory),MemberCertDTO.class);
        if (CollUtil.isNotEmpty(memberCertHistoryList)) {
            //处理附件信息
            iMemberCertBiz.handleMemberCert(memberCertHistoryList);
            //设置营业执照
            memberDTO.setBusinessCert(memberCertHistoryList.stream()
                    .filter(v->CharSequenceUtil.equals(v.getCertType(),MemberCertTypeEnum.BUSINESS_LICENSE.getCode()))
                    .findFirst().orElse(null));
            //设置一般资质
            memberDTO.setMemberCertDTOList(memberCertHistoryList.stream()
                    .filter(v->CharSequenceUtil.equals(v.getCertType(),MemberCertTypeEnum.OTHER_LICENSE.getCode()))
                    .collect(Collectors.toList()));
            //设置身份证信息
            memberDTO.setIdCardInformation(memberCertHistoryList.stream()
                    .filter(v->CharSequenceUtil.equals(v.getCertType(),MemberCertTypeEnum.IDENTITY_LICENSE.getCode()))
                    .findFirst().orElse(null));
        }
    }

    private static boolean checkHistoryCertType(MemberDTO memberDTO, MemberCertHistory history, MemberCertDTO memberCertDTO) {
        if (history.getCertType().equals(MemberCertTypeEnum.BUSINESS_LICENSE.getCode())) {
            memberDTO.setBusinessCert(memberCertDTO);
            return true;
        }
        return false;
    }

    private MemberDTO getMemberDTO(String memberApprovalRequestId, MemberApproveRequest request) {
        MemberDTO memberDTO = new MemberDTO();
        memberDTO.setLastTimeRequestId(request.getRequestId());
        memberDTO.setLastTimeRequestType(request.getRequestType());
        memberDTO.setLastTimeRequestRejectReason(request.getApproveText());
        memberDTO.setLastTimeRequestStatus(request.getStatus());
        memberDTO.setLastTimeRequestTime(request.getCreateTime());

        memberDTO.setProvinceList(Lists.newArrayList());
        memberDTO.setTenantList(Lists.newArrayList());
        memberDTO.setGoodsCategoryList(Lists.newArrayList());
        MemberHistory memberHistory = memberHistoryBiz.getByRequestId(memberApprovalRequestId);
        if (memberHistory != null && !BooleanUtil.isTrue(memberHistory.getDelFlg())) {
            BeanUtils.copyProperties(memberHistory, memberDTO);
            this.handleProvinceAndGoodsCategoryList(memberDTO, memberHistory.getProvince(), memberHistory.getGoodsCategory());
        }

        return memberDTO;
    }


    private void handleProvinceAndGoodsCategoryList(MemberDTO memberDTO, String province, String goodsCategory) {
        if (Objects.isNull(memberDTO))
            return;

        if (CharSequenceUtil.isNotBlank(province)) {
            memberDTO.setProvinceList(
                    CharSequenceUtil.split(province, ",").stream().map(v -> new KeyValueDTO(v, null)).collect(Collectors.toList())
            );
        }
        if (CharSequenceUtil.isNotBlank(goodsCategory)) {
            memberDTO.setGoodsCategoryList(
                    CharSequenceUtil.split(goodsCategory, ",").stream().map(v -> new KeyValueDTO(v, null)).collect(Collectors.toList())
            );
        }
    }

    private void checkApprove(MemberRequestDTO memberRequestDTO, boolean needApprove) {
        if (needApprove) {
            MemberApproveRequest tempRequest = iMemberApproveRequestBiz.hasNoApproveRequest(memberRequestDTO.getMemberId());
            if (tempRequest != null) {
                throw new BizException(MemberCode.MEMBER_NOT_ALLOW_NEW_REQUEST,
                        tempRequest.getRequestNum(), EnumUtil.getMessageByCode(tempRequest.getRequestType(), ApproveRequestTypeEnum.class));
            }
        }
    }

    private void checkMemberForSubmitRegister(MemberRequestDTO memberRequestDTO, Member byId) {
        if (byId == null || BooleanUtil.isTrue(byId.getDelFlg())) {
            throw new BizException(MemberCode.MEMBER_NOT_EXIST, memberRequestDTO.getMemberId());
        }
        // 判断是否是企业买家
        if (MemberTypeEnum.ENTERPRISE_BUYER.getCode().equals(memberRequestDTO.getMemberType()) && byId.getMemberType().startsWith(MemberDTO.ENTERPRISE_TYPE_PRE)) {
            throw new BizException(MemberCode.MEMBER_ALREADY_ENTERPRISE, MemberTypeEnum.ENTERPRISE_BUYER.getMsg());
        }
        // 判断是否是企业卖家
        if (MemberTypeEnum.ENTERPRISE_SELLER.getCode().equals(memberRequestDTO.getMemberType()) && (byId.getSellerFlg() != null && byId.getSellerFlg().intValue() == 1)) {
            throw new BizException(MemberCode.MEMBER_ALREADY_ENTERPRISE, MemberTypeEnum.ENTERPRISE_SELLER.getMsg());
        }
    }

    private void checkTemp(MemberRequestDTO memberRequestDTO) {
        String temp = getTemp(memberRequestDTO);
        if (StringUtils.isEmpty(temp)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "工商执照号码不可为空");
        }
        if (checkCreditCode(temp, memberRequestDTO.getMemberId())) {
            throw new BizException(MemberCode.CREDIT_CODE_IS_EXIST, temp);
        }
        // 公司名是否重复
        if (checkMemberNameUsed(memberRequestDTO.getMemberId(), memberRequestDTO.getMemberName())) {
            throw new BizException(MemberCode.MEMBER_NAME_IS_EXIST, memberRequestDTO.getMemberName());
        }
    }

    private String getTemp(MemberRequestDTO memberRequestDTO) {
        String temp;
        if (memberRequestDTO.getIsSyncretic().equals(MemberDTO.FLG_FALSE)) {
            temp = memberRequestDTO.getBusinessLicenseCode();
        } else {
            temp = memberRequestDTO.getCreditCode();
        }
        return temp;
    }

    private boolean checkCreditCode(String code, String memberId) {
        Member member = new Member();
        member.setCreditCode(code);
        member.setDelFlg(Boolean.FALSE);
        List<Member> select = mapper.select(member);
        for (Member m : select) {
            if (StringUtils.isNotBlank(m.getMemberId()) && !m.getMemberId().equals(memberId)) {
                return true;
            }
        }
        MemberHistory history = new MemberHistory();
        history.setCreditCode(code);
        history.setStatus(MemberStatusEnum.AUTH_ING.getCode());
        List<MemberHistory> select2 = memberHistoryBiz.find(history);
        for (MemberHistory m : select2) {
            if (StringUtils.isNotBlank(m.getMemberId()) && !m.getMemberId().equals(memberId)) {
                return true;
            }
        }
        return false;
    }

    private boolean compareDate(Date d1, Date d2) {
        if (d1 == null && d2 == null) {
            return true;
        }
        String s1 = d1 == null ? "1970-01-01" : new SimpleDateFormat("yyyy-MM-dd").format(d1);
        String s2 = d2 == null ? "1970-01-01" : new SimpleDateFormat("yyyy-MM-dd").format(d2);
        return compareString(s1, s2);
    }

    private boolean compareString(String s1, String s2) {
        if (s1 == null && s2 == null) {
            return true;
        }
        s1 = s1 == null ? "" : s1;
        s2 = s2 == null ? "" : s2;
        return StringUtils.equals(s1, s2);
    }

    private boolean compareBigDecimal(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return true;
        }
        a = a == null ? new BigDecimal(0) : a;
        b = b == null ? new BigDecimal(0) : b;
        return a.compareTo(b) == 0;
    }

    private void saveNormalCert(List<MemberCertHistory> normalCertHistory,String memberId){
        //先删 再增
        MemberCert remove = new MemberCert();
        remove.setDelFlg(Boolean.TRUE);
        Condition removeCondition = new Condition(MemberCert.class);
        removeCondition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(DuplicateString.CERT_TYPE,MemberCertTypeEnum.OTHER_LICENSE.getCode())
                .andEqualTo(DuplicateString.MEMBER_ID,memberId);
        memberCertMapper.updateByConditionSelective(remove,removeCondition);

        if(CollUtil.isEmpty(normalCertHistory))
            return;
        List<MemberCert> normalCerts = normalCertHistory.stream().map(v->{
            MemberCert target = BeanUtil.toBean(v,MemberCert.class);
            target.setStatus(AdvertStatusEnum.APPROVED.getCode());
            return target;
        }).collect(Collectors.toList());

        iMemberCertBiz.batchInsert(normalCerts);
    }

    private void handleLastRequestInfo(String id, MemberDTO memberDTO) {
        //查询最近一次经营信息变更请求数据
        MemberApproveRequest businessRequest = iMemberApproveRequestBiz.findLastChangeRequest(id,ApproveRequestTypeEnum.CHANGE_BUSINESS);
        if(Objects.nonNull(businessRequest)){
            memberDTO.setLastBusinessRequestId(businessRequest.getRequestId());
            memberDTO.setLastBusinessRequestTime(businessRequest.getRequestTime());
            memberDTO.setLastBusinessStatus(businessRequest.getStatus());
            memberDTO.setLastBusinessRejectReason(businessRequest.getApproveText());
        }

        //查询最近一次一般资质变更请求数据
        MemberApproveRequest normalCertRequest = iMemberApproveRequestBiz.findLastChangeRequest(id,ApproveRequestTypeEnum.CHANGE_NORMAL_CERT);
        if(Objects.nonNull(normalCertRequest)){
            memberDTO.setLastNormalCertRequestId(normalCertRequest.getRequestId());
            memberDTO.setLastNormalCertRequestTime(normalCertRequest.getRequestTime());
            memberDTO.setLastNormalCertStatus(normalCertRequest.getStatus());
            memberDTO.setLastNormalCertRejectReason(normalCertRequest.getApproveText());
        }
    }

    private static Date getAprrovalOrCreateTime(MemberApproveRequest request) {
        Date date = request.getApproveTime();
        if(Objects.isNull(date))
            date = request.getCreateTime();
        if(Objects.isNull(date))
            date = new Date();
        return date;
    }
}
