package com.cnoocshell.member;

import com.cnoocshell.common.annotation.ExcludeFromComponetScan;
import com.cnoocshell.kafka.annotation.EnableKafka;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import tk.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;


@SpringBootApplication
@EnableFeignClients({"com.cnoocshell.base.api",
		"com.cnoocshell.goods.api"})
@EnableRedisHttpSession
@EnableAsync
@EnableCaching(proxyTargetClass = true)
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableKafka
@ComponentScan(value = {
		"com.cnoocshell.member",
		"com.cnoocshell.common.exception",
		"com.cnoocshell.common.config",
		"com.cnoocshell.common.service",
		"com.cnoocshell.common.filter",
		"com.cnoocshell.common.aop",
		"com.cnoocshell.common.cache",
		"com.cnoocshell.kafka",
		"com.cnoocshell.common.service.lock"
}, excludeFilters = {
		@ComponentScan.Filter(type = FilterType.ANNOTATION, value = ExcludeFromComponetScan.class)
})
@MapperScan("com.cnoocshell.member.dao.mapper")
@EnableTransactionManagement
@EnableDiscoveryClient
@Slf4j
public class MemberApplication {

	@Bean(name = "memberTransaction")
	public PlatformTransactionManager txManager(DataSource dataSource) {
		return new DataSourceTransactionManager(dataSource);
	}

	@Bean("myTaskAsyncPool")
	public Executor myTaskAsyncPool() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		//核心线程池大小
		executor.setCorePoolSize(20);
		//最大线程数
		executor.setMaxPoolSize(40);
		//队列容量
		executor.setQueueCapacity(300);
		//活跃时间
		executor.setKeepAliveSeconds(50);
		//线程名字前缀
		executor.setThreadNamePrefix("baseExecutor-");

		// setRejectedExecutionHandler：当pool已经达到max size的时候，如何处理新任务
		// CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
		executor.initialize();
		return executor;
	}

	public static void main(String[] args) {
		SpringApplication.run(MemberApplication.class, args);
		log.info("启动成功!");
	}
}
