package com.cnoocshell.member.service.handler;

import com.alibaba.fastjson.JSON;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.member.dao.vo.Member;
import com.cnoocshell.mq.core.MQMessage;
import com.cnoocshell.mq.core.service.IMQProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * @Author: <EMAIL>
 * @created 9:58 19/02/2019
 * @description 会员信息同步，发送消息到队列
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SynchronizeMemberHandler {
    @Qualifier("kafkaProducer")
    @Autowired
    private IMQProducer producer;

    public void syncMemberInfo(Member member) {
        MQMessage<Member> mqMessage = new MQMessage<>();
        mqMessage.setExchange(MqTopicConstant.MEMBER_CHANGE_TOPIC);
        log.info("向队列发送同步的会员信息 --->>> {}", member);
        mqMessage.setData(member);
        try {
            producer.send(mqMessage);
        } catch (Exception e) {
            log.error("SynchronizeMemberHandler.syncMemberInfo error:",e);
        }
    }
}
