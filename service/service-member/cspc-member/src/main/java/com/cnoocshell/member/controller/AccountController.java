package com.cnoocshell.member.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.redis.UserInfoKeys;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.member.api.dto.account.*;
import com.cnoocshell.member.api.dto.account.checkCode.CheckEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.CheckSMSCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetSMSCodeDTO;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.session.LoginInfo;
import com.cnoocshell.member.exception.DuplicateString;
import com.cnoocshell.member.service.IAccountService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

@Slf4j
@Tag(name = "Account", description = "账户名称唯一，手机号不唯一<br/> 个人通过前端页面PC或手机注册，手机号不可为空，不可重复<br/> 手机上注册则用户名使用uuidGenerator随机生成<br/> 管理员或企业主账号注册，则不检查手机号是否重复的问题<br/> 如果一个手机号在账户表中唯一，则可以用账户名和手机号登陆均可<br/> 如果手机号不唯一，且相同手机号对应的账号没有绑定到一起，则只能用账户名登陆<br/> 如果手机号不唯一，但是同手机号对应的账号已经绑定到一起，则用账户名或手机号登陆均可，默认登陆的账号为第一个密码校验通过的账号<br/> 各接口内部不没有校验短信验证码，请调用接口前保证调用的有效性")
@RestController
@RequestMapping("/account")
@RequiredArgsConstructor
public class AccountController {

    private final IAccountService iAccountService;
    private final BizRedisService bizRedisService;

    private static final String ATTRIBUTE_NAME_ACCOUNT_NAME = "_accountMobile";

    @ApiOperation("根据用户名或手机号和密码，本地数据库登陆校验")
    @PostMapping(value = "/loginVerification")
    public AccountDTO loginVerification(@RequestBody AccountLoginDTO accountLoginDTO) {
        return iAccountService.loginVerification(accountLoginDTO);
    }

    @ApiOperation("校验手机号是否已被个人账号使用")
    @PostMapping(value = "/checkMobilePhoneExistsByPersonl")
    public boolean checkMobilePhoneExistsByPersonl(@RequestParam("phoneNumber") String phoneNumber) {
        return iAccountService.checkMobilePhoneExistsByPersonl(phoneNumber);
    }

    @ApiOperation("校验新手机号是否已被企业主账号使用")
    @PostMapping(value = "/checkMobilePhoneExistsByMasterAccount")
    public boolean checkMobilePhoneExistsByMasterAccount(@RequestParam("phoneNumber") String phoneNumber) {
        return iAccountService.checkMobilePhoneExistsByMasterAccount(phoneNumber);
    }

    @ApiOperation("手机动态码登陆")
    @PostMapping(value = "/loginByPhoneVerificationCode")
    public AccountDTO loginByPhoneVerificationCode(@RequestBody PhoneCodeLoginDTO phoneCodeLoginDTO) {
        return iAccountService.loginByPhoneVerificationCode(phoneCodeLoginDTO);
    }

    @ApiOperation("校验短信验证码")
    @PostMapping(value = "/checkSMSCode")
    public Boolean checkSMSCode(@Valid @RequestBody CheckSMSCodeDTO dto) {
        return iAccountService.checkSMSCode(dto);
    }

    @ApiOperation("校验新用户名是否已被占用(注册或修改用户名前校验使用)")
    @PostMapping(value = "/checkAccountNameExists")
    public boolean checkAccountNameExists(@RequestParam("newAccountName") String newAccountName) {
        return iAccountService.checkAccountNameExists(newAccountName);
    }

    @ApiOperation("1个人PC注册账户<br/> 个人通过前端页面PC注册，手机号不可为空，不可重复<br/>")
    @PostMapping(value = "/registerAccountByPC")
    public ItemResult<AccountDTO> registerAccountByPC(@RequestBody AccountPCRegisterDTO accountRegisterDTO) {
        try{
            return ItemResult.success( iAccountService.registerAccountByPC(accountRegisterDTO));
        } catch (BizException e) {
            log.error("registerAccountByPC 业务异常：",e);
           return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        }catch (Exception e){
            log.error("个人PC注册账户 registerAccountByPC:{}",e);
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(),"个人注册失败");
        }
    }

    @ApiOperation("根据id查询单个账户信息")
    @PostMapping(value = "/findById")
    public AccountDTO findById(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId) {
        AccountDTO dto = iAccountService.findById(accountId);
        String accountName = dto.getAccountName();
        if (Objects.isNull(accountName) || accountName.trim().isEmpty()) {
            String mobile = dto.getMobile();
            dto.setAccountName(mobile.substring(0, 3) + "****" + mobile.substring(8));
        }
        return dto;
    }

    @ApiOperation("根据id查询单个账户信息")
    @GetMapping("/findByEmail")
    AccountDTO findByEmail(@RequestParam("email") String email) {
        return iAccountService.findByEmail(email);
    }

    @ApiOperation("根据id查询单个账户简单信息")
    @PostMapping(value = "/findSimpleById")
    public AccountSimpleDTO findSimpleById(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId) {
        return iAccountService.findSimpleById(accountId);
    }

    @ApiOperation("根据手机号查找账号，并根据登陆类型（即登陆所在app）过滤账号")
    @PostMapping(value = "/findByMobilePhoneAndLoginType")
    public List<AccountDTO> findByMobilePhoneAndLoginType(@RequestParam("mobilePhoneNumber") String mobilePhoneNumber,
                                                          @RequestParam(value = "loginType", required = false) String loginType) {
        return iAccountService.findByMobilePhoneAndLoginType(mobilePhoneNumber, loginType);
    }

    @ApiOperation("校验邮件验证码")
    @PostMapping(value = "/checkEmailCode")
    public ItemResult<Boolean> checkEmailCode(@Valid @RequestBody CheckEmailCodeDTO dto) {
        try{
            return ItemResult.success(iAccountService.checkEmailCode(dto));
        }catch (BizException e){
            return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        }catch (Exception e){
            log.error("检查邮件验证码异常 error:",e);
            return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"系统错误,请联系管理员");
        }
    }

    @ApiOperation("校验手机号是否已被占用")
    @PostMapping(value = "/checkMobilePhoneExists")
    public boolean checkMobilePhoneExists(@RequestParam("phoneNumber") String phoneNumber) {
        return iAccountService.checkMobilePhoneExists(phoneNumber);
    }

    @ApiOperation("获取短信验证码")
    @PostMapping(value = "/getSMSCode")
    public String getSMSCode(@Valid @RequestBody GetSMSCodeDTO dto) {
        return iAccountService.getSMSCode(dto);
    }

    @ApiOperation("第2步 已登陆用户，校验短信验证码，修改自己的手机号")
    @PostMapping(value = "/updateMobilePhone")
    public void updateMobilePhone(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId, @RequestParam("phoneNumber") String phoneNumber, @RequestParam("operator") String operator, @RequestParam(value = "ip", required = false) String ip) {
        iAccountService.updateMobilePhone(accountId, phoneNumber, operator, ip);
    }

    @ApiOperation("获取电子邮件验证码")
    @PostMapping(value = "/getEmailCode")
    public String getEmailCode(@Valid @RequestBody GetEmailCodeDTO dto) {
        return iAccountService.getEmailCode(dto);
    }


    @ApiOperation("更新账户基本信息")
    @PostMapping(value = "/updateBaseInfo")
    public void updateBaseInfo(@RequestBody AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO) {
        iAccountService.updateBaseInfo(accountBaseInfoUpdateDTO);
    }

    @ApiOperation("第2步  已登陆用户，修改自己的用户名(如果账号名称就是手机号，则账户名称也将被修改)")
    @PostMapping(value = "/updateAccountName")
    public void updateAccountName(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId,
                                  @RequestParam("newAccountName") String newAccountName,
                                  @RequestParam("operator") String operator,
                                  @RequestParam(value = "ip", required = false) String ip) {
        iAccountService.updateAccountName(accountId, newAccountName, operator, ip);
        bizRedisService.del(UserInfoKeys.USER_INFO_KEY + accountId);
    }

    @ApiOperation("7企业主账号创建子账号（含角色、数据权限、组织机构、销售区域）")
    @PostMapping(value = "/registerSubAccountByMaster")
    public ItemResult<AccountDTO> registerSubAccountByMaster(@RequestBody AccountRegisterByMasterDTO param) {
        if(CharSequenceUtil.isNotBlank(param.getEmail())){
            if(iAccountService.existsAccountByEmail(param.getEmail(),null))
                return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"邮箱已存在");
        }
        try{
            return ItemResult.success(iAccountService.registerSubAccountByMaster(param));
        }catch (BizException e){
            return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        }catch (Exception e){
            return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"创建账户失败");
        }
    }

    @ApiOperation("根据条件查询账号（平台管理账号列表查询使用）")
    @PostMapping(value = "/findAll")
    public PageInfo<AccountDTO> findAll(@RequestBody AccountSearchDTO accountSearchDTO){
        return iAccountService.findAll(accountSearchDTO);
    }

    @ApiOperation("禁用账号")
    @PostMapping(value = "/disabled")
    public void disabled(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId, 
                         @RequestParam("reason") String reason, 
                         @RequestParam("operator") String operator)  {
        iAccountService.disabled(accountId, reason, operator);
    }

    @ApiOperation("启用账号")
    @PostMapping(value = "/enabled")
    public void enabled(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId, 
                        @RequestParam("operator") String operator)  {
        iAccountService.enabled(accountId, operator);
    }

    @ApiOperation("根据账户名查询单个账户信息")
    @PostMapping(value = "/findByAccountName")
    public AccountDTO findByAccountName(@RequestParam(DuplicateString.ACCOUNT_NAME) String accountName) {
        return iAccountService.findByAccountName(accountName);
    }

    @ApiOperation("根据id查询单个账户信息(含角色、权限、销售区域、组织机构、数据权限)")
    @PostMapping(value = "/findDetailById")
    public AccountDTO findDetailById(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId) {
        return iAccountService.findDetailById(accountId);
    }

    @ApiOperation("更新子账账户信息")
    @PostMapping(value = "/updateSubAccountInfo")
    public ItemResult<Boolean> updateSubAccountInfo(@RequestBody SubAccountUpdateDTO subAccountUpdateDTO) {
        if(CharSequenceUtil.isNotBlank(subAccountUpdateDTO.getEmail())){
            if(iAccountService.existsAccountByEmail(subAccountUpdateDTO.getEmail(),subAccountUpdateDTO.getAccountId()))
                return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"邮箱已存在");
        }
        if(CharSequenceUtil.isNotBlank(subAccountUpdateDTO.getMobile())){
            if(iAccountService.existsAccountByMobile(subAccountUpdateDTO.getMobile(),subAccountUpdateDTO.getAccountId()))
                return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"手机号已存在");
        }
        try {
            iAccountService.updateSubAccountInfo(subAccountUpdateDTO);
            return ItemResult.success(true);
        }catch (BizException e){
            return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        }catch (Exception e){
            return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"修改账户信息失败");
        }
    }

    @ApiOperation("改变账号的类型 类型参考：AccountDTO中的常量 改变账号的类型 类型参考：AccountDTO中的常量")
    @PostMapping(value = "/changeAccountType")
    public void changeAccountType(@RequestBody ChangeAccountTypeDTO changeAccountTypeDTO){
        iAccountService.changeAccountType(changeAccountTypeDTO);
    }

    @ApiOperation("刷新在线用户角色、权限、销售区域信息")
    @PostMapping(value = "/refreshLoginInfo")
    public void refreshLoginInfo(@Valid @RequestBody RefreshLoginInfoDTO dto) {
        iAccountService.refreshLoginInfo(dto);
    }

    @ApiOperation("解绑账号")
    @PostMapping(value = "/unBindingAccount")
    public void unBindingAccount(@RequestParam("currUserAccountId") String currUserAccountId,
                                 @RequestParam("unBindingAccountId") String unBindingAccountId) {
        iAccountService.unBindingAccount(currUserAccountId, unBindingAccountId);

    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || StringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }

    private void updateLoginInfo(LoginInfo loginInfo, HttpServletRequest request) {
        //保存session  Spring HttpSession 自动保存session到redis
        request.getSession().setAttribute(LoginInfo.SESSION_NAME, loginInfo);
        request.getSession().setAttribute(ATTRIBUTE_NAME_ACCOUNT_NAME, loginInfo.getMobile());
    }

    @ApiOperation("根据ids查询账户信息(仅含账户表信息)")
    @PostMapping(value = "/findByIds")
    public List<AccountDTO> findByIds(@RequestBody List<String> ids) {
        return iAccountService.findByIds(ids);
    }

    @ApiOperation("根据角色code获取用户名称")
    @PostMapping(value = "/findAccountNameByRoleCode")
    public List<AccountNameDTO> findAccountNameByRoleCode(@RequestParam("roleCode") String roleCode) {
        return iAccountService.findAccountNameByRoleCode(roleCode);
    }

    @ApiOperation("根据用户ID查询用户信息")
    @PostMapping(value = "/findAccountByAccountId")
    public List<AccountInfoReturnDTO> findAccountByAccountId(@RequestBody List<String> ids) {
        return iAccountService.findAccountByAccountId(ids);
    }

    @ApiOperation("根据角色code获取用户名称")
    @PostMapping(value = "/listSimpleByIds")
    public List<AccountSimpleDTO> listSimpleByIds(@RequestBody List<String> accountIds) {
        return iAccountService.listSimpleByIds(accountIds);
    }

    @GetMapping("/existsAccountByEmail")
    Boolean existsAccountByEmail(@RequestParam String email,
                                 @RequestParam(required = false) String exceptAccountId){
        return iAccountService.existsAccountByEmail(email, exceptAccountId);
    }
}
