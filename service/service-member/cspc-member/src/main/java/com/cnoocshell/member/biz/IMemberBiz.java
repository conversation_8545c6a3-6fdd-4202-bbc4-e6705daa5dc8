package com.cnoocshell.member.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.dto.report.MemberAccountReportQueryDTO;
import com.cnoocshell.member.api.dto.report.MemberAccountReportResultDTO;
import com.cnoocshell.member.api.dto.report.MemberReportQueryDTO;
import com.cnoocshell.member.api.dto.report.MemberReportResultDTO;
import com.cnoocshell.member.api.session.Operator;
import com.cnoocshell.member.dao.vo.Member;
import com.cnoocshell.member.dao.vo.MemberApproveRequest;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * @DESCRIPTION:
 */
public interface IMemberBiz extends IBaseBiz<Member> {
    void cleanRedisCache(String operator);

    String findMaxMemberCode();

    boolean countMemberCode(String memberCode);

    /**
     * 根据ID查看用户简要信息
     *
     * @param memberId
     * @return
     */
    MemberSimpleDTO findMemberSimpleById(String memberId);

    Member findById(String id);

    void insertSelective(Member member);

    /**
     * 分页获取会员请求
     *
     * @param query
     * @return
     */
    Page<MemberApprovalRequestDTO> pageMemberApprovalRequests(MemberApprovalRequestQueryDTO query, PageInfo pageInfo);

    /**
     * 获取会员申请(变更信息)详情
     *
     * @param memberApprovalRequestId
     * @return
     */
    MemberDTO getMemberApprovalDetails(String memberApprovalRequestId);

    /**
     * 根据ID查找会员（只有已审批的信息）
     *
     * @param id
     * @return
     */
    MemberDTO findRealMemberById(String id);

    /**
     * 按照id查询用户（包括未审批的信息）
     *
     * @param id
     * @return
     */
    MemberDTO findMemberDetailById(String id);

    /**
     * 根据资质ID查找认证详情
     *
     * @param accountId
     * @return
     */
    MemberCertDTO findRealNameCertByAccountId(String accountId);

    /**
     * 提交企业注册
     * requestType - 申请类型
     * 必须指明每个资质的资质类型，是经营资质、卖家资质、承运商资质。。。
     *
     * @param memberRequestDTO
     * @param operatorId
     */
    String submitRegister(MemberRequestDTO memberRequestDTO, String requestType, boolean needApprove, int proxy, String operatorId);

    boolean checkMemberNameUsed(String memberId, String memberName);

    /**
     * 审批或者拒绝资质请求
     *
     * @param approveRequestId
     * @param message
     */
    void approveOrRejectRequest(String approveRequestId, String newCarrierType, String message, boolean approve, Operator operator, String appName);

    /**
     * 更新企业注册信息（审批通过时使用）
     *
     * @param memberRequestDTO
     * @param member
     * @param request
     * @param operatorId
     * @return
     */
    void updateRegisterInfo(MemberRequestDTO memberRequestDTO, Member member, MemberApproveRequest request, String operatorId);


    /**
     * 审批是更新企业经营信息
     *
     * @param memberBusinessInfoDTO
     * @param memberId
     * @param requestId
     * @param operatorId
     */
    void updateBusinessInfo(MemberBusinessInfoDTO memberBusinessInfoDTO, String memberId, String requestId, String operatorId);


    /**
     * 更新会员基本信息
     *
     * @param memberBaseInfoDTO
     * @param operatorId
     */
    void updateBaseInfo(MemberBaseInfoDTO memberBaseInfoDTO, String operatorId);

    /**
     * 分页查询用户
     *
     * @param query
     * @return
     */
    Page<MemberListViewDTO> pageMemberListView(MemberQueryDTO query, PageInfo pageInfo);

    /**
     * 启用用户
     *
     * @param memberId
     */
    void enableMember(String memberId, String operatorId);

    /**
     * 禁用用户
     *
     * @param memberId
     */
    void disableMember(String memberId, String operatorId);

    /**
     * 根据ID查找用户详情（不包括资质信息）
     *
     * @param memberId
     * @return
     */
    MemberDetailDTO findMemberById(String memberId);

    /**
     * 变更企业经营信息
     *
     * @param memberBusinessInfoDTO
     * @param needApprove
     * @param operatorId
     */
    String updateBusinessInfo(MemberBusinessInfoDTO memberBusinessInfoDTO, boolean needApprove, String operatorId);

    /**
     * 一般资质变更（修改）
     *
     * @param memberCertDtoList
     */
    String updateCert(List<MemberCertDTO> memberCertDtoList, boolean needApproval, String operatorId, String memberId);

    /**
     * 获取一条变更记录
     *
     * @param requestId
     * @return
     */
    MemberApprovalRequestDTO findMemberApprovalRequest(String requestId);

    /**
     * 获取会员申请(变更信息)详情
     *
     * @param memberApprovalRequestId
     * @return
     */
    MemberBusinessRequestDetailDTO getMemberApprovalBusinessDetails(String memberApprovalRequestId);

    List<Member> listByCodes(List<String> memberCodes);

    int countByMemberCodes(List<String> memberCodes);

    int updateCrmCode(String crmCode,String memberId);

    List<AccountSimpleDTO> listAccountsByMemberCodes(QueryMemberAccountDTO param);

    List<Member> listByIds(List<String> memberIds);

    void handleAccountMemberName(List<AccountSimpleDTO> accounts);


    /**
     *分页获取企业变更请求 意向变更、经营信息变更、资质变更
     *
     */
    PageInfo<MemberApprovalRequestDTO> pageMemberChangeRequest(MemberApprovalRequestQueryDTO query, PageInfo pageInfo);

    PageInfo<MemberReportResultDTO> memberReport(MemberReportQueryDTO param);

    PageInfo<MemberAccountReportResultDTO> memberAccountReport(MemberAccountReportQueryDTO param);
}
