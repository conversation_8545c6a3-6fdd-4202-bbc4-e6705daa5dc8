package com.cnoocshell.member.biz;

import com.cnoocshell.member.api.dto.account.RefreshLoginInfoDTO;
import com.cnoocshell.member.dao.vo.Account;

import java.util.List;
import java.util.Set;

/**
 * @DESCRIPTION:修改会话信息
 */
public interface IAccountSessionBiz {
    void forceOffLineSync(String accountId, String terminal, String exceptionSessionId, String msg, String operator);

    /**
     * sync更新在线用户的账户信息-手机号
     *
     * @param accountId
     * @param mobile
     */
    void updateSessionLoginInfoMobile(String accountId, String mobile);

    /**
     * sync更新在线用户的账户信息-账户名称、简称、
     * @param account
     */
    void updateSessionLoginInfoAccountInfo(Account account);

    /**
     * sync更新在线用户的账户信息-账号名
     * @param accountId
     * @param accountName
     */
    void updateSessionLoginInfoAccountName(String accountId,String accountName);

    void updateSessionLoginInfoMemberName(String memberId,String memberName,String memberShortName);

    void refreshLoginInfo(RefreshLoginInfoDTO dto);

    List<String> findLoginSessionId(Set<String> accountIdSet, Integer offset, Integer limitSize);

    void forceOffLineSyncBySameTerminal(String accountId, String loginType, String exceptionSessionId, String msg, String terminal);

    void removeSession(String sessionId);
}
