package com.cnoocshell.member.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.member.api.dto.member.MemberPurchaseGoodsIntentionDTO;
import com.cnoocshell.member.api.dto.member.intention.MemberIntentionSimpleDTO;
import com.cnoocshell.member.api.dto.report.MemberIntentionReportResultDTO;
import com.cnoocshell.member.dao.vo.MemberPurchaseGoodsIntention;

import java.util.Collection;
import java.util.List;

public interface IMemberPurchaseGoodsIntentionBiz extends IBaseBiz<MemberPurchaseGoodsIntention> {
    List<MemberPurchaseGoodsIntention> listByMemberId(String memberId);

    void saveIntentionList(List<MemberPurchaseGoodsIntentionDTO> intentionList,String memberId,String memberCode,String operatorId);

    void removeByPrimaryKey(List<MemberPurchaseGoodsIntention> intentions);

    void removeByIds(Collection<String> ids);


    /**
     *处理意向中商品名称与品类名称
     */
    void handleNameByIntentions(List<MemberPurchaseGoodsIntentionDTO> intentionList);

    void saveNewIntentionList(List<MemberPurchaseGoodsIntentionDTO> intentionList,String memberId,String memberCode,String operatorId);

    void updateIntentionType(List<MemberPurchaseGoodsIntention> intentions,String operatorId);

    List<MemberIntentionReportResultDTO> queryMemberIntentionReport(List<String> memberIds, List<String> goodsCodes);

    List<String> queryMemberIdConcatGoodsCodeBySaleChannel(List<String> memberIds,List<String> saleChannels);

    List<MemberIntentionSimpleDTO> queryIntentionBySaleChannel(List<String> saleChannels);
}
