package com.cnoocshell.member.biz.impl;

import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.member.api.dto.account.AccountChangeHistoryDTO;
import com.cnoocshell.member.api.dto.account.PageAccountChangeHistoryDTO;
import com.cnoocshell.member.biz.IAccountChangeHistoryBiz;
import com.cnoocshell.member.dao.mapper.AccountChangeHistoryMapper;
import com.cnoocshell.member.dao.vo.AccountChangeHistory;
import com.cnoocshell.member.exception.DuplicateString;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountChangeHistoryBiz extends BaseBiz<AccountChangeHistory> implements IAccountChangeHistoryBiz {

    public static final String IS_EFFECT = "isEffect";

    private final UUIDGenerator uuidGenerator;
    private final AccountChangeHistoryMapper accountChangeHistoryMapper;

    @Override
    public void add(AccountChangeHistory accountChangeHistory, String operator) {
        String id = uuidGenerator.gain();
        accountChangeHistory.setAccountHistoryId(id);
        setOperatorInfo(accountChangeHistory,operator,true);
        accountChangeHistoryMapper.insertSelective(accountChangeHistory);
    }

    @Override
    public List<AccountChangeHistory> findByQuery(AccountChangeHistoryDTO accountChangeHistoryDTO) {
        Condition condition = new Condition(AccountChangeHistory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DuplicateString.DEL_FLG,0);
        if(StringUtils.isNotBlank(accountChangeHistoryDTO.getAccountId())){
            criteria.andEqualTo(DuplicateString.ACCOUNT_ID,accountChangeHistoryDTO.getAccountId());
        }
        if(StringUtils.isNotBlank(accountChangeHistoryDTO.getRealName())){
            criteria.andLike("realName","%" + accountChangeHistoryDTO.getRealName() + "%");
        }
        if(StringUtils.isNotBlank(accountChangeHistoryDTO.getOperatroName())){
            criteria.andLike("operatroName","%" + accountChangeHistoryDTO.getOperatroName() + "%");
        }
        if(StringUtils.isNotBlank(accountChangeHistoryDTO.getWay())){
            criteria.andEqualTo("way",accountChangeHistoryDTO.getWay());
        }
        if(accountChangeHistoryDTO.getIsEffect() != null){
            criteria.andEqualTo(IS_EFFECT,accountChangeHistoryDTO.getIsEffect());
        }
        if(accountChangeHistoryDTO.getIsImmediateEffect() != null){
            criteria.andEqualTo("isImmediateEffect",accountChangeHistoryDTO.getIsImmediateEffect());
        }
        condition.orderBy(DuplicateString.CREATE_TIME).desc();
        return accountChangeHistoryMapper.selectByCondition(condition);
    }

    @Override
    public PageInfo<AccountChangeHistory> pageHistoryInfoList(PageAccountChangeHistoryDTO pageAccountChangeHistoryDTO) {
        Condition condition = new Condition(AccountChangeHistory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE);
        CommonUtils.andEqualToIfNotNBank(criteria,DuplicateString.ACCOUNT_ID,pageAccountChangeHistoryDTO.getAccountId());
        CommonUtils.andEqualToIfNotNBank(criteria, "way", pageAccountChangeHistoryDTO.getWay());
        CommonUtils.andEqualToIfNotNull(criteria, IS_EFFECT, pageAccountChangeHistoryDTO.getIsEffect());
        CommonUtils.andEqualToIfNotNull(criteria, "isImmediateEffect", pageAccountChangeHistoryDTO.getIsImmediateEffect());
        CommonUtils.andLikeIfNotBank(criteria, "realName", pageAccountChangeHistoryDTO.getRealName());
        CommonUtils.andLikeIfNotBank(criteria, "operatroName", pageAccountChangeHistoryDTO.getOperatroName());
        
        condition.orderBy(DuplicateString.CREATE_TIME).desc();
        PageMethod.startPage(pageAccountChangeHistoryDTO.getPageNum(),pageAccountChangeHistoryDTO.getPageSize());
        return new PageInfo<>(accountChangeHistoryMapper.selectByCondition(condition));
    }
}
