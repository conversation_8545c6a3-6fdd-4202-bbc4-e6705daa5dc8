package com.cnoocshell.member.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.member.api.dto.member.MemberPurchaseGoodsIntentionDTO;
import com.cnoocshell.member.biz.IMemberPurchaseGoodsIntentionBiz;
import com.cnoocshell.member.biz.IMemberPurchaseGoodsIntentionHistoryBiz;
import com.cnoocshell.member.dao.mapper.MemberPurchaseGoodsIntentionHistoryMapper;
import com.cnoocshell.member.dao.vo.MemberPurchaseGoodsIntentionHistory;
import com.cnoocshell.member.exception.DuplicateString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MemberPurchaseGoodsIntentionHistoryBiz
        extends BaseBiz<MemberPurchaseGoodsIntentionHistory>
        implements IMemberPurchaseGoodsIntentionHistoryBiz {
    private final MemberPurchaseGoodsIntentionHistoryMapper intentionHistoryMapper;
    private final IMemberPurchaseGoodsIntentionBiz iMemberPurchaseGoodsIntentionBiz;

    @Override
    public List<MemberPurchaseGoodsIntentionHistory> listByRequestId(String requestId) {
        Condition condition = new Condition(MemberPurchaseGoodsIntentionHistory.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(DuplicateString.REQUEST_ID,requestId);

        return this.findByCondition(condition);
    }

    @Override
    public void saveIntentionHistoryList(List<MemberPurchaseGoodsIntentionDTO> intentionList,String memberId,String memberCode, String requestId, String operatorId) {
        if(CollUtil.isEmpty(intentionList))
            return;
        List<MemberPurchaseGoodsIntentionHistory> list = BeanUtil.copyToList(intentionList,MemberPurchaseGoodsIntentionHistory.class);
        list.forEach(v->{
            v.setId(this.getUuidGeneratorGain());
            v.setMemberId(memberId);
            v.setMemberCode(memberCode);
            v.setRequestId(requestId);
            setOperatorInfo(v,operatorId,true);
        });

        this.insertList(list);
    }

    @Override
    public List<MemberPurchaseGoodsIntentionDTO> getByRequestId(String requestId) {
        List<MemberPurchaseGoodsIntentionHistory> list = this.listByRequestId(requestId);
        if (CollUtil.isEmpty(list))
            return null;
        List<MemberPurchaseGoodsIntentionDTO> result = BeanUtil.copyToList(list, MemberPurchaseGoodsIntentionDTO.class);
        iMemberPurchaseGoodsIntentionBiz.handleNameByIntentions(result);
        return result;
    }
}