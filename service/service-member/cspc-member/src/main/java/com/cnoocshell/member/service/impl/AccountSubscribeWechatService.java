package com.cnoocshell.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.integration.enums.WechatTemplateEnum;
import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.dto.account.AccountSubscribeWechatDTO;
import com.cnoocshell.member.api.dto.account.MbAccountSubscribeWechatDTO;
import com.cnoocshell.member.api.dto.account.QueryAccountSubscribeWechatDTO;
import com.cnoocshell.member.biz.IAccountSubscribeWechatBiz;
import com.cnoocshell.member.dao.mapper.MbAccountSubscribeWechatMapper;
import com.cnoocshell.member.dao.vo.MbAccountSubscribeWechat;
import com.cnoocshell.member.exception.DuplicateString;
import com.cnoocshell.member.service.IAccountService;
import com.cnoocshell.member.service.IAccountSubscribeWechatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AccountSubscribeWechatService implements IAccountSubscribeWechatService {

    @Autowired
    private IAccountSubscribeWechatBiz accountSubscribeWechatBiz;

    @Autowired
    private IAccountService accountService;

    @Resource
    private MbAccountSubscribeWechatMapper mbAccountSubscribeWechatMapper;

    @Override
    public ItemResult<String> createSubscribeRecord(MbAccountSubscribeWechatDTO accountSubscribeWechatDTO) {
        //添加校验根据业务No去检查，如果存在则订阅失败


        AccountDTO accountDTO = accountService.findById(accountSubscribeWechatDTO.getCreateUser());
        MbAccountSubscribeWechat accountSubscribeWechat = MbAccountSubscribeWechat.builder().build();
        BeanUtils.copyProperties(accountSubscribeWechatDTO, accountSubscribeWechat);
        accountSubscribeWechat.setAccountId(accountDTO.getAccountId());
        accountSubscribeWechat.setAccountRealName(accountDTO.getRealName());
        accountSubscribeWechat.setOpenId(accountDTO.getWechatId());
        accountSubscribeWechat.setTemplateName(WechatTemplateEnum.getTemplateNameByTemplateId(accountSubscribeWechatDTO.getTemplateId()));
        accountSubscribeWechat.setSendStatus("0");
        accountSubscribeWechat.setDelFlg(false);
        accountSubscribeWechat.setCreateUser(accountDTO.getAccountId());
        accountSubscribeWechat.setCreateTime(new Date());
        int insert = accountSubscribeWechatBiz.insert(accountSubscribeWechat);
        return insert > 0 ? new ItemResult<>("创建订阅记录成功！") : new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "创建订阅记录失败");
    }

    @Override
    public ItemResult<Boolean> checkAccountSubscribe(MbAccountSubscribeWechatDTO accountSubscribeWechatDTO) {
        Condition condition = accountSubscribeWechatBiz.newCondition();
        condition.createCriteria().andEqualTo("accountId", accountSubscribeWechatDTO.getAccountId())
                .andEqualTo("businessNo", accountSubscribeWechatDTO.getBusinessNo());
        List<MbAccountSubscribeWechat> byCondition = accountSubscribeWechatBiz.findByCondition(condition);
        return new ItemResult<>(CollectionUtils.isNotEmpty(byCondition));
    }

    @Override
    public ItemResult<String> updateSendStatus(MbAccountSubscribeWechatDTO accountSubscribeWechatDTO) {
        if (Objects.isNull(accountSubscribeWechatDTO)
                || StringUtils.isEmpty(accountSubscribeWechatDTO.getOpenId())
                || StringUtils.isEmpty(accountSubscribeWechatDTO.getBusinessNo())) {
            return new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "参数为空");
        }
        accountSubscribeWechatDTO.setSendStatus("1");
        accountSubscribeWechatDTO.setUpdateUser("system");
        accountSubscribeWechatDTO.setUpdateTime(new Date());
        mbAccountSubscribeWechatMapper.updateSendStatus(accountSubscribeWechatDTO);
        return new ItemResult<>("修改成功");
    }

    @Override
    public List<AccountSubscribeWechatDTO> querySubscribeByCondition(QueryAccountSubscribeWechatDTO param) {
        Condition condition = accountSubscribeWechatBiz.newCondition();
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(DuplicateString.BUSINESS_TYPE, param.getBusinessType())
                .andEqualTo("sendStatus","0");
        CommonUtils.andInIfNotEmpty(criteria, DuplicateString.BUSINESS_NO, param.getBusinessNo());
        CommonUtils.andInIfNotEmpty(criteria, DuplicateString.ACCOUNT_ID, param.getAccountId());
        return BeanUtil.copyToList(accountSubscribeWechatBiz.findByCondition(condition), AccountSubscribeWechatDTO.class);
    }

}
