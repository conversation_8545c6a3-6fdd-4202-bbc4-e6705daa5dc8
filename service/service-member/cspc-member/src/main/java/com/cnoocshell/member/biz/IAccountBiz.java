package com.cnoocshell.member.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.member.api.dto.account.*;
import com.cnoocshell.member.dao.vo.Account;
import com.github.pagehelper.Page;

import java.util.List;

/**
 * @DESCRIPTION:实现service接口中的方法
 */
public interface IAccountBiz extends IBaseBiz<Account> {
    void cleanRedisCache(String operator);

    /**
     * 根据用户名或手机号和密码，本地数据库登陆校验
     *
     * @param accountLoginDTO 登录信息
     * @return 校验通过则返回账户名和是否需要修改密码的判断，否则返回空
     */
    AccountDTO loginVerification(AccountLoginDTO accountLoginDTO);

    /**
     * 根据id查询单个账户信息
     *
     * @param accountId 账户id
     * @return Account    单个账户信息
     */
    Account findById(String accountId);

    /**
     * 第1步 已登陆用户，修改自己的手机号前，校验新手机号是否已被占用
     *
     * @param newMobilePhone 新用户名
     * @return 存在则返回true
     */
    boolean checkMobilePhoneExists(String newMobilePhone, Integer... accountType);

    /**
     * 根据手机号和短信验证码登陆，且短信验证码已被验证通过
     *
     * @param phoneCodeLoginDTO
     * @return
     */
    AccountDTO loginByPhoneVerificationCode(PhoneCodeLoginDTO phoneCodeLoginDTO);

    /**
     * 根据手机号查询账户信息
     *
     * @param mobilePhoneNumber 手机号
     * @return Account    单个账户信息
     */
    List<Account> findByMobilePhone(String mobilePhoneNumber, Integer accountType);

    Account findByEmail(String email);

    /**
     * 第1步  已登陆用户，修改自己的用户名前，校验新用户名是否已被占用
     *
     * @param newAccountName 新用户名
     * @return 存在则返回true
     */
    boolean checkAccountNameExists(String newAccountName);

    /**
     * 个人注册账户,账户类型为AccountDTO.ACCOUNT_TYPE_PERSONAL
     *
     * @param account 注册账户
     * @return Account 账户对象
     */
    Account registerAccount(Account account);

    /**
     * 第2步 已登陆用户，校验短信验证码，修改自己的手机号
     *
     * @param accountId      账户id
     * @param newMobilePhone 新手机
     * @param operator       操作信息
     */
    void updateMobilePhone(String accountId, String newMobilePhone, String operator);


    /**
     * 更新账户基本信息
     *
     * @param account  账户
     * @param operator 操作人AccountId
     */
    Account updateBaseInfo(Account account, String operator);

    /**
     * 根据主键更新
     *
     * @param account
     * @return
     */
    Account updateById(Account account);

    /**
     * 第2步  已登陆用户，修改自己的用户名
     *
     * @param accountId      账户id
     * @param newAccountName 新用户名
     * @param operator       操作信息
     */
    void updateAccountName(String accountId, String newAccountName, String operator);

    int updateMemberName(String memberId, String memberName, String memberShortName);

    /**
     * 添加子账户,账户类型为AccountDTO.ACCOUNT_TYPE_MEMBER_SUB
     *
     * @param subAccountRegisterDTO 注册子账户的对象
     * @return Account 账户Account对象
     */
    Account registerSubAccount(SubAccountRegisterDTO subAccountRegisterDTO);

    Page<Account> findAll(AccountSearchDTO accountSearchDTO);

    /**
     * 禁用账号
     *
     * @param accountId
     */
    void disabled(String accountId, String reason, String operator);

    /**
     * 启用账号
     *
     * @param accountId
     */
    void enabled(String accountId, String operator);

    Account findByAccountName(String accountName);

    Account findById(String accountId, String memberId);

    /**
     * 更新子账账户信息
     *
     * @param subAccountUpdateDTO 子账户信息(含id、组织机构、角色、销售区域、数据权限)
     */
    Account updateSubAccountInfo(SubAccountUpdateDTO subAccountUpdateDTO);

    List<Account> findByMemberIdAndAccountType(String memberId, Integer accountType);

    void unBindingAccount(String currUserAccountId, String unBindingAccountId);

    List<Account> findByIds(List<String> ids);

    List<String> listAccountIdByMemberCodes(List<String> memberCodes);

    boolean existsAccountByEmail(String email,String exceptAccountId);

    List<Account> listByIds(List<String> ids);

    List<Account> listByMobile(String mobile);

    boolean existById(String accountId);

    boolean existsAccountByMobile(String mobile, String exceptAccountId);
}
