package com.cnoocshell.member.consumer;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.member.api.dto.account.AccountAgreementDTO;
import com.cnoocshell.member.service.IAccountAgreementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountAgreementConsumer {
    private final IAccountAgreementService iAccountAgreementService;

    @KafkaListener(topics = MqTopicConstant.SAVE_AGREEMENT_TOPIC)
    public void saveAgreementConsumer(ConsumerRecord<String, String> data) {
        if(Objects.isNull(data) || CharSequenceUtil.isBlank(data.value())){
            log.info("用户协议记录 入参数据为空");
            return;
        }
        try{
            List<AccountAgreementDTO> params = JSONUtil.toList(data.value(),AccountAgreementDTO.class);
            iAccountAgreementService.saveAgreement(params);
        }catch (Exception e){
            log.error("用户协议记录 异常：",e);
        }
    }
}
