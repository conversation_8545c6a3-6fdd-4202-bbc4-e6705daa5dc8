package com.cnoocshell.member.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.member.api.dto.member.MemberPurchaseGoodsIntentionDTO;
import com.cnoocshell.member.dao.vo.MemberPurchaseGoodsIntentionHistory;

import java.util.List;

public interface IMemberPurchaseGoodsIntentionHistoryBiz extends IBaseBiz<MemberPurchaseGoodsIntentionHistory> {
    List<MemberPurchaseGoodsIntentionHistory> listByRequestId(String requestId);

    void saveIntentionHistoryList(List<MemberPurchaseGoodsIntentionDTO> intentionList,
                                  String memberId,
                                  String memberCode,
                                  String requestId,
                                  String operatorId);

    List<MemberPurchaseGoodsIntentionDTO> getByRequestId(String requestId);
}
