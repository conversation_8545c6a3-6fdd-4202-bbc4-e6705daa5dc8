
package com.cnoocshell.member.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;

@Slf4j
public class RequestUtils {

	// 添加私有构造函数以防止实例化
	private RequestUtils() {
		throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
	}

	public static String getPlatformType(HttpServletRequest request) {
		String userAgent = request.getHeader("user-agent");
		
		if(StringUtils.isBlank(userAgent)) {
			log.error("请求中的user-agent为空");
			return "";
		}
		
		if (userAgent.indexOf("Android") != -1) {
			// 安卓
			log.info("请求来之android");
			return "android";
		} else if (userAgent.indexOf("iPhone") != -1 || userAgent.indexOf("iPad") != -1) {
		    // 苹果
			log.info("请求来之ios");
		    return "ios";
		} else {  
			// 电脑
			log.info("请求来之pc");
			return "pc";
		}
	}
	
	public static String getRequestIp(HttpServletRequest request) {
		String ip = request.getRemoteAddr();
		log.info("请求的ip是：" + ip);
		return ip;
	}
	
}
