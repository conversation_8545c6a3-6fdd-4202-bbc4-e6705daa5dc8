package com.cnoocshell.member.controller;

import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.dto.member.enums.AdvertStatusEnum;
import com.cnoocshell.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.cnoocshell.member.api.dto.member.intention.MemberIntentionSimpleDTO;
import com.cnoocshell.member.api.dto.report.MemberAccountReportQueryDTO;
import com.cnoocshell.member.api.dto.report.MemberAccountReportResultDTO;
import com.cnoocshell.member.api.dto.report.MemberReportQueryDTO;
import com.cnoocshell.member.api.dto.report.MemberReportResultDTO;
import com.cnoocshell.member.exception.DuplicateString;
import com.cnoocshell.member.service.IMemberService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;


@Tag(name = "Member", description = "会员对外服务接口")
@Slf4j
@RestController
@RequestMapping("/member")
@RequiredArgsConstructor
public class MemberController {
    private final IMemberService iMemberService;

    @ApiOperation("根据ID查看会员简要信息")
    @PostMapping(value = "/findMemberSimpleById")
    public MemberSimpleDTO findMemberSimpleById(@RequestParam(DuplicateString.MEMBER_ID) String memberId) {
        return iMemberService.findMemberSimpleById(memberId);
    }

    @ApiOperation("分页获取会员请求")
    @PostMapping(value = "/pageRegisterMemberApprovalRequests")
    public PageInfo<MemberApprovalRequestDTO> pageRegisterMemberApprovalRequests(@RequestBody MemberApprovalRequestQueryDTO query,
                                                                                 @RequestParam("pageNum") Integer pageNum,
                                                                                 @RequestParam("pageSize") Integer pageSize) {
        return iMemberService.pageRegisterMemberApprovalRequests(query, pageNum, pageSize);
    }

    @ApiOperation("获取会员请求详情")
    @PostMapping(value = "/getMemberApprovalDetails")
    public MemberDTO getMemberApprovalDetails(@RequestParam("memberApprovalRequestId") String memberApprovalRequestId) {
        return iMemberService.getMemberApprovalDetails(memberApprovalRequestId);
    }

    @ApiOperation("按照id查询会员（只包括已审批的资质）")
    @PostMapping(value = "/findRealMemberById")
    public MemberDTO findRealMemberById(@RequestParam("id") String id) {
        return iMemberService.findRealMemberById(id);
    }

    @ApiOperation("根据ID查找会员详情（没有资质信息）")
    @PostMapping(value = "/findMemberDetailById")
    public MemberDTO findMemberDetailById(@RequestParam(DuplicateString.MEMBER_ID) String memberId) {
        return iMemberService.findMemberDetailById(memberId);
    }

    @ApiOperation("根据账户ID查找实名认证详情")
    @PostMapping(value = "/findRealNameCertByAccountId")
    public MemberCertDTO findRealNameCertByAccountId(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId) {
        return iMemberService.findRealNameCertByAccountId(accountId);
    }

    @ApiOperation("提交企业注册（买家） requestType - 申请类型 必须指明每个资质的资质类型，是经营资质、卖家资质、承运商资质。。。")
    @PostMapping(value = "/registerBuyer")
    public ItemResult<String> registerBuyer(@RequestBody MemberRequestDTO memberRequestDTO) {
        try{
            return ItemResult.success(iMemberService.registerBuyer(memberRequestDTO));
        }catch (BizException e){
            return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        }catch (Exception e){
            log.error("提交企业注册 registerBuyer error:",e);
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(),"提交企业注册失败");
        }
    }

    @ApiOperation("分页获取会员请求")
    @PostMapping(value = "/pageMemberApprovalRequests")
    public PageInfo<MemberApprovalRequestDTO> pageMemberApprovalRequests(@RequestBody MemberApprovalRequestQueryDTO query,
                                                                         @RequestParam("pageNum") Integer pageNum,
                                                                         @RequestParam("pageSize") Integer pageSize) {
        return iMemberService.pageMemberApprovalRequests(query, pageNum, pageSize);
    }

    @ApiOperation("审批请求")
    @PostMapping(value = "/approveRequest")
    public void approveRequest(@RequestBody ApproveRequestDTO dto) {
        iMemberService.approveRequest(dto);
    }

    @ApiOperation("拒绝请求")
    @PostMapping(value = "/rejectRequest")
    public void rejectRequest(@RequestBody RejectRequestDTO dto) {
        iMemberService.rejectRequest(dto);
    }

    @ApiOperation("更新会员基本信息")
    @PostMapping(value = "/updateBaseInfo")
    public void updateBaseInfo(@RequestBody MemberBaseInfoDTO memberBaseInfoDTO) {
        iMemberService.updateBaseInfo(memberBaseInfoDTO);
    }

    @ApiOperation("分页查询会员")
    @PostMapping(value = "/pageMemberListView")
    public PageInfo<MemberListViewDTO> pageMemberListView(@RequestBody MemberQueryDTO query,
                                                          @RequestParam("pageNum") Integer pageNum,
                                                          @RequestParam("pageSize") Integer pageSize) {
        return iMemberService.pageMemberListView(query, pageNum, pageSize);
    }

    @ApiOperation("禁用会员")
    @PostMapping(value = "/disableMember")
    public void disableMember(@RequestParam(DuplicateString.MEMBER_ID) String memberId,
                              @RequestParam("operatorId") String operatorId) {
        iMemberService.disableMember(memberId, operatorId);
    }

    @ApiOperation("启用会员")
    @PostMapping(value = "/enableMember")
    public void enableMember(@RequestParam(DuplicateString.MEMBER_ID) String memberId,
                             @RequestParam("operatorId") String operatorId) {
        iMemberService.enableMember(memberId, operatorId);
    }

    @ApiOperation("按照id查询会员（包括资质）")
    @GetMapping(value = "/findMemberById")
    public MemberDetailDTO findMemberById(@RequestParam("id") String id) {
        return iMemberService.findMemberById(id);
    }

    @ApiOperation("变更企业经营信息")
    @PostMapping(value = "/updateBusinessInfo")
    public String updateBusinessInfo(@RequestBody MemberBusinessInfoDTO memberBusinessInfoDTO) {
        return iMemberService.updateBusinessInfo(memberBusinessInfoDTO);

    }

    @ApiOperation("一般资质变更,必须写明资质类型 certType、CertId")
    @PostMapping(value = "/updateCert")
    public String updateCert(@RequestBody UpdateCertDTO dto) {
        return iMemberService.updateCert(dto);
    }

    @ApiOperation("获取一条变更记录")
    @PostMapping(value = "/findMemberApprovalRequest")
    public MemberApprovalRequestDTO findMemberApprovalRequest(@RequestParam("requestId") String requestId) {
        return iMemberService.findMemberApprovalRequest(requestId);
    }

    @ApiOperation("获取经营信息审批详情")
    @PostMapping(value = "/getMemberApprovalBusinessDetails")
    public MemberBusinessRequestDetailDTO getMemberApprovalBusinessDetails(@RequestParam("requestId") String requestId) {
        return iMemberService.getMemberApprovalBusinessDetails(requestId);
    }

    @ApiOperation("获取会员商品购买意向")
    @GetMapping("/getIntentionsByMemberId")
    public List<MemberPurchaseGoodsIntentionDTO> getIntentionsByMemberId(@RequestParam("memberId") String memberId) {
        return iMemberService.getIntentionsByMemberId(memberId);
    }

    @ApiOperation("获取会员变更商品购买意向明细")
    @GetMapping("/getIntentionsByRequestId")
    List<MemberPurchaseGoodsIntentionDTO> getIntentionsByRequestId(@RequestParam("requestId") String requestId){
        return iMemberService.getIntentionsByRequestId(requestId);
    }

    @ApiOperation("维护会员购买商品意向销售信息")
    @PostMapping("/maintainMemberGoodsIntention")
    ItemResult<Boolean> maintainMemberGoodsIntention(@RequestBody SubmitMemberPurchaseGoodsIntentionDTO param) {
        try {
            if (!iMemberService.maintainMemberGoodsIntention(param))
                return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "维护会员购买商品意向销售信息失败");

        } catch (Exception e) {
            log.error("维护会员购买商品意向销售信息 错误:",e);
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "维护会员购买商品意向销售信息失败");
        }
        return ItemResult.success(Boolean.TRUE);
    }

    @ApiOperation("审核通过会员商品意向信息变更")
    @PostMapping("/passMemberGoodsIntention")
    ItemResult<Boolean> passMemberGoodsIntention(@RequestBody SubmitMemberPurchaseGoodsIntentionDTO param) {
        try {
            if (!iMemberService.passMemberGoodsIntention(param))
                return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "审核失败");

        } catch (Exception e) {
            log.error("审核通过会员商品意向信息变更 错误:",e);
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "审核失败");
        }
        return ItemResult.success(Boolean.TRUE);
    }

    @ApiOperation("买家审批通过")
    @PostMapping(value = "/approveRequestByBuyerRegister")
    ItemResult<Boolean> approveRequestByBuyerRegister(@RequestBody ApproveRequestDTO param){
        try {
            if (!iMemberService.approveRequestByBuyerRegister(param))
                return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "买家企业注册审核失败");

        } catch (Exception e) {
            log.error("买家企业注册审核 错误:",e);
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "买家企业注册审核失败");
        }
        return ItemResult.success(Boolean.TRUE);
    }

    @ApiOperation("提交会员商品意向数据变更请求")
    @PostMapping(value = "/submitMemberIntentionChange")
    ItemResult<Boolean> submitMemberIntentionChange(@RequestBody SubmitMemberPurchaseGoodsIntentionDTO param){
        try {
            if(BooleanUtil.isTrue(iMemberService.existRequest(param.getMemberId(), ApproveRequestTypeEnum.CHANGE_GOODS_INTENTION, Arrays.asList(AdvertStatusEnum.NEW_REQUEST)))){
                throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR,"存在未审批的购买商品意向变更请求");
            }
            if (!iMemberService.submitMemberIntentionChange(param))
                return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "提交会员商品意向数据变更请求失败");

        } catch (BizException e) {
            log.error("提交会员商品意向数据变更请求 业务校验错误：",e);
            return ItemResult.fail(e.getErrorCode().getCode(),e.getOnlyMessage());
        } catch (Exception e) {
            log.error("提交会员商品意向数据变更请求 错误:",e);
            return ItemResult.fail(MemberCode.MEMBER_OTHER_ERROR.getCode(), "提交会员商品意向数据变更请求失败");
        }
        return ItemResult.success(Boolean.TRUE);
    }

    @ApiOperation("根据会员ID查找会员代码")
    @GetMapping(value="/findMemberCodeByMemberId")
    public String findMemberCodeByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
        return iMemberService.findMemberCodeByMemberId(memberId);
    }

    @ApiOperation("根据意向商品ids获取用户信息")
    @PostMapping(value="/findMemberInfoByGoodsIds")
    public List<MemberPurchaseGoodsIntentionDTO> findMemberInfoByGoodsIds(@RequestBody List<String> goodsIds){
        return iMemberService.findMemberInfoByGoodsIds(goodsIds);
    }

    @ApiOperation("按照ids查询会员（包括资质）")
    @PostMapping(value = "/findMemberByIds")
    public List<MemberDetailDTO> findMemberByIds(@RequestBody List<String> memberIds) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return Collections.emptyList();
        }
        return iMemberService.findMemberByIds(memberIds);
    }

    @ApiOperation("根据会员编码获取会员主账户消息")
    @PostMapping(value = "/listAccountsByMemberCodes")
    public List<AccountSimpleDTO> listAccountsByMemberCodes(@RequestBody QueryMemberAccountDTO param){
        return iMemberService.listAccountsByMemberCodes(param);
    }

    @ApiOperation("根据会员编码获取会员信息")
    @PostMapping(value = "/listSimpleMemberByCodes")
    List<MemberSimpleDTO> listSimpleMemberByCodes(@RequestBody List<String> memberCodes){
        return iMemberService.listSimpleMemberByCodes(memberCodes);
    }

    @GetMapping(value = "/findMembersBySalesUserIdAndGoodsCode")
    public List<MemberPurchaseGoodsIntentionDTO> findMembersBySalesUserIdAndGoodsCode(@RequestParam String goodsId, @RequestParam String salesUserId) {
        return iMemberService.findMembersBySalesUserIdAndGoodsCode(goodsId, salesUserId);
    }

    @GetMapping(value = "/queryMemberInfo")
    public List<MemberDataInfoDTO> queryMemberInfo(@RequestParam String goodsCode) {
        return iMemberService.queryMemberInfo(goodsCode);
    }

    @ApiOperation("根据商品和销售人员ID查询匹配会员意向信息")
    @PostMapping(value = "/queryMemberBySaleInfo")
    List<MemberPurchaseGoodsIntentionDTO> queryMemberBySaleInfo(@RequestBody QueryIntentionInfoDTO param){
        return iMemberService.queryMemberBySaleInfo(param);
    }

    @ApiModelProperty("根据商品code查找会员信息")
    @PostMapping(path = "/queryMemberInfoByGoodsCode")
    List<MemberInfoForGoodsDTO> queryMemberInfoByGoodsCode(@RequestBody MemberQueryByGoodsDTO memberQueryByGoodsDTO){
        return iMemberService.queryMemberInfoByGoodsCode(memberQueryByGoodsDTO);
    }

    @ApiOperation("根据会员code查找意向商品")
    @GetMapping(value = "/queryGoodsByMemberCode")
    public List<MemberGoodsInfoDTO> queryGoodsByMemberCode(@RequestParam List<String> memberCodeList){
        return iMemberService.queryGoodsByMemberCode(memberCodeList);
    }

    @ApiOperation("查询企业用户(报表使用)")
    @PostMapping(value = "/queryMemberByMemberNameAndCrmCode")
    public PageInfo<MemberSimpleDataDTO> queryMemberByMemberNameAndCrmCode(@RequestBody MemberSimpleDataDTO memberSimpleDataDTO) {
        return iMemberService.queryMemberByMemberNameAndCrmCode(memberSimpleDataDTO);
    }

    @ApiOperation("企业保证金状态列表查询接口")
    @PostMapping(value = "/queryMemberDepositStatus")
    public PageInfo<MemberDepositStatusDataDTO> queryMemberDepositStatus(@RequestBody MemberDepositStatusDTO dto) {
        return iMemberService.queryMemberDepositStatus(dto);
    }

    @ApiOperation("企业保证金状态保存接口")
    @PostMapping(value = "/saveMemberDepositStatus")
    public Boolean saveMemberDepositStatus(@RequestBody MemberSaveDepositStatusDTO dto) {
        return iMemberService.saveMemberDepositStatus(dto);
    }

    @ApiOperation("企业保证金状态列表导出接口")
    @PostMapping(value = "/exportMemberDepositStatus")
    public ExportExcelDTO exportMemberDepositStatus(@RequestBody MemberDepositStatusDTO dto) {
        return iMemberService.exportMemberDepositStatus(dto);
    }

    @ApiOperation("企业保证金状态列表导出接口数据")
    @PostMapping(value = "/exportMemberDepositStatusDataList")
    public List<MemberDepositStatusExportDTO> exportMemberDepositStatusDataList(@RequestBody MemberDepositStatusDTO dto) {
        return iMemberService.exportMemberDepositStatusDataList(dto);
    }

    @ApiOperation("企业管理员申请退款接口")
    @PostMapping(value = "/requestRefund")
    public Boolean requestRefund(@RequestBody MemberRefundDepositStatusDTO dto) {
        return iMemberService.requestRefund(dto);
    }

    @ApiOperation("企业保证金状态导入接口")
    @PostMapping(value = "/import/excel")
    public ItemResult<MemberDepositStatusImportResultDTO>  importExcel( @RequestBody MemberDepositStatusImportDataDTO dto) {
        return iMemberService.importExcel(dto);
    }

    @ApiOperation("查询企业保证金状态接口")
    @GetMapping(value = "/query/deposit/status")
    public String  queryDepositStatus(@RequestParam("memberCode") String memberCode) {
        return iMemberService.queryDepositStatus(memberCode);
    }

    @ApiOperation("查询goodsCode")
    @GetMapping(value = "/query/queryGoodsCodeByMemberId")
    public List<String>  queryGoodsCodeByMemberId(@RequestParam("memberId") String memberId) {
        return iMemberService.queryGoodsCodeByMemberId(memberId);
    }

    @ApiOperation("会员是否存在企业注册记录")
    @GetMapping(value = "/existEnterpriseRegistrationRequest")
    Boolean existEnterpriseRegistrationRequest(@RequestParam("memberId") String memberId,
                                               @RequestParam(value = "status",required = false) String status){
        return iMemberService.existEnterpriseRegistrationRequest(memberId,status);
    }

    @ApiOperation("查询可参与竞价其它客户")
    @PostMapping("/queryBiddingMemberList")
    List<BiddingMemberDTO> queryBiddingMemberList(@RequestBody QueryBiddingMemberDTO param){
        return iMemberService.queryBiddingMemberList(param);
    }

    @GetMapping("/queryMemberByLikeCrmCode")
    List<MemberSimpleDTO> queryMemberByLikeCrmCode(@RequestParam String crmCode){
        return iMemberService.queryMemberByLikeCrmCode(crmCode);
    }

    @ApiOperation("更加memberCode查询会员信息 根据goodsCode查询会员意向信息")
    @PostMapping("/queryMemberIntentionInfo")
    List<MemberIntentionInfoDTO> queryMemberIntentionInfo(@RequestBody QueryMemberIntentionInfoDTO param){
        return iMemberService.queryMemberIntentionInfo(param);
    }

    @ApiOperation("客户报表 列表查询")
    @PostMapping("/memberReport")
    public PageInfo<MemberReportResultDTO> memberReport(@RequestBody MemberReportQueryDTO param){
        return iMemberService.memberReport(param);
    }

    @ApiOperation("客户账号报表 列表查询")
    @PostMapping("/memberAccountReport")
    public PageInfo<MemberAccountReportResultDTO> memberAccountReport(@RequestBody MemberAccountReportQueryDTO param) {
        return iMemberService.memberAccountReport(param);
    }

    @ApiOperation("根据销售渠道编码查询匹配的意向信息")
    @PostMapping("/queryIntentionBySaleChannel")
    public List<MemberIntentionSimpleDTO> queryIntentionBySaleChannel(@RequestBody List<String> saleChannels){
        return iMemberService.queryIntentionBySaleChannel(saleChannels);
    }
}