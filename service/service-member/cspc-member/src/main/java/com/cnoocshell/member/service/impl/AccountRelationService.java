package com.cnoocshell.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.cnoocshell.base.api.dto.role.RolePageReponseDTO;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationInfoDTO;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationPage;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationPageRequestDTO;
import com.cnoocshell.member.api.dto.accountRelation.QueryAccountRelationDTO;
import com.cnoocshell.member.biz.impl.AccountRelationBiz;
import com.cnoocshell.member.dao.mapper.AccountRelationMapper;
import com.cnoocshell.member.dao.vo.AccountRelation;
import com.cnoocshell.member.exception.DuplicateString;
import com.cnoocshell.member.service.IAccountRelationService;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AccountRelationService implements IAccountRelationService {

    public static final String ACCOUNT_ID = "accountId";
    public static final String LINK_ACCOUNT_ID = "linkAccountId";
    @Autowired
    private AccountRelationBiz accountRelationBiz;

    @Autowired
    private AccountRelationMapper accountRelationMapper;

    @Autowired
    private UUIDGenerator uuidGenerator;


    @Override
    public PageInfo<AccountRelationPage> findAccountRelationByCondition(AccountRelationPageRequestDTO requestDTO) {
        PageInfo<AccountRelationPage> resDTO = new PageInfo<>();
        PageInfo<AccountRelation> accountRelationPageInfo = accountRelationBiz.findAccountRelationByCondition(requestDTO);
        if (null != accountRelationPageInfo) {
            BeanUtils.copyProperties(accountRelationPageInfo, resDTO);
            if (CollectionUtils.isNotEmpty(accountRelationPageInfo.getList())) {
                List<AccountRelationPage> pageList = accountRelationPageInfo.getList().stream()
                        .map(role -> {
                            AccountRelationPage res = new AccountRelationPage();
                            BeanUtils.copyProperties(role, res);
                            return res;
                        }).collect(Collectors.toList());
                resDTO.setList(pageList);
            }
        }
        return resDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addAccountRelation(AccountRelationInfoDTO requestDTO) {
        //对code进行重复校验
        Condition condition = new Condition(AccountRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(ACCOUNT_ID, requestDTO.getAccountId());
        criteria.andEqualTo(LINK_ACCOUNT_ID,requestDTO.getLinkAccountId());
        criteria.andEqualTo(DuplicateString.DEL_FLG,false);
        int count = accountRelationMapper.selectCountByCondition(condition);
        if (count > 0) {
            return false;
        }
        AccountRelation accountRelation = new AccountRelation();
        BeanUtils.copyProperties(requestDTO,accountRelation);
        accountRelation.setCreateTime(new Date());
        accountRelation.setDelFlg(false);
        accountRelation.setCreateUser("");
        accountRelation.setId(uuidGenerator.gain());
        accountRelationMapper.insert(accountRelation);
        return true;
    }

    @Override
    public int deleteAccountRelation(String id) {
        return accountRelationMapper.deleteAccountRelation(id);
    }

    @Override
    public List<AccountRelationInfoDTO> queryAccountRelation(QueryAccountRelationDTO param) {
        Condition condition = new Condition(AccountRelation.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE);
        CommonUtils.andInIfNotEmpty(criteria,DuplicateString.ACCOUNT_ID,param.getAccountIds());
        CommonUtils.andInIfNotEmpty(criteria,"accountRole",param.getAccountRoles());
        CommonUtils.andInIfNotEmpty(criteria,"linkAccountRole",param.getLinkAccountRoles());

        return BeanUtil.copyToList(accountRelationBiz.findByCondition(condition),AccountRelationInfoDTO.class);
    }
}
