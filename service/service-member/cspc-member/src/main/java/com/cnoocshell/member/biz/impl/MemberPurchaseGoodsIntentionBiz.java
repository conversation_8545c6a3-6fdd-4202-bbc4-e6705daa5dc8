package com.cnoocshell.member.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.goods.api.dto.GoodsCategorySimpleDTO;
import com.cnoocshell.goods.api.dto.GoodsSimpleDTO;
import com.cnoocshell.goods.api.service.IGoodsCategoryService;
import com.cnoocshell.goods.api.service.IGoodsService;
import com.cnoocshell.member.api.dto.member.MemberPurchaseGoodsIntentionDTO;
import com.cnoocshell.member.api.dto.member.intention.MemberIntentionSimpleDTO;
import com.cnoocshell.member.api.dto.report.MemberIntentionReportResultDTO;
import com.cnoocshell.member.api.enums.IntentionTypeEnum;
import com.cnoocshell.member.biz.IMemberPurchaseGoodsIntentionBiz;
import com.cnoocshell.member.dao.mapper.MemberPurchaseGoodsIntentionMapper;
import com.cnoocshell.member.dao.vo.MemberPurchaseGoodsIntention;
import com.cnoocshell.member.exception.DuplicateString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MemberPurchaseGoodsIntentionBiz
        extends BaseBiz<MemberPurchaseGoodsIntention>
        implements IMemberPurchaseGoodsIntentionBiz {
    private final MemberPurchaseGoodsIntentionMapper intentionMapper;
    private final IGoodsCategoryService iGoodsCategoryService;
    private final IGoodsService iGoodsService;

    public List<MemberPurchaseGoodsIntention> listByMemberId(String memberId) {
        Condition condition = new Condition(MemberPurchaseGoodsIntention.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.MEMBER_ID, memberId)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        return this.findByCondition(condition);
    }

    @Override
    public void saveIntentionList(List<MemberPurchaseGoodsIntentionDTO> intentionList,String memberId, String memberCode, String operatorId) {
        MemberPurchaseGoodsIntention remove = new MemberPurchaseGoodsIntention();
        remove.setDelFlg(Boolean.TRUE);
        setOperatorInfo(remove,operatorId,false);

        //先删除
        Condition condition = new Condition(MemberPurchaseGoodsIntention.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andEqualTo(DuplicateString.MEMBER_ID,memberId);
        intentionMapper.updateByConditionSelective(remove,condition);

        //再新增
        this.saveNewIntentionList(intentionList,memberId,memberCode,operatorId);
    }

    @Override
    public void removeByPrimaryKey(List<MemberPurchaseGoodsIntention> intentions) {
        this.removeByIds(intentions.stream().map(v->v.getId()).collect(Collectors.toSet()));
    }

    @Override
    public void removeByIds(Collection<String> ids) {
        if(CollUtil.isEmpty(ids))
            return;
        Condition condition = new Condition(MemberPurchaseGoodsIntention.class);
        condition.createCriteria()
                .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                .andIn(DuplicateString.ID,ids);
        MemberPurchaseGoodsIntention remove = new MemberPurchaseGoodsIntention();
        remove.setDelFlg(Boolean.TRUE);
        intentionMapper.updateByConditionSelective(remove,condition);
    }

    @Override
    public void handleNameByIntentions(List<MemberPurchaseGoodsIntentionDTO> intentionList) {
        if(CollUtil.isEmpty(intentionList))
            return;
        List<String> goodsIds = intentionList.stream()
                .filter(v->CharSequenceUtil.isBlank(v.getGoodsName()))
                .map(v->v.getGoodsId())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        List<String> goodsCategoryIds = intentionList.stream()
                .filter(v->CharSequenceUtil.isBlank(v.getGoodsCategoryName()))
                .map(v->v.getGoodsCategoryId())
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, GoodsSimpleDTO> goodsMap = null;
        Map<String,GoodsCategorySimpleDTO> goodsCategoryMap = null;
        if(CollUtil.isNotEmpty(goodsIds)) {
            goodsMap = CommonUtils.getMap(iGoodsService.findGoodsSimpleByIds(goodsIds),GoodsSimpleDTO::getGoodsId);
        }
        if(CollUtil.isNotEmpty(goodsCategoryIds)) {
            goodsCategoryMap = CommonUtils.getMap(iGoodsCategoryService.findCategorySimpleByIds(goodsCategoryIds), GoodsCategorySimpleDTO::getCategoryId);
        }
        for (MemberPurchaseGoodsIntentionDTO v : intentionList) {
            GoodsSimpleDTO goods = CommonUtils.getByKey(goodsMap,v.getGoodsId());
            if(Objects.nonNull(goods))
                v.setGoodsName(goods.getGoodsName());
            GoodsCategorySimpleDTO goodsCategory = CommonUtils.getByKey(goodsCategoryMap,v.getGoodsCategoryId());
            if(Objects.nonNull(goodsCategory))
                v.setGoodsCategoryName(goodsCategory.getCategoryName());
        }
    }

    @Override
    public void saveNewIntentionList(List<MemberPurchaseGoodsIntentionDTO> intentionList, String memberId, String memberCode, String operatorId) {
        if(CollUtil.isEmpty(intentionList))
            return;
        List<MemberPurchaseGoodsIntention> saveList = intentionList.stream().map(v->{
            MemberPurchaseGoodsIntention target = BeanUtil.toBean(v,MemberPurchaseGoodsIntention.class);
            target.setId(super.getUuidGeneratorGain());
            target.setMemberId(memberId);
            target.setMemberCode(memberCode);

            setOperatorInfo(target,operatorId,true);
            return target;
        }).collect(Collectors.toList());

        this.insertList(saveList);
    }

    @Override
    public void updateIntentionType(List<MemberPurchaseGoodsIntention> intentions, String operatorId) {
        if (CollUtil.isEmpty(intentions))
            return;
        List<String> mainIds = intentions.stream()
                .filter(v -> Objects.equals(IntentionTypeEnum.MAIN.getType(), v.getIntentionType()))
                .map(v -> v.getId())
                .collect(Collectors.toList());
        List<String> secondaryIds = intentions.stream()
                .filter(v -> Objects.equals(IntentionTypeEnum.SECONDARY.getType(), v.getIntentionType()))
                .map(v -> v.getId())
                .collect(Collectors.toList());

        //更新为主要
        this.updateIntentionTypeByIds(mainIds, IntentionTypeEnum.MAIN, operatorId);
        //更新为次要
        this.updateIntentionTypeByIds(secondaryIds, IntentionTypeEnum.SECONDARY, operatorId);
    }

    @Override
    public List<MemberIntentionReportResultDTO> queryMemberIntentionReport(List<String> memberIds, List<String> goodsCodes) {
        return intentionMapper.queryMemberIntentionReport(memberIds, goodsCodes);
    }

    @Override
    public List<String> queryMemberIdConcatGoodsCodeBySaleChannel(List<String> memberIds, List<String> saleChannels) {
        return intentionMapper.queryMemberIdConcatGoodsCodeBySaleChannel(memberIds,saleChannels);
    }

    @Override
    public List<MemberIntentionSimpleDTO> queryIntentionBySaleChannel(List<String> saleChannels) {
        if(CollUtil.isEmpty(saleChannels))
            return Collections.emptyList();

        return intentionMapper.queryIntentionBySaleChannel(saleChannels);
    }

    private void updateIntentionTypeByIds(List<String> ids, IntentionTypeEnum intentionType, String operatorId) {
        if (CollUtil.isEmpty(ids))
            return;
        MemberPurchaseGoodsIntention intention = new MemberPurchaseGoodsIntention();
        intention.setIntentionType(intentionType.getType());
        intention.handleUser(operatorId, false);
        Condition condition = new Condition(MemberPurchaseGoodsIntention.class);
        condition.createCriteria().andIn(DuplicateString.ID, ids);

        intentionMapper.updateByConditionSelective(intention, condition);
    }
}
