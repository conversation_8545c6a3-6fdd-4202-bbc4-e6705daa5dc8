package com.cnoocshell.member.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.dao.mapper.AccountRelationMapper;
import com.cnoocshell.member.dao.vo.AccountRelation;
import com.cnoocshell.member.exception.DuplicateString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountConsumer {
    private final AccountRelationMapper accountRelationMapper;

    @KafkaListener(topics = MqTopicConstant.ACCOUNT_CHANGE_TOPIC)
    public void accountChangeConsumer(ConsumerRecord<String, String> data) {
        if (Objects.isNull(data) || CharSequenceUtil.isBlank(data.value())) {
            log.info("账户信息变更 入参数据为空");
            return;
        }
        log.info("账户信息变更 入参数据：{}", data.value());
        try {
            AccountSimpleDTO accountSimple = JSONUtil.toBean(data.value(), AccountSimpleDTO.class);
            if (Objects.isNull(accountSimple) || CharSequenceUtil.isBlank(accountSimple.getAccountId())) {
                log.info("账户信息变更 入参数据为空");
                return;
            }
            AccountRelation mainRelation = new AccountRelation();
            AccountRelation linkRelation = new AccountRelation();
            if (CharSequenceUtil.isNotBlank(accountSimple.getRealName())) {
                mainRelation.setAccountRealName(accountSimple.getRealName());
                linkRelation.setLinkAccountRealName(accountSimple.getRealName());
            }

            Condition mainCondition = new Condition(AccountRelation.class);
            mainCondition.createCriteria()
                    .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                    .andEqualTo(DuplicateString.ACCOUNT_ID, accountSimple.getAccountId());
            Condition linkCondition = new Condition(AccountRelation.class);
            linkCondition.createCriteria()
                    .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                    .andEqualTo(DuplicateString.LINK_ACCOUNT_ID, accountSimple.getAccountId());

            //更新关联主账户名称
            accountRelationMapper.updateByConditionSelective(mainRelation, mainCondition);
            //更新关联账户名称
            accountRelationMapper.updateByConditionSelective(linkRelation, linkCondition);
        } catch (Exception e) {
            log.error("账户信息变更  更新账户关联信息失败 error:{}", e);
        }
    }
}
