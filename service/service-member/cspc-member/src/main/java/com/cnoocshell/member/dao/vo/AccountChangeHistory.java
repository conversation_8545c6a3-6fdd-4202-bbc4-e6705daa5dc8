package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "mb_account_change_history")
@EqualsAndHashCode(callSuper = true)
public class AccountChangeHistory extends BaseEntity {
    /**
     * ID
     */
    @Id
    @Column(name = "account_history_id")
    private String accountHistoryId;

    /**
     * 变更会员ID
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 变更会员真实姓名
     */
    @Column(name = "real_name")
    private String realName;

    /**
     * 操作人真实姓名
     */
    @Column(name = "operatro_name")
    private String operatroName;

    /**
     * 变更方式（授权，启用，禁用）
     */
    private String way;

    /**
     * 账号角色ID集合
     */
    @Column(name = "role_id_list")
    private String roleIdList;

    /**
     * 修改原因
     */
    private String reason;

    /**
     * 是否生效
     */
    @Column(name = "is_effect")
    private Integer isEffect;

    /**
     * 是否立即生效
     */
    @Column(name = "is_immediate_effect")
    private Integer isImmediateEffect;

    /**
     * 生效时间
     */
    @Column(name = "effect_time")
    private Date effectTime;
}