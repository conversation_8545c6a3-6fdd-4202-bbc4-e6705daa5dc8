package com.cnoocshell.member.biz.impl;

import com.cnoocshell.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Service;

/**
 * @Author: <EMAIL>
 * @Date: 13/09/2018 17:00
 * @DESCRIPTION:
 */
@Service
public class MemberApproveRequestIncrementIdGenerator extends AbstractIBusinessIdGenerator {

    @Override
    public String businessCodePrefix() {
        return super.gainDateString();
    }
}
