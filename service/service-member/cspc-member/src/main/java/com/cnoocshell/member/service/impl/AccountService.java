package com.cnoocshell.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.cnoocshell.base.api.dto.account.QueryAccountRoleDTO;
import com.cnoocshell.base.api.dto.account.SimpleAccountRoleDTO;
import com.cnoocshell.base.api.enums.BaseRoleTypeEnum;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.annotation.AddLog;
import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.redis.UserInfoKeys;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.utils.MD5;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.member.annotation.LoginLog;
import com.cnoocshell.member.api.dto.account.*;
import com.cnoocshell.member.api.dto.account.checkCode.CheckEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.CheckSMSCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetSMSCodeDTO;
import com.cnoocshell.member.api.dto.base.RoleDTO;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.api.dto.member.enums.AccountTypeEnum;
import com.cnoocshell.member.biz.*;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.member.dao.mapper.AccountMapper;
import com.cnoocshell.member.dao.mapper.MemberMapper;
import com.cnoocshell.member.dao.vo.Account;
import com.cnoocshell.member.dao.vo.Member;
import com.cnoocshell.member.dao.vo.MemberPurchaseGoodsIntention;
import com.cnoocshell.member.exception.DuplicateString;
import com.cnoocshell.member.service.IAccountService;
import com.cnoocshell.member.service.IPasswordService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @DESCRIPTION:账户对外服务实现
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AccountService implements IAccountService {
    private final IAccountBiz accountBiz;
    private final IPasswordService passwordService;
    private final IVerificationCodeBiz verificationCodeBiz;
    private final IRoleService roleService;
    private final IAccountSessionBiz accountSessionBiz;
    private final BizRedisService bizRedisService;
    private final MemberMapper memberMapper;

    private final AccountMapper accountMapper;
    private final IMemberBiz iMemberBiz;
    private final ISmsSendService iSmsSendService;
    //todo 发送短信
//    private final SMSMessageProducerImpl sMSMessageProducerImpl;

    @AddLog(encryptedFieldName = "password", operatorIPFieldName = "ip")
    @LoginLog
    @Override
    public AccountDTO loginVerification(AccountLoginDTO accountLoginDTO) {
        AccountDTO accountDTO = accountBiz.loginVerification(accountLoginDTO);
        return accountDTO;
    }

    @AddLog(operatorIPFieldName = "ip", sessionIdFieldName = "sessionId")
    @LoginLog
    @Override
    public AccountDTO loginByPhoneVerificationCode(PhoneCodeLoginDTO phoneCodeLoginDTO) {
        return accountBiz.loginByPhoneVerificationCode(phoneCodeLoginDTO);
    }

    @Override
    public boolean checkMobilePhoneExistsByPersonl(String phoneNumber) {
        isBlank(phoneNumber, "手机号");
        return accountBiz.checkMobilePhoneExists(phoneNumber, AccountTypeEnum.PERSONAL_ACCOUNT.getAccountType());
    }

    @Override
    public boolean checkMobilePhoneExistsByMasterAccount(String phoneNumber) {
        return accountBiz.checkMobilePhoneExists(phoneNumber, AccountTypeEnum.ENTERPRISE_MASTER_ACCOUNT.getAccountType());
    }

    @Override
    public Boolean checkSMSCode(CheckSMSCodeDTO dto) {
        verificationCodeBiz.checkSMSCode(dto);
        return true;
    }

    @AddLog(encryptedFieldName = {"password"}, operatorIPFieldName = "registerIP")
    @Override
    public AccountDTO registerAccountByPC(AccountPCRegisterDTO dto) {
        if (StringUtils.isBlank(dto.getSmsCode())) {
            throw new MemberBizException(MemberCode.VALIDATION_ERROR, "验证码不可为空");
        }
        Account account = dto2Vo(dto);
        AccountDTO accountDTO = vo2Dto(accountBiz.registerAccount(account));

        return accountDTO;
    }

    @Override
    public boolean checkAccountNameExists(String newAccountName) {
        if (StringUtils.isBlank(newAccountName)) {
            return false;
        }
        isBlank(newAccountName, "账户名称");
        return accountBiz.checkAccountNameExists(newAccountName);
    }

    @Override
    public AccountDTO findById(String accountId) {
        if (StringUtils.isBlank(accountId)) {
            return null;
        }
        return vo2Dto(accountBiz.findById(accountId));
    }

    @Override
    public AccountDTO findByEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return null;
        }
        return vo2Dto(accountBiz.findByEmail(email));
    }

    @Override
    public AccountSimpleDTO findSimpleById(String accountId) {
        if (StringUtils.isBlank(accountId)) {
            return null;
        }
        return BeanUtil.toBean(accountBiz.findById(accountId), AccountSimpleDTO.class);
    }

    /**
     * 根据手机号查找账号，并根据登陆类型（即登陆所在app）过滤账号
     *
     * @param mobilePhoneNumber 手机号
     * @param loginType
     * @return
     */
    @Override
    public List<AccountDTO> findByMobilePhoneAndLoginType(String mobilePhoneNumber, String loginType) {
        log.info("findByMobilePhoneAndLoginType:{},{}", mobilePhoneNumber, loginType);
        List<AccountDTO> list = findByMobilePhone(mobilePhoneNumber);
        log.info("findByMobilePhoneAndLoginType,findByMobilePhone accountList:{}", list == null || list.isEmpty() ? "" : JSON.toJSONString(list.stream().map(AccountDTO::getAccountName).collect(Collectors.toSet())));
        if (list == null || list.isEmpty() || StringUtils.isBlank(loginType)) {
            return list;
        }
        //查询账号能登录的平台有哪些
        Map<String, Set<String>> accountPlatformSet = roleService.getPlatformByAccountIds(list.stream().map(AccountDTO::getAccountId).collect(Collectors.toSet()));
        log.info("findByMobilePhoneAndLoginType getPlatformByAccountIds:{}", JSON.toJSONString(accountPlatformSet));
        list = list.stream().filter(item -> accountPlatformSet.containsKey(item.getAccountId()) && accountPlatformSet.get(item.getAccountId()).contains(loginType)).collect(Collectors.toList());
        log.info("findByMobilePhoneAndLoginType,account:{}", list.stream().map(AccountDTO::getAccountName).collect(Collectors.toSet()));
        return list;
    }

    /**
     * 根据手机号查询账户信息
     *
     * @param mobilePhoneNumber 手机号
     * @return AccountDTO    单个账户信息
     */
    @Override
    public List<AccountDTO> findByMobilePhone(String mobilePhoneNumber) {
        if (StringUtils.isBlank(mobilePhoneNumber)) {
            return null;
        }
        return vo2Dto(accountBiz.findByMobilePhone(mobilePhoneNumber, null));
    }

    @Override
    public Boolean checkEmailCode(CheckEmailCodeDTO dto) {
        return verificationCodeBiz.checkEmailCode(dto);
    }

    @Override
    public boolean checkMobilePhoneExists(String phoneNumber) {
        isBlank(phoneNumber, "手机号");
        return accountBiz.checkMobilePhoneExists(phoneNumber, null);
    }

    @Override
    public String getSMSCode(GetSMSCodeDTO dto) {
        return verificationCodeBiz.getSMSCode(dto);
    }

    @AddLog(operatorIndex = 2, operatorIPIndex = 3)
    @Override
    public void updateMobilePhone(String accountId, String newMobilePhone, String operator, String ip) {
        isBlank(accountId, "账户id");
        isBlank(newMobilePhone, "手机号");

        Account account = accountBiz.findById(accountId);
        if (Objects.isNull(account)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "原账号不存在或已被删除");
        }
        if (Objects.equals(newMobilePhone, account.getMobile())) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "新手机号与原手机号一致");
        }
        Condition condition = accountBiz.newCondition();
        condition.and()
                .andEqualTo("mobile", newMobilePhone)
                .andEqualTo("delFlg", false);
        List<Account> accounts = accountBiz.findByCondition(condition);
        if (accounts.size() != 0) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "新手机号已注册");
        }
        accountBiz.updateMobilePhone(accountId, newMobilePhone, operator);
        accountSessionBiz.updateSessionLoginInfoMobile(accountId, newMobilePhone);
        //todo 发送短信提示
//        sMSMessageProducerImpl.sendUpdatePhone(account.getAccountId(), newMobilePhone.substring(newMobilePhone.length() - 4), account.getRegisterSign());
    }

    @Override
    public String getEmailCode(GetEmailCodeDTO dto) {
        return verificationCodeBiz.getEmailCode(dto);
    }

    @AddLog(operatorFieldName = "operator", operatorIPFieldName = "operatorIP")
    @Override
    public void updateBaseInfo(AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO) {
        if (accountBaseInfoUpdateDTO == null) {
            log.info("accountBaseInfoUpdateDTO is null.");
            return;
        }
        Account account = dto2Vo(accountBaseInfoUpdateDTO);
        Account account2 = accountBiz.findById(accountBaseInfoUpdateDTO.getAccountId());
        Account account3 = accountBiz.updateBaseInfo(account, accountBaseInfoUpdateDTO.getOperator());
        accountSessionBiz.updateSessionLoginInfoAccountInfo(account3);
        if (account2 != null) {
            //todo 发送短信提示
            //sMSMessageProducerImpl.sendUpdateAccountInfo(account2.getMemberId(), account2.getAccountId(), account2.getAccountName(), account.getRegisterSign());
            bizRedisService.del(UserInfoKeys.USER_INFO_KEY + account2.getAccountId());
        }
    }

    @AddLog(operatorIndex = 2, operatorIPIndex = 3)
    @Override
    public void updateAccountName(String accountId, String newUserName, String operator, String ip) {
        isBlank(accountId, "账户id");
        isBlank(newUserName, "账户名称");
        Account account = accountBiz.findById(accountId);
        accountBiz.updateAccountName(accountId, newUserName, operator);
        accountSessionBiz.updateSessionLoginInfoAccountName(accountId, newUserName);
        if (account != null) {
            //todo 发送短信提示
//            sMSMessageProducerImpl.sendUpdateAccountInfo(account.getMemberId(), account.getAccountId(), account.getAccountName(), account.getRegisterSign());
        }
    }

    @AddLog(encryptedFieldName = {"password"}, operatorFieldName = "operatorId", operatorIPFieldName = "registerIP")
    @Override
    public AccountDTO registerSubAccountByMaster(AccountRegisterByMasterDTO dto) {
        isBlank(dto.getOperatorId(), "操作人");
        SubAccountRegisterDTO subAccountRegisterDTO = new SubAccountRegisterDTO();
        BeanUtils.copyProperties(dto, subAccountRegisterDTO);

        return registerSubAccountAndSendSMSMessage(subAccountRegisterDTO);
    }

    @Override
    public PageInfo<AccountDTO> findAll(AccountSearchDTO accountSearchDTO) {
        if (CharSequenceUtil.isNotBlank(accountSearchDTO.getRoleInfo())) {
            List<SimpleAccountRoleDTO> accountRoles = roleService.listBySimpleQuery(new QueryAccountRoleDTO(accountSearchDTO.getRoleInfo(), null, null));
            if (CollUtil.isEmpty(accountRoles))
                return PageInfo.EMPTY;
            List<String> accountIds = accountRoles.stream().map(v -> v.getAccountId()).distinct().collect(Collectors.toList());
            accountSearchDTO.setAccountIds(accountIds);
        }
        Page<Account> page1 = accountBiz.findAll(accountSearchDTO);
        Page<AccountDTO> page2 = new Page<>(page1.getPageNum(), page1.getPageSize());
        page2.setTotal(page1.getTotal());

        Map<String,List<SimpleAccountRoleDTO>> accountRoleGroup = null;
        if(CollUtil.isNotEmpty(page1.getResult())){
            List<String> accountIds = page1.getResult().stream().map(Account::getAccountId).collect(Collectors.toList());
            QueryAccountRoleDTO queryAccountRoleDTO = new QueryAccountRoleDTO(null, accountIds, null);
            log.info("new QueryAccountRoleDTO {}", queryAccountRoleDTO);
            List<SimpleAccountRoleDTO> simpleAccountRoleDTOS
                    = roleService.listBySimpleQuery(queryAccountRoleDTO);
            accountRoleGroup = CommonUtils.group(simpleAccountRoleDTOS,SimpleAccountRoleDTO::getAccountId);

            for (Account account : page1.getResult()) {
                AccountDTO accountDTO = vo2Dto(account);
                accountDTO.setRoleList(BeanUtil.copyToList(CommonUtils.getByKey(accountRoleGroup,account.getAccountId()),RoleDTO.class));
                page2.add(accountDTO);
            }
        }

        return new PageInfo<>(page2);
    }

    @Override
    public void disabled(String accountId, String reason, String operator) {
        accountBiz.disabled(accountId, reason, operator);
        accountSessionBiz.forceOffLineSync(accountId, null, null, "账户已被管理员禁用", null);
    }

    @Override
    public void enabled(String accountId, String operator) {
        accountBiz.enabled(accountId, operator);
    }

    /**
     * 根据账户名查询单个账户信息
     *
     * @param accountName 账户名(userName)
     * @return AccountDTO    单个账户信息
     */
    @Override
    public AccountDTO findByAccountName(String accountName) {
        if (StringUtils.isBlank(accountName)) {
            return null;
        }
        Account account = accountBiz.findByAccountName(accountName);
        if (account == null) {
            return null;
        }
        AccountDTO accountDTO = vo2Dto(account);

        List<com.cnoocshell.base.api.dto.role.RoleDTO> roleList = roleService.getRoleByAccountId(account.getAccountId());
        accountDTO.setRoleList(BeanUtil.copyToList(roleList,RoleDTO.class));
        return accountDTO;
    }

    /**
     * 根据id查询单个账户信息
     *
     * @param accountId 账户id
     * @return AccountDTO    单个账户信息
     */
    @Override
    public AccountDTO findDetailById(String accountId) {
        return findDetailById(accountId, null);
    }

    @Override
    public AccountDTO findDetailById(String accountId, String memberId) {
        if (StringUtils.isBlank(accountId)) {
            return null;
        }

        Account account = accountBiz.findById(accountId, memberId);
        if (account == null) {
            return null;
        }
        AccountDTO accountDTO = vo2Dto(account);
        //角色
        List<com.cnoocshell.base.api.dto.role.RoleDTO> roleList = roleService.getRoleByAccountId(account.getAccountId());
        accountDTO.setRoleList(BeanUtil.copyToList(roleList,RoleDTO.class));
        return accountDTO;
    }

    @AddLog(operatorFieldName = "operatorId", operatorIPFieldName = "operatorIP")
    @Override
    public void updateSubAccountInfo(SubAccountUpdateDTO subAccountUpdateDTO) {
        Account account = accountBiz.findById(subAccountUpdateDTO.getAccountId());
        Account account2 = accountBiz.updateSubAccountInfo(subAccountUpdateDTO);
        accountSessionBiz.updateSessionLoginInfoAccountInfo(account2);
        if (account != null) {
            bizRedisService.del(UserInfoKeys.USER_INFO_KEY + account.getAccountId());
        }
    }

    @AddLog(operatorFieldName = "operatorId", operatorIPFieldName = "operatorIP")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeAccountType(ChangeAccountTypeDTO changeAccountTypeDTO) {
        String memberId = changeAccountTypeDTO.getMemberId();
        String accountId = changeAccountTypeDTO.getAccountId();
        isBlank(accountId, DuplicateString.ACCOUNT_ID);

        Account newMainAccount = accountBiz.findById(accountId);
        if (newMainAccount == null) {
            throw new MemberBizException(BasicCode.DATA_NOT_EXIST, "账号不存在");
        }
        if (StringUtils.isBlank(memberId)) {
            memberId = newMainAccount.getMemberId();
        }

        //获取主账号
        Member member = memberMapper.findById(memberId);
        if (accountId.equals(member.getMainAccountId())) {
            List<Account> masterAccountList = accountBiz.findByMemberIdAndAccountType(memberId, AccountTypeEnum.ENTERPRISE_MASTER_ACCOUNT.getAccountType());
            if (CollUtil.isNotEmpty(masterAccountList)) {
                masterAccountList.stream().filter(item -> !StringUtils.equals(item.getAccountId(), accountId)).forEach(item -> {
                    item.setAccountType(AccountTypeEnum.ENTERPRISE_SUB_ACCOUNT.getAccountType());
                    accountBiz.updateSelective(item);
                });
            }
            throw new MemberBizException(MemberCode.MEMBER_OTHER_ERROR, "他已经是主账户了");
        }
        Account oldMainAccount = accountBiz.findById(member.getMainAccountId());
        //原来的主账号变为子账号
        oldMainAccount.setAccountType(AccountTypeEnum.ENTERPRISE_SUB_ACCOUNT.getAccountType());
        //被改变的账号变为主账号
        newMainAccount.setAccountType(AccountTypeEnum.ENTERPRISE_MASTER_ACCOUNT.getAccountType());


        // fix bug #8087 如果新主账号没有角色，会导致原主账号既没有角色也没有注册应用，将无法显示在员工列表里
        if (StringUtils.isEmpty(oldMainAccount.getRegisterApp())) {
            oldMainAccount.setRegisterApp(newMainAccount.getRegisterApp());
        }

        Date now = new Date();
        oldMainAccount.setUpdateTime(now);
        accountBiz.updateSelective(oldMainAccount);
        newMainAccount.setUpdateTime(now);
        accountBiz.updateSelective(newMainAccount);
        member.setMainAccountId(accountId);

        //改变会员表中的主账号
        member.setUpdateTime(now);
        iMemberBiz.updateSelective(member);

        //更新主账号角色
        roleService.updateMainAccountRoleType(oldMainAccount.getAccountId(), accountId, accountId,changeAccountTypeDTO.getIsSeller());

        //子、主账号强制退出
        accountSessionBiz.forceOffLineSync(oldMainAccount.getAccountId(),null,null,"主、子账号切换 强制退出",changeAccountTypeDTO.getOperatorId());
        accountSessionBiz.forceOffLineSync(accountId,null,null,"主、子账号切换 强制退出",changeAccountTypeDTO.getOperatorId());
        // 企业管理员变更主账号 发送短信通知相关销售人员 通知买家
        if(!BooleanUtil.isTrue(changeAccountTypeDTO.getIsSeller()))
            notifyByChangeMainAccount(member, newMainAccount);
    }

    private void notifyByChangeMainAccount(Member member, Account newMainAccount) {
        // 企业管理员变更主账号
        List<String> param = List.of(member.getMemberName(), newMainAccount.getRealName(), newMainAccount.getMobile());

        // 企业关联销售人员手机号
        List<MemberPurchaseGoodsIntention> memberPurchaseGoodsIntentions = iMemberPurchaseGoodsIntentionBiz
                                                                            .listByMemberId(member.getMemberId());
        List<String> saleUserIdList = memberPurchaseGoodsIntentions.stream()
                                                            .map(MemberPurchaseGoodsIntention::getSaleUserId)
                                                            .distinct().collect(Collectors.toList());
        List<Account> smsMoblieList = accountBiz.findByIds(saleUserIdList);
        List<String> contactPhones = smsMoblieList.stream()
                .map(Account::getMobile)
                .filter(CharSequenceUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        SmsDTO sms = new SmsDTO(SmsTemplateEnum.ENTERPRISE_SUB_TO_MAIN_ACCOUNT_CODE.getCode(), contactPhones, param);
        iSmsSendService.sendSms(sms);
    }

    private final IMemberPurchaseGoodsIntentionBiz iMemberPurchaseGoodsIntentionBiz;
    @Override
    public void refreshLoginInfo(RefreshLoginInfoDTO dto) {
        accountSessionBiz.refreshLoginInfo(dto);
    }

    @Override
    public void unBindingAccount(String currUserAccountId, String unBindingAccountId) {
        accountBiz.unBindingAccount(currUserAccountId, unBindingAccountId);
    }

    private AccountDTO registerSubAccountAndSendSMSMessage(SubAccountRegisterDTO subParam) {
        if (StringUtils.isBlank(subParam.getMobile())) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "手机号不能为空");
        }
        Account account = accountBiz.registerSubAccount(subParam);

        //强制退出登录
        accountSessionBiz.forceOffLineSync(account.getAccountId(),null,null,"创建子账户:子账户强制下线",subParam.getOperatorId());

        // 企业子账号创建流程 发送短信
        List<String> param = Arrays.asList(account.getMobile());
        List<String> contactPhones = Arrays.asList(account.getMobile());
        SmsDTO sms = new SmsDTO(SmsTemplateEnum.ENTERPRISE_SUB_ACCOUNT_CREATION_CODE.getCode(), contactPhones, param);
        iSmsSendService.sendSms(sms);
        return vo2Dto(account);
    }


    private void isBlank(String str, String fieldName) {
        if (StringUtils.isBlank(str)) {
            throw new MemberBizException(MemberCode.IS_BLANK, fieldName);
        }
    }

    private Account dto2Vo(Object dto) {
        if (dto == null) {
            return null;
        }
        Account account = new Account();
        BeanUtils.copyProperties(dto, account);
        return account;
    }

    private AccountDTO vo2Dto(Account account) {
        if (account == null) {
            return null;
        }
        AccountDTO accountDTO = new AccountDTO();
        BeanUtils.copyProperties(account, accountDTO);
        return accountDTO;
    }

    private List<AccountDTO> vo2Dto(List<Account> accountList) {
        List<AccountDTO> dtoList = Lists.newArrayList();
        if (accountList != null && !accountList.isEmpty()) {
            accountList.forEach(item -> dtoList.add(vo2Dto(item)));
        }
        return dtoList;
    }

    @Override
    public List<AccountDTO> findByIds(List<String> ids) {
        return vo2Dto(accountBiz.findByIds(ids));
    }

    @Override
    public List<AccountNameDTO> findAccountNameByRoleCode(String roleCode) {
        List<String> accountIdList = roleService.getAccountIdByRoleCode(roleCode);
        if (CollectionUtils.isEmpty(accountIdList)){
            return new ArrayList<>();
        }
        Condition condition = new Condition(Account.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn(DuplicateString.ACCOUNT_ID, accountIdList)
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE)
                .andEqualTo(DuplicateString.STATUS, Boolean.FALSE);
        List<Account> accountList = accountMapper.selectByCondition(condition);
        List<AccountNameDTO> accountNameDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(accountList)) {
            accountList.forEach(account -> {
                AccountNameDTO accountNameDTO = new AccountNameDTO();
                BeanUtils.copyProperties(account,accountNameDTO);
                accountNameDTOList.add(accountNameDTO);
            });
        }
        return accountNameDTOList;
    }

    @Override
    public List<AccountInfoReturnDTO> findAccountByAccountId(List<String> ids) {
        return accountMapper.findAccountByAccountId(ids);
    }

    @Override
    public List<AccountSimpleDTO> listSimpleByIds(List<String> accountIds) {
        if(CollUtil.isEmpty(accountIds))
            return Collections.emptyList();
        List<AccountSimpleDTO> accounts = BeanUtil.copyToList(accountBiz.listByIds(accountIds), AccountSimpleDTO.class);
        iMemberBiz.handleAccountMemberName(accounts);
        return accounts;
    }

    @Override
    public boolean existsAccountByEmail(String email,String exceptAccountId){
        return accountBiz.existsAccountByEmail(email,exceptAccountId);
    }

    @Override
    public boolean existsAccountByMobile(String mobile, String exceptAccountId) {
        return accountBiz.existsAccountByMobile(mobile,exceptAccountId);
    }
}