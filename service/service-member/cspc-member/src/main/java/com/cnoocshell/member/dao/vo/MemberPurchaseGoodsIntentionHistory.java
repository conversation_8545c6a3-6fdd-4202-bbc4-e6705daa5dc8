package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "mb_member_purchase_goods_intention_history")
public class MemberPurchaseGoodsIntentionHistory extends BaseEntity {
    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "request_id")
    private String requestId;

    @Column(name = "member_id")
    private String memberId;

    @Column(name  ="member_code")
    private String memberCode;

    @Column(name = "intention_type")
    private Integer intentionType;

    @Column(name = "goods_category_id")
    private String goodsCategoryId;

    @Column(name = "goods_id")
    private String goodsId;

    @Column(name = "goods_code")
    private String goodsCode;


    /**
     * 销售人员ID
     */
    @Column(name = "sale_user_id")
    private String saleUserId;

    /**
     * 销售人员真实姓名
     */
    @Column(name = "sale_user_name")
    private String saleUserName;

    /**
     * 销售渠道(字典)
     */
    @Column(name = "sale_channel")
    private String saleChannel;

    /**
     * 维护销售信息的操作人ID
     */
    @Column(name = "maintain_user_id")
    private String maintainUserId;

    /**
     * 维护销售信息的操作人姓名
     */
    @Column(name = "maintain_user_name")
    private String maintainUserName;

    /**
     * 维护销售信息的操作时间
     */
    @Column(name = "maintain_time")
    private Date maintainTime;

}