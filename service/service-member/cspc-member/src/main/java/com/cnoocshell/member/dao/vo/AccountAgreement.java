package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import com.cnoocshell.member.api.enums.AgreementBusinessTypeEnum;
import com.cnoocshell.member.api.enums.AgreementTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "mb_account_agreement")
@EqualsAndHashCode(callSuper = true)
public class AccountAgreement extends BaseEntity {
    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "account_id")
    private String accountId;


    /**
     *协议类型：用户协议 VO_AGREEMENT_TYPE_USER，隐私声明 VO_AGREEMENT_TYPE_PRIVATE，交易协议 VO_AGREEMENT_TYPE_TRADE
     * {@link AgreementTypeEnum}
     */
    @Column(name = "agreement_type")
    private String agreementType;

    /**
     *协议版本号
     */
    @Column(name = "agreement_version")
    private String agreementVersion;


    /**
     *业务单号：account_code，bidding_buyer_detail_id，enquiry_buyer_detail_id
     */
    @Column(name = "business_no")
    private String businessNo;


    /**
     *业务单号类型：账号ID - VO_NO_TYPE_ACCOUNT_ID，询价报量明细ID - VO_NO_TYPE_ENQUIRY_BUYER_DETAIL_ID，竞价报量明细ID - VO_NO_TYPE_BIDDING_BUYER_DETAIL_ID
     * {@link AgreementBusinessTypeEnum}
     */
    @Column(name = "business_type")
    private String businessType;
}