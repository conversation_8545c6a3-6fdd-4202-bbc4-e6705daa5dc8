package com.cnoocshell.member.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.account.AccountSubscribeWechatDTO;
import com.cnoocshell.member.api.dto.account.MbAccountSubscribeWechatDTO;
import com.cnoocshell.member.api.dto.account.QueryAccountSubscribeWechatDTO;

import java.util.List;
import org.springframework.web.bind.annotation.RequestBody;

public interface IAccountSubscribeWechatService {

    ItemResult<String> createSubscribeRecord(MbAccountSubscribeWechatDTO accountSubscribeWechatDTO);

    ItemResult<Boolean> checkAccountSubscribe(MbAccountSubscribeWechatDTO accountSubscribeWechatDTO);

    List<AccountSubscribeWechatDTO> querySubscribeByCondition(QueryAccountSubscribeWechatDTO param);
    ItemResult<String> updateSendStatus(@RequestBody MbAccountSubscribeWechatDTO accountSubscribeWechatDTO);

}
