package com.cnoocshell.member.biz.impl;

import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.member.biz.IAccountChangePasswordHistoryBiz;
import com.cnoocshell.member.dao.vo.Account;
import com.cnoocshell.member.dao.vo.AccountChangePasswordHistory;
import org.springframework.stereotype.Service;

@Service
public class AccountChangePasswordHistoryBiz extends BaseBiz<AccountChangePasswordHistory> implements IAccountChangePasswordHistoryBiz {

    @Override
    public void addHistory(Account account, String reason, String operator) {
        AccountChangePasswordHistory history = new AccountChangePasswordHistory();
        history.setMbAccountChangePasswordHistoryId(this.getUuidGeneratorGain());
        history.setAccountId(account.getAccountId());
        history.setPassword(account.getPassword());
        history.setPasswordSalt(account.getPasswordSalt());
        history.setReason(reason);
        history.handleUser(operator,true);
        this.insert(history);
    }
}
