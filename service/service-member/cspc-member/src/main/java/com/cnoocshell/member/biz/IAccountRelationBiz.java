package com.cnoocshell.member.biz;

import com.cnoocshell.base.api.dto.role.RolePageRequestDTO;
import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationPageRequestDTO;
import com.cnoocshell.member.dao.vo.AccountRelation;
import com.github.pagehelper.PageInfo;

public interface IAccountRelationBiz extends IBaseBiz<AccountRelation> {

    PageInfo<AccountRelation> findAccountRelationByCondition(AccountRelationPageRequestDTO requestDTO);
}
