package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 密码变更历史
 */
@Data
@Table(name = "mb_account_change_password_history")
@EqualsAndHashCode(callSuper = true)
public class AccountChangePasswordHistory extends BaseEntity {

    /**
     * id
     */
    @Id
    @Column(name = "mb_account_change_password_history_id")
    private String mbAccountChangePasswordHistoryId;

    /**
     * 账号ID
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 变更前的密码
     */
    @Column(name = "password")
    private String password;

    /**
     * 变更前的密码对应的盐
     */
    @Column(name = "password_salt")
    private String passwordSalt;

    /**
     * 变更原因
     */
    @Column(name = "reason")
    private String reason;
}