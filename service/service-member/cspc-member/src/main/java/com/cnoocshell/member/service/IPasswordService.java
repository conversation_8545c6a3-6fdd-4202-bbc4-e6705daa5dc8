package com.cnoocshell.member.service;


/**
 * 密码服务 
 * 
 * 1，MD5[ MD5[ RSA[密文] ] + 盐 ] 不发短信（密文为RSA加密的）
 * 2，MD5[ MD5[ RSA[密文] ] + 盐 ] 发短信（密文为RSA加密的）
 * 3，MD5[ 密文 + 盐 ] 不发短信  （密文为MD5加密的）
 * 4，MD5[ 密文 + 盐 ] 发短信（密文为MD5加密的）
 * 
 * 数据库保存的密码是：MD5[ MD5[密码明文] + 盐 ]
 * 
 * 需要发送短信时，前端的明文密码采用RSA加密后发送到后台，后台采用MD5[ MD5[ RSA[密文] ] + 盐 ]保存到数据库
 * 不需要发送短信时，前端的明文密码采用MD5加密后发送到后台，后台采用MD5[ 密文 + 盐 ]
 * 
 * @Author: <EMAIL>
 *
 */
public interface IPasswordService {

	

	/**
	 * 后台创建密码发送短信
	 * @param phoneNum 发送短信的手机号
	 * @param salt 密码盐
	 * @return 加密后密码
	 */
	public String createPasswordAndSendSMS(String phoneNum, String salt,String sign);
	
	
	/**
	 * 后台创建密码不发短信
	 * @param salt 密码盐
	 * @return 加密后的密码
	 */
	public String createPasswordButNoSMS(String salt);
	
	/**
	 * 比较密码
	 * @param inPassword 输入密码
	 * @param realPassword 数据库保存的密码
	 * @param salt 密码盐
	 * @return 密码正确返回true
	 */
	public boolean comparePassword(String inPassword, String realPassword, String salt);
	
	
	/**
	 * 对前端传过来的密码进行处理，得到保存到数据库的密码
	 * @param inPassword 前端数据的密码
	 * @param salt 密码盐
	 * @return 加密后的密码
	 */
	public String buildPassword(String inPassword, String salt);


	
	/**
	 * 获取公钥
	 * @return 公钥字符串
	 */
	public String getPublicKey();
	
	
	/**
	 * RSA私钥解密
	 * @param data 密文
	 * @return 解密后的字符串
	 */
	public String getRsaDecryptByprivateKey(String data);
	
	/**
	 * RSA公钥加密
	 * @param data
	 * @return 加密后的字符串
	 */
	public String getRsaEncryptByPublicKey(String data);
}
