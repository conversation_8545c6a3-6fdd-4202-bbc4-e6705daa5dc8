package com.cnoocshell.member.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.member.dao.vo.MemberHistory;

import java.util.List;

/**
 * @DESCRIPTION:
 */
public interface IMemberHistoryBiz extends IBaseBiz<MemberHistory> {

    boolean insertSelective(MemberHistory memberHistory);

    MemberHistory getByRequestId(String requestId);

    List<MemberHistory> listByRequestIds(List<String> requestIds);
}
