package com.cnoocshell.member.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.member.api.dto.account.AccountInfoReturnDTO;
import com.cnoocshell.member.api.dto.member.*;
import com.cnoocshell.member.api.dto.report.*;
import com.cnoocshell.member.dao.vo.Member;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Date;
import java.util.List;

@Mapper
public interface MemberMapper extends IBaseMapper<Member> {

    @Select("select max(member_code) as memberCode from mb_member")
    String findMaxMemberCode();

    @Select("select * from mb_member where del_flg = 0 and member_id=#{memberId}")
    Member findById(@Param("memberId") String memberId);

    @Select("select count(*)from mb_member where member_code=#{memberCode}")
    int countByMemberCode(@Param("memberCode") String memberCode);

    List<MemberDataInfoDTO> queryMemberDataInfo(String goodsCode);

    List<MemberInfoForGoodsDTO> queryMemberInfoByGoodsCode(@Param("dto") MemberQueryByGoodsDTO dto);

    List<MemberGoodsInfoDTO> queryGoodsByMemberCode(List<String> memberCodeList);

    List<MemberSimpleDataDTO> queryMemberByMemberNameAndCrmCode(MemberSimpleDataDTO memberSimpleDataDTO);

    List<MemberDepositStatusDataDTO> queryMemberDepositStatus(@Param("dto")MemberDepositStatusDTO dto);

    void requestRefund(@Param("dto") MemberRefundDepositStatusDTO dto);

    void updateDepositStatus(@Param("unPayUserList") List<String> unPayUserList, @Param("code") String code, @Param("accountId") String accountId, @Param("date") Date date);

    String queryMainGoodsCode(@Param("memberId") String memberId);

    List<AccountInfoReturnDTO> queryAccountInfo(List<String> accountIds);

    String queryDepositStatus(String memberCode);

    List<String> selectMemberCodeByDepositStatus(@Param("memberCodes") List<String> memberCodes,@Param("depositStatus") String depositStatus);

    @Update("UPDATE mb_member SET  crm_code=#{crmCode} where member_id=#{memberId} and del_flg=0")
    int updateCrmCode(@Param("crmCode") String crmCode, @Param("memberId") String memberId);

    String queryMemberDepositStatusByAccountId(@Param("accountId") String accountId);

    List<MemberReportResultDTO> memberReport(@Param("param") MemberReportQueryDTO param);

    List<MemberAccountReportSimpleDTO> memberAccountReport(@Param("param") MemberAccountReportQueryDTO param);

    List<MemberAccountReportResultDTO> queryAccountReportInfo(@Param("accountIds")List<String> accountIds);
}
