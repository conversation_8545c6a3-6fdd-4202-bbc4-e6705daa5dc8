package com.cnoocshell.member.service.impl;

import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.common.RSAService;
import com.cnoocshell.common.utils.MD5;
import com.cnoocshell.integration.enums.SmsTemplateEnum;
import com.cnoocshell.member.api.dto.exception.MemberBizException;
import com.cnoocshell.member.api.dto.exception.MemberCode;
import com.cnoocshell.member.common.PassWordGeneratorUtil;
import com.cnoocshell.member.service.IPasswordService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;


@Service
@Slf4j
@RequiredArgsConstructor
public class PasswordService implements IPasswordService {
	
//	private final SMSMessageProducerImpl sMSMessageProducerImpl;
    private final RSAService rSAService;

	private final ISmsSendService iSmsSendService;


	@Override
	public String createPasswordAndSendSMS(String accountId, String salt,String sign) {
		String passWord = PassWordGeneratorUtil.generatorPassWord(8);
		log.info("创建密码发送短信 ：{}：{}",accountId,"********");
		
		log.info("重置密码并发送短信：{} {}",accountId,passWord);
//		sMSMessageProducerImpl.sendPassword(accountId,passWord,sign);
        //MD5[ MD5[明文] + 盐 ]
        String pw = MD5.getMD5(MD5.getMD5(passWord) + salt);
        
        return pw.toLowerCase();
	}

	@Override
	public String createPasswordButNoSMS(String salt) {
		String passWord = PassWordGeneratorUtil.generatorPassWord(8);
		log.info("创建密码 ：{}","********");
		
		//MD5[ MD5[明文] + 盐 ]
        String pw = MD5.getMD5(MD5.getMD5(passWord) + salt);
        
        return pw.toLowerCase();
	}

	@Override
	public boolean comparePassword(String inPassword, String realPassword, String salt) {
		String pw = "";
		if(32 == inPassword.length()) {
			//是进过md5加密的, MD5[ 密文 + 盐 ]
			pw = MD5.getMD5(inPassword.toLowerCase() + salt);
		} else {
			//是经过RSA加密的，MD5[ MD5[ RSA[密文] ] + 盐 ]
			try {
				String decryptpw = rSAService.getRsaDecrypt(inPassword);
				pw = MD5.getMD5(MD5.getMD5(decryptpw) + salt);
			} catch (Exception e) {
				log.error("==========》RSA解密失败", e);
				throw new MemberBizException(MemberCode.PASSWD_DECRYPT_ERROR,"用户名密码不正确");
			}
		}

        //密码正确
        return StringUtils.equalsIgnoreCase(pw, realPassword);
	}

	@Override
	public String buildPassword(String inPassword, String salt) {
		String pw = "";
		if(32 == inPassword.length()) {
			//是进过md5加密的, MD5[ 密文 + 盐 ]
			pw = MD5.getMD5(inPassword.toLowerCase() + salt);
		} else {
			//是经过RSA加密的，MD5[ MD5[ RSA[密文] ] + 盐 ]
			try {
				String decryptpw = rSAService.getRsaDecrypt(inPassword);
				pw = MD5.getMD5(MD5.getMD5(decryptpw) + salt);
			} catch (Exception e) {
				log.error("==========》RSA解密失败", e);
				throw new MemberBizException(MemberCode.PASSWD_DECRYPT_ERROR,"用户名密码不正确");
			}
		}
		
		return pw.toLowerCase();
	}

	@Override
	public String getPublicKey() {
		if(!rSAService.isHavePublicKey()) {
			log.info("没有配置RSA私钥，不能获取公钥");
			throw new MemberBizException(BasicCode.DATA_NOT_EXIST, "【没有配置RSA私钥，不能获取公钥】");
		}
		return rSAService.getPublicKey();
	}

	@Override
	public String getRsaDecryptByprivateKey(String data) {
		if(!rSAService.isHavePrivateKey()) {
			log.info("没有配置RSA私钥，不能解密");
			throw new MemberBizException(BasicCode.DATA_NOT_EXIST, "【没有配置RSA私钥，不能解密】");
		}
		
		try {
			return rSAService.getRsaDecrypt(data);
		} catch (Exception e) {
			log.error("RSA解密失败", e);
			throw new MemberBizException(BasicCode.INVALID_PARAM, "RSA解密失败");
		}
	}

	@Override
	public String getRsaEncryptByPublicKey(String data) {
		if(!rSAService.isHavePublicKey()) {
			log.info("没有配置RSA公钥，不能加密");
			throw new MemberBizException(BasicCode.DATA_NOT_EXIST, "【没有配置RSA公钥，不能加密】");
		}
		
		try {
			return rSAService.getRsaEncrypt(data);
		} catch (Exception e) {
			log.error("RSA加密失败", e);
			throw new MemberBizException(BasicCode.INVALID_PARAM, "RSA加密失败");
		}
	}

}
