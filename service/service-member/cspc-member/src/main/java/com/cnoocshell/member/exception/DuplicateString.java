package com.cnoocshell.member.exception;

/**
 * 多次重复的字符串提取到该类
 */
public class DuplicateString {


    private DuplicateString() {
        throw new IllegalStateException("DuplicateString class");
    }

    public static final String ACCOUNT_TYPE = "accountType";
    public static final String ACCOUNT_CANNOT_BIND = "无法绑定该账户";
    public static final String ACCOUNT_NAME_OR_PD_INCORRECT = "用户名或密码不正确";
    public static final String ACCOUNT_NAME = "accountName";
    public static final String ACCOUNT_ID = "accountId";
    public static final String ACCOUNT_CODE = "accountCode";
    public static final String WECHAT_OPEN_ID = "wechatOpenId";
    public static final String MEMBER_ID = "memberId";
    public static final String CUSTOMER_ID = "customerId";
    public static final String MEMBER_CODE = "memberCode";
    public static final String DEL_FLG = "delFlg";
    public static final String STATUS = "status";
    public static final String TRY_AGAIN_IN_24_HOURS = "请24小时后再试";
    public static final String CREATE_TIME = "createTime";
    public static final String MEMBER_NAME = "memberName";
    public static final String REQUEST_ID = "requestId";
    public static final String REQUEST_TYPE = "requestType";
    public static final String REQUEST_TIME = "requestTime";
    public static final String UPDATE_TIME = "updateTime";


    public static final String CERT_TYPE = "certType";


    public static final String METHOD_DISCARD = "method discard";

    public static final String ADCODE_ASC = " `adcode` asc ";
    public static final String ADCODE_100000 = "100000";
    public static final String PROVINCE = "province";
    public static final String DISTRICT = "district";
    public static final String STREET = "street";
    public static final String LEVEL = "level";

    public static final String PAGE_CODE = "pageCode";

    public static final String SERVICE_CODE = "serviceCode";

    public static final String AGENT_FLAG = "agentFlag";

    public static final String ORG_ID = "orgId";

    public static final String MEMBER_TYPE = "memberType";


    public static final String SELLER_FLG = "sellerFlg";

    public static final String MOBILE = "mobile";

    public static final String SEX = "sex";

    public static final String EMPLOYEE_ID = "employeeId";
    public static final String DEPARTMENT = "department";
    public static final String POSITION = "position";
    public static final String REAL_NAME = "realName";

    public static final String OPERATOR_ID = "operatorId";

    public static final String ID = "id";
    public static final String EMAIL = "email";
    public static final String GOODS_ID = "goodsId";
    public static final String BUSINESS_TYPE = "businessType";
    public static final String BUSINESS_NO = "businessNo";
    public static final String LINK_ACCOUNT_ID = "linkAccountId";

    public static final String CRM_CODE = "crmCode";
    public static final String GOODS_CODE = "goodsCode";
}
