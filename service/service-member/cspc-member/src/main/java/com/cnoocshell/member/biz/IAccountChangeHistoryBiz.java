package com.cnoocshell.member.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.member.api.dto.account.AccountChangeHistoryDTO;
import com.cnoocshell.member.api.dto.account.PageAccountChangeHistoryDTO;
import com.cnoocshell.member.dao.vo.AccountChangeHistory;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface IAccountChangeHistoryBiz extends IBaseBiz<AccountChangeHistory> {

    void add(AccountChangeHistory accountChangeHistory, String operator);

    /**
     * 根据DTO查询员工授权，启用，禁用历史
     */
    List<AccountChangeHistory> findByQuery(AccountChangeHistoryDTO accountChangeHistoryDTO);

    /**
     * 根据DTO分页查询员工授权，启用，禁用历史
     */
    PageInfo<AccountChangeHistory> pageHistoryInfoList(PageAccountChangeHistoryDTO pageAccountChangeHistoryDTO);
}
