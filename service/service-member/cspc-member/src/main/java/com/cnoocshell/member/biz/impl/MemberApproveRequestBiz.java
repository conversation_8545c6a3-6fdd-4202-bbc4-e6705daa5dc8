package com.cnoocshell.member.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.service.IEmailSendService;
import com.cnoocshell.member.api.dto.member.enums.AdvertStatusEnum;
import com.cnoocshell.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.cnoocshell.member.biz.IMemberApproveRequestBiz;
import com.cnoocshell.member.dao.mapper.AccountMapper;
import com.cnoocshell.member.dao.mapper.MemberApproveRequestMapper;
import com.cnoocshell.member.dao.vo.Account;
import com.cnoocshell.member.dao.vo.Member;
import com.cnoocshell.member.dao.vo.MemberApproveRequest;
import com.cnoocshell.member.exception.DuplicateString;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MemberApproveRequestBiz extends BaseBiz<MemberApproveRequest> implements IMemberApproveRequestBiz {

    private final MemberApproveRequestMapper memberApproveRequestMapper;
    private final MemberApproveRequestIncrementIdGenerator memberApproveRequestIncrementIdGenerator;
    private final UUIDGenerator uuidGenerator;
    private final AccountMapper accountMapper;
    private final IEmailSendService emailSendService;

    @Override
    public String newRequestByContactName(String memberId, String memberCode,
                                          String requestType,
                                          String memberType, String memberName,
                                          String contactName, String contactPhone,
                                          String changeMessage, boolean needApproval, String operatorId) {
        MemberApproveRequest memberApproveRequest = new MemberApproveRequest();
        memberApproveRequest.setRequestType(requestType);
        memberApproveRequest.setChangeMessage(changeMessage);
        memberApproveRequest.setRequestMemberId(memberId);
        memberApproveRequest.setMemberCode(memberCode);
        memberApproveRequest.setRequestTime(new Date());
        memberApproveRequest.setMemberType(memberType);
        Account account = accountMapper.selectByPrimaryKey(operatorId);
        approveRequestInfo(memberApproveRequest, needApproval, account);
        memberApproveRequest.setContactName(contactName);
        memberApproveRequest.setMemberName(memberName);
        memberApproveRequest.setContactPhone(contactPhone);
        memberApproveRequest.setMemberId(memberId);
        setOperInfo(memberApproveRequest, operatorId, true);
        memberApproveRequest.setRequestId(uuidGenerator.gain());
        memberApproveRequest.setRequestNum(memberApproveRequestIncrementIdGenerator.incrementCode());
        memberApproveRequestMapper.insert(memberApproveRequest);
        return memberApproveRequest.getRequestId();
    }

    @Override
    public List<String> getRegisterTypeList() {
        List<String> res = new ArrayList<>();
        res.add(ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER.getCode());
        res.add(ApproveRequestTypeEnum.REGISTER_ENTERPRISE_SELLER.getCode());
        return res;
    }

    @Override
    public List<String> getIsDealList() {
        List<String> res = new ArrayList<>();
        res.add(AdvertStatusEnum.APPROVED.getCode());
        res.add(AdvertStatusEnum.REJECT.getCode());
        return res;
    }

    @Override
    public MemberApproveRequest findLastChangeRequest(String memberId) {
        return memberApproveRequestMapper.findLastChangeRequest(memberId);
    }

    @Override
    public MemberApproveRequest hasNoApproveRequest(String memberId) {
        return hasNoApproveRequest(memberId, null);
    }

    @Override
    public MemberApproveRequest hasNoApproveRequest(String memberId, String accountId) {
        Condition condition = new Condition(MemberApproveRequest.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DuplicateString.MEMBER_ID, memberId)
                .andEqualTo(DuplicateString.STATUS, AdvertStatusEnum.NEW_REQUEST.getCode())
                .andEqualTo(DuplicateString.DEL_FLG, Boolean.FALSE);
        if (StringUtils.isNotBlank(accountId)) {
            criteria.andEqualTo(DuplicateString.ACCOUNT_ID, accountId);
        }

        List<MemberApproveRequest> list = memberApproveRequestMapper.selectByCondition(condition);
        if (list != null && !list.isEmpty() && list.get(0) != null) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public void approveMemberRequest(String requestId, String approveText, String operatorId) {
        updateMemberRequest(requestId, approveText, AdvertStatusEnum.APPROVED.getCode(), operatorId);
    }

    @Override
    public void rejectMemberRequest(String requestId, String approveText, String operatorId) {
        updateMemberRequest(requestId, approveText, AdvertStatusEnum.REJECT.getCode(), operatorId);
    }

    @Override
    public String newRequest(String memberId, String memberCode, String memberName, String requestType, String memberType, String changeMessage, boolean needApproval, String operatorId) {
        MemberApproveRequest memberApproveRequest = new MemberApproveRequest();
        memberApproveRequest.setRequestType(requestType);
        memberApproveRequest.setChangeMessage(changeMessage);
        memberApproveRequest.setRequestMemberId(memberId);
        memberApproveRequest.setMemberCode(memberCode);
        memberApproveRequest.setRequestTime(new Date());
        memberApproveRequest.setMemberType(memberType);
        memberApproveRequest.setMemberName(memberName);
        Account account = accountMapper.selectByPrimaryKey(operatorId);
        approveRequestInfo(memberApproveRequest, needApproval, account);
        if (account != null) {
            memberApproveRequest.setContactName(account.getAccountName());
            memberApproveRequest.setContactPhone(account.getMobile());
        }
        memberApproveRequest.setMemberId(memberId);
        setOperatorInfo(memberApproveRequest, operatorId, true);
        memberApproveRequest.setRequestId(uuidGenerator.gain());
        memberApproveRequest.setRequestNum(memberApproveRequestIncrementIdGenerator.incrementCode());
        memberApproveRequestMapper.insert(memberApproveRequest);
        return memberApproveRequest.getRequestId();
    }

    @Override
    public String newRequest(Member member,Boolean needApprove,ApproveRequestTypeEnum requestType,String operatorId) {
        MemberApproveRequest request = new MemberApproveRequest();
        request.setRequestType(requestType.getCode());
        request.setRequestMemberId(member.getMemberId());
        request.setMemberCode(member.getMemberCode());
        request.setRequestTime(new Date());
        request.setMemberType(member.getMemberType());
        request.setMemberName(member.getMemberName());
        if(BooleanUtil.isTrue(needApprove)){
            request.setStatus(AdvertStatusEnum.NEW_REQUEST.getCode());
            request.setChangeMessage(requestType.getMsg());
        }else{
            request.setStatus(AdvertStatusEnum.APPROVED.getCode());
            request.setChangeMessage(requestType.getMsg()+":无需审批");
        }

        Account account = accountMapper.selectByPrimaryKey(operatorId);
        if (account != null) {
            request.setContactName(account.getAccountName());
            request.setContactPhone(account.getMobile());
        }
        request.setMemberId(member.getMemberId());
        setOperatorInfo(request, operatorId, true);
        request.setRequestId(uuidGenerator.gain());
        request.setRequestNum(memberApproveRequestIncrementIdGenerator.incrementCode());
        memberApproveRequestMapper.insert(request);
        return request.getRequestId();
    }

    private Map<String,Object> getEmailTemplateParam(MemberApproveRequest request, Account saleUserAccount){
        Map<String,Object> emailTemplate = new HashMap<>();
        emailTemplate.put("mainProductSalesManagerName",saleUserAccount.getRealName());
        emailTemplate.put("customerName",request.getMemberName());
        emailTemplate.put("contactPersonName",request.getContactName());
        emailTemplate.put("contactPhone",request.getContactPhone());
        return emailTemplate;
    }

    @Override
    public MemberApproveRequest findLastChangeRequest(String memberId, ApproveRequestTypeEnum requestType) {
        return memberApproveRequestMapper.findLastChangeRequestByType(memberId,requestType.getCode());
    }

    @Override
    public MemberApproveRequest findLastChangeRequest(String memberId, ApproveRequestTypeEnum requestType, AdvertStatusEnum status) {
        return memberApproveRequestMapper.findLastChangeRequestByCondition(memberId,requestType.getCode(),status.getCode());
    }

    @Override
    public MemberApproveRequest findLastRequest(String memberId, List<ApproveRequestTypeEnum> requestType, List<AdvertStatusEnum> status) {
        List<String> requestTypes = null;
        if(CollUtil.isNotEmpty(requestType)){
            requestTypes = requestType.stream().map(ApproveRequestTypeEnum::getCode).collect(Collectors.toList());
        }
        List<String> statuses = null;
        if(CollUtil.isNotEmpty(status)){
            statuses = status.stream().map(AdvertStatusEnum::getCode).collect(Collectors.toList());
        }
        return memberApproveRequestMapper.findLastRequest(memberId,requestTypes,statuses);
    }

    @Override
    public MemberApproveRequest findLatestRequest(String memberId, ApproveRequestTypeEnum requestType, AdvertStatusEnum status, Date approvalOrCreateTime) {
        return memberApproveRequestMapper.findLatestRequest(memberId,requestType.getCode(),status.getCode(),approvalOrCreateTime);
    }


    private void updateMemberRequest(String requestId, String approveText, String status, String operatorId) {
        if (StringUtils.isEmpty(requestId)) {
            throw new BizException(BasicCode.PARAM_NULL, "requestId: " + requestId);
        }
        MemberApproveRequest request = new MemberApproveRequest();
        request.setRequestId(requestId);
        request.setApproveText(approveText);
        request.setApproveId(operatorId);
        Account account = accountMapper.selectByPrimaryKey(operatorId);
        if (account != null) {
            request.setApproveName(account.getAccountName());
        }
        request.setStatus(status);
        request.setApproveTime(new Date());
        setOperInfo(request, operatorId, false);
        memberApproveRequestMapper.updateByPrimaryKeySelective(request);
    }


    private void approveRequestInfo(MemberApproveRequest memberApproveRequest, boolean needApproval, Account account) {
        if (needApproval) {
            memberApproveRequest.setStatus(AdvertStatusEnum.NEW_REQUEST.getCode());
        } else {
            memberApproveRequest.setStatus(AdvertStatusEnum.APPROVED.getCode());
            memberApproveRequest.setApproveText("");
            memberApproveRequest.setApproveTime(new Date());
            if (account == null) {
                return;
            }
            memberApproveRequest.setApproveId(account.getAccountId());
            if (!StringUtils.isEmpty(account.getRealName())) {
                memberApproveRequest.setApproveName(account.getRealName());
            } else {
                memberApproveRequest.setApproveName(account.getAccountName());
            }
        }
    }
}
