package com.cnoocshell.member.biz;

import com.cnoocshell.member.api.dto.account.AccountLoginInfoDTO;
import com.cnoocshell.member.dao.vo.AccountLoginInfo;

import java.util.List;
import java.util.Set;

/**
 * @DESCRIPTION:
 */
public interface IAccountLoginInfoBiz {

    /**
     * 正常／强制登出时，更新登录信息
     * @param oldSessionId 下线的session
     * @param newSessionId 新上线的session
     * @param reason
     * @param operator
     */
    int offline(String oldSessionId,String newSessionId,String reason,String operator);

    List<String> findLastSessionId(String accountId, String loginType);

    /**
     * 查询最近一个没有退出登录的记录
     * @param accountId
     * @return
     */
    AccountLoginInfo findByAccountId(String accountId);


    List<String> findLoginSessionId(Integer offset,Integer limitSize);

    List<String> findLoginSessionId(Set<String> accountIdSet, Integer offset, Integer limitSize);

    List<String> findLastSessionIdByTerminal(String accountId, String terminal);

    void insertLoginInfo(AccountLoginInfoDTO param);

}
