package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "mb_account")
@EqualsAndHashCode(callSuper = true)
public class Account extends BaseEntity {
    /**
     * 会员id
     */
    @Id
    @Column(name = "account_id")
    private String accountId;
    /**
     * 子账号代码（可用于登录）
     */
    @Column(name = "account_code")
    private String accountCode;

    /**
     * 账户名(登陆使用，唯一)
     */
    @Column(name = "account_name")
    private String accountName;

    /**
     * 账户昵称
     */
    @Column(name = "account_nickname")
    private String accountNickname;

    /**
     * 账户真实姓名
     */
    @Column(name = "real_name")
    private String realName;

    /**
     * 0个人/1企业主账号/2企业子账号
     */
    @Column(name = "account_type")
    private Integer accountType;

    /**
     * 会员id
     */
    @Column(name = "member_id")
    private String memberId;

    @Column(name = "member_code")
    private String memberCode;

    /**
     * 会员名称
     */
    @Column(name = "member_name")
    private String memberName;
    /**
     * 会员简称
     */
    @Column(name = "member_short_name")
    private String memberShortName;
    /**
     * 工号
     */
    @Column(name = "employee_id")
    private String employeeId;

    /**
     * 部门
     */
    @Column(name = "department")
    private String department;
    /**
     * 职务
     */
    @Column(name = "position")
    private String position;

    /**
     * 账号状态（ 0 可用 1 禁用 ）
     */
    @Column(name = "status")
    private Boolean status;

    /**
     * 锁定状态
     */
    @Column(name = "lock_status")
    private Boolean lockStatus;

    /**
     * 锁定结束时间
     */
    @Column(name = "lock_end_time")
    private Date lockEndTime;

    /**
     * 锁定原因
     */
    @Column(name = "lock_reason")
    private String lockReason;

    /**
     * 密码盐值
     */
    @Column(name = "password_salt")
    private String passwordSalt;

    /**
     * 密码
     */
    @Column(name = "password")
    private String password;

    /**
     * 是否需要修改密码
     */
    @Column(name = "need_update_password")
    private Boolean needUpdatePassword;

    /**
     * 手机号上次修改时间
     */
    @Column(name = "mobile_update_time")
    private Date mobileUpdateTime;
    /**
     * 手机号
     */
    @Column(name = "mobile")
    private String mobile;

    /**
     * 邮箱
     */
    @Column(name = "email")
    private String email;

    /**
     * 性别（0 男    1女    2保密）
     */
    @Column(name = "sex")
    private Integer sex;

    /**
     * 头像大图URL
     */
    @Column(name = "head_pic")
    private String headPic;

    /**
     * 头像小图URL
     */
    @Column(name = "head_pic_min")
    private String headPicMin;

    /**
     * 生日
     */
    @Column(name = "birth_day")
    private Date birthDay;

    /**
     * 微信号
     */
    @Column(name = "wechat_id")
    private String wechatId;

    /**
     * 登录失败次数
     */
    @Column(name = "login_failure_count")
    private Integer loginFailureCount;

    /**
     * 登陆设备token(手机每次登陆需更新该字段)
     */
    @Column(name = "driver_token")
    private String driverToken;

    /**
     * 登陆设备系统（ios或android）
     */
    @Column(name = "mobile_os")
    private String mobileOs;

    /**
     * 登录日期
     */
    @Column(name = "last_login_date")
    private Date lastLoginDate;

    /**
     * 上次登录地址ip
     */
    @Column(name = "last_login_ip")
    private String lastLoginIp;

    /**
     * 密码重置秘钥
     */
    @Column(name = "password_recover_key")
    private String passwordRecoverKey;

    /**
     * 注册时使用的应用名称
     */
    @Column(name = "register_app")
    private String registerApp;

    /**
     * 发送短信需要的签名
     */
    @Column(name = "register_sign")
    private String registerSign;

    /**
     * 密钥失效时间(15分钟)
     */
    @Column(name = "key_lost_time")
    private Date keyLostTime;

    /**
     * 注册来源(0 默认  1 手机app   2微信程序  以后有其它方式再加)
     */
    @Column(name = "register_type")
    private Integer registerType;
}