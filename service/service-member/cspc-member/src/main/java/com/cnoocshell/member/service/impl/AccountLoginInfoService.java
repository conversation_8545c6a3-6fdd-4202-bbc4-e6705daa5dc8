package com.cnoocshell.member.service.impl;

import com.cnoocshell.member.api.dto.account.AccountLoginInfoDTO;
import com.cnoocshell.member.biz.IAccountLoginInfoBiz;
import com.cnoocshell.member.biz.IAccountSessionBiz;
import com.cnoocshell.member.dao.vo.AccountLoginInfo;
import com.cnoocshell.member.service.IAccountLoginInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Author: <EMAIL>
 * @Date: 08/11/2018 18:55
 * @DESCRIPTION:
 */
@Service
@RequiredArgsConstructor
public class AccountLoginInfoService implements IAccountLoginInfoService {

    private final IAccountLoginInfoBiz accountLoginInfoBiz;
    private final IAccountSessionBiz iAccountSessionBiz;


    @Override
    public void offline(String sessionId, String reason, String operator) {
        accountLoginInfoBiz.offline(sessionId,null,reason,operator);
        //删除redis中的session 数据
        iAccountSessionBiz.removeSession(sessionId);
    }

    @Override
    public void forceOffline(String accountId,String reason,String operator){
        AccountLoginInfo accountLoginInfo = accountLoginInfoBiz.findByAccountId(accountId);
        if( accountLoginInfo != null ){
            accountLoginInfoBiz.offline(accountLoginInfo.getSessionId(),null,reason,operator);
        }
    }

    @Override
    public void insertLoginInfo(AccountLoginInfoDTO param) {
        accountLoginInfoBiz.insertLoginInfo(param);
    }
}
