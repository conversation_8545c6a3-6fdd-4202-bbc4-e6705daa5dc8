package com.cnoocshell.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.utils.BeanConvertUtils;
import com.cnoocshell.member.api.dto.account.AccountChangeHistoryDTO;
import com.cnoocshell.member.api.dto.account.PageAccountChangeHistoryDTO;
import com.cnoocshell.member.biz.IAccountChangeHistoryBiz;
import com.cnoocshell.member.dao.vo.AccountChangeHistory;
import com.cnoocshell.member.service.IAccountChangeHistoryService;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AccountChangeHistoryService implements IAccountChangeHistoryService {

    private final IAccountChangeHistoryBiz accountChangeHistoryBiz;

    @Override
    public void add(AccountChangeHistoryDTO accountChangeHistoryDTO, String operator) {
        accountChangeHistoryBiz.add(dto2lVo(accountChangeHistoryDTO), operator);
    }


    @Override
    public List<AccountChangeHistoryDTO> findByQuery(AccountChangeHistoryDTO accountChangeHistoryDTO) {
        List<AccountChangeHistoryDTO> list = new ArrayList<>();
        List<AccountChangeHistory> result = accountChangeHistoryBiz.findByQuery(accountChangeHistoryDTO);
        if( result != null ){
            list = result.stream().map(this::vo2Dto).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public PageInfo<AccountChangeHistoryDTO> pageHistoryInfoList(PageAccountChangeHistoryDTO pageAccountChangeHistoryDTO) {
        PageInfo<AccountChangeHistoryDTO> pageInfo = new PageInfo<>();
        PageInfo<AccountChangeHistory> result = accountChangeHistoryBiz.pageHistoryInfoList(pageAccountChangeHistoryDTO);
        if( result != null ){
            BeanUtils.copyProperties(result,pageInfo);
            if( result.getList() != null && !result.getList().isEmpty() ) {
                pageInfo.setList(result.getList().stream().map(this::vo2Dto).collect(Collectors.toList()));
            }
        }
        return pageInfo;
    }


    private AccountChangeHistory dto2lVo(AccountChangeHistoryDTO dto){
        AccountChangeHistory vo = BeanConvertUtils.convert(dto,AccountChangeHistory.class);
        if( vo != null ){
            vo.setRoleIdList(CollUtil.join(dto.getRoleIdList(),";"));
        }
        return vo;
    }

    private AccountChangeHistoryDTO vo2Dto(AccountChangeHistory accountChangeHistory){
        if(accountChangeHistory == null){
            return null;
        }
        AccountChangeHistoryDTO dto = new AccountChangeHistoryDTO();
        BeanUtils.copyProperties(accountChangeHistory,dto);
        if(null != accountChangeHistory.getRoleIdList()){
            dto.setRoleIdList(BeanUtil.copyToList(CharSequenceUtil.split(accountChangeHistory.getRoleIdList(),";"),Integer.class));
        }
        return dto;
    }
}