package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "mb_account_login_info")
@EqualsAndHashCode(callSuper = true)
public class AccountLoginInfo extends BaseEntity {
    /**
     * id
     */
    @Id
    private String id;

    /**
     * 会话id
     */
    @Column(name = "session_id")
    private String sessionId;

    /**
     * 账户id
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 登录id
     */
    @Column(name = "login_name")
    private String loginName;

    /**
     * 登录类型
     */
    @Column(name = "login_type")
    private String loginType;

    /**
     * 登录时间
     */
    @Column(name = "login_time")
    private Date loginTime;

    /**
     * 登录结果
     */
    @Column(name = "login_comment")
    private String loginComment;

    /**
     * 登出时间
     */
    @Column(name = "logout_time")
    private Date logoutTime;

    /**
     * 登出结果
     */
    @Column(name = "logout_comment")
    private String logoutComment;

    /**
     * 登录ip
     */
    private String ip;

    /**
     * 登录mac
     */
    private String mac;

    /**
     * 登录终端
     */
    private String terminal;

    /**
     * 终端系统
     */
    private String os;

    /**
     * 终端系统版本
     */
    @Column(name = "os_version")
    private String osVersion;

    /**
     * 设备品牌
     */
    @Column(name = "device_brand")
    private String deviceBrand;

    /**
     * 国际移动设备识别码
     */
    private String imei;

    @Column(name = "driver_token")
    private String driverToken;

    /**
     * 浏览器信息
     */
    @Column(name = "user_agent")
    private String userAgent;
}