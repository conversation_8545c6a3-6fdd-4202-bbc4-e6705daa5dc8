package com.cnoocshell.member.dao.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 微信小程序订阅记录表对应的实体类。
 */
@Entity
@Table(name = "mb_account_subscribe_wechat")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MbAccountSubscribeWechat {

    /**
     * 主键ID，使用自增策略。
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private String id;

    /**
     * 账号ID，不能为空。
     */
    @Column(name = "account_id", nullable = false, length = 32)
    private String accountId;

    /**
     * 账户真实姓名。
     */
    @Column(name = "account_real_name", length = 32)
    private String accountRealName;

    /**
     * 订阅业务编号。
     */
    @Column(name = "business_no", length = 32)
    private String businessNo;

    /**
     * 订阅业务类型：ENQUIRY-询价，BIDDING-竞价。
     */
    @Column(name = "business_type", length = 32)
    private String businessType;

    /**
     * 微信用户的openid。
     */
    @Column(name = "open_id", length = 32)
    private String openId;

    /**
     * 模板ID。
     */
    @Column(name = "template_id", length = 64)
    private String templateId;

    /**
     * 模板名称。
     */
    @Column(name = "template_name", length = 32)
    private String templateName;

    /**
     * 推送状态：0未推送，1已推送。
     */
    @Column(name = "send_status", length = 32)
    private String sendStatus;

    /**
     * 删除标志，默认值为0（false）。
     */
    @Column(name = "del_flg", columnDefinition = "TINYINT(1) default 0")
    private Boolean delFlg;

    /**
     * 创建人。
     */
    @Column(name = "create_user", length = 32)
    private String createUser;

    /**
     * 创建时间。
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 修改人。
     */
    @Column(name = "update_user", length = 32)
    private String updateUser;

    /**
     * 修改时间。
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

}