package com.cnoocshell.member.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.redis.UserInfoKeys;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.member.dao.mapper.AccountMapper;
import com.cnoocshell.member.dao.vo.Account;
import com.cnoocshell.member.dao.vo.Member;
import com.cnoocshell.member.exception.DuplicateString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MemberConsumer {
    private final AccountMapper accountMapper;
    private final BizRedisService bizRedisService;

    @KafkaListener(topics = MqTopicConstant.MEMBER_CHANGE_TOPIC)
    public void memberChangeConsumer(ConsumerRecord<String, String> data) {
        if(Objects.isNull(data) || CharSequenceUtil.isBlank(data.value())){
            log.info("企业信息变更 入参数据为空");
            return;
        }

        log.info("企业信息变更 入参数据：{}",data.value());

        try{
            Member member = JSONUtil.toBean(data.value(), Member.class);
            if(Objects.isNull(member) || CharSequenceUtil.isBlank(member.getMemberCode())){
                log.info("企业信息变更 会员入参数据为空");
                return;
            }
            Account account = new Account();
            account.setMemberName(member.getMemberName());
            account.setMemberShortName(member.getMemberShortName());

            log.info("企业信息变更 账户会员信息更新开始 account:{}",account);

            Condition condition = new Condition(Account.class);
            Condition.Criteria criteria = condition.createCriteria()
                    .andEqualTo(DuplicateString.DEL_FLG,Boolean.FALSE)
                    .andEqualTo(DuplicateString.MEMBER_CODE,member.getMemberCode());
            CommonUtils.andEqualToIfNotNBank(criteria,DuplicateString.MEMBER_ID,member.getMemberId());

            List<Account> accounts = accountMapper.selectByCondition(condition);

            int rs = accountMapper.updateByConditionSelective(account,condition);

            //清除会员下账户信息缓存，加载最新会员信息
            this.cleanAccountsUserInfoCache(accounts);

            log.info("企业信息变更 账户会员信息更新完成 rs:{}",rs);
        }catch (Exception e){
            log.info("企业信息变更 更新账户会员信息失败 error:",e);
        }
    }

    private void cleanAccountsUserInfoCache(List<Account> accounts) {
        if (CollUtil.isEmpty(accounts))
            return;
        List<String> userInfoKeys = accounts.stream()
                .filter(v -> CharSequenceUtil.isNotBlank(v.getAccountId()))
                .map(v -> UserInfoKeys.USER_INFO_KEY + v.getAccountId())
                .collect(Collectors.toList());
        log.info("清除账户用户信息缓存开始 userInfoKeys：{}",userInfoKeys);
        if (CollUtil.isEmpty(userInfoKeys))
            return;
        try {
            bizRedisService.batchDel(userInfoKeys);

            log.info("清除账户用户信息缓存结束 userInfoKeys：{}",userInfoKeys);
        } catch (Exception e) {
            log.error("清除账户用户信息缓存失败 keys:{} ", userInfoKeys, e);
        }
    }

}
