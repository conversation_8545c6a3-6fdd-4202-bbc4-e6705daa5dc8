package com.cnoocshell.member.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.member.api.dto.member.enums.AdvertStatusEnum;
import com.cnoocshell.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.cnoocshell.member.dao.vo.Member;
import com.cnoocshell.member.dao.vo.MemberApproveRequest;

import java.util.Date;
import java.util.List;

public interface IMemberApproveRequestBiz extends IBaseBiz<MemberApproveRequest> {
    String newRequestByContactName(String memberId,String memberCode,
                                   String requestType,
                                   String memberType, String memberName,
                                   String contactName, String contactPhone,
                                   String changeMessage, boolean needApproval, String operatorId);

    List<String> getRegisterTypeList();

    List<String> getIsDealList();

    /**
     * 查找会员最近一次变更
     * @param memberId 会员id
     * @return 变更记录
     */
    MemberApproveRequest findLastChangeRequest(String memberId);

    MemberApproveRequest hasNoApproveRequest(String memberId);

    MemberApproveRequest hasNoApproveRequest(String memberId, String accountId);

    /**
     * 审批一条请求
     * @param requestId
     * @param approveText
     */
    void approveMemberRequest(String requestId, String approveText, String operatorId);
    /**
     * 拒绝一条请求
     * @param requestId
     * @param approveText
     */
    void rejectMemberRequest(String requestId, String approveText, String operatorId);

    String newRequest(String memberId,String memberCode, String memberName, String requestType, String memberType, String changeMessage, boolean needApproval, String operatorId);

    String newRequest(Member member,Boolean needApprove, ApproveRequestTypeEnum requestType,String operatorId);

    MemberApproveRequest findLastChangeRequest(String memberId, ApproveRequestTypeEnum requestType);

    MemberApproveRequest findLastChangeRequest(String memberId, ApproveRequestTypeEnum requestType, AdvertStatusEnum status);

    MemberApproveRequest findLastRequest(String memberId,List<ApproveRequestTypeEnum> requestType,List<AdvertStatusEnum> status);

    /**
     * 查找会员最新的变更请求
     *
     * @param memberId             会员id
     * @param requestType          请求类型
     * @param status               请求状态
     * @param approvalOrCreateTime 审批或创建时间 不为空 审批时间或创建时间小于该时间
     * @return 最新的变更请求记录
     */
    MemberApproveRequest findLatestRequest(String memberId, ApproveRequestTypeEnum requestType, AdvertStatusEnum status, Date approvalOrCreateTime);

}
