package com.cnoocshell.member.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.member.dao.vo.AccountRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface AccountRelationMapper extends IBaseMapper<AccountRelation> {

    @Update("update mb_account_relation set del_flg = 1 where id = #{id} and del_flg = 0")
    int deleteAccountRelation(@Param("id") String id);
}
