package com.cnoocshell.member.biz.impl;


import com.cnoocshell.common.service.common.BaseBiz;
import com.cnoocshell.member.biz.IAccountSubscribeWechatBiz;
import com.cnoocshell.member.dao.vo.MbAccountSubscribeWechat;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class AccountSubscribeWechatBiz extends BaseBiz<MbAccountSubscribeWechat> implements IAccountSubscribeWechatBiz {


}
