package com.cnoocshell.member.service;

import com.cnoocshell.member.api.dto.account.*;
import com.cnoocshell.member.api.dto.account.checkCode.CheckEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.CheckSMSCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetEmailCodeDTO;
import com.cnoocshell.member.api.dto.account.checkCode.GetSMSCodeDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 账户名称唯一，手机号不唯一<br/>
 * 个人通过前端页面PC或手机注册，手机号不可为空，不可重复<br/>
 * 手机上注册则用户名使用uuidGenerator随机生成<br/>
 * 管理员或企业主账号注册，则不检查手机号是否重复的问题<br/>
 * 如果一个手机号在账户表中唯一，则可以用账户名和手机号登陆均可<br/>
 * 如果手机号不唯一，且相同手机号对应的账号没有绑定到一起，则只能用账户名登陆<br/>
 * 如果手机号不唯一，但是同手机号对应的账号已经绑定到一起，则用账户名或手机号登陆均可，默认登陆的账号为第一个密码校验通过的账号<br/>
 * 各接口内部不没有校验短信验证码，请调用接口前保证调用的有效性
 *
 * @Description 账号对外服务接口
 */
public interface IAccountService {
    /**
     * 根据用户名或手机号和密码，本地数据库登陆校验
     *
     * @param accountLoginDTO 登录信息（用户名或手机号）
     * @return 校验通过则返回账户名和是否需要修改密码的标识，否则返回空
     */
    AccountDTO loginVerification(AccountLoginDTO accountLoginDTO);

    /**
     * 手机动态码登陆
     *
     * @param phoneCodeLoginDTO 登录对象
     * @return 账户信息
     */
    AccountDTO loginByPhoneVerificationCode(PhoneCodeLoginDTO phoneCodeLoginDTO);

    /**
     * 校验手机号是否已被个人账号使用
     *
     * @param phoneNumber 手机号 personal
     * @return 存在则返回true
     */
    boolean checkMobilePhoneExistsByPersonl(String phoneNumber);

    /**
     * 校验新手机号是否已被企业主账号使用
     *
     * @param phoneNumber 手机号
     * @return 存在则返回true
     */
    boolean checkMobilePhoneExistsByMasterAccount(String phoneNumber);

    /**
     * 校验短信验证码
     *
     * @param dto 校验短信验证码DTO对象
     * @return
     */
    Boolean checkSMSCode(CheckSMSCodeDTO dto);

    /**
     * 1个人PC注册账户<br/>
     * 个人通过前端页面PC注册，手机号不可为空，不可重复<br/>
     *
     * @param accountRegisterDTO 注册账户
     * @return AccountDTO 账户DTO对象
     */
    AccountDTO registerAccountByPC(AccountPCRegisterDTO accountRegisterDTO);

    /**
     * 校验新用户名是否已被占用(注册或修改用户名前校验使用)
     *
     * @param newAccountName 新用户名
     * @return 存在则返回true
     */
    boolean checkAccountNameExists(String newAccountName);

    /**
     * 根据id查询单个账户信息(仅含账户表信息)
     *
     * @param accountId 账户id
     * @return AccountDTO    单个账户信息
     */
    AccountDTO findById(String accountId);

    AccountDTO findByEmail(String email);

    /**
     * 根据id查询账户简单信息
     *
     * @param accountId 账户id
     * @return 账户简单信息
     */
    AccountSimpleDTO findSimpleById(String accountId);

    /**
     * 根据手机号和会员登陆类型（即所在app）查询账户信息
     *
     * @param mobilePhoneNumber 手机号
     * @param loginType         会员登陆类型
     * @return AccountDTO    账户信息的List
     */
    List<AccountDTO> findByMobilePhoneAndLoginType(String mobilePhoneNumber, String loginType);

    /**
     * 根据手机号查询账户信息
     *
     * @param mobilePhoneNumber 手机号
     * @return AccountDTO    账户信息的List
     */
    List<AccountDTO> findByMobilePhone(String mobilePhoneNumber);

    /**
     * 校验电子邮件验证码
     *
     * @param dto 校验短信验证码DTO对象
     * @return
     */
    Boolean checkEmailCode(CheckEmailCodeDTO dto);

    /**
     * 校验手机号是否已被占用
     *
     * @param phoneNumber 手机号
     * @return 存在则返回true
     */
    boolean checkMobilePhoneExists(String phoneNumber);

    /**
     * 获取短信验证码
     *
     * @param dto 短信验证码DTO对象
     */
    String getSMSCode(GetSMSCodeDTO dto);

    /**
     * 第2步 已登陆用户，校验短信验证码，修改自己的手机号
     *
     * @param accountId   账户id
     * @param phoneNumber 新手机
     * @param operator    操作人AccountId
     * @param ip          操作人ip
     */
    void updateMobilePhone(String accountId, String phoneNumber, String operator, String ip);

    /**
     * 获取电子邮件验证码
     *
     * @param dto 短信验证码DTO对象
     */
    String getEmailCode(GetEmailCodeDTO dto);

    /**
     * 更新账户基本信息
     *
     * @param accountBaseInfoUpdateDTO 账户基本信息
     */
    void updateBaseInfo(AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO);

    /**
     * 第2步  已登陆用户，修改自己的用户名(如果账号名称就是手机号，则账户名称也将被修改)
     *
     * @param accountId      账户id
     * @param newAccountName 新用户名
     * @param operator       操作人AccountId
     */
    void updateAccountName(String accountId, String newAccountName, String operator, String ip);

    /**
     * 7企业主账号创建子账号（含角色、数据权限、组织机构、销售区域）
     *
     * @param accountRegisterByMasterDTO 注册账户
     * @return AccountDTO 账户DTO对象
     */
    AccountDTO registerSubAccountByMaster(AccountRegisterByMasterDTO accountRegisterByMasterDTO);

    /**
     * 根据条件查询账号（平台管理账号列表查询使用）
     *
     * @param accountSearchDTO 查询条件集合
     * @return 账号信息的分页信息
     */
    PageInfo<AccountDTO> findAll(AccountSearchDTO accountSearchDTO);

    /**
     * 禁用账号
     *
     * @param accountId 账户id
     */
    void disabled(String accountId, String reason, String operator);

    /**
     * 启用账号
     *
     * @param accountId 账户id
     */
    void enabled(String accountId, String operator);

    /**
     * 根据账户名查询单个账户信息
     *
     * @param accountName 账户名(userName)
     * @return AccountDTO    单个账户信息
     */
    AccountDTO findByAccountName(String accountName);

    /**
     * 根据id查询单个账户信息(含角色、权限、销售区域、组织机构、数据权限)
     *
     * @param accountId 账户id
     * @return AccountDTO    单个账户信息
     */
    AccountDTO findDetailById(String accountId);

    AccountDTO findDetailById(String accountId, String memberId);

    /**
     * 更新子账账户信息
     *
     * @param subAccountUpdateDTO 子账户信息(含id、组织机构、角色、销售区域、数据权限)
     */
    void updateSubAccountInfo(SubAccountUpdateDTO subAccountUpdateDTO);

    /**
     * 切换主账户
     * 改变账号的类型 类型参考：AccountDTO中的常量
     *
     * @param changeAccountTypeDTO 　方法参数（包含会员id和账户id）
     * @return
     */
    void changeAccountType(ChangeAccountTypeDTO changeAccountTypeDTO);

    void refreshLoginInfo(RefreshLoginInfoDTO dto);

    /**
     * 解绑账号
     * @param currUserAccountId	当前用户id
     * @param unBindingAccountId	要解绑的用户id
     */
    void unBindingAccount(String currUserAccountId,String unBindingAccountId);

    /**
     * 根据ids查询账户信息(仅含账户表信息)
     * @param ids ids
     * @return	AccountDTO	账户信息
     */
    List<AccountDTO> findByIds(List<String> ids);

    List<AccountNameDTO> findAccountNameByRoleCode(String roleCode);

    List<AccountInfoReturnDTO> findAccountByAccountId(List<String> ids);

    List<AccountSimpleDTO> listSimpleByIds(List<String> accountIds);

    boolean existsAccountByEmail(String email,String exceptAccountId);

    boolean existsAccountByMobile(String mobile,String exceptAccountId);
}
