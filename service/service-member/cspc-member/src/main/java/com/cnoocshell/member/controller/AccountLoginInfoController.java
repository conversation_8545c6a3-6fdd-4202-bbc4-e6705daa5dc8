package com.cnoocshell.member.controller;

import com.cnoocshell.member.api.dto.account.AccountLoginInfoDTO;
import com.cnoocshell.member.service.IAccountLoginInfoService;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * @Version:2
 * @Description:null
 */

@Tag(name = "AccountLoginInfo", description = "登陆日志查询接口")
@RestController
@RequestMapping("/accountLoginInfo")
@RequiredArgsConstructor
public class AccountLoginInfoController {

    private final IAccountLoginInfoService iAccountLoginInfoService;

    @ApiOperation("下线时更新登录日志")
    @PostMapping(value = "/offline")
    public void offline(@RequestParam(value = "sessionId") String sessionId, @RequestParam("reason") String reason, @RequestParam(value = "operator", required = false) String operator) {
        iAccountLoginInfoService.offline(sessionId, reason, operator);
    }

    @ApiOperation("新增登录记录")
    @PostMapping(value = "/insertLoginInfo")
    public void insertLoginInfo(@RequestBody AccountLoginInfoDTO param) {
        iAccountLoginInfoService.insertLoginInfo(param);
    }

}
