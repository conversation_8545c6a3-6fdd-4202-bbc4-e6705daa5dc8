package com.cnoocshell.member.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.member.api.dto.member.BiddingMemberDTO;
import com.cnoocshell.member.api.dto.member.QueryBiddingMemberDTO;
import com.cnoocshell.member.api.dto.member.intention.MemberIntentionSimpleDTO;
import com.cnoocshell.member.api.dto.report.MemberIntentionReportResultDTO;
import com.cnoocshell.member.dao.vo.MemberPurchaseGoodsIntention;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MemberPurchaseGoodsIntentionMapper extends IBaseMapper<MemberPurchaseGoodsIntention> {

    List<BiddingMemberDTO> queryBiddingMemberList(@Param("param") QueryBiddingMemberDTO param);

    List<MemberIntentionReportResultDTO> queryMemberIntentionReport(@Param("memberIds") List<String> memberIds,
                                                                    @Param("goodsCodes") List<String> goodsCodes);

    List<String> queryMemberIdConcatGoodsCodeBySaleChannel(@Param("memberIds") List<String> memberIds,
                                                           @Param("saleChannels") List<String> saleChannels);

    List<MemberIntentionSimpleDTO> queryIntentionBySaleChannel(@Param("saleChannels") List<String> saleChannels);
}
