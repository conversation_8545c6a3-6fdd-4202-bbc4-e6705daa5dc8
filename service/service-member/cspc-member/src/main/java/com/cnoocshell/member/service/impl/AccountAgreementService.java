package com.cnoocshell.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.cnoocshell.member.api.dto.account.AccountAgreementDTO;
import com.cnoocshell.member.biz.IAccountAgreementBiz;
import com.cnoocshell.member.dao.vo.AccountAgreement;
import com.cnoocshell.member.service.IAccountAgreementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountAgreementService implements IAccountAgreementService {
    private final IAccountAgreementBiz iAccountAgreementBiz;

    @Override
    public void saveAgreement(List<AccountAgreementDTO> list) {
        if(CollUtil.isEmpty(list))
            return;
        List<AccountAgreement> agreements = list.stream().map(v->{
            AccountAgreement target = BeanUtil.toBean(v, AccountAgreement.class);
            target.setId(iAccountAgreementBiz.getUuidGeneratorGain());
            target.handleUser(v.getAccountId(),true);
            return target;
        }).collect(Collectors.toList());

        iAccountAgreementBiz.insertList(agreements);
    }

}
