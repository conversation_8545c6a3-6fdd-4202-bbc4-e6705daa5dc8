package com.cnoocshell.member.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "mb_account_relation")
@EqualsAndHashCode(callSuper = true)
public class AccountRelation extends BaseEntity {

    /**
     * 会员id
     */
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 账号ID
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 账号真实姓名
     */
    @Column(name = "account_real_name")
    private String accountRealName;

    /**
     * 账户角色
     */
    @Column(name = "account_role")
    private String accountRole;

    /**
     * 关联账号ID
     */
    @Column(name = "link_account_id")
    private String linkAccountId;

    /**
     * 关联账号真实姓名
     */
    @Column(name = "link_account_real_name")
    private String linkAccountRealName;

    /**
     * 关联账号角色
     */
    @Column(name = "link_account_role")
    private String linkAccountRole;
}
