package com.cnoocshell.member.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.member.dao.vo.AccountLoginInfo;
import com.cnoocshell.member.exception.DuplicateString;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

@Mapper
public interface AccountLoginInfoMapper extends IBaseMapper<AccountLoginInfo> {

    /**
     * 查询一个账号最近1天的最近20个相同登陆方式的登陆sessionId
     *
     * @param accountId
     * @return
     */
    @Select("select session_id from mb_account_login_info where account_id='${accountId}' and logout_time is null and now() - login_time <1000000 order by login_time desc limit ${limitSize}")
    List<String> findLastSessionIdByAccountId(@Param(DuplicateString.ACCOUNT_ID) String accountId, @Param("limitSize") Integer limitSize);

    @Select("select session_id from mb_account_login_info where account_id='${accountId}' and login_type='${loginType}' and logout_time is null and now() - login_time <1000000 order by login_time desc limit 20")
    List<String> findLastSessionId(@Param(DuplicateString.ACCOUNT_ID) String accountId, @Param("loginType") String loginType);

    @Select("select session_id from mb_account_login_info where logout_time is null and now() - login_time <1000000 order by login_time desc limit ${offset},${limitSize}")
    List<String> findLoginSessionId(@Param("offset") Integer offset, @Param("limitSize") Integer limitSize);

    List<String> findLoginSessionId2(@Param("accountIdSet") Set<String> accountIdSet, @Param("ondDayBefore") String ondDayBefore, @Param("offset") Integer offset, @Param("limitSize") Integer limitSize);


    List<String> findLastSessionIdByTerminal(@Param(DuplicateString.ACCOUNT_ID) String accountId, @Param("terminal") String terminal);
}