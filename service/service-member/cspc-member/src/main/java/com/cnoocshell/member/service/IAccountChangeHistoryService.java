package com.cnoocshell.member.service;

import com.cnoocshell.member.api.dto.account.AccountChangeHistoryDTO;
import com.cnoocshell.member.api.dto.account.PageAccountChangeHistoryDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;


public interface IAccountChangeHistoryService {
    /**
     * 新增员工授权，启用，禁用历史
     *
     * @param accountChangeHistoryDTO 新增员工授权，启用，禁用历史入参
     * @param operator                操作人
     */
    void add(AccountChangeHistoryDTO accountChangeHistoryDTO, String operator);

    /**
     * 根据DTO查询员工授权，启用，禁用历史
     *
     * @param accountChangeHistoryDTO 根据DTO查询员工授权，启用，禁用历史入参
     */
    List<AccountChangeHistoryDTO> findByQuery(AccountChangeHistoryDTO accountChangeHistoryDTO);

    /**
     * 根据DTO分页查询员工授权，启用，禁用历史
     *
     * @param pageAccountChangeHistoryDTO 根据DTO分页查询员工授权，启用，禁用历史入参
     */
    PageInfo<AccountChangeHistoryDTO> pageHistoryInfoList(PageAccountChangeHistoryDTO pageAccountChangeHistoryDTO);
}
