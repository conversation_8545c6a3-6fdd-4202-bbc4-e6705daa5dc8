package com.cnoocshell.member.biz;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.member.api.dto.member.MemberCertDTO;
import com.cnoocshell.member.api.dto.member.enums.MemberCertTypeEnum;
import com.cnoocshell.member.dao.vo.MemberCert;

import java.util.List;

public interface IMemberCertBiz extends IBaseBiz<MemberCert> {
    void setMemberCertAttachment(String attachmentId, MemberCertDTO memberCertDTO);

    List<MemberCertDTO> findByMemberId(String memberId);

    List<MemberCertDTO> cert2CertDTO(List<MemberCert> list);

    void saveCertDTOList(List<MemberCertDTO> memberCertDTOList, String memberId, String accountId, String operatorId);

    void signDeleteSameCertType(MemberCert cert, String memberId);

    List<MemberCert> findByMemberId(String memberId,List<MemberCertTypeEnum> certType);

    void handleMemberCert(List<MemberCertDTO> certs);
}
