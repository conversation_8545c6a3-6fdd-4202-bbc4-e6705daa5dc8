package com.cnoocshell.member.biz;

import com.cnoocshell.member.api.dto.account.checkCode.*;

/**
 * @DESCRIPTION:
 */
public interface IVerificationCodeBiz {

    /**
     * 获取图形验证码
     * @param dto
     */
    CaptchaDTO getCaptcha(GetCaptchaDTO dto);

    /**
     * 校验图形验证码
     * @param dto
     */
    void checkCaptcha(CheckCaptchaDTO dto);

    /**
     * 获取短信验证码
     * @param dto
     */
    String getSMSCode(GetSMSCodeDTO dto);

    /**
     * 校验短信验证码
     * @param dto
     * @return
     */
    void checkSMSCode(CheckSMSCodeDTO dto);
    //邮件验证码
    boolean checkEmailCode(CheckEmailCodeDTO dto);

    String getEmailCode(GetEmailCodeDTO dto);

    void cleanRedisKeys(String operator);
}
