package com.cnoocshell.member.common;

/**
 * @DESCRIPTION:
 */
public class RedisKey {

    // 私有构造函数，防止实例化
    private RedisKey() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated.");
    }

    public static final String USER_LOGIN_RETRY_COUNT="member:login:retry_count:";

    public static final String MEMBER_SERVICE_AREA = "member:service:area";

    public static final String MEMBER_ACCOUNT_CACHE = "member:account:cache:";
}
