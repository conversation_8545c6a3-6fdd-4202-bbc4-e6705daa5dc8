package com.cnoocshell.member.service;

import com.cnoocshell.base.api.dto.role.RolePageReponseDTO;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationInfoDTO;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationPage;
import com.cnoocshell.member.api.dto.accountRelation.AccountRelationPageRequestDTO;
import com.cnoocshell.member.api.dto.accountRelation.QueryAccountRelationDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface IAccountRelationService {

    PageInfo<AccountRelationPage> findAccountRelationByCondition(AccountRelationPageRequestDTO requestDTO);

    Boolean addAccountRelation(AccountRelationInfoDTO requestDTO);

    int deleteAccountRelation(String id);

    List<AccountRelationInfoDTO> queryAccountRelation(QueryAccountRelationDTO param);
}
