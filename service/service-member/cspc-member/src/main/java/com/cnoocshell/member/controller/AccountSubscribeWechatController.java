package com.cnoocshell.member.controller;


import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.member.api.dto.account.AccountSubscribeWechatDTO;
import com.cnoocshell.member.api.dto.account.MbAccountSubscribeWechatDTO;
import com.cnoocshell.member.api.dto.account.QueryAccountSubscribeWechatDTO;
import com.cnoocshell.member.service.impl.AccountSubscribeWechatService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/accountSubscribeWechat")
public class AccountSubscribeWechatController {

    @Autowired
    private AccountSubscribeWechatService accountSubscribeWechatService;

    @ApiOperation("用户订阅微信消息记录")
    @PostMapping(value = "/createSubscribeRecord")
    public ItemResult<String> createSubscribeRecord(@RequestBody MbAccountSubscribeWechatDTO accountSubscribeWechatDTO) {
        return accountSubscribeWechatService.createSubscribeRecord(accountSubscribeWechatDTO);
    }

    /**
     *
     * @param accountSubscribeWechatDTO
     * @return 返回true表示已订阅
     */
    @ApiOperation("检查用户是否已订阅")
    @PostMapping(value = "/checkAccountSubscribe")
    public ItemResult<Boolean> checkAccountSubscribe(@RequestBody MbAccountSubscribeWechatDTO accountSubscribeWechatDTO) {
        return accountSubscribeWechatService.checkAccountSubscribe(accountSubscribeWechatDTO);
    }

    @ApiOperation("根据条件查询微信通知订阅记录")
    @PostMapping(value = "/querySubscribeByCondition")
    List<AccountSubscribeWechatDTO> querySubscribeByCondition(@RequestBody QueryAccountSubscribeWechatDTO param){
     return accountSubscribeWechatService.querySubscribeByCondition(param);
    }

    /**
     *
     * @param accountSubscribeWechatDTO
     * @return 返回true表示已订阅
     */
    @ApiOperation("检查用户是否已订阅")
    @PostMapping(value = "/updateSendStatus")
    public ItemResult<String> updateSendStatus(@RequestBody MbAccountSubscribeWechatDTO accountSubscribeWechatDTO) {
        return accountSubscribeWechatService.updateSendStatus(accountSubscribeWechatDTO);
    }

}
