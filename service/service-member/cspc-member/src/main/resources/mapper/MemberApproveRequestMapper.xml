<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.member.dao.mapper.MemberApproveRequestMapper">
<select id="pageByCondition" resultType="com.cnoocshell.member.dao.vo.MemberApproveRequest">
    select tb1.*
    from mb_member_approve_request tb1
             left join mb_member tb2 on tb1.member_id = tb2.member_id
    where tb1.del_flg = 0
    <if test="param.memberId != null and param.memberId !=''">
        AND tb2.member_id = #{param.memberId}
    </if>
    <if test="param.memberCode != null and param.memberCode !=''">
        AND tb1.member_code=#{param.memberCode}
    </if>
    <if test="param.requestId != null and param.requestId !=''">
        AND tb1.request_id=#{param.requestId}
    </if>
    <if test="param.requestNum != null and param.requestNum !=''">
        AND tb1.request_num=#{param.requestNum}
    </if>
    <if test="param.memberName != null and param.memberName !=''">
        AND tb1.member_name like CONCAT('%',#{param.memberName},'%')
    </if>
    <if test="param.contactName != null and param.contactName !=''">
        AND tb1.contact_name like CONCAT('%',#{param.contactName},'%')
    </if>
    <if test="param.contactPhone != null and param.contactPhone !=''">
        AND tb1.contact_phone like CONCAT('%',#{param.contactPhone},'%')
    </if>
    <if test="param.requestType != null and param.requestType !=''">
        AND tb1.request_type = #{param.requestType}
    </if>
    <if test="param.memberType != null and param.memberType !=''">
        AND tb2.member_type = #{param.memberType}
    </if>
    <if test="param.status != null and param.status !=''">
        AND tb1.status = #{param.status}
    </if>
    order by create_time desc
</select>

    <select id="findLastRequest" resultType="com.cnoocshell.member.dao.vo.MemberApproveRequest">
        select * from mb_member_approve_request
        where member_id =#{memberId}
        and del_flg = 0
        <if test="requestTypeList != null and requestTypeList.size>0">
            AND request_type IN
            <foreach collection="requestTypeList" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size>0">
            AND status IN
            <foreach collection="statusList" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>
        order by request_time desc limit 1
    </select>

    <select id="pageMemberChangeRequest" resultType="com.cnoocshell.member.dao.vo.MemberApproveRequest">
        select tb1.*
        from mb_member_approve_request tb1
        left join mb_member tb2 on tb1.member_id = tb2.member_id
        where tb1.del_flg = 0 AND tb1.status != '600'
        <if test="param.memberId != null and param.memberId !=''">
            AND tb2.member_id = #{param.memberId}
        </if>
        <if test="param.memberCode != null and param.memberCode !=''">
            AND tb1.member_code=#{param.memberCode}
        </if>
        <if test="param.requestId != null and param.requestId !=''">
            AND tb1.request_id=#{param.requestId}
        </if>
        <if test="param.requestNum != null and param.requestNum !=''">
            AND tb1.request_num=#{param.requestNum}
        </if>
        <if test="param.memberName != null and param.memberName !=''">
            AND tb1.member_name like CONCAT('%',#{param.memberName},'%')
        </if>
        <if test="param.contactName != null and param.contactName !=''">
            AND tb1.contact_name like CONCAT('%',#{param.contactName},'%')
        </if>
        <if test="param.contactPhone != null and param.contactPhone !=''">
            AND tb1.contact_phone like CONCAT('%',#{param.contactPhone},'%')
        </if>
        <if test="param.requestType != null and param.requestType !=''">
            AND tb1.request_type = #{param.requestType}
        </if>
        <if test="param.memberType != null and param.memberType !=''">
            AND tb2.member_type = #{param.memberType}
        </if>
        <if test="param.status != null and param.status !=''">
            AND tb1.status = #{param.status}
        </if>
        order by create_time desc
    </select>

    <select id="findLatestRequest" resultType="com.cnoocshell.member.dao.vo.MemberApproveRequest">
        select tb1.*
        from mb_member_approve_request tb1
        where tb1.del_flg = 0
          and tb1.member_id = #{memberId}
          and tb1.request_type = #{requestType}
          and tb1.status = #{status}
          and COALESCE(tb1.approve_time, tb1.create_time) &lt; #{approvalOrCreateTime}
        order by COALESCE(tb1.approve_time, tb1.create_time) desc
        limit 1
    </select>
</mapper>