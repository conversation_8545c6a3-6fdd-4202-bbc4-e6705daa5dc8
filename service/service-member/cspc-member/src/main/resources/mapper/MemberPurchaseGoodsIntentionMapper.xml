<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.member.dao.mapper.MemberPurchaseGoodsIntentionMapper">
    <select id="queryBiddingMemberList" resultType="com.cnoocshell.member.api.dto.member.BiddingMemberDTO">
        select tb0.member_id,
        tb0.member_code,
        tb0.member_name,
        tb0.crm_code,
        GROUP_CONCAT(main_goods_code) 'main_goods_codes',
        GROUP_CONCAT(minor_goods_code) 'minor_goods_codes'
        from (select tb1.member_id,
        tb1.member_code,
        tb1.crm_code,
        tb1.member_name,
        CASE WHEN tb2.intention_type = 1 THEN tb2.goods_code ELSE null END AS 'main_goods_code',
        CASE WHEN tb2.intention_type = 2 THEN tb2.goods_code ELSE null END AS 'minor_goods_code'
        from mb_member tb1
        left join mb_member_purchase_goods_intention tb2
        on tb1.member_id = tb2.member_id and tb2.del_flg = 0
        where tb1.del_flg = 0
        and EXISTS (select 1
        FROM mb_member_purchase_goods_intention tb3
        where tb3.del_flg = 0
        and tb1.member_id = tb3.member_id
        and tb3.goods_code = #{param.goodsCode})
        <if test="param.memberName != null and param.memberName !=''">
            and tb1.member_name like CONCAT('%', #{param.memberName}, '%')
        </if>
        <if test="param.crmCode != null and param.crmCode !=''">
            and tb1.crm_code like CONCAT('%', #{param.crmCode}, '%')
        </if>
        <if test="param.notInMemberCodes != null and param.notInMemberCodes.size>0">
            and tb1.member_code not in
            <foreach collection="param.notInMemberCodes" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <if test="param.inMemberCodes != null and param.inMemberCodes.size>0">
            and tb1.member_code  in
            <foreach collection="param.inMemberCodes" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>
        <if test="param.mainGoods != null and param.mainGoods !=''">
            AND EXISTS(
                select 1 FROM mb_member_purchase_goods_intention tb4
                where tb4.del_flg=0 and tb4.intention_type=1
                 and tb1.member_id = tb4.member_id
                AND tb4.goods_id=#{param.mainGoods}
            )
        </if>
        <if test="param.minorGoods != null and param.minorGoods !=''">
            AND EXISTS(
            select 1 FROM mb_member_purchase_goods_intention tb5
            where tb5.del_flg=0 and tb5.intention_type=2
            and tb1.member_id = tb5.member_id
            AND tb5.goods_id=#{param.minorGoods}
            )
        </if>

        order by tb1.member_code, tb2.intention_type) tb0
        group by tb0.member_id
    </select>

    <select id="queryMemberIntentionReport"
            resultType="com.cnoocshell.member.api.dto.report.MemberIntentionReportResultDTO">
        select
        tb1.member_id ,
        tb1.goods_code ,
        tb1.intention_type ,
        tb1.sale_channel
        from mb_member_purchase_goods_intention tb1
        where tb1.del_flg = 0
        <if test="memberIds != null and memberIds.size>0">
            and tb1.member_id IN
            <foreach collection="memberIds" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <if test="goodsCodes != null and goodsCodes.size>0">
            and tb1.goods_code IN
            <foreach collection="goodsCodes" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>
    </select>

    <select id="queryMemberIdConcatGoodsCodeBySaleChannel" resultType="java.lang.String">
        select DISTINCT concat(tb1.member_id, '_', tb1.goods_code)
        from mb_member_purchase_goods_intention tb1
        where tb1.del_flg = 0
        and tb1.member_id is not null
        and tb1.goods_code is not null
        <if test="memberIds != null and memberIds.size > 0">
            and tb1.member_id IN
            <foreach collection="memberIds" item="memberId" open="(" separator="," close=")">
                #{memberId}
            </foreach>
        </if>
        <if test="saleChannels != null and saleChannels.size > 0">
            and tb1.sale_channel IN
            <foreach collection="saleChannels" item="saleChannel" open="(" separator="," close=")">
                #{saleChannel}
            </foreach>
        </if>
    </select>

    <select id="queryIntentionBySaleChannel"
            resultType="com.cnoocshell.member.api.dto.member.intention.MemberIntentionSimpleDTO">
        select
        tb1.id,
        tb1.member_id ,
        tb1.member_code ,
        tb1.intention_type ,
        tb1.goods_category_id ,
        tb1.goods_id ,
        tb1.goods_code ,
        tb1.sale_channel,
        CONCAT(tb1.goods_code,'_',tb1.member_code) AS 'goodsCodeConcatMemberCode'
        FROM
        mb_member_purchase_goods_intention tb1
        where tb1.del_flg =0
        and tb1.goods_code is not null
        and tb1.member_code is not null
        <if test="saleChannels != null and saleChannels.size > 0">
            and tb1.sale_channel IN
            <foreach collection="saleChannels" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
