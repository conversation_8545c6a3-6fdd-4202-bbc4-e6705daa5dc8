<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.member.dao.mapper.AccountLoginInfoMapper">

    <select id="findLoginSessionId2" resultType="java.lang.String">
        select session_id
        from mb_account_login_info
        where logout_time is null and login_time>'${ondDayBefore}'
        <foreach collection='accountIdSet' index='index' item='item' open=' and account_id in (' separator=','
                 close=')'>
            #{item}
        </foreach>
        order by login_time desc limit ${offset},${limitSize}
    </select>

    <select id="findLastSessionIdByTerminal" resultType="java.lang.String">
        select session_id
        from mb_account_login_info
        where account_id = #{accountId}
          <if test="terminal != null and terminal !=''">
              and terminal = #{terminal}
          </if>
          and logout_time is null
          and now() - login_time &lt; 1000000
        order by login_time desc limit 20
    </select>
</mapper>