<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.member.dao.mapper.MbAccountSubscribeWechatMapper">
    <!-- 定义ResultMap -->
    <resultMap id="BaseResultMap" type="com.cnoocshell.member.dao.vo.MbAccountSubscribeWechat">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="account_id" property="accountId" jdbcType="VARCHAR"/>
        <result column="account_real_name" property="accountRealName" jdbcType="VARCHAR"/>
        <result column="business_no" property="businessNo" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="open_id" property="openId" jdbcType="VARCHAR"/>
        <result column="template_id" property="templateId" jdbcType="VARCHAR"/>
        <result column="template_name" property="templateName" jdbcType="VARCHAR"/>
        <result column="send_status" property="sendStatus" jdbcType="VARCHAR"/>
        <result column="del_flg" property="delFlg" jdbcType="BIT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <update id="updateSendStatus">
        update
        mb_account_subscribe_wechat
        set
        send_status = #{sendStatus},
        update_user = #{updateUser},
        update_time = #{updateTime}
        where
        business_no = #{businessNo}
        and open_id = #{openId}
    </update>

</mapper>
