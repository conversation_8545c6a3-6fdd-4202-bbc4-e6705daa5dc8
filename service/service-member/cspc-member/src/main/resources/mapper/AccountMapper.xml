<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.member.dao.mapper.AccountMapper">

    <select id="findAccountByAccountId" resultType="com.cnoocshell.member.api.dto.account.AccountInfoReturnDTO">
        SELECT
        account_id,
        real_name ,
        email ,
        mobile
        FROM
        mb_account
        where
        del_flg=0
        and
        account_id in
        <foreach item="item" index="index" collection="accountIds" open="(" separator="," close=" )">
            #{item}
        </foreach>
    </select>
<select id="listAccountIdByMemberCodes" resultType="java.lang.String">
    select account_id from mb_account
    where del_flg=0 and
    member_code in
    <foreach collection="memberCodes" item="item1" open="(" separator="," close=")">
        #{item1}
    </foreach>
</select>
    <select id="selectByAccountIds" resultType="com.cnoocshell.member.dao.vo.Account">
        select * from mb_account
        where del_flg=0 and
        account_id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=" )">
            #{item}
        </foreach>
    </select>
</mapper>
