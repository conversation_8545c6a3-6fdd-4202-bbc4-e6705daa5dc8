<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.member.dao.mapper.MemberMapper">

    <update id="requestRefund">
        update
        mb_member set
        deposit_status = #{dto.depositStatus},
        deposit_status_update_time = #{dto.depositStatusUpdateTime},
        deposit_status_update_user = #{dto.accountId}
        where
        member_id = #{dto.memberId}
    </update>

    <update id="updateDepositStatus">
        update
        mb_member
        set
        deposit_status = #{code},
        deposit_status_update_time = #{date},
        deposit_status_update_user =#{accountId}
        where
        crm_code in
        <foreach item="item" index="index" collection="unPayUserList" open="(" separator="," close=" )">
            #{item}
        </foreach>
    </update>

    <select id="queryMemberDataInfo" resultType="com.cnoocshell.member.api.dto.member.MemberDataInfoDTO">
        SELECT
        DISTINCT
        mm.member_name,
        mm.member_code
        FROM
        mb_member_purchase_goods_intention mmp
        inner join mb_member mm on
        mm.member_id = mmp.member_id
        where
        mmp.goods_code = #{goodsCode}
        and mmp.del_flg =0
        and mm.del_flg =0
    </select>

    <select id="queryMemberInfoByGoodsCode"  resultType="com.cnoocshell.member.api.dto.member.MemberInfoForGoodsDTO">
        SELECT
        mm.member_code,
        mmpgi.intention_type,
        mmpgi.goods_id,
        mm.crm_code
        FROM
        mb_member_purchase_goods_intention mmpgi
        inner join mb_member mm on  mm.member_id = mmpgi.member_id
        where
        mmpgi.del_flg =0
        and mm.del_flg =0
        and mmpgi.member_code in
        <foreach item="item" index="index" collection="dto.memberCodeList" open="(" separator="," close=" )">
            #{item}
        </foreach>
        <if test = "dto.crmCode != null  and dto.crmCode !=''">
            and mm.crm_code  LIKE CONCAT('%', #{dto.crmCode}, '%')
        </if>
        <if test = "dto.creditCode != null  and dto.creditCode !=''">
            and mm.credit_code   LIKE CONCAT('%', #{dto.creditCode}, '%')
        </if>
    </select>

    <select id="queryGoodsByMemberCode" resultType="com.cnoocshell.member.api.dto.member.MemberGoodsInfoDTO">
        SELECT
        mmpgi.intention_type,
        mmpgi.goods_id,
        mmpgi.goods_code,
        mm.member_code,
        mm.crm_code
        FROM
        mb_member_purchase_goods_intention mmpgi  inner join mb_member mm on  mm.member_id = mmpgi.member_id
        where
        mmpgi.member_code in
        <foreach item="item" index="index" collection="memberCodeList" open="(" separator="," close=" )">
            #{item}
        </foreach>
        and mmpgi.del_flg = 0
        and mm.del_flg =0
    </select>

    <select id="queryMemberByMemberNameAndCrmCode" resultType="com.cnoocshell.member.api.dto.member.MemberSimpleDataDTO">
        select member_name,member_code,crm_code from mb_member
        where del_flg = 0 and member_type = '201'
        <if test="memberName != null and memberName !=''" >
            and member_name LIKE CONCAT('%', #{memberName}, '%')
        </if>
        <if test="crmCode != null and crmCode !=''" >
            and crm_code LIKE CONCAT('%', #{crmCode}, '%')
        </if>
        order by crm_code
    </select>

    <select id="queryMemberDepositStatus"  resultType="com.cnoocshell.member.api.dto.member.MemberDepositStatusDataDTO">
        SELECT
        DISTINCT
        mm.member_id,
        mm.member_name,
        mm.crm_code ,
        mm.deposit_status,
        ma.real_name,
        mm.deposit_status_update_time updateTime,
        mm.member_code
        FROM
        mb_member_purchase_goods_intention mmp
        inner join mb_member mm on  mm.member_code = mmp.member_code
        left join mb_account ma on  ma.account_id = mm.deposit_status_update_user and ma.del_flg =0
        where
        mmp.goods_code in
        <foreach item="item" index="index" collection="dto.goodsCodeList" open="(" separator="," close=" )">
            #{item}
        </foreach>
        and mm.member_type = #{dto.memberType}
        <if test="dto.memberName !=null and dto.memberName !='' ">
            and mm.member_name  LIKE CONCAT('%', #{dto.memberName}, '%')
        </if>
        <if test="dto.crmCode !=null and dto.crmCode !='' ">
            and mm.crm_code  LIKE CONCAT('%', #{dto.crmCode}, '%')
        </if>
        <if test="dto.depositStatus !=null and dto.depositStatus !='' ">
            and mm.deposit_status  LIKE CONCAT('%', #{dto.depositStatus}, '%')
        </if>
        <if test="dto.depositStatusUpdateStartTime != null and dto.depositStatusUpdateStartTime !='' ">
            and mm.deposit_status_update_time &gt;= #{dto.depositStatusUpdateStartTime}
        </if>
        <if test="dto.depositStatusUpdateEndTime != null and dto.depositStatusUpdateEndTime !='' ">
            and mm.deposit_status_update_time &lt;= #{dto.depositStatusUpdateEndTime}
        </if>
        and mm.del_flg =0
        and mmp.del_flg =0
        and mm.crm_code is not null
        order by CONVERT(mm.member_name USING gbk) ASC
    </select>

    <select id="queryMainGoodsCode" resultType="java.lang.String">
        SELECT
        goods_code
        FROM
        mb_member_purchase_goods_intention
        where
        member_id = #{memberId}
        and del_flg = 0
        and intention_type = 1
        limit 1
    </select>

    <select id="queryAccountInfo" resultType="com.cnoocshell.member.api.dto.account.AccountInfoReturnDTO">
        SELECT
        account_id,
        real_name ,
        email ,
        mobile
        FROM
        mb_account
        where
        account_id in
        <foreach item="item" index="index" collection="accountIds" open="(" separator="," close=" )">
            #{item}
        </foreach>
    </select>

    <select id="queryDepositStatus" resultType="java.lang.String">
        SELECT
        deposit_status
        FROM
        mb_member
        where
        member_code = #{memberCode}
        and del_flg = 0
        limit 1
    </select>

    <select id="selectMemberCodeByDepositStatus" resultType="java.lang.String">
        select member_code from mb_member
        where del_flg=0
        and member_code in
        <foreach collection="memberCodes" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
    </select>

    <select id="queryMemberDepositStatusByAccountId" resultType="java.lang.String">
        SELECT
        ma.member_id
        FROM
        mb_account ma
        inner join mb_member mm on
        mm.member_id = ma.member_id
        where
        ma.account_id = #{accountId}
        and ma.del_flg = 0
        and mm.del_flg = 0
        limit 1
    </select>

    <select id="memberReport" resultType="com.cnoocshell.member.api.dto.report.MemberReportResultDTO">
        select tb2.member_id,
        tb2.member_code,
        tb2.crm_code,
        tb2.member_name,
        tb2.status,
        CASE
        WHEN tb2.status = '201' THEN 1
        ELSE 0
        END AS 'able_status',
        tb2.deposit_status,
        tb1.goods_category_id AS 'categoryIdLevelTwo',
        tb1.sale_channel,
        tb1.goods_code,
        tb1.intention_type,
        tb1.sale_user_id,
        tb3.real_name AS 'sale_user_name',
        tb3.employee_id 'sale_user_employee_id',
        (select tb9.approve_time
        from mb_member_approve_request tb9
        where tb9.member_id = tb2.member_id
        and tb9.request_type = '104'
        and tb9.status = '400'
        order by tb9.approve_time desc,
        tb9.create_time desc
        limit 1) AS 'approve_time'
        from mb_member_purchase_goods_intention tb1
        left join mb_member tb2 on
        tb1.member_id = tb2.member_id
        and tb2.del_flg = 0
        left join mb_account tb3 on
        tb1.sale_user_id = tb3.account_id
        and tb3.del_flg = 0
        where tb1.del_flg = 0
        -- 查询买家企业类型
        and tb2.member_type = '201'
        <!-- 分类编码匹配商品编码查询 -->
        <if test="param.categoryIdByCategoryCodes != null and param.categoryIdByCategoryCodes.size > 0">
            and tb1.goods_category_id in
            <foreach item="item1" collection="param.categoryIdByCategoryCodes" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <!-- 产品名称模糊匹配商品编码查询 -->
        <if test="param.goodsCodeByGoodsName != null and param.goodsCodeByGoodsName.size > 0">
            and tb1.goods_code in
            <foreach item="item2" collection="param.goodsCodeByGoodsName" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>
        <!-- SAP物料号模糊匹配商品编码查询 -->
        <if test="param.goodsCodeBySapMaterialCode != null and param.goodsCodeBySapMaterialCode.size > 0">
            and tb1.goods_code in
            <foreach item="item3" collection="param.goodsCodeBySapMaterialCode" open="(" separator="," close=")">
                #{item3}
            </foreach>
        </if>
        <if test="param.memberName != null and param.memberName != ''">
            and tb2.member_name like CONCAT('%', #{param.memberName}, '%')
        </if>
        <if test="param.crmCode != null and param.crmCode != ''">
            and tb2.crm_code like CONCAT('%', #{param.crmCode}, '%')
        </if>
        <if test="param.ableStatus != null">
            <choose>
                <when test="param.ableStatus == 1">
                    and tb2.status = '201'
                </when>
                <when test="param.ableStatus == 0">
                    and tb2.status != '201'
                </when>
            </choose>
        </if>
        <if test="param.intentionType != null and param.intentionType != ''">
            and tb1.intention_type = #{param.intentionType}
        </if>
        <if test="param.saleChannel != null and param.saleChannel.size > 0">
            and tb1.sale_channel in
            <foreach item="item4" collection="param.saleChannel" open="(" separator="," close=")">
                #{item4}
            </foreach>
        </if>
        <!-- 数据权限限制 -->
        <if test="param.dataPermissionGoodsCodes != null and param.dataPermissionGoodsCodes.size > 0">
            and tb1.goods_code in
            <foreach item="item5" collection="param.dataPermissionGoodsCodes" open="(" separator="," close=")">
                #{item5}
            </foreach>
        </if>
        order by approve_time desc,
        tb2.crm_code,
        tb1.intention_type
    </select>

    <select id="memberAccountReport" resultType="com.cnoocshell.member.api.dto.report.MemberAccountReportSimpleDTO">
        select
        DISTINCT
        tb1.member_id ,
        tb1.member_code,
        tb2.account_id
        from mb_member tb1
        left join mb_account tb2 on tb1.member_id =tb2.member_id and tb2.del_flg =0
        where tb1.del_flg =0
        <!-- 只查询企业买家 -->
        AND tb1.member_type ='201'
        <if test="param.crmCode != null and param.crmCode != ''">
            and tb1.crm_code like CONCAT('%',#{param.crmCode},'%')
        </if>
        <if test="param.memberName != null and param.memberName != ''">
            and tb1.member_name like CONCAT('%',#{param.memberName},'%')
        </if>
        <if test="param.ableStatus != null">
            <choose>
                <when test="param.ableStatus == 1">
                    and tb1.status = '201'
                </when>
                <when test="param.ableStatus == 0">
                    and tb1.status != '201'
                </when>
            </choose>
        </if>
        <if test="param.accountType != null">
            and tb2.account_type = #{param.accountType}
        </if>
        <if test="param.accountStatus != null">
            and tb2.status = #{param.accountStatus}
        </if>
        <if test="param.realName != null and param.realName != ''">
            and tb2.real_name like CONCAT('%',#{param.realName},'%')
        </if>
        <if test="param.mobile != null and param.mobile != ''">
            and tb2.mobile like CONCAT('%',#{param.mobile},'%')
        </if>
        <!-- 数据权限查询 -->
        <if test="param.dataPermissionGoodsCodes != null and param.dataPermissionGoodsCodes.size>0">
            AND EXISTS(select 1
            from mb_member_purchase_goods_intention tb3
            where tb3.del_flg =0
            and tb3.member_id =tb1.member_id
            and tb3.goods_code in
            <foreach collection="param.dataPermissionGoodsCodes" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
            )
        </if>
        <!-- 销售渠道 -->
        <if test="param.saleChannel != null and param.saleChannel.size>0">
            AND EXISTS(select 1
            from mb_member_purchase_goods_intention tb3
            where tb3.del_flg =0
            and tb3.member_id =tb1.member_id
            and tb3.sale_channel in
            <foreach collection="param.saleChannel" item="item5" open="(" separator="," close=")">
                #{item5}
            </foreach>
            )
        </if>
    </select>

    <select id="queryAccountReportInfo" resultType="com.cnoocshell.member.api.dto.report.MemberAccountReportResultDTO">
        select tb1.account_id,
        tb1.account_type,
        tb1.status,
        tb1.real_name,
        tb1.mobile,
        tb2.member_id,
        tb2.member_code,
        tb2.member_name,
        tb2.crm_code,
        tb2.credit_code,
        CASE
        WHEN tb2.status = '201' THEN 1
        ELSE 0
        END AS 'able_status'
        from mb_account tb1
        left join mb_member tb2 on tb1.member_id = tb2.member_id and tb2.del_flg = 0
        where tb1.del_flg = 0
        <if test="accountIds != null and accountIds.size>0">
            and tb1.account_id in
            <foreach collection="accountIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
