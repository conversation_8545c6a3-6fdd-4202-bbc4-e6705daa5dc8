server:
  port: 8089
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: cspc-service-member
  profiles:
    active: ${ENV:dev}
  cloud:
    nacos:
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
      config:
        profile: ${spring.application.active:dev}
        namespace: ${NACOS_NAMESPACE:cnoocshell-dev}
        server-addr: ${NACOS_SERVER_ADDR:27.40.98.108:8848} # Nacos 服务器地址
        file-extension: yaml          # 配置文件扩展名
        file—prefix: ${spring.application.name:dev}
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}  # Nacos 服务器地址

relation:
  tag:
    group:
      size: 10

#mybatis:
mybatis.typeAliasesPackage: com.cnoocshell.member.dao
mybatis.mapperScanPackage: com.cnoocshell.member.dao
mybatis.mapperLocations: "classpath:/mapper/*.xml"
mybatis.configLocation: "classpath:/mybatis-config.xml"