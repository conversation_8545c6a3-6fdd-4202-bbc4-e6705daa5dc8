<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>service-member</artifactId>
        <groupId>com.cnoocshell</groupId>
        <version>2.1.4-RELEASE</version>
    </parent>
    <artifactId>cspc-member</artifactId>
    <packaging>jar</packaging>
    <name>cspc-member</name>
    <description>Member Service</description>

    <properties>
        <docker.deploy.version>${project.version}</docker.deploy.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.cnoocshell</groupId>
            <artifactId>service-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.3</version>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>1.2.3</version>
            <scope>runtime</scope>
        </dependency>

        <!--session同步-->
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cnoocshell</groupId>
            <artifactId>kafka-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cnoocshell</groupId>
            <artifactId>cspc-member-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cnoocshell</groupId>
            <artifactId>cspc-base-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cnoocshell</groupId>
            <artifactId>cspc-goods-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cnoocshell</groupId>
            <artifactId>generator</artifactId>
            <version>2.1.4-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 图片验证码 -->
        <!-- https://mvnrepository.com/artifact/com.cuisongliu/kaptcha-spring-boot-autoconfigure -->
        <dependency>
            <groupId>com.cuisongliu</groupId>
            <artifactId>kaptcha-spring-boot-autoconfigure</artifactId>
            <version>1.3</version>
        </dependency>
        <dependency>
            <groupId>com.cnoocshell</groupId>
            <artifactId>cspc-interface-api</artifactId>
        </dependency>
        <!--		<dependency>-->
        <!--			<groupId>org.springframework.data</groupId>-->
        <!--			<artifactId>spring-data-redis</artifactId>-->
        <!--		</dependency>-->

    </dependencies>

    <build>
        <finalName>cspc-member</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.5</version>
                <configuration>
                    <configurationFile>${basedir}/src/main/resources/generator/generatorConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.31</version>
                    </dependency>
                    <dependency>
                        <groupId>org.mybatis.generator</groupId>
                        <artifactId>mybatis-generator-core</artifactId>
                        <version>1.3.5</version>
                    </dependency>
                    <dependency>
                        <groupId>tk.mybatis</groupId>
                        <artifactId>mapper</artifactId>
                        <version>3.4.0</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <dependencies>
                    <!-- spring热部署 -->
                    <dependency>
                        <groupId>org.springframework</groupId>
                        <artifactId>springloaded</artifactId>
                        <version>1.2.8.RELEASE</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
