package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BiddingCustomerInfoDTO implements Serializable {

    @ApiModelProperty("竞价ID")
    private String enquiryId;

    @ApiModelProperty("竞价单号")
    private String biddingNo;

    @ApiModelProperty("客户名称")
    private String memberName;

    @ApiModelProperty("企业代码")
    private String creditCode;

    @ApiModelProperty("CRM代码")
    private String crmCode;

    @ApiModelProperty("主要意向商品")
    private String mainGoods;

    @ApiModelProperty("次要意向商品")
    private String secondoryGoods;

    @ApiModelProperty("竞价产品编码")
    private String goodsCode;

    @ApiModelProperty("在该会员编码范围内")
    private List<String> inMemberCodes;
}
