package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class BiddingCreateDataDTO implements Serializable {

    private String id;

    private String enquiryNo;

    private String enquiryName;

    private String salesPlanNo;

    private String salesPlanName;

    private String goodsCode;

    private String goodsName;

    private String categoryCode;

    private Date deliveryEffectStartDate;

    private Date deliveryEffectEndDate;

    private BigDecimal remainSellableQuantity;

    private String currentStatus;

    @ApiModelProperty("付款条件")
    private String payCondition;

    @ApiModelProperty("价格贸易条款")
    private String priceTradeTerm;

    @ApiModelProperty("交易币种")
    private String tradeCurrency;

    @ApiModelProperty("包装方式")
    private String pack;

    @ApiModelProperty("销售组")
    private String salesGroup;
}
