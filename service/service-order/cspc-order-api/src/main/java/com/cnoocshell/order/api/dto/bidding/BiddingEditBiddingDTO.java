package com.cnoocshell.order.api.dto.bidding;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class BiddingEditBiddingDTO implements Serializable {

    @ApiModelProperty("biddingId")
    private String id;

    @ApiModelProperty("enquiryId")
    private String enquiryId;

    @ApiModelProperty("salesId")
    private String salesId;

    @ApiModelProperty("竞价场次编号")
    private String biddingNo;

    @ApiModelProperty("竞价场次名称")
    private String biddingName;

    @ApiModelProperty("竞价产品")
    private String goodsName;

    @ApiModelProperty("竞价开始时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime biddingStartTime;

    @ApiModelProperty("提货结束日期")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime deliveryEffectEndDate;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateUser;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("竞价客户")
    private List<BiddingCustomerListDTO> customerList;
}
