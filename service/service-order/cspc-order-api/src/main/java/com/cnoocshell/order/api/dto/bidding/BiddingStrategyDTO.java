package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.cnoocshell.order.api.dto.BaseResultDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class BiddingStrategyDTO extends BaseResultDTO {
    @ApiModelProperty("基准价")
    private BigDecimal standardPrice;

    @ApiModelProperty("本周预计价")
    private String currentWeekExpectPrice;

    @ApiModelProperty("保本售价")
    private BigDecimal costPrice;

    @ApiModelProperty("剩余可售量")
    private BigDecimal remainSellableQuantity;

    @ApiModelProperty("本次申请可售量")
    private BigDecimal currApplySellableQuantity;

    @ApiModelProperty("可销保底量")
    private BigDecimal minSellableQuantity;

    @ApiModelProperty("市场行情及判断")
    private String marketSituation;

    @ApiModelProperty("竞价策略说明")
    private String description;

    @ApiModelProperty("支持材料（附件）")
    private String supportDocument;

    @ApiModelProperty("最新基准价")
    private BigDecimal lastStandardPrice;

    @ApiModelProperty("首轮竞价开始时间")
    private Date biddingStartTime;

    @ApiModelProperty("首轮竞价结束时间")
    private Date biddingEndTime;

    @ApiModelProperty("最新竞价开始时间")
    private Date lastBiddingStartTime;

    @ApiModelProperty("最新竞价结束时间")
    private Date lastBiddingEndTime;

    @ApiModelProperty("提货有效期开始时间")
    private Date deliveryEffectStartDate;

    @ApiModelProperty("提货有效期结束时间")
    private Date deliveryEffectEndDate;

    @ApiModelProperty("类别编码")
    private String categoryCode;

    @ApiModelProperty(value = "类别名称")
    private String categoryName;

    @ApiModelProperty("商品编码")
    private String goodsCode;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty(value = "运输方式",notes = "字典")
    private String deliveryMode;

    @ApiModelProperty("配送收费标准（附件）")
    private String deliveryCostStandard;
    @ApiModelProperty("自提指南（附件）")
    private String selfPickupGuide;
    @ApiModelProperty("自提承运商（附件）")
    private String selfPickupCarrier;

    @ApiModelProperty("策略提交人姓名")
    private String submitUserName;
    @ApiModelProperty("策略提交人ID")
    private String submitUser;
    @ApiModelProperty("策略提交时间")
    private Date submitTime;

    @ApiModelProperty("策略更新人ID")
    private String submitUpdateUser;
    @ApiModelProperty("策略更新人姓名")
    private String submitUpdateUserName;
    @ApiModelProperty("策略更新时间")
    private Date submitUpdateTime;

    @ApiModelProperty("基于上周五原料毛利 Over C2/C3/PO")
    private BigDecimal grossMargin;

    @ApiModelProperty("对标市场价格")
    private String marketPrice;

    @ApiModelProperty("撤回人ID")
    private String withdrawUser;
    @ApiModelProperty("撤回人姓名")
    private String withdrawUserName;
    @ApiModelProperty("撤回原因")
    private String withdrawReason;
    @ApiModelProperty("撤回时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date withdrawTime;

    public void initNull() {
        this.submitUserName = null;
        this.submitUser = null;
        this.submitTime = null;
        this.submitUpdateUser = null;
        this.submitUpdateUserName = null;
        this.submitUpdateTime = null;
        this.grossMargin = null;
        this.marketPrice = null;
        this.withdrawUser = null;
        this.withdrawUserName = null;
        this.withdrawReason = null;
        this.withdrawTime = null;
    }

}
