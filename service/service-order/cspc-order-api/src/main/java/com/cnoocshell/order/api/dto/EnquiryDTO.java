package com.cnoocshell.order.api.dto;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.cnoocshell.order.api.dto.analysis.EnquiryAnalysisResultDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanInfoDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 询价类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnquiryDTO {

    private String id;

    private String enquiryNo;

    private String enquiryName;

    private String payCondition;

    private String priceTradeTerm;

    private String tradeCurrency;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date enquiryStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date enquiryEndTime;

    private String status;

    private String goodsCode;

    private String goodsName;

    private String categoryCode;

    private BigDecimal maxPrice;

    private BigDecimal minPrice;

    private BigDecimal totalQuantity;

    private String salesPlanNo;

    private String salesPlanId;

    private String salesPlanName;

    private String operator;

    private Integer pageNum;

    private Integer pageSize;

    private String createUserName;
    private String updateUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    private String createStartTime;

    private String createEndTime;

    //分类codes
    private List<String> categoryCodes;

    //goodsCodes
    private List<String> goodsCodes;

    //返回询价详情
    private List<EnquiryBuyerDetailDTO> enquiryBuyerDetailDTOS;

    private SalesPlanInfoDTO salesPlanInfoDTO;

    private EnquiryDataDetailDTO enquiryDataDetailDTO;

    //memberCode
    private String memberCode;

    //检查询价剩余量
    private boolean checkEnquirySurplus;


    private String deliveryCostStandard;//配送收费标准（附件）
    private String selfPickupGuide;//自提指南（附件）
    private String selfPickupCarrier;//自提承运商（附件）

    private List<String> statusArray;//状态

    private List<String> statusArrayList;

    private List<String> participationStatusArray;//参与状态

    private List<String> enquiryNos;

    //参与状态，DONE:已参与，TODO,未参与
    private String participationStatus;

    private String applySellableQuantity;

    private String clientType;

    private String pack;

    @ApiModelProperty("询价取消人ID")
    private String cancelUser;

    @ApiModelProperty("询价取消人姓名")
    private String cancelUserName;

    @ApiModelProperty("取消原因")
    private String cancelReason;

    @ApiModelProperty("取消时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date cancelTime;

    @ApiModelProperty(value = "是否存在竞价数据 true:存在 false:不存在",notes = "用于判断是否展示通知竞价取消按钮")
    private Boolean existBidding;

    @ApiModelProperty(value = "询价分数据",notes = "询价分析图")
    private List<EnquiryAnalysisResultDTO> enquiryAnalysis;

    @ApiModelProperty(value = "终端类型",notes = "PC:PC端 MP:小程序端")
    private String terminal;
}