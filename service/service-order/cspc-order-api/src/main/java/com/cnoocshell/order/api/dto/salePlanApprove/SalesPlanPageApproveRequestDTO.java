package com.cnoocshell.order.api.dto.salePlanApprove;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SalesPlanPageApproveRequestDTO implements Serializable {

    @ApiModelProperty("账号id")
    private String accountId;

    @ApiModelProperty("销售计划编号")
    private String planNo;

    @ApiModelProperty("销售计划名称")
    private String planName;

    @ApiModelProperty("产品")
    private String product;

    @ApiModelProperty("提货开始日期")
    private Date deliveryEffectStartDate;

    @ApiModelProperty("提货结束日期")
    private Date deliveryEffectEndDate;

    @ApiModelProperty("是否需要询价")
    private Integer isCreatedInquiry;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("创建人id")
    private String createUser;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("创建开始时间")
    private Date createStartDate;

    @ApiModelProperty("创建开始时间")
    private Date createEndDate;

    @ApiModelProperty("页码")
    private int pageNum;

    @ApiModelProperty("单页条数")
    private int pageSize;
}
