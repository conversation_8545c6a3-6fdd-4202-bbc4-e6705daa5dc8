package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BiddingBuyerDTO implements Serializable {

    @ApiModelProperty("买家编码")
    private String memberCode;

    @ApiModelProperty("买家名称")
    private String memberName;

    @ApiModelProperty("商品编码")
    private String goodsCode;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("买家来源：ENQUIRY询价，SALES_PLAN销售计划，INVITED被邀请")
    private String buyerSource;

    private Integer biddingCount;

}
