package com.cnoocshell.order.api.constant;

public class ColumnConstant {

    private ColumnConstant() {
        throw new IllegalStateException("ColumnConstant class");
    }

    public static final String ID = "id";
    public static final String STATUS = "status";
    public static final String DEL_FLG = "delFlg";
    public static final String BIDDING_NO = "biddingNo";
    public static final String ENQUIRY_NO = "enquiryNo";
    public static final String SALES_PLAN_NO = "salesPlanNo";
    public static final String GOODS_CODE = "goodsCode";
    public static final String CATEGORY_CODE = "categoryCode";
    public static final String CREATE_USER = "createUser";
    public static final String UPDATE_USER = "updateUser";
    public static final String CREATE_TIME = "createTime";
    public static final String UPDATE_TIME = "updateTime";
    public static final String CURRENT_ROUND = "currentRound";
    public static final String PLAN_NO = "planNo";
    public static final String DEAL_NO = "dealNo";
    public static final String MEMBER_CODE = "memberCode";
    public static final String OLD_CURRENT_ROUND = "oldCurrentRound";
}
