package com.cnoocshell.order.api.enums;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ReportDealStatusEnum {
    DEAL("DEAL","成交","竞价状态已完成且最终提交成交结果的数据"),
    NOT_DEAL("NOT_DEAL","未成交","竞价状态作废且提交不成交结果的数据"),
    OTHER("/","/","非 成交和未成交状态，在多轮次情况或多次报量，最后轮次最新报量之前的报量数据状态");

    private String status;
    private String name;
    private String desc;

    public static final String getNameByStatus(String status) {
        ReportDealStatusEnum statusEnum = Arrays.stream(values())
                .filter(v-> CharSequenceUtil.equals(v.getStatus(),status))
                .findFirst().orElse(null);
        return Objects.nonNull(statusEnum) ? statusEnum.getName() : null;
    }
}
