package com.cnoocshell.order.api.dto.enquiry;

import com.cnoocshell.common.dto.OperatorDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RemoveEnquiryDTO extends OperatorDTO {
    @ApiModelProperty("询价单号")
    @NotBlank(message = "询价单号不能为空")
    private String enquiryNo;
}
