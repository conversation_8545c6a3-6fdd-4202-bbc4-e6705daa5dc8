package com.cnoocshell.order.api.dto.bidding.report;

import com.cnoocshell.common.dto.OperatorDTO;
import com.cnoocshell.order.api.enums.BiddingStatusEnum;
import com.cnoocshell.order.api.enums.EnquiryStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * NEW 客户活跃度报表入参
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MemberActivityReportQueryDTO extends OperatorDTO {
    @ApiModelProperty(value = "分页参数 是否分页",notes = "控制分页列表和导出查询是否分页处理",hidden = true)
    private Boolean needPage = Boolean.TRUE;
    @ApiModelProperty(value = "分页参数 页码")
    private Integer pageNum = 1;
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 20;


    @ApiModelProperty(value = "会员名称", notes = "模糊查询")
    private String memberName;

    @ApiModelProperty(value = "CRM编码", notes = "模糊查询 前端传值")
    private String crmCode;
    @ApiModelProperty(value = "CRM编码模糊匹配到的会员编号", hidden = true)
    private List<String> memberCodeByCrmCode;

    @ApiModelProperty(value = "SAP物料编码", notes = "模糊查询 前端传值")
    private String sapMaterialCode;
    @ApiModelProperty(value = "SAP物料编码模糊匹配到的产品编码", hidden = true)
    private List<String> goodsCodeBySapMaterialCode;

    @ApiModelProperty(value = "产品分类", notes = "多选匹配")
    private List<String> categoryCodes;
    @ApiModelProperty(value = "产品分类转换查询", notes = "多选匹配", hidden = true)
    private List<String> goodsCodeByCategoryCode;

    @ApiModelProperty(value = "开始时间", notes = "时间范围匹配")
    private Date startTime;
    @ApiModelProperty(value = "结束时间", notes = "时间范围匹配")
    private Date endTime;

    @ApiModelProperty(value = "产品名称", notes = "模糊匹配")
    private String goodsName;

    @ApiModelProperty(value = "销售渠道", notes = "多选匹配")
    private List<String> saleChannels;
    @ApiModelProperty(value = "销售渠道转换查询", notes = "多选匹配 由goods_memberCode 拼接", hidden = true)
    private List<String> goodsCodeConcatMemberCodeBySaleChannel;

    @ApiModelProperty(value = "竞价状态范围", notes = "默认查询已完成、已作废状态 预留状态查询范围")
    private List<String> defaultBiddingStatus = Arrays.asList(BiddingStatusEnum.COMPLETED.getStatus(),
            BiddingStatusEnum.CANCELLED.getStatus());

    @ApiModelProperty(value = "询价状态范围", notes = "默认查询已结束、已作废状态 预留状态查询范围")
    private List<String> defaultEnquiryStatus = Arrays.asList(EnquiryStatusEnum.END.getCode(),
            EnquiryStatusEnum.CANCELLED.getCode());

    @ApiModelProperty(value = "数据权限 商品编码", hidden = true)
    private List<String> dataPermissionGoodsCodes;
}
