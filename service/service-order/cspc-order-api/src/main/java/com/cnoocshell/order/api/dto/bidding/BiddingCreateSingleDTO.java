package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class BiddingCreateSingleDTO implements Serializable {

    @ApiModelProperty("ID")
    private String ID;

    @ApiModelProperty("竞价开始时间")
    private LocalDateTime biddingStartTime;

    @ApiModelProperty("竞价开始时间")
    private LocalDateTime biddingEndTime;

    @ApiModelProperty("保存状态 DRAFT：保存-草稿  TO_SUBMIT_STRATEGY：提交-待开始")
    private String status;

    @ApiModelProperty("操作人ID")
    private String accountId;

    @ApiModelProperty("操作人姓名")
    private String realName;
}
