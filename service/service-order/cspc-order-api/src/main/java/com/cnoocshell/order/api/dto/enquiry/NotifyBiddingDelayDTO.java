package com.cnoocshell.order.api.dto.enquiry;

import com.cnoocshell.common.dto.OperatorDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotifyBiddingDelayDTO extends OperatorDTO {
    @ApiModelProperty("询价单号")
    @NotBlank(message = "询价单号不能为空")
    private String enquiryNo;

    @ApiModelProperty(value = "原因",notes = "预留字段 防止后面添加通知取消竞价原因")
    private String reason;
}
