package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.order.api.dto.AccountUserDTO;
import com.cnoocshell.order.api.dto.bidding.group.BiddingDealConfirmGroup;
import com.cnoocshell.order.api.dto.bidding.group.DefaultGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BiddingDealConfirmDTO extends AccountUserDTO {
    @ApiModelProperty("竞价场次编号")
    @NotBlank(message = "竞价场次编号不能为空",groups = {BiddingDealConfirmGroup.class, DefaultGroup.class})
    private String biddingNo;

    @ApiModelProperty("不成交原因")
    @NotBlank(message = "不成交原因不能为空", groups = {BiddingDealConfirmGroup.class})
    private String notDealReason;

    @ApiModelProperty("超量成交备注")
    @Size(max = 500,message = "超量成交备注字符串限制输入500字符",groups = {BiddingDealConfirmGroup.class})
    private String overDealRemark;

    @ApiModelProperty("买家竞价明细ID")
    @NotNull(message = "买家竞价价明细ID不能为空", groups = {DefaultGroup.class})
    private List<String> biddingBuyerDetailIds;
}
