package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BiddingSubmitStrategySaveDTO implements Serializable {

    @ApiModelProperty("id")
    private String biddingId;

    @ApiModelProperty("申请基准价格")
    private BigDecimal standardPrice;

    @ApiModelProperty("对标市场价格")
    private String marketPrice;

    @ApiModelProperty("基于上周五原料毛利 Over C2/C3/PO")
    private BigDecimal grossMargin;

    @ApiModelProperty("本周预计价格")
    private String currentWeekExpectPrice;

    @ApiModelProperty("保本售价")
    private BigDecimal costPrice;

    @ApiModelProperty("剩余可售量")
    private BigDecimal remainSellableQuantity;

    @ApiModelProperty("本次申请可售量")
    private BigDecimal currApplySellableQuantity;

    @ApiModelProperty("可销保底量")
    private BigDecimal minSellableQuantity;

    @ApiModelProperty("市场行情及判断")
    private String marketSituation;

    @ApiModelProperty("竞价策略说明")
    private String description;

    @ApiModelProperty("支持材料")
    private String supportDocument;

    @ApiModelProperty("操作人ID")
    private String accountId;

    @ApiModelProperty("操作人姓名")
    private String realName;
}
