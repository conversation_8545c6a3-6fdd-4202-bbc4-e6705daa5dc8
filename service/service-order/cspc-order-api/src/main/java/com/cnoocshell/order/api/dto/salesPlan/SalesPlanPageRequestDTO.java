package com.cnoocshell.order.api.dto.salesPlan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SalesPlanPageRequestDTO implements Serializable {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("账号id")
    private String accountId;

    @ApiModelProperty("会员id")
    private String memberId;

    @ApiModelProperty("销售计划编号")
    private String planNo;

    @ApiModelProperty("销售计划名称")
    private String planName;

    @ApiModelProperty("产品")
    private String goodsName;

    @ApiModelProperty("提货开始日期")
    private Date deliveryEffectStartDate;

    @ApiModelProperty("提货结束日期")
    private Date deliveryEffectEndDate;

    @ApiModelProperty("是否创建询价")
    private Integer isCreatedInquiry;

    @ApiModelProperty("是否需要询价")
    private Integer isNeedInquiry;

    @ApiModelProperty("状态")
    private List<String> status;

    @ApiModelProperty("创建人id")
    private String createUser;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("创建开始时间")
    private Date createStartDate;

    @ApiModelProperty("创建开始时间")
    private Date createEndDate;

    @ApiModelProperty("页码")
    private int pageNum;

    @ApiModelProperty("单页条数")
    private int pageSize;
}
