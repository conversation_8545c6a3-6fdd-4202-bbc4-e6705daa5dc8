package com.cnoocshell.order.api.dto.bidding;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class BiddingApproveListDTO implements Serializable {

    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("审批内容")
    private String approveType;

    @ApiModelProperty("审批人")
    private String createUserName;

    @ApiModelProperty("审批结果")
    private String approveResult;

    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("原因")
    private String rejectedReason;
}
