package com.cnoocshell.order.api.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

public enum SalesChannelEnum {

	VO_SALES_CHANNEL_RS("VO_SALES_CHANNEL_RS", "贸易商"),
	VO_SALES_CHANNEL_EU("VO_SALES_CHANNEL_EU", "终端客户"),
	VO_SALES_CHANNEL_DS("VO_SALES_CHANNEL_DS", "分销商");

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    SalesChannelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public static SalesChannelEnum getByCode(String code){
		return Arrays.stream(values())
				.filter(v-> StringUtils.equals(code,v.getCode()))
				.findFirst().orElse(null);
	}

	public static final String getNameByCode(String code){
		SalesChannelEnum salesChannelEnum = getByCode(code);
		return Objects.nonNull(salesChannelEnum) ? salesChannelEnum.getDescription() : null;
	}

}
