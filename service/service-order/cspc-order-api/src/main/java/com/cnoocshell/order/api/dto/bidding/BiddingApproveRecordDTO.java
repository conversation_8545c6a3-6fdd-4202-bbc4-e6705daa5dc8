package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.order.api.dto.BaseResultDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@ApiModel("竞价审批记录")
@EqualsAndHashCode(callSuper = true)
public class BiddingApproveRecordDTO extends BaseResultDTO {
    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("场次编号")
    private String biddingNo;

    @ApiModelProperty(value = "审批类型",notes = "审批类型：STRATEGY竞价策略审批，RESULT成交结果审批")
    private String approveType;

    @ApiModelProperty(value = "审批结果",notes = "审批结果：REJECTED已驳回、APPROVED已通过")
    private String approveResult;

    @ApiModelProperty("原因")
    private String rejectedReason;
}
