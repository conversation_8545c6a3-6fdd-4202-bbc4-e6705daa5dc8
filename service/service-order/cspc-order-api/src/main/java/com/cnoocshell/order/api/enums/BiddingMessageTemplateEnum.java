package com.cnoocshell.order.api.enums;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


/**
 *站内信模板
 */
@Getter
@AllArgsConstructor
public enum BiddingMessageTemplateEnum {
    NOTIFY_BIDDING_START("竞价即将开始","尊敬的{}，您关注的竞价场次{}将于{}开始，竞价产品为{}。诚邀您准时参加，谢谢。","竞价时间开始前10 分钟"),
    NOTIFY_LAST_BIDDING_START("即将开始下一轮竞价","尊敬的{}，" +
            "竞价场次{}此轮已结束，" +
            "因竞价总量超过我方计划可售量，我方已调整竞价价格，" +
            "诚邀您重新参与场次{}的竞价，" +
            "竞价产品为{}，" +
            "该场次将于{}开始。" +
            "请您在竞价开始前做好准备工作，并预留出宝贵时间参与，谢谢。","销售经理，进入\"成交结果待确认\"/\"成交结果已驳回\"状态的竞价场次详情页面，点击【调整价格】按钮，并提交成功"),
    NOTIFY_BIDDING_RESULT_BY_DEAL("竞价结果通知","尊敬的{}，恭喜您，您参与的竞价场次{}已配对成功，合同编号为{}。请您及时联系我们的销售人员和客服人员，尽快签署《销售合同》，谢谢！",
            "\"1.销售经理确认成交时（成交总量<=申请可售量 ），且提交成功\n" +
                    "2.销售经理确认成交后（成交总量>申请可售量），且提交后CM审批通过时\n" +
                    "（注：以上条件，均可独立触发通知）\""),
    NOTIFY_BIDDING_RESULT_BY_DEAL_TO_SALES("竞价结果通知","尊敬的{}，您所负责的客户{}在竞价场次{}中已确认成交，合同编号为{}，请及时联系客户沟通合同细节，谢谢！",
            "\"1.销售经理确认成交时（成交总量<=申请可售量 ），且提交成功\n" +
                    "2.销售经理确认成交后（成交总量>申请可售量），且提交后CM审批通过时\n" +
                    "（注：以上条件，均可独立触发通知）\""),
    NOTIFY_BIDDING_RESULT_BY_NOT_DEAL("竞价场次未成交","尊敬的{}，抱歉地通知您，您参与的竞价场次{}未能配对成功。期待您的下次参与！","\"销售经理确认不成交，且提交成功\n" +
            "\""),
    NOTIFY_WAIT_APPROVAL_BY_CMMS("成交结果待审批","尊敬的{}，您有一条竞价场次编号为{}的竞价成交结果待审批，请及时处理，谢谢。","提交成交结果（成交量>可售量）提醒"),
    NOTIFY_WAIT_APPROVAL_BY_CM("成交结果待审批","尊敬的{}，您有一条竞价场次{}的成交结果待审批，请及时查阅，谢谢。","提交成交结果（成交量>可售量）第一级审批通过提醒"),
    NOTIFY_APPROVED_BY_CM("成交结果审批已通过","尊敬的{}，您提交的成交结果申请已通过审批，竞价场次编号为{}，望知悉，谢谢。","成交结果审批通过"),
    NOTIFY_REJECT_BY_APPROVAL("成交结果审批已驳回","尊敬的{}，您提交的成交结果申请已被驳回，竞价场次编号为{}，请及时查阅，谢谢。","成交结果审批驳回"),
    NOTIFY_ENQUIRY_START("询价即将开始","尊敬的{}，您关注的询价场次{}将于{}开始，询价产品为{}。诚邀您准时参加，谢谢。","询价时间开始前10 分钟"),

    NOTIFY_CANCEL_ENQUIRY("询价取消通知","尊敬的{}，很抱歉地通知您，您关注的询价场次{}，询价产品为{}，已被取消，敬请期待下一场询价，谢谢！","询价场次为待开始、询价中状态时，销售经理提交取消询价场次时"),

    NOTIFY_BIDDING_DELAY("竞价取消通知","尊敬的{}，很抱歉地通知您，您关注的明日即将开展的产品{}的竞价已被取消，新的竞价场次开启时间，敬请等候后续通知，谢谢！","询价已正常结束，第二天的竞价无法正常开始，销售经理手动通过询价触发通知"),
    NOTIFY_WITHDRAW_STRATEGY_TO_CMMS("竞价策略撤回申请待审批","尊敬的{}，您有一条竞价场次编号为{}的竞价策略撤回申请待审批，请及时处理，谢谢。","1. 销售经理进入【竞价策略已通过】状态的竞价场次详情页面，点击【撤回竞价策略】按钮并确认提交"),
    NOTIFY_WITHDRAW_STRATEGY_TO_CM("竞价策略撤回申请待审批","尊敬的{}，您有一条新的竞价策略撤回申请待审批，竞价场次编号为{}，请及时处理，谢谢。","\"1.市场开发部经理第一级审批人，进入竞价场次审批列表，勾选\"\"撤回待审批\"\"状态的竞价场次，点击批量审批通过\n" +
            "2.市场开发部经理第一级审批人，进入\"\"撤回待审批\"\"状态的竞价场次详情页面，点击通过\n" +
            "（注：以上条件，均可独立触发通知）\""),
    NOTIFY_WITHDRAW_STRATEGY_APPROVED_TO_WITHDRAW_USER("竞价策略撤回申请已通过","尊敬的{}，您提交的竞价策略撤回申请已通过审批，竞价场次编号为{}，望知悉，谢谢。","\"1.销售总监第二级审批人，进入竞价场次审批列表，勾选\"\"撤回审批中\"\"状态的竞价场次，点击批量审批通过\n" +
            "2.销售总监第二级审批人，进入\"\"撤回审批中\"\"状态的竞价场次详情页面，点击通过\n" +
            "（注：以上条件，均可独立触发通知）\""),
    NOTIFY_WITHDRAW_STRATEGY_REJECTED_TO_WITHDRAW_USER("竞价策略撤回申请已驳回","尊敬的{}，您提交的竞价场次编号为{}的竞价策略撤回申请已被驳回，请及时查阅，谢谢。","\"1.市场开发部经理第一级审批人，进入竞价场次审批列表，勾选\"\"撤回待审批\"\"状态的竞价场次，点击批量审批驳回\n" +
            "2.销售总监第二级审批人，进入竞价场次审批列表，勾选\"\"撤回审批中\"\"状态的竞价场次，点击批量审批驳回\n" +
            "3.市场开发部经理第一级审批人，进入\"\"撤回待审批\"\"状态的竞价场次详情页面，点击驳回\n" +
            "4.销售总监第二级审批人，进入\"\"撤回审批中\"\"状态的竞价场次详情页面，点击驳回\n" +
            "（注：以上条件，均可独立触发通知）\""),
    NOTIFY_CANCEL_BIDDING_TO_CMMS("竞价场次取消申请待审批","尊敬的{}，您有一条竞价场次编号为{}的竞价取消申请待审批，请及时处理，谢谢。","1. 销售经理进入【竞价策略已通过】状态的竞价场次详情页面，点击【取消】按钮并确认提交"),
    NOTIFY_CANCEL_BIDDING_TO_CM("竞价场次取消待审批","尊敬的{}，您有一条新的竞价场次取消申请待审批，竞价场次编号为{}，请及时处理，谢谢。","\"1.市场开发部经理第一级审批人，进入竞价场次审批列表，勾选\"\"撤回待审批\"\"状态的竞价场次，点击批量审批通过\n" +
            "2.市场开发部经理第一级审批人，进入\"\"撤回待审批\"\"状态的竞价场次详情页面，点击通过\n" +
            "（注：以上条件，均可独立触发通知）\""),
    NOTIFY_CANCEL_BIDDING_APPROVED_TO_CANCEL_USER("竞价场次取消申请已通过","尊敬的{}，您提交的竞价场次取消申请已通过审批，竞价场次编号为{}，望知悉，谢谢。","\"1.销售总监第二级审批人，进入竞价场次审批列表，勾选\"\"撤回审批中\"\"状态的竞价场次，点击批量审批通过\n" +
            "2.销售总监第二级审批人，进入\"\"撤回审批中\"\"状态的竞价场次详情页面，点击通过\n" +
            "（注：以上条件，均可独立触发通知）\""),
    NOTIFY_CANCEL_BIDDING_REJECTED_TO_CANCEL_USER("竞价场次取消申请已驳回","尊敬的{}，您提交的竞价场次编号为{}的竞价场次取消申请已被驳回，请及时查阅，谢谢。","\"1.市场开发部经理第一级审批人，进入竞价场次审批列表，勾选\"\"撤回待审批\"\"状态的竞价场次，点击批量审批驳回\n" +
            "2.销售总监第二级审批人，进入竞价场次审批列表，勾选\"\"撤回审批中\"\"状态的竞价场次，点击批量审批驳回\n" +
            "3.市场开发部经理第一级审批人，进入\"\"撤回待审批\"\"状态的竞价场次详情页面，点击驳回\n" +
            "4.销售总监第二级审批人，进入\"\"撤回审批中\"\"状态的竞价场次详情页面，点击驳回\n" +
            "（注：以上条件，均可独立触发通知）\""),
    NOTIFY_CANCEL_BIDDING_TO_BUYER("竞价取消通知","尊敬的{}，很抱歉地通知您，您关注的竞价场次{}，竞价产品为{}，已被取消，敬请期待下一场竞价，谢谢！","竞价策略审批通过后，因不可抗力因素，需紧急取消此场竞价。即销售经理提交取消竞价场次申请后，二级审批人销售总监审批通过时"),
    ;
    private String title;
    private String msg;

    private String desc;

    public String getMsg(Object...params){
        if(Objects.isNull(params))
            return this.msg;

        return CharSequenceUtil.format(this.msg,params);
    }
}
