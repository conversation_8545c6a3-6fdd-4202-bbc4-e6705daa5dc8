package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.order.api.dto.PageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BiddingSalesListDTO extends PageDTO implements Serializable {

    @ApiModelProperty("竞价场次编号")
    private String biddingNo;

    @ApiModelProperty("竞价场次名称")
    private String biddingName;

    @ApiModelProperty("竞价产品")
    private List<String> goodsNameList;

    @ApiModelProperty("竞价场次状态")
    private String status;

    @ApiModelProperty("竞价场次状态")
    private List<String> statusParams;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate createStartTime;

    @ApiModelProperty("创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate createEndTime;

    @ApiModelProperty("允许查看的状态列表")
    private List<String> statusList;

    @ApiModelProperty("操作人ID")
    private String accountId;

    @ApiModelProperty("操作人姓名")
    private String realName;

    @ApiModelProperty("用户角色")
    private List<String> roleList;

    @ApiModelProperty("用户查看范围权限商品")
    private List<String> goodsCodeList;

    @ApiModelProperty("页面勾选的竞价数据")
    private List<String> biddingNoList;

    @ApiModelProperty("查看详情竞价NO")
    private String detailBiddingNo;
}
