package com.cnoocshell.order.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum BiddingBuyerStatusEnum {
    TODO("TODO", "未参与"),
    DONE("DONE", "已参与"),
    ENQUIRY("ENQUIRY", "询价"),
    SALES_PLAN("SALES_PLAN", "销售计划"),
    INVITED("INVITED", "被邀请"),
    ;
    private String status;

    private String name;

    public static BiddingBuyerStatusEnum getByStatus(String status) {
        return Arrays.stream(values())
                .filter(v -> StringUtils.equals(v.getStatus(), status))
                .findFirst()
                .orElse(null);
    }
}
