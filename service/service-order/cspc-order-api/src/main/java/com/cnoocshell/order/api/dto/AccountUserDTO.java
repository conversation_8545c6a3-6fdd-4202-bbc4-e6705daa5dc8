package com.cnoocshell.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("账户用户")
@AllArgsConstructor
@NoArgsConstructor
public class AccountUserDTO {
    @ApiModelProperty("账户ID")
    private String accountId;

    @ApiModelProperty("姓名")
    private String realName;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("员工ID")
    private String employeeId;
}
