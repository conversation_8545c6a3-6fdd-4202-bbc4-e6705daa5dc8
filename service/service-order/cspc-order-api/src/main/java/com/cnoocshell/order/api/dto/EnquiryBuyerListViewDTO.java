package com.cnoocshell.order.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class EnquiryBuyerListViewDTO implements Serializable {

    @ApiModelProperty( "id")
    private String id;

    @ApiModelProperty( "询价场次编号")
    private String enquiryNo;

    @ApiModelProperty( "询价场次名称")
    private String enquiryName;

    @ApiModelProperty("询价场次状态")
    private String status;

    @ApiModelProperty("参与状态")
    private String participationStatus;

    @ApiModelProperty( "产品")
    private String goodsName;

    @ApiModelProperty( "询价开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enquiryStartTime;

    @ApiModelProperty( "询价结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enquiryEndTime;
    
}
