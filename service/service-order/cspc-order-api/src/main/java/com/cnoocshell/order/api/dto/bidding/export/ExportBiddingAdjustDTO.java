package com.cnoocshell.order.api.dto.bidding.export;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.cnoocshell.common.excel.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("导出调价记录")
public class ExportBiddingAdjustDTO {
    @ApiModelProperty("序号")
    @Excel(index = 1, title = "序号")
    private String serialNum;

    @ApiModelProperty("调整前价格")
    @Excel(index = 5, title = "调整前出厂价（元/吨）")
    private BigDecimal oldStandardPrice;

    @ApiModelProperty("调整后价格")
    @Excel(index = 10, title = "调整后出厂价（元/吨）")
    private BigDecimal newStandardPrice;

    @ApiModelProperty("竞价开始时间")
    @Excel(index = 15, title = "本轮竞价开始时间", dateFormat = DatePatternConstant.NORMAL_FORMAT)
    private Date biddingStartTime;

    @ApiModelProperty("竞价结束时间")
    @Excel(index = 20, title = "本轮竞价结束时间", dateFormat = DatePatternConstant.NORMAL_FORMAT)
    private Date biddingEndTime;

    @ApiModelProperty("创建人名称")
    @Excel(index = 25, title = "调价人")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @Excel(index = 30, title = "调价时间", dateFormat = DatePatternConstant.NORMAL_FORMAT)
    private Date createTime;
}
