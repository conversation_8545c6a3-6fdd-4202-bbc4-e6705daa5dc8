package com.cnoocshell.order.api.dto.analysis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class BiddingAnalysisResultDTO {
    @ApiModelProperty(value = "轮次")
    private Integer round;
    @ApiModelProperty(value = "轮次名称")
    private String roundName;
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    @ApiModelProperty(value = "竞价总报量")
    private BigDecimal totalQuantity;
    @ApiModelProperty(value = "销售渠道客户分析")
    private List<BiddingAnalysisSaleChannelDTO> saleChannelAnalysis;
}
