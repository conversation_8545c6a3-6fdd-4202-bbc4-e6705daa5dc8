package com.cnoocshell.order.api.enums;

import com.cnoocshell.order.api.constant.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BiddingExcelHeaderEnum {
    BIDDING_EXCEL_1("竞价场次编号"),
    BIDDING_EXCEL_2("产品名称"),
    BIDDING_EXCEL_3("SAP物料编号"),
    BIDDING_EXCEL_4("剩余可售量"),
    BIDDING_EXCEL_5("基于上周五原料 毛利 Over C2/C3/PO\n" +
            Constants.TON_UNIT),
    BIDDING_EXCEL_6("保本售价\n" +
            Constants.TON_UNIT),
    BIDDING_EXCEL_7("对标市场价格\n" +
            Constants.TON_UNIT),
    BIDDING_EXCEL_8("本周预计价格\n" +
            Constants.TON_UNIT),
    BIDDING_EXCEL_9("申请基准价格\n" +
            Constants.TON_UNIT),
    BIDDING_EXCEL_10("本周可售量\n" +
            Constants.TON),
    BIDDING_EXCEL_11("可销保底量\n" +
            Constants.TON),
    BIDDING_EXCEL_12("市场行情及判断"),
    BIDDING_EXCEL_113("竞价策略说明"),
    ;
    private final String key;
}
