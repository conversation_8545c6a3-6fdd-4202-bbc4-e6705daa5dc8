package com.cnoocshell.order.api.dto.bidding.export;

import com.cnoocshell.common.excel.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("导出竞价记录")
public class ExportBiddingBuyerDetailDTO {
    @ApiModelProperty("序号")
    @Excel(index = 1, title = "序号")
    private String serialNum;

    @ApiModelProperty("竞价轮次")
    private Integer currentRound;
    @ApiModelProperty(name = "竞价轮次", notes = "第{}轮")
    @Excel(index = 5, title = "竞价轮次")
    private String currentRoundName;

    @ApiModelProperty("会员编码")
    private String memberCode;
    @ApiModelProperty(name = "客户名称")
    @Excel(index = 10, title = "客户名称")
    private String memberName;

    @Excel(index = 15, title = "客户编码")
    private String crmCode;

    @Excel(index = 20, title = "出厂价（元/吨）")
    private BigDecimal currentStandardPrice;

    @Excel(index = 25, title = "报量（吨）")
    private BigDecimal wantQuantity;

    @ApiModelProperty(value = "配送方式", notes = "值集 VS_DELIVERY_METHOD")
    private String deliveryMode;
    @Excel(index = 30, title = "运输方式")
    private String deliveryModeName;

    @ApiModelProperty(value = "包装方式",notes = "值集 VS_PACKAGE_METHOD")
    private String pack;
    @Excel(index = 33,title = "包装方式")
    private String packName;

    @Excel(index = 35, title = "提交人")
    private String createUserName;

    @Excel(index = 40, title = "提交时间")
    private Date createTime;
}
