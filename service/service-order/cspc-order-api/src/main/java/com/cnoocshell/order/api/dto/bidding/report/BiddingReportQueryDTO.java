package com.cnoocshell.order.api.dto.bidding.report;

import com.cnoocshell.common.dto.OperatorDTO;
import com.cnoocshell.order.api.enums.BiddingStatusEnum;
import com.cnoocshell.order.api.enums.ReportDealStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class BiddingReportQueryDTO extends OperatorDTO {
    @ApiModelProperty(value = "分页参数 是否分页",notes = "控制分页列表和导出查询是否分页处理",hidden = true)
    private Boolean needPage = Boolean.TRUE;

    @ApiModelProperty(value = "分页参数 页码")
    private Integer pageNum = 1;
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "销售计划编号",notes = "模糊查询")
    private String salesPlanNo;

    @ApiModelProperty(value = "产品分类",notes = "多选匹配")
    private List<String> categoryCodes;

    @ApiModelProperty(value = "产品分类名称",notes = "模糊查询 前端传值")
    private String categoryName;
    @ApiModelProperty(value = "产品分类名称模糊匹配到的产品分类编码",hidden = true)
    private List<String> categoryCodeByCategoryName;

    @ApiModelProperty(value = "产品名称",notes = "模糊匹配")
    private String goodsName;

    @ApiModelProperty(value = "SAP物料编码",notes = "模糊查询 前端传值")
    private String sapMaterialCode;
    @ApiModelProperty(value = "SAP物料编码模糊匹配到的产品编码",hidden = true)
    private List<String> goodsCodeBySapMaterialCode;

    @ApiModelProperty(value = "会员名称",notes = "模糊查询")
    private String memberName;

    @ApiModelProperty(value = "CRM编码",notes = "模糊查询 前端传值")
    private String crmCode;
    @ApiModelProperty(value = "CRM编码模糊匹配到的会员编号",hidden = true)
    private List<String> memberCodeByCrmCode;

    @ApiModelProperty(value = "竞价场次编号",notes = "模糊查询")
    private String biddingNo;

    @ApiModelProperty(value = "竞价开始日期-从",notes = "时间范围匹配")
    private Date startLastBiddingLastStartTime;
    @ApiModelProperty(value = "竞价开始日期-至",notes = "时间范围匹配")
    private Date endLastBiddingLastStartTime;

    /**
     *{@link BiddingStatusEnum}
     */
    @ApiModelProperty(value = "竞价状态",notes = "多选匹配 参考 BiddingStatusEnum")
    private List<String> status;

    /**
     *{@link ReportDealStatusEnum}
     */
    @ApiModelProperty(value = "成交状态",notes = "单选精确匹配 参考 ReportDealStatusEnum")
    private String dealStatus;

    @ApiModelProperty(value = "数据权限 商品编码",hidden = true)
    private List<String> dataPermissionGoodsCodes;
}
