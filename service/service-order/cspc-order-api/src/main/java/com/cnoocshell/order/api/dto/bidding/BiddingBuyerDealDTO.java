package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.cnoocshell.order.api.dto.AccountUserDTO;
import com.cnoocshell.order.api.dto.BaseResultDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("竞价结果")
@EqualsAndHashCode(callSuper = true)
public class BiddingBuyerDealDTO extends BaseResultDTO {
    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("竞价场次编号")
    private String biddingNo;

    @ApiModelProperty("成交状态 true:已成交")
    private Boolean dealStatus = false;

    @ApiModelProperty("成交时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date dealTime;

    @ApiModelProperty("提交成交时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date submitDealTime;

    @ApiModelProperty("成交确认操作人ID")
    private String dealOperator;
    @ApiModelProperty("成交确认操作人姓名")
    private String dealOperatorName;
    @ApiModelProperty("不成交原因")
    private String notDealReason;


    @ApiModelProperty("SAP合同编号")
    private String sapContractNo;

    @ApiModelProperty("销售人员")
    private AccountUserDTO saleUser;

    @ApiModelProperty("客服")
    private AccountUserDTO customerServiceUser;

    @ApiModelProperty("最新基准价")
    private BigDecimal lastStandardPrice;

    @ApiModelProperty("买家成交明细")
    private List<BiddingBuyerDetailDTO> buyerDealDetail;

    private Integer dealCount;

    private String memberCode;
}
