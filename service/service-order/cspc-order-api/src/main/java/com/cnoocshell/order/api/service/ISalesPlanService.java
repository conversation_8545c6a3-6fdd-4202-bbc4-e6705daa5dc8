package com.cnoocshell.order.api.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.order.api.dto.salesPlan.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name ="cspc-service-order")
public interface ISalesPlanService {

    @PostMapping(value = "/salesPlan/findSalePlanById", consumes = "application/json")
    SalesDetailDTO findSalePlanById(@RequestParam("id") String id,@RequestParam("accountId") String accountId,@RequestParam("memberId") String memberId);

    @PostMapping(value ="/salesPlan/findSalePlanByCondition", consumes = "application/json")
    PageInfo<SalesPlanPageResponseDTO> findSalePlanByCondition(@RequestBody SalesPlanPageRequestDTO requestDTO);


    @PostMapping(value = "/salesPlan/addSalesPlan", consumes = "application/json")
    String addSalesPlan(@RequestBody SalesPlanInfoDTO requestDTO);


    @PostMapping("/salesPlan/updateSalesPlan")
    String updateSalesPlan(@RequestBody SalesPlanInfoDTO requestDTO);

    @PostMapping("/salesPlan/revokeSalesPlan")
    String revokeSalesPlan(@RequestParam("id") String id);

    @ApiOperation("删除草稿")
    @PostMapping("/salesPlan/removeDraft")
    ItemResult<Void> removeDraft(@RequestBody RemoveSalesPlanDTO param);
}
