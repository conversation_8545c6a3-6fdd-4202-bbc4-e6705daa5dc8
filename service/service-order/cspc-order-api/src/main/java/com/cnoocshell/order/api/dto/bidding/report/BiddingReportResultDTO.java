package com.cnoocshell.order.api.dto.bidding.report;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.cnoocshell.common.excel.Excel;
import com.cnoocshell.order.api.dto.BaseResultDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class BiddingReportResultDTO extends BaseResultDTO {
    @ApiModelProperty(value = "产品一级分类", notes = "编码")
    private String categoryCodeLevelOne;
    @ApiModelProperty(value = "产品一级分类", notes = "名称")
    @Excel(index = 1, title = "产品一级分类")
    private String categoryNameLevelOne;

    @ApiModelProperty(value = "产品二级分类", notes = "编码")
    private String categoryCodeLevelTwo;
    @ApiModelProperty(value = "产品二级分类", notes = "名称")
    @Excel(index = 5, title = "产品二级分类")
    private String categoryNameLevelTwo;

    @ApiModelProperty(value = "产品编码")
    private String goodsCode;
    @ApiModelProperty(value = "产品名称")
    @Excel(index = 10, title = "产品名称")
    private String goodsName;

    @ApiModelProperty(value = "SAP物料编码")
    @Excel(index = 15, title = "SAP物料编号")
    private String sapMaterialCode;

    @ApiModelProperty(value = "销售计划编号")
    @Excel(index = 20, title = "销售计划编号")
    private String salesPlanNo;
    @ApiModelProperty(value = "计划可售量", notes = "销售计划中申请预计可售量")
    @Excel(index = 25, title = "计划可售量")
    private BigDecimal applySellableQuantity;

    @ApiModelProperty(value = "提货开始日期", notes = "竞价数据上的提货开始时间 MM/dd/yyyy")
    @Excel(index = 30, title = "提货开始日期", dateFormat = "MM/dd/yyyy", isDate = true)
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date deliveryEffectStartDate;
    @ApiModelProperty(value = "提货结束日期", notes = "竞价数据上的提货结束时间 MM/dd/yyyy")
    @Excel(index = 35, title = "提货结束日期", dateFormat = "MM/dd/yyyy", isDate = true)
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date deliveryEffectEndDate;

    @ApiModelProperty(value = "竞价开始时间", notes = "yyyy-MM-dd HH:mm:ss")
    @Excel(index = 40, title = "竞价开始时间", dateFormat = "yyyy-MM-dd HH:mm:ss", isDate = true)
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date lastBiddingStartTime;
    @ApiModelProperty(value = "竞价结束时间", notes = "yyyy-MM-dd HH:mm:ss")
    @Excel(index = 45, title = "竞价结束时间", dateFormat = "yyyy-MM-dd HH:mm:ss", isDate = true)
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date lastBiddingEndTime;

    @ApiModelProperty(value = "竞价开始日期", notes = "竞价场次的开始日期 MM/dd/yyyy")
    @Excel(index = 50, title = "竞价开始日期", dateFormat = "MM/dd/yyyy", isDate = true)
    @JsonFormat(pattern = DatePatternConstant.DATE_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.DATE_FORMAT)
    private Date biddingStartDate;

    @ApiModelProperty(value = "竞价场次编号")
    @Excel(index = 55, title = "竞价场次编号")
    private String biddingNo;

    @ApiModelProperty(value = "本次申请可售量")
    @Excel(index = 60, title = "本次申请可售量")
    private BigDecimal currApplySellableQuantity;

    @ApiModelProperty(value = "竞价轮次")
    @Excel(index = 65, title = "竞价轮次")
    private Integer round;

    @ApiModelProperty(value = "出厂价", notes = "每轮次的最新基准价格")
    @Excel(index = 70, title = "出厂价")
    private BigDecimal standardPrice;

    @ApiModelProperty(value = "客户编码")
    @Excel(index = 75, title = "客户编码")
    private String crmCode;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty(value = "客户名称")
    @Excel(index = 80, title = "客户名称")
    private String memberName;

    @ApiModelProperty(value = "客户渠道", notes = "销售渠道编码")
    private String saleChannel;
    @ApiModelProperty(value = "客户渠道", notes = "销售渠道名称")
    @Excel(index = 85, title = "客户渠道")
    private String saleChannelName;

    @ApiModelProperty(value = "提报量",notes = "一次未参与的客户报量展示为 /")
    @Excel(index = 90, title = "提报量")
    private String wantQuantity;

    @ApiModelProperty(value = "成交编号")
    private String dealNo;

    @ApiModelProperty(value = "成交状态")
    private String dealStatus;
    @ApiModelProperty(value = "成交状态名称")
    @Excel(index = 95, title = "成交状态")
    private String dealStatusName;

    @ApiModelProperty(value = "竞价状态")
    private String status;
    @ApiModelProperty(value = "竞价状态名称")
    @Excel(index = 100, title = "竞价场次状态")
    private String statusName;
}
