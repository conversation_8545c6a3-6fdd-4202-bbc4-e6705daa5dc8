package com.cnoocshell.order.api.dto.analysis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BiddingAnalysisInfoDTO {
    @ApiModelProperty(value = "竞价单号")
    private String biddingNo;
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;
    @ApiModelProperty(value = "轮次")
    private Integer currentRound;
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    @ApiModelProperty(value = "会员编码")
    private String memberCode;
    @ApiModelProperty(value = "报量")
    private BigDecimal wantQuantity;
}
