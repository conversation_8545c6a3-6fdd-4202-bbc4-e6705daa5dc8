package com.cnoocshell.order.api.service;

import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.order.api.dto.*;
import com.cnoocshell.order.api.dto.bidding.export.ExportBiddingEnquiryDataDTO;
import com.cnoocshell.order.api.dto.bidding.export.ExportSellNumDataDTO;
import com.cnoocshell.order.api.dto.enquiry.CancelEnquiryDTO;
import com.cnoocshell.order.api.dto.enquiry.EnquiryBiddingTimesDTO;
import com.cnoocshell.order.api.dto.enquiry.NotifyBiddingDelayDTO;
import com.cnoocshell.order.api.dto.enquiry.RemoveEnquiryDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(name ="cspc-service-order")
public interface IOrderService {

	@PostMapping(value = "/enquiry/createEnquiry", consumes = "application/json")
	ItemResult<String> createEnquiry(@RequestBody EnquiryDTO enquiryDTO);

	@PostMapping(value = "/enquiry/queryEnquiryList")
	ItemResult<PageInfo<EnquiryDTO>> queryEnquiryList(@RequestBody EnquiryDTO enquiryDTO);

	@PostMapping(value = "/enquiryBuyer/buyerQueryEnquiryList")
	ItemResult<PageInfo<EnquiryBuyerListViewDTO>> buyerQueryEnquiryList(@RequestBody EnquiryBuyerListQueryDTO enquiryBuyerListQueryDTO);

	@PostMapping(value = "/enquiry/getEnquiryDetail")
	ItemResult<EnquiryDTO> getEnquiryDetail(@RequestBody EnquiryDTO enquiryDTO);

	@PostMapping(value = "/enquiry/updateEnquiry")
	ItemResult<EnquiryDTO> updateEnquiry(@RequestBody EnquiryDTO enquiryDTO);

	@PostMapping(value = "/enquiryBuyer/createEnquiryBuyer")
	ItemResult<String> createEnquiryBuyer(@RequestBody EnquiryBuyerDTO enquiryBuyerDTO);

	@PostMapping(value = "/enquiryBuyer/getEnquiryBuyDetail")
	ItemResult<EnquiryDTO> getEnquiryBuyDetail(@RequestBody EnquiryBuyerDTO enquiryBuyerDTO);

	@GetMapping(value = "/enquiry/getEnquiryGoods")
	ItemResult<List<GoodsSimpleDTO>> getEnquiryGoods(@RequestParam("accountId") String accountId);

	@GetMapping(value = "/enquiry/exportEnquiryBuyerDetail")
	ItemResult<ExportExcelDTO> exportEnquiryBuyerDetail(@RequestParam("enquiryNo") String enquiryNo);

	@PostMapping(value = "/bidding/queryBiddingEnquiryDataList")
	ItemResult<PageInfo<ExportBiddingEnquiryDataDTO>> queryBiddingEnquiryDataList(@RequestBody ExportBiddingEnquiryDataDTO queryCondition);

	@PostMapping(value = "/bidding/querySellNumDataList")
	ItemResult<PageInfo<ExportSellNumDataDTO>> querySellNumDataList(@RequestBody ExportSellNumDataDTO exportSellNumDataDTO);

	@PostMapping(value = "/bidding/exportBiddingEnquiryDataList")
	ItemResult<ExportExcelDTO> exportBiddingEnquiryDataList(ExportBiddingEnquiryDataDTO queryCondition);

	@PostMapping(value = "/bidding/exportSellNumDataList")
	ItemResult<ExportExcelDTO> exportSellNumDataList(ExportSellNumDataDTO exportSellNumDataDTO);

	@GetMapping(value = "/enquiry/getEnquiryBiddingTimes")
	ItemResult<EnquiryBiddingTimesDTO> getEnquiryBiddingTimes(@RequestParam("accountId") String accountId);

	@ApiOperation("取消询价")
	@PostMapping("/enquiry/cancelEnquiry")
	ItemResult<Boolean> cancelEnquiry(@RequestBody CancelEnquiryDTO param);

	@ApiOperation("竞价场次推迟通知")
	@PostMapping("/enquiry/notifyBiddingDelay")
	ItemResult<Void> notifyBiddingDelay(@RequestBody NotifyBiddingDelayDTO param);

	@ApiOperation("删除草稿")
	@PostMapping("/enquiry/removeDraft")
	ItemResult<Void> removeDraft(@RequestBody RemoveEnquiryDTO param);
}
