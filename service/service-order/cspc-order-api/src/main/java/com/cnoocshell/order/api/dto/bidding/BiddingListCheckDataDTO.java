package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.order.api.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BiddingListCheckDataDTO extends PageDTO implements Serializable {

    @ApiModelProperty("竞价场次编号")
    private List<String> biddingNoList;

    @ApiModelProperty("操作人ID")
    private String accountId;
}
