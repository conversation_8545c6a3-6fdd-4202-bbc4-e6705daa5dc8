package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BiddingCreateReturnDTO implements Serializable {

    @ApiModelProperty("关联的销售计划已无剩余可售量，无法创建竞价场次--询价")
    private List<String> quantityGoodsNameList;

    @ApiModelProperty("状态条件不符--询价")
    private List<String> statusGoodsNameList;

    @ApiModelProperty("关联的销售计划已无剩余可售量，无法创建竞价场次--销售计划")
    private List<String> quantitySalesNoList;

    @ApiModelProperty("状态条件不符--销售计划")
    private List<String> statusSalesNoList;

    @ApiModelProperty("竞价开始时间销售计划提货结束时间不满足校验的询价单号")
    private List<String> verifyFailDeliveryEffectEndDateEnquiryNoList;

    @ApiModelProperty("成功条数")
    private int successNo;

    @ApiModelProperty("失败条数")
    private int errorNo;
}
