package com.cnoocshell.order.api.dto.bidding;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BiddingApproveDTO {
    @ApiModelProperty("竞价编号")
    @NotBlank(message = "竞价编号不能为空")
    private String biddingNo;

    @ApiModelProperty("审批类型")
    private String approveType;

    @ApiModelProperty("驳回原因")
    @NotBlank(message = "驳回原因不能为空")
    private String reason;

    @ApiModelProperty(value = "操作人", hidden = true)
    private String operatorId;

    @ApiModelProperty(value = "操作人姓名", hidden = true)
    private String operatorName;
}
