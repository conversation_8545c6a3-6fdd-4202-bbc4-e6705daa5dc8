package com.cnoocshell.order.api.constant;

public class XxlJobConstant {

    private XxlJobConstant(){
        throw new IllegalStateException("XxlJobConstant class");
    }

    /**
     *竞价通知定时任务
     */
    public static final String NOTIFY_BIDDING_START_JOB = "notifyBiddingStartJob";

    /**
     *竞价开始定时任务
     */
    public static final String BIDDING_START_JOB = "biddingStartJob";

    /**
     *竞价结束定时任务
     */
    public static final String BIDDING_END_JOB = "biddingEndJob";


    /**
     *询价通知定时任务
     */
    public static final String NOTIFY_ENQUIRY_START_JOB = "notifyEnquiryStartJob";

    /**
     *询价开始定时任务
     */
    public static final String ENQUIRY_START_JOB = "enquiryStartJob";

    /**
     *询价结束定时任务
     */
    public static final String ENQUIRY_END_JOB = "enquiryEndJob";
}
