package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BiddingCustomerListDTO implements Serializable {

    @ApiModelProperty("询价ID")
    private String enquiryId;

    @ApiModelProperty("客户名称")
    private String memberName;

    @ApiModelProperty("客户编码")
    private String memberCode;

    @ApiModelProperty("客户crm编码")
    private String crmCode;

    @ApiModelProperty("是否参与询价")
    private String participationStatus;

    @ApiModelProperty("次要意向商品")
    private String mainGoods;

    @ApiModelProperty("主要意向商品")
    private String minorGoods;

    @ApiModelProperty("商品code")
    private String goodsCode;
}
