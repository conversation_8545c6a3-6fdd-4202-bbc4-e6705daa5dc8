package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.order.api.dto.AccountUserDTO;
import com.cnoocshell.order.api.dto.bidding.permission.BiddingPermission;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class QueryBiddingDetailDTO extends AccountUserDTO {
    @ApiModelProperty("竞价编号")
    @NotBlank(message = "竞价编号不能为空")
    private String biddingNo;

    @ApiModelProperty(value = "是否卖家",hidden = true)
    private Boolean isSeller = false;

    @ApiModelProperty(value = "买家会员编号",hidden = true)
    private String buyerMemberCode;

    @ApiModelProperty(value = "数据权限控制",hidden = true)
    private List<BiddingPermission> permissions;

    @ApiModelProperty(value = "是否仅销售人员",hidden = true)
    private Boolean isOnlySalePersonal;

    @ApiModelProperty(value = "查看来源 1：竞价列表 2：竞价审批列表",hidden = true)
    private int dataFrom;

    @ApiModelProperty(value = "角色")
    private List<String> roleList;
}
