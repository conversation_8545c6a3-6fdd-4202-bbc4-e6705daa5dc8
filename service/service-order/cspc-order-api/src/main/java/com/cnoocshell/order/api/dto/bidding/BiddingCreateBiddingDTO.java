package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class BiddingCreateBiddingDTO implements Serializable {

    @ApiModelProperty("enquiry List")
    private List<String> idList;

    @ApiModelProperty("sale id")
    private String id;

    @ApiModelProperty("竞价开始时间")
    private LocalDateTime biddingStartTime;

    @ApiModelProperty("竞价开始时间")
    private LocalDateTime biddingEndTime;

    @ApiModelProperty("保存状态 DRAFT：保存-草稿  TO_SUBMIT_STRATEGY：提交-待开始")
    private String status;

    @ApiModelProperty("操作人ID")
    private String accountId;

    @ApiModelProperty("操作人姓名")
    private String realName;

    @ApiModelProperty("新增竞价人员信息")
    private List<String> buyerList;

    @ApiModelProperty("创建来源 1：询价 2：销售计划")
    private int dataSource;

    @ApiModelProperty("付款条件")
    private String payCondition;

    @ApiModelProperty("价格贸易条款")
    private String priceTradeTerm;

    @ApiModelProperty("交易币种")
    private String tradeCurrency;
}
