package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RemainingSellableQuantityDTO {
    @ApiModelProperty("竞价编号")
    private String biddingNo;

    @ApiModelProperty("销售编号")
    private String planNo;

    @ApiModelProperty("销售计划申请预计可售量")
    private BigDecimal applySellableQuantity;

    @ApiModelProperty("剩余可销售量")
    private BigDecimal remainingSellableQuantity;
}
