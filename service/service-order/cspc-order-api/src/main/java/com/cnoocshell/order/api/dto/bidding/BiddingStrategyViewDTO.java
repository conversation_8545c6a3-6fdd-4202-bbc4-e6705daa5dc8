package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.order.api.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
public class BiddingStrategyViewDTO implements Serializable {

    @ApiModelProperty("竞价基础信息")
    private BiddingEditBiddingDTO biddingEditBiddingDTO;

    @ApiModelProperty("竞价策略")
    private BiddingStrategyDetailDTO biddingStrategyDetailDTO;

    @ApiModelProperty("审批记录")
    private List<BiddingApproveListDTO> biddingApproveListDTOList;

}
