package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.cnoocshell.order.api.dto.BaseResultDTO;
import com.cnoocshell.order.api.dto.bidding.group.BiddingAdjustGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("调价记录")
@EqualsAndHashCode(callSuper = true)
public class BiddingAdjustDTO extends BaseResultDTO {
    @ApiModelProperty("竞价单号")
    @NotBlank(message = "竞价场次编号不能为空",groups = BiddingAdjustGroup.class)
    private String biddingNo;

    @ApiModelProperty("调整前价格")
    private BigDecimal oldStandardPrice;

    @ApiModelProperty("调整后价格")
    @NotNull(message = "调整后价格不能为空",groups = BiddingAdjustGroup.class)
    private BigDecimal newStandardPrice;

    @ApiModelProperty("竞价开始时间")
    @NotNull(message = "下轮竞价开始时间不能为空",groups = BiddingAdjustGroup.class)
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date biddingStartTime;

    @ApiModelProperty("竞价结束时间")
    @NotNull(message = "下轮竞价结束时间不能为空",groups = BiddingAdjustGroup.class)
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date biddingEndTime;
}
