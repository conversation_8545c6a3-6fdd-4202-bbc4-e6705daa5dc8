package com.cnoocshell.order.api.dto.salesPlan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class SalesPlanPageResponseDTO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("销售计划编号")
    private String planNo;

    @ApiModelProperty("销售计划名称")
    private String planName;

    @ApiModelProperty("产品名称")
    private String goodsName;

    @ApiModelProperty("提货开始日期")
    private LocalDate deliveryEffectStartDate;

    @ApiModelProperty("提货结束日期")
    private LocalDate deliveryEffectEndDate;

    @ApiModelProperty("申请预计可售量（吨）")
    private BigDecimal applySellableQuantity;

    @ApiModelProperty("预计竞价场次次数")
    private Integer expectBiddingCount;

    @ApiModelProperty("是否创建询价")
    private Integer isCreatedInquiry;

    @ApiModelProperty("是否需要询价")
    private Integer isNeedInquiry;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人id")
    private String createUser;

    @ApiModelProperty("创建人名称")
    private String createUserName;
}
