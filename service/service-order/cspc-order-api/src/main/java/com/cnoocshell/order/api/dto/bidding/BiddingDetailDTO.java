package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.order.api.dto.analysis.BiddingAnalysisResultDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel("竞价详情")
public class BiddingDetailDTO {
    @ApiModelProperty("竞价基础信息")
    private BiddingBasicDTO biddingBasic;

    @ApiModelProperty(value = "竞价策略",notes = "买家 竞价信息")
    private BiddingStrategyDTO biddingStrategy;

    @ApiModelProperty("调价记录")
    private List<BiddingAdjustDTO> biddingAdjustList;

    @ApiModelProperty("买家竞价记录")
    private List<BiddingBuyerDetailDTO> biddingBuyerDetails;

    @ApiModelProperty("审批记录")
    private List<BiddingApproveRecordDTO> approveList;

    @ApiModelProperty(value = "买家竞价结果",notes = "卖家成交结果")
    private BiddingBuyerDealDTO biddingBuyerDeal;

    @ApiModelProperty(value = "竞价分析数据",notes = "竞价分析图")
    private List<BiddingAnalysisResultDTO> biddingAnalysis;
}
