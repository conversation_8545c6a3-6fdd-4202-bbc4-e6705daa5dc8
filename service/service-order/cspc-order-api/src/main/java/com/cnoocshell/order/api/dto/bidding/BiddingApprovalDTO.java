package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.order.api.constant.OrderNumberConstant;
import com.cnoocshell.order.api.dto.PageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BiddingApprovalDTO extends PageDTO implements Serializable {

    @ApiModelProperty("id List")
    private List<String> biddingIdList;

    @ApiModelProperty("竞价场次编号")
    private List<String> biddingNoList;

    @ApiModelProperty("原因/备注")
    @Size(max = 500,message = "原因/备注限制输入500字符")
    private String reason;

    @ApiModelProperty("1:通过,2：驳回")
    @NotNull(message = "审批状态不能为空")
    private Integer checkStatus;

    @ApiModelProperty("操作人ID")
    private String accountId;

    @ApiModelProperty("操作人姓名")
    private String realName;

    @ApiModelProperty("操作人邮箱")
    private String email;

    @ApiModelProperty("用户角色")
    private List<String> roleList;
}
