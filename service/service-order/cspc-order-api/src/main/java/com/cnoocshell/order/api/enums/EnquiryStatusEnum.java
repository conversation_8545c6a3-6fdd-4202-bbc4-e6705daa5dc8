package com.cnoocshell.order.api.enums;

/**
 * 代表询价状态的枚举类型，每个枚举值包含一个唯一的编码和描述信息。
 * 枚举值包括草稿、待开始、询价中、已结束以及已作废等状态。
 */
public enum EnquiryStatusEnum {

	DRAFT("DRAFT", "草稿"),
	TO_START("TO_START", "待开始"),
	ENQUIRING("ENQUIRING", "询价中"),
	END("END", "已结束"),
	CANCELLED("CANCELLED", "已作废"),


	/**
	 *2025.5.8 取消询价CR添加状态
	 */
	ENQUIRY_CANCEL("ENQUIRY_CANCEL","已取消");

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    EnquiryStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

}
