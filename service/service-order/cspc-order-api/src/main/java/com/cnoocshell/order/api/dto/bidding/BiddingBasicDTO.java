package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.cnoocshell.order.api.dto.BaseResultDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel("竞价基础消息")
@Data
@EqualsAndHashCode(callSuper = true)
public class BiddingBasicDTO extends BaseResultDTO {
    @ApiModelProperty("竞价ID")
    private String id;

    @ApiModelProperty("竞价场次编号")
    private String biddingNo;

    @ApiModelProperty("竞价场次名称")
    private String biddingName;

    @ApiModelProperty("可参与竞价客户数量")
    private Integer biddingBuyerCount;

    @ApiModelProperty("当前竞价轮次")
    private Integer currentRound;

    @ApiModelProperty("最新竞价开始时间")
    private Date lastBiddingStartTime;

    @ApiModelProperty("最新竞价结束时间")
    private Date lastBiddingEndTime;

    @ApiModelProperty("参与状态 ture:已参与")
    private Boolean joinStatus = false;

    @ApiModelProperty("成交状态 true:已成交")
    private Boolean dealStatus;

    @ApiModelProperty(value = "付款条款",notes = "值集 VS_PAYMENT_TERMS")
    private String payCondition;

    @ApiModelProperty(value = "价格贸易条款",notes = "值集 VS_TRADE_TERMS")
    private String priceTradeTerm;

    @ApiModelProperty(value = "交易币种",notes = "值集 VS_TRADE_CURRENCY")
    private String tradeCurrency;

    @ApiModelProperty("竞价状态")
    private String status;

    @ApiModelProperty("销售计划ID")
    private String salePlanId;

    @ApiModelProperty("销售计划单号")
    private String salePlanNo;

    @ApiModelProperty("询价ID")
    private String enquiryId;

    @ApiModelProperty("询价单号")
    private String enquiryNo;

    @ApiModelProperty("包装方式")
    private String pack;

    @ApiModelProperty("销售组")
    private String salesGroup;

    @ApiModelProperty("买家是否参与上一轮竞价")
    private Boolean joinLastRoundBidding = true;

    @ApiModelProperty("是否超量")
    private Boolean overQuantity = Boolean.FALSE;

    @ApiModelProperty("超量成交备注")
    private String overDealRemark;

    @ApiModelProperty("取消人ID")
    private String cancelUser;
    @ApiModelProperty("取消人姓名")
    private String cancelUserName;
    @ApiModelProperty("取消原因")
    private String cancelReason;
    @ApiModelProperty("取消时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date cancelTime;

    public void initNull(){
        this.cancelUser = null;
        this.cancelUserName = null;
        this.cancelReason = null;
        this.cancelTime = null;
    }
}
