package com.cnoocshell.order.api.dto.enquiry;

import com.cnoocshell.common.excel.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("导出询价记录")
public class ExportEnquiryBuyerDetailDTO {
    @ApiModelProperty("序号")
    @Excel(index = 1, title = "序号")
    private int serialNum;

    @ApiModelProperty(name = "客户名称")
    @Excel(index = 5, title = "客户名称")
    private String memberName;

    @Excel(index = 10, title = "客户编码")
    private String crmCode;

    @Excel(index = 15, title = "销售渠道")
    private String saleChannel;

    @Excel(index = 25, title = "出厂价（元/吨）")
    private BigDecimal price;

    @Excel(index = 20, title = "报量（吨）")
    private BigDecimal quantity;

    @ApiModelProperty(value = "配送方式", notes = "值集 VS_DELIVERY_METHOD")
    private String deliveryMode;
    @Excel(index = 30, title = "配送方式")
    private String deliveryModeName;

    @ApiModelProperty(value = "包装方式",notes = "值集 VS_PACKAGE_METHOD")
    private String pack;
    @Excel(index = 33,title = "包装方式")
    private String packName;

    @Excel(index = 35, title = "提交人")
    private String createUserName;

    @Excel(index = 40, title = "提交时间")
    private Date createTime;
}
