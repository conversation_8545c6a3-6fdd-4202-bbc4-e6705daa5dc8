package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("竞价简单消息model")
public class SimpleBiddingDTO {
    @ApiModelProperty("竞价ID")
    private String id;
    @ApiModelProperty("竞价场次编号")
    private String biddingNo;
    @ApiModelProperty("竞价场次名称")
    private String biddingName;
    @ApiModelProperty("询价场次编号")
    private String enquiryNo;
    @ApiModelProperty("询价场次名称")
    private String enquiryName;
    @ApiModelProperty("销售计划编号")
    private String salesPlanNo;
    @ApiModelProperty("销售计划名称")
    private String salesPlanName;
    @ApiModelProperty("商品编码")
    private String goodsCode;
    @ApiModelProperty("商品名称")
    private String goodsName;
    @ApiModelProperty("品类编码")
    private String categoryCode;
    @ApiModelProperty("竞价状态")
    private String status;
    @ApiModelProperty("竞价开始最新时间")
    private Date lastBiddingStartTime;
    @ApiModelProperty("竞价结束最新时间")
    private Date lastBiddingEndTime;
}
