package com.cnoocshell.order.api.dto.salesPlan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class SalesDetailDTO implements Serializable {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("销售计划编号")
    private String planNo;

    @ApiModelProperty("销售计划名称")
    private String planName;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("产品")
    private String categoryCode;

    @ApiModelProperty("产品")
    private String goodsCode;

    @ApiModelProperty("产品名称")
    private String goodsName;

    @ApiModelProperty("工厂编码")
    private String factoryCode;

    @ApiModelProperty("是否创建询价")
    private Integer isCreatedInquiry;

    @ApiModelProperty("是否需要询价")
    private Integer isNeedInquiry;

    @ApiModelProperty("运输方式")
    private String deliveryMode;

    @ApiModelProperty("提货开始日期")
    private LocalDate deliveryEffectStartDate;

    @ApiModelProperty("提货结束日期")
    private LocalDate deliveryEffectEndDate;

    @ApiModelProperty("期初库存")
    private String storageQuantity;

    @ApiModelProperty("发货期产量")
    private String deliveryProductionQuantity;

    @ApiModelProperty("LTC")
    private String ltcQuantity;

    @ApiModelProperty("Spot Carryover量")
    private String spotCarryoverQuantity;

    @ApiModelProperty("目标库存量")
    private String targetStorageQuantity;

    @ApiModelProperty("others量")
    private String othersQuantity;

    @ApiModelProperty("申请预计可售量")
    private String applySellableQuantity;

    @ApiModelProperty("系统计算可售量")
    private String systemSellableQuantity;

    @ApiModelProperty("预计竞价场次次数")
    private Integer expectBiddingCount;

    @ApiModelProperty("配送收费标准（附件）")
    private String deliveryCostStandard;

    @ApiModelProperty("自提指南（附件）")
    private String selfPickupGuide;

    @ApiModelProperty("自提承运商（附件））")
    private String selfPickupCarrier;

    @ApiModelProperty("该销售计划下关联的所有竞价成交量")
    private String totalDealQuantity;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人id")
    private String createUser;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人名称")
    private String updateUserName;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
