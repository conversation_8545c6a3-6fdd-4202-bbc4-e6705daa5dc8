package com.cnoocshell.order.api.dto.bidding.export;

import com.cnoocshell.common.excel.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel("查询可售量")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExportSellNumDataDTO {
    @ApiModelProperty("竞价场次编号")
    @Excel(index = 1, title = "竞价场次编号")
    private String biddingNo;

    @ApiModelProperty(name = "竞价场次名称")
    @Excel(index = 5, title = "竞价场次名称")
    private String biddingName;

    @ApiModelProperty(name = "产品名称")
    @Excel(index = 10, title = "产品名称")
    private String goodsName;

    @ApiModelProperty(name = "竞价策略申请预计可售量（吨）")
    @Excel(index = 15, title = "竞价策略申请预计可售量（吨）")
    private BigDecimal applySellableQuantity;

    @ApiModelProperty(name = "本场已售量")
    @Excel(index = 20, title = "本场已售量")
    private BigDecimal nowSellableQuantity;

    @ApiModelProperty(name = "本场剩余可售量")
    @Excel(index = 20, title = "本场剩余可售量")
    private BigDecimal surplusSellableQuantity;

    @ApiModelProperty(name = "申请预计可售量")
    @Excel(index = 20, title = "申请预计可售量")
    private BigDecimal expectedSellableQuantity;

    @ApiModelProperty(name = "系统计算可售量")
    @Excel(index = 20, title = "系统计算可售量")
    private BigDecimal systemComputingSellableQuantity;

    private String startDate;

    private String endDate;

    private Integer pageNum;

    private Integer pageSize;

}
