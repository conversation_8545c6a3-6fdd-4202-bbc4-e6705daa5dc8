package com.cnoocshell.order.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 询价数据详情
 * 页面数据示意
 *  distributionOne  distributionTwoLeft  distributionTwoRight distributionThree
 * minPrice      medianOne            average           medianTwo      maxPrice
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnquiryDataDetailDTO {

    private BigDecimal maxPrice;

    private BigDecimal minPrice;

    private BigDecimal medianOne; // 中位数1

    private BigDecimal medianTwo; // 中位数2

    private Long distributionOne;//分布1

    private Long distributionTwoLeft;
    private Long distributionTwoRight;

    private Long distributionThree;

    private BigDecimal totalQty;//总量

    @ApiModelProperty(value = "最大最小价格平均数")
    private BigDecimal average;

}