package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.order.api.dto.PageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BiddingBuyerQueryDTO extends PageDTO implements Serializable {

    @ApiModelProperty("竞价场次编号")
    private String biddingNo;

    @ApiModelProperty("竞价场次名称")
    private String biddingName;

    @ApiModelProperty("竞价产品")
    private List<String> goodsNameList;

    @ApiModelProperty("竞价产品code")
    private List<String> goodsCodeList;

    @ApiModelProperty("竞价场次状态")
    private List<String> status;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate createStartTime;

    @ApiModelProperty("创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate createEndTime;

    @ApiModelProperty("允许查看的状态列表")
    private List<String> statusList;

    @ApiModelProperty("操作人ID")
    private String accountId;

    @ApiModelProperty("操作人姓名")
    private String realName;

    @ApiModelProperty("操作人code")
    private String memberCode;

    @ApiModelProperty("用户角色")
    private List<String> roleList;

    @ApiModelProperty("不能能够查看的状态")
    private List<String> cannotLookStatus;

    @ApiModelProperty("参与状态")
    private String participationStatus;

    @ApiModelProperty("成交状态")
    private Boolean transactionStatus;

    @ApiModelProperty("完成状态")
    private String lastStatus;
}
