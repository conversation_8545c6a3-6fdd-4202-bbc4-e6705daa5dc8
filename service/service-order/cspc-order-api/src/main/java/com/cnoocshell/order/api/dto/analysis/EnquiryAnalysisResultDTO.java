package com.cnoocshell.order.api.dto.analysis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 *询价分析图 数据结果
 */
@Data
public class EnquiryAnalysisResultDTO {

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
}
