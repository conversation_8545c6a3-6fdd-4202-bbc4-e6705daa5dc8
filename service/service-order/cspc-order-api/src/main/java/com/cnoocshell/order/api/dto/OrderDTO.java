package com.cnoocshell.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("订单DTO")
public class OrderDTO implements Serializable{

//    private static final long serialVersionUID = 3915838488205634931L;

    /**
     * 仓库id
     */
    @ApiModelProperty("仓库id")
    private String storeId;


    /**
     * 购物车id
     */
    @ApiModelProperty("购物车id")
    private List<String> cartResourceIds;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private String orderId;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderCode;


    @ApiModelProperty("是否一级订单(为true表示当前订单为背靠背的一级订单)")
    private Boolean primaryOrder=false;
    @ApiModelProperty("是否二级订单(为true表示当前订单为背靠背的二级级订单)")
    private Boolean secondaryOrder=false;

    /**
     * 订单版本
     */
    @ApiModelProperty("订单版本")
    private Integer orderVersion;

    /**
     * 订单类型
     */
    @ApiModelProperty("订单类型")
    private String orderType;

    /**
     * 订单状态
     */
    @ApiModelProperty("订单状态")
    private String orderStatus;

    /**
     * 调价状态
     */
    @ApiModelProperty("调价状态")
    private String adjustStatus;

    /**
     * 卖家id
     */
    @ApiModelProperty("卖家id")
    private String sellerId;

    /**
     * 卖家名称
     */
    @ApiModelProperty("卖家名称")
    private String sellerName;

    /**
     * 卖家联系人
     */
    @ApiModelProperty("卖家联系人")
    private String sellerContact;

    /**
     * 卖家联系方式
     */
    @ApiModelProperty("卖家联系方式")
    private String sellerContactWay;

    /**
     * 买家Id
     */
    @ApiModelProperty("买家Id")
    private String buyerId;

    /**
     * 买家类型
     */
    @ApiModelProperty("买家类型")
    private String buyerType;

    /**
     * 买家名称
     */
    @ApiModelProperty("买家名称")
    private String buyerName;

    /**
     * 买家联系人
     */
    @ApiModelProperty("买家联系人")
    private String buyerContact;

    /**
     * 买家联系方式
     */
    @ApiModelProperty("买家联系方式")
    private String buyerContactWay;

    /**
     * 是否已锁价
     */
    @ApiModelProperty("是否已锁价")
    private Boolean ifLocked;

    /**
     * 锁价时间
     */
    @ApiModelProperty("锁价时间")
    private Date pricelockTime;

    /**
     * 锁价类型
     */
    @ApiModelProperty("锁价类型")
    private String pricelockType;

    /**
     * 协议Id
     */
    @ApiModelProperty("协议Id")
    private String dealsId;

    /**
     * 协议序列号
     */
    @ApiModelProperty("协议序列号")
    private String dealsSequence;

    /**
     * 协议名称
     */
    @ApiModelProperty("协议名称")
    private String dealsName;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 业务员id
     */
    @ApiModelProperty("业务员id")
    private String salesmanId;

    /**
     * 业务员姓名
     */
    @ApiModelProperty("业务员姓名")
    private String salesmanName;

    /**
     * 业务员电话
     */
    @ApiModelProperty("业务员电话")
    private String salesmanMobile;

    /**
     * 组织结构id
     */
    @ApiModelProperty("组织结构id")
    private String orgId;

    /**
     * 组织机构名称
     */
    @ApiModelProperty("组织机构名称")
    private String orgName;

    /**
     * erp订单号
     */
    @ApiModelProperty("erp订单号")
    private String erporderCode;

    /**
     * 收货地址Id
     */
    @ApiModelProperty("收货地址Id")
    private String addressId;

    /**
     * 渠道
     */
    @ApiModelProperty("渠道")
    private String createWay;

    /**
     * 是否线下支付
     */
    @ApiModelProperty("是否线下支付")
    private Boolean ifUnderlinePay;

    /**
     * 是否买家可关闭
     */
    @ApiModelProperty("是否买家可关闭")
    private Boolean ifBuyercanclose;

    /**
     * 是否卖家可关闭
     */
    @ApiModelProperty("是否卖家可关闭")
    private Boolean ifSellercanclose;

    /**
     * 销售区域详细路径
     */
    @ApiModelProperty("销售区域详细路径")
    private String saleRegionPath;

    /**
     * 销售区域一级
     */
    @ApiModelProperty("销售区域一级")
    private String saleRegion1;

    /**
     * 销售区域二级
     */
    @ApiModelProperty("销售区域二级")
    private String saleRegion2;

    /**
     * 销售区域三级
     */
    @ApiModelProperty("销售区域三级")
    private String saleRegion3;

    /**
     * 销售区域四级
     */
    @ApiModelProperty("销售区域四级")
    private String saleRegion4;

    /**
     * 销售区域五级
     */
    @ApiModelProperty("销售区域五级")
    private String saleRegion5;

    /**
     * 销售区域一级
     */
    @ApiModelProperty("销售区域一级")
    private String saleRegionName1;

    /**
     * 销售区域二级
     */
    @ApiModelProperty("销售区域二级")
    private String saleRegionName2;

    /**
     * 销售区域三级
     */
    @ApiModelProperty("销售区域三级")
    private String saleRegionName3;

    /**
     * 销售区域四级
     */
    @ApiModelProperty("销售区域四级")
    private String saleRegionName4;

    /**
     * 销售区域五级
     */
    @ApiModelProperty("销售区域五级")
    private String saleRegionName5;

    /**
     * 订单卖家确认时间
     */
    @ApiModelProperty("订单卖家确认时间")
    private Date sellerConfirmTime;

    /**
     * 开始发货时间
     */
    @ApiModelProperty("开始发货时间")
    private Date startDeliverTime;

    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    private Date completeTime;

    /**
     * 订单关闭时间
     */
    @ApiModelProperty("订单关闭时间")
    private Date closeTime;

    /**
     * 门店代码
     */
    @ApiModelProperty("门店代码")
    private String shopCode;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String shopName;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private Date payTime;

    /**
     * 支付状态
     */
    @ApiModelProperty("支付状态")
    private String payStatus;

    /**
     * 发货状态
     */
    @ApiModelProperty("发货状态")
    private String deliverStatus;

    /**
     * 开票状态
     */
    @ApiModelProperty("开票状态")
    private String invoiceStatus;

    /**
     * 退款状态
     */
    @ApiModelProperty("退款状态")
    private String refundStatus;

    /**
     * 是否评论
     */
    @ApiModelProperty("是否评论")
    private Boolean ifRemark;

    /**
     * 卸货点编码
     */
    @ApiModelProperty("卸货点编码")
    private String unloadingCode;

    /**
     * 是否退款
     */
    @ApiModelProperty("是否退款")
    private Boolean ifRefund;

    /**
     * 是否补款
     */
    @ApiModelProperty("是否补款")
    private Boolean ifRepay;

    /**
     * 是否卖家关闭
     */
    @ApiModelProperty("是否卖家关闭")
    private Boolean ifSellerClose;

    /**
     * 是否平台关闭
     */
    @ApiModelProperty("是否平台关闭")
    private Boolean ifPlatformClose;

    /**
     * 是否需要卖家确认
     */
    @ApiModelProperty("是否需要卖家确认")
    private Boolean ifNeedsellerconfirm;

    /**
     * 是否可以多次发货
     */
    @ApiModelProperty("是否可以多次发货")
    private Boolean ifDeliverTimes;

    /**
     * 是否代客下单
     */
    @ApiModelProperty("是否代客下单")
    private Boolean ifSellerCreate;

    /**
     * 买家备注
     */
    @ApiModelProperty("买家备注")
    private String buyerMemo;

    /**
     * 卖家备注
     */
    @ApiModelProperty("卖家备注")
    private String sellerMemo;

    /**
     * 币种
     */
    @ApiModelProperty("币种")
    private String currency;

    /**
     * 币种符号
     */
    @ApiModelProperty("币种符号")
    private String currencySymbol;

    /**
     * 币种名称
     */
    @ApiModelProperty("币种名称")
    private String currencyName;

    /**
     * 配送方式
     */
    @ApiModelProperty("配送方式")
    private String deliverWay;

    /**
     * 支付期限
     */
    @ApiModelProperty("支付期限")
    private Date payTimeLimit;

    /**
     * 提货期限
     */
    @ApiModelProperty("提货期限")
    private Date takeTimeLimit;

    /**
     * 税率
     */
    @ApiModelProperty("税率")
    private Byte taxRate;

    /**
     * 订单原总金额
     */
    @ApiModelProperty("订单原总金额")
    private BigDecimal originOrderAmount;

    /**
     * 物流原总金额
     */
    @ApiModelProperty("物流原总金额")
    private BigDecimal originLogisticAmount;

    /**
     * 资源原总金额
     */
    @ApiModelProperty("资源原总金额")
    private BigDecimal originResourceAmount;

    /**
     * 其他原总金额
     */
    @ApiModelProperty("其他原总金额")
    private BigDecimal originOthersAmount;

    /**
     * 订单实际总金额
     */
    @ApiModelProperty("订单实际总金额")
    private BigDecimal actualOrderAmount;

    /**
     * 物流实际总费用
     */
    @ApiModelProperty("物流实际总费用")
    private BigDecimal actualLogisticAmount;

    /**
     * 资源实际总金额
     */
    @ApiModelProperty("资源实际总金额")
    private BigDecimal actualResourceAmount;

    /**
     * 其他实际总金额
     */
    @ApiModelProperty("其他实际总金额")
    private BigDecimal actualOthersAmount;

    /**
     * 订单已执行总金额
     */
    @ApiModelProperty("订单已执行总金额")
    private BigDecimal realtimeOrderAmount;

    /**
     * 物流已执行总金额
     */
    @ApiModelProperty("物流已执行总金额")
    private BigDecimal realtimeLogisticAmount;

    /**
     * 货物已执行总金额
     */
    @ApiModelProperty("货物已执行总金额")
    private BigDecimal realtimeResourceAmount;

    /**
     * 其他已执行总金额
     */
    @ApiModelProperty("其他已执行总金额")
    private BigDecimal realtimeOthersAmount;

    /**
     * 已支付总金额
     */
    @ApiModelProperty("已支付总金额")
    private BigDecimal orderPayedAmount;

    /**
     * 申请退款总金额
     */
    @ApiModelProperty("申请退款总金额")
    private BigDecimal requestRefundAmount;

    /**
     * 实际退款总金额
     */
    @ApiModelProperty("实际退款总金额")
    private BigDecimal realRefundAmount;

    /**
     * 补款金额
     */
    @ApiModelProperty("补款金额")
    private BigDecimal supplementAmount;

    /**
     * 结算金额
     */
    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    /**
     * 未付款
     */
    @ApiModelProperty("未付款")
    private BigDecimal orderUnPayAmount;

    /**
     * 应支付金额
     * 正常发订单从下单至结束状态：应支付金额＝订单总金额
     * 短发订单执行中：应支付金额＝订单总金额
     * 短发订单结束状态：应支付金额＝实发总金额＜订单总金额
     * 超发订单执行中和订单结束：应支付金额＝实发总金额＞订单总金额
     */
    @ApiModelProperty("应支付金额")
    private BigDecimal shouldPayAmount;

    @ApiModelProperty("退货金额合计")
    private BigDecimal itemRefundAmount;

    /**
     * 收货地址国家编码
     */
    @ApiModelProperty("收货地址国家编码")
    private String countryCode;

    /**
     * 收货地址国家名称
     */
    @ApiModelProperty("收货地址国家名称")
    private String countryName;

    /**
     * 收货地址省编码
     */
    @ApiModelProperty("收货地址省编码")
    private String provinceCode;

    /**
     * 收货地址省名称
     */
    @ApiModelProperty("收货地址省名称")
    private String provinceName;

    /**
     * 收货地址市/区编码
     */
    @ApiModelProperty("收货地址市/区编码")
    private String cityCode;

    /**
     * 收货地址市/区名称
     */
    @ApiModelProperty("收货地址市/区名称")
    private String cityName;

    /**
     * 收货地址区县编码
     */
    @ApiModelProperty("收货地址区县编码")
    private String districtCode;

    /**
     * 收货地址区县名称
     */
    @ApiModelProperty("收货地址区县名称")
    private String districtName;

    /**
     * 收货地址街道编码
     */
    @ApiModelProperty("收货地址街道编码")
    private String streetCode;

    /**
     * 收货地址街道名称
     */
    @ApiModelProperty("收货地址街道名称")
    private String streetName;

    /**
     * 详细收货地址
     */
    @ApiModelProperty("详细收货地址")
    private String addressDetail;

    /**
     * 收货地经纬度
     */
    @ApiModelProperty("收货地经纬度")
    private String addressMap;

    /**
     * 收货人
     */
    @ApiModelProperty("收货人")
    private String receiver;

    /**
     * 收货人姓名
     */
    @ApiModelProperty("收货人姓名")
    private String receiverName;

    /**
     * 收货人电话
     */
    @ApiModelProperty("收货人电话")
    private String receiverPhone;

    /**
     * 提货时间
     */
    @ApiModelProperty("提货时间")
    private Date takeTime;

    /**
     * 提货时间段
     */
    @ApiModelProperty("提货时间段")
    private String takeTimeQuantum;

    /**
     * 买家取消原因
     */
    @ApiModelProperty("买家取消原因")
    private String buyerCancelReason;

    /**
     * 线下支付凭证
     */
    @ApiModelProperty("线下支付凭证")
    private String underLinePic;

    /**
     * 线下支付备注
     */
    @ApiModelProperty("线下支付备注")
    private String underLineMome;

    /**
     * 支付方式 PayWayEnum
     */
    @ApiModelProperty("支付方式 PayWayEnum")
    private List<String> payWay;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private List<String> payWayText;

    /**
     * 支持的支付方式 PayWayEnum
     */
    @ApiModelProperty("支持的支付方式 PayWayEnum")
    private List<String> supportPayWay;

    /**
     * 搬运方式
     */
    @ApiModelProperty("搬运方式")
    private String carryWay;

    /**
     * 搬运楼层
     */
    @ApiModelProperty("搬运楼层")
    private String carryFloor;

    /**
     * 搬运楼层字符串
     */
    @ApiModelProperty("搬运楼层字符串")
    private String carryFloorStr;

    /**
     * 买家删除
     */
    @ApiModelProperty("买家删除")
    private Boolean buyerDel;

    /**
     * 卖家删除
     */
    @ApiModelProperty("卖家删除")
    private Boolean sellerDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createUser;
    @Deprecated(since = "2.1.4-RELEASE")
    private String createUserNm;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty("修改者")
    private String updateUser;

    @ApiModelProperty("特殊费用")
    private boolean specPay;

    /**
     * 优惠码id
     */
    @ApiModelProperty("优惠码id")
    private String discountId;

    /**
     * 台班费
     */
    @ApiModelProperty("台班费")
    private BigDecimal machineShiftCost;

    /**
     * 搬运费
     */
    @ApiModelProperty("搬运费")
    private BigDecimal truckage;

    /**
     * 其他费用 （空载费）
     */
    @ApiModelProperty("其他费用 （空载费）")
    private BigDecimal othersAmount;

    /**
     * 项目信息
     */
    @ApiModelProperty("项目信息")
    private String project;

    /**
     * 是否有搬运费规则
     */
    @ApiModelProperty("是否有搬运费规则")
    private boolean isHaveCartageRule;

    /**
     *  是否需要业务员
     */
    @ApiModelProperty("是否需要业务员")
    private boolean isNeedSalesman = false;

    /**
     * erp操作异常信息
     */
    @ApiModelProperty("erp操作异常信息")
    private String erpErroescription;

    /**
     * 订单是否调价
     */
    @ApiModelProperty("订单是否调价")
    private boolean isAdjust;

    /**
     * 付款确认类型
     */
    @ApiModelProperty("付款确认类型")
    private String paymentConfirmType;

    /**
     * 确认退款标识
     */
    @ApiModelProperty("确认退款标识")
    private Integer confirmRefundFlag;

    /**
     * 调价前实际结算总额
     */
    @ApiModelProperty("调价前实际结算总额")
    private BigDecimal totalPreAdjustAmount;

    /**
     * 支持搬运标识
     */
    @ApiModelProperty("支持搬运标识")
    private Integer supportCarryFlag;


    /**
     * 是否自动确认发货: 0:否 1:是
     */
    @ApiModelProperty(value = "是否自动确认发货: 0:否 1:是", required = true)
    private Boolean autoConfirmTake;

    /**
     * 运费支付对象
     */
    @ApiModelProperty("运费支付对象")
    private String carriagePayeeId;

    /**
     * 代理订单类型
     */
    @ApiModelProperty("代理订单类型")
    private String proxyOrderType;

    @ApiModelProperty("web层返前端使用(合同单是否显示价格)")
    private Boolean showPrice=true;

    /**
     * 二级订单买家名称
     */
    @ApiModelProperty("二级订单买家名称")
    private String secondaryBuyerName;

    /**
     * 自动支付方式
     */
    @ApiModelProperty("自动支付方式, 如果使用了自动支付，此字段不为空")
    private String autoPayWay;

    /**
     * ERP支付账户编码
     */
    @ApiModelProperty("ERP支付账户编码")
    private String mdmCode;

    /**
     * 船运是否同意调配 0:不同意 1:同意
     */
    @ApiModelProperty("ERP支付账户编码")
    private Integer ifAgreeDispatch;

    /**
     * 订单扩展信息
     */
    @ApiModelProperty("生产基地偏好")
    private String basePreference;
    @ApiModelProperty("收货地别名")
    private String alias;

    @ApiModelProperty("发票类型")
    private String billType;

    @ApiModelProperty("是否需要支付")
    private Boolean needPayment;

}
