package com.cnoocshell.order.api.dto.enquiry;

import com.cnoocshell.common.dto.OperatorDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@EqualsAndHashCode(callSuper = true)
public class CancelEnquiryDTO extends OperatorDTO {
    @ApiModelProperty("询价单号")
    @NotBlank(message = "询价单号不能为空")
    private String enquiryNo;

    @ApiModelProperty(value = "取消原因")
    @Size(max = 500,message = "取消原因限制输入500字符")
    @NotBlank(message = "取消原因不能为空")
    private String cancelReason;
}
