package com.cnoocshell.order.api.dto.bidding;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BiddingImportExcelDTO implements Serializable {

    @Alias("竞价场次编号")
    private String biddingNo;

    @Alias("产品名称")
    private String goodsName;

    @Alias("SAP物料编号")
    private String sapMaterialCode;

    @Alias("剩余可售量")
    private String remainSellAbleQuantity;

    @Alias("基于上周五原料 毛利 Over C2/C3/PO\n" +
            "（元/吨）")
    private String grossMargin;

    @Alias("保本售价\n" +
            "（元/吨）")
    private String costPrice;

    @Alias("对标市场价格\n" +
            "（元/吨）")
    private String marketPrice;

    @Alias("本周预计价格\n" +
            "（元/吨）")
    private String currentWeekExpectPrice;

    @Alias("申请基准价格\n" +
            "（元/吨）")
    private String standardPrice;

    @Alias("本周可售量\n" +
            "（吨）")
    private String currApplySellableQuantity;

    @Alias("可销保底量\n" +
            "（吨）")
    private String minSellableQuantity;

    @Alias("市场行情及判断")
    private String marketSituation;

    @Alias("竞价策略说明")
    private String description;
}
