package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.cnoocshell.order.api.dto.AccountUserDTO;
import com.cnoocshell.order.api.dto.BaseResultDTO;
import com.cnoocshell.order.api.dto.bidding.group.BiddingBuyerDetailGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("买家竞价记录")
@EqualsAndHashCode(callSuper = true)
public class BiddingBuyerDetailDTO extends BaseResultDTO {
    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("竞价场次编号")
    @NotBlank(message = "竞价场次编号不能为空",groups = {BiddingBuyerDetailGroup.class})
    private String biddingNo;

    @ApiModelProperty("买家编码")
    private String memberCode;

    @ApiModelProperty("买家名称")
    private String memberName;

    @ApiModelProperty("CRM 编码")
    private String crmCode;

    @ApiModelProperty("当前竞价轮次")
    private Integer currentRound;

    @ApiModelProperty("当前价格")
    private BigDecimal currentStandardPrice;

    @ApiModelProperty("报量")
    @NotNull(message = "报量不能为空",groups = {BiddingBuyerDetailGroup.class})
    private BigDecimal wantQuantity;

    @ApiModelProperty("运输方式 1自提，2配送")
    @NotBlank(message = "配送方式不能为空",groups = {BiddingBuyerDetailGroup.class})
    private String deliveryMode;

    @ApiModelProperty("包装方式")
    @NotBlank(message = "包装方式不能为空",groups = {BiddingBuyerDetailGroup.class})
    private String pack;

    @ApiModelProperty("总价")
    private BigDecimal totalAmount;

    @ApiModelProperty("销售单位（元/吨）")
    private String unit;

    @ApiModelProperty("竞价开始时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date biddingStartTime;

    @ApiModelProperty("竞价结束时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date biddingEndTime;

    @ApiModelProperty("SAP合同编号")
    private String sapContractNo;

    @ApiModelProperty("销售人员")
    private AccountUserDTO saleUser;

    @ApiModelProperty("客服")
    private AccountUserDTO customerServiceUser;

    @ApiModelProperty("会员ID")
    private String memberId;

    @ApiModelProperty("成交单号")
    private String dealNo;
    @ApiModelProperty("合同创建状态true:成功")
    private Boolean createContractStatus;
    @ApiModelProperty("创建合同失败原因")
    private String createContractFailReason;
}
