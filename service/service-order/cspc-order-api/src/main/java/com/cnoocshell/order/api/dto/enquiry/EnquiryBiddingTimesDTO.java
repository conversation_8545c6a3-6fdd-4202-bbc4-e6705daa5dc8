package com.cnoocshell.order.api.dto.enquiry;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("查询竞价询价次数")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnquiryBiddingTimesDTO {

    @ApiModelProperty("询价次数")
    private Integer enquiryTimes;

    @ApiModelProperty("竞价次数")
    private Integer biddingTimes;

}
