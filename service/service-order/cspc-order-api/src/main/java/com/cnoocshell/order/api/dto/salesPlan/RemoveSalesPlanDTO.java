package com.cnoocshell.order.api.dto.salesPlan;

import com.cnoocshell.common.dto.OperatorDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RemoveSalesPlanDTO extends OperatorDTO {
    @ApiModelProperty("销售计划编号")
    @NotBlank(message = "销售计划编号不能为空")
    private String salesPlanNo;
}
