package com.cnoocshell.order.api.enums;

/**
 * 参与状态
 */
public enum ParticipationStatusEnum {

	TODO("TODO", "未参与"),
	DONE("DONE", "已参与");

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    ParticipationStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

}
