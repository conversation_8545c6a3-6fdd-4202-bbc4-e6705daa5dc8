package com.cnoocshell.order.api.enums;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ApproveTypeEnum {
    STRATEGY("STRATEGY", "竞价策略审批"),
    RESULT("RESULT", "成交结果审批"),

    // CR 价策略审批通过数据需要撤回和取消进行逆向流程，新增以下类型
    STRATEGY_WITHDRAW("STRATEGY_WITHDRAW","竞价策略撤回审批"),
    BIDDING_CANCEL("BIDDING_CANCEL","竞价场次取消审批");

    private String status;
    private String name;

    public static ApproveTypeEnum getByStatus(String status) {
        return Arrays.stream(values())
                .filter(v-> CharSequenceUtil.equals(status,v.status))
                .findFirst().orElse(null);
    }
}
