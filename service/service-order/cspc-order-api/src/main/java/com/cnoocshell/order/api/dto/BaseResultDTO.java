package com.cnoocshell.order.api.dto;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BaseResultDTO {
    @ApiModelProperty("创建人ID")
    private String createUser;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date createTime;

    @ApiModelProperty("修改人ID")
    private String updateUser;

    @ApiModelProperty("修改人名称")
    private String updateUserName;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date updateTime;

    public void defaultUser(BaseResultDTO user) {
        this.createUser = user.createUser;
        this.createUserName = user.createUserName;
        this.createTime = user.createTime;
        this.updateUser = user.updateUser;
        this.updateUserName = user.updateUserName;
        this.updateTime = user.updateTime;
    }
}
