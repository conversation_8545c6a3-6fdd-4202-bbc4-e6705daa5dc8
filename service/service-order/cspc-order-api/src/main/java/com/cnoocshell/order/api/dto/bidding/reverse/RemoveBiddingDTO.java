package com.cnoocshell.order.api.dto.bidding.reverse;

import com.cnoocshell.common.dto.OperatorDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RemoveBiddingDTO extends OperatorDTO {
    @ApiModelProperty("竞价单号")
    @NotBlank(message = "竞价场次编号不能为空")
    private String biddingNo;
}
