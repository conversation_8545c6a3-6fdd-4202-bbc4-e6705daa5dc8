package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BiddingEmailDTO implements Serializable {

    @ApiModelProperty("竞价no")
    private String biddingNo;

    @ApiModelProperty("竞价名称")
    private String biddingName;

    @ApiModelProperty( "商品编码")
    private String categoryCode;

    @ApiModelProperty( "商品编码")
    private String goodsCode;
}
