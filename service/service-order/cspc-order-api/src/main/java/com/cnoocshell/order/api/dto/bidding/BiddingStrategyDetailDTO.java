package com.cnoocshell.order.api.dto.bidding;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class BiddingStrategyDetailDTO implements Serializable {

    @ApiModelProperty("id")
    private String biddingId;

    @ApiModelProperty("申请基准价格")
    private BigDecimal standardPrice;

    @ApiModelProperty("本周预计价格")
    private String currentWeekExpectPrice;

    @ApiModelProperty("保本售价")
    private BigDecimal costPrice;

    @ApiModelProperty("剩余可售量")
    private BigDecimal remainSellableQuantity;

    @ApiModelProperty("本次申请可售量")
    private BigDecimal currApplySellableQuantity;

    @ApiModelProperty("可销保底量")
    private BigDecimal minSellableQuantity;

    @ApiModelProperty("市场行情及判断")
    private String marketSituation;

    @ApiModelProperty("竞价策略说明")
    private String description;

    @ApiModelProperty("支持材料")
    private String supportDocument;

    @ApiModelProperty("对标市场价格")
    private String marketPrice;

    @ApiModelProperty("基于上周五原料毛利 Over C2/C3/PO")
    private BigDecimal grossMargin;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("修改人")
    private String updateUser;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人")
    private String submitUserName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime submitTime;

    @ApiModelProperty("修改人")
    private String submitUpdateUserName;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime submitUpdateTime;
}
