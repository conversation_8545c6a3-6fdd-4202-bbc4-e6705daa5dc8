package com.cnoocshell.order.api.dto.bidding.export;

import com.cnoocshell.common.excel.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("导出/查询 询价/竞价记录数据")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExportBiddingEnquiryDataDTO {
    @ApiModelProperty("企业名称")
    @Excel(index = 1, title = "企业名称")
    private String memberName;

    @ApiModelProperty(name = "客户编码")
    @Excel(index = 5, title = "客户编码")
    private String crmCode;

    @ApiModelProperty(name = "询价次数")
    @Excel(index = 10, title = "询价次数")
    private Integer enquiryCount;

    @ApiModelProperty(name = "竞价次数")
    @Excel(index = 15, title = "竞价次数")
    private Integer biddingCount;

    @ApiModelProperty(name = "成交次数")
    @Excel(index = 20, title = "成交次数")
    private Integer dealCount;

    private String startDate;

    private String endDate;

    private Integer pageNum;

    private Integer pageSize;

    private String memberCode;

}
