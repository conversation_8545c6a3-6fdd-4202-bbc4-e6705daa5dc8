package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BiddingQueryCheckDataExcelDTO implements Serializable {

    private String excelNo;

    @ApiModelProperty("竞价场次编号")
    private String biddingNo;

    @ApiModelProperty("产品名称")
    private String goodsName;

    @ApiModelProperty("SAP物料编号")
    private String sapMaterialCode;

    @ApiModelProperty("剩余可售量")
    private BigDecimal remainSellableQuantity;
}
