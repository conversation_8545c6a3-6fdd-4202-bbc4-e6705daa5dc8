package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BiddingStrategyDetailExportExcelDTO implements Serializable {

    private String excelNo;

    @ApiModelProperty("竞价场次编号")
    private String biddingNo;

    @ApiModelProperty("产品类别")
    private String categoryName;

    @ApiModelProperty("产品名称")
    private String goodsName;

    @ApiModelProperty("基于上周五原料毛利 Over C2/C3/PO")
    private String grossMargin;

    @ApiModelProperty("保本售价")
    private String costPrice;

    @ApiModelProperty("对标市场价格")
    private String marketPrice;

    @ApiModelProperty("询价最高报价")
    private String maxPrice;

    @ApiModelProperty("询价最低报价")
    private String minPrice;

    @ApiModelProperty("本周预计价格")
    private String currentWeekExpectPrice;

    @ApiModelProperty("申请基准价格")
    private String standardPrice;

    @ApiModelProperty("询价总量")
    private String enquiryTotal;

    @ApiModelProperty("本次申请可售量")
    private String currApplySellableQuantity;

    @ApiModelProperty("可销保底量")
    private String minSellableQuantity;

    @ApiModelProperty("提货有效期开始时间")
    private String deliveryEffectStartDate;

    @ApiModelProperty("提货有效期结束时间")
    private String deliveryEffectEndDate;

    @ApiModelProperty("市场行情及判断")
    private String marketSituation;

    @ApiModelProperty("竞价策略说明")
    private String description;
}
