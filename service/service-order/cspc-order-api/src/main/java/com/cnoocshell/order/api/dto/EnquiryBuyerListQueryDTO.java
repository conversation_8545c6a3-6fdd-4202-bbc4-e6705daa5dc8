package com.cnoocshell.order.api.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 询价类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnquiryBuyerListQueryDTO extends PageDTO{

    private String id;

    private String enquiryNo;

    private String enquiryName;

    private String goodsName;

    @ApiModelProperty("询价状态")
    private String status;

    private List<String> statuslist;

    @ApiModelProperty("参与状态")
    private String participationStatus;

    private String createStartTime;

    private String createEndTime;

    @ApiModelProperty("用户角色")
    private List<String> roleList;

    private String accountId;

    private String memberCode;

    @ApiModelProperty("询价产品code")
    private List<String> goodsCodeList;
}