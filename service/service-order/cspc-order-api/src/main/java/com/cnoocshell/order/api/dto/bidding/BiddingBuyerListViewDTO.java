package com.cnoocshell.order.api.dto.bidding;

import com.cnoocshell.common.constant.DatePatternConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class BiddingBuyerListViewDTO implements Serializable {

    @ApiModelProperty( "id")
    private String id;

    @ApiModelProperty( "竞价场次编号")
    private String biddingNo;

    @ApiModelProperty( "竞价场次名称")
    private String biddingName;

    @ApiModelProperty("竞价场次状态")
    private String status;

    @ApiModelProperty("参与状态")
    private String participationStatus;

    @ApiModelProperty("成交状态")
    private Boolean transactionStatus;

    @ApiModelProperty( "竞价产品")
    private String goodsName;

    @ApiModelProperty( "竞价开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime biddingStartTime;

    @ApiModelProperty( "竞价结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime biddingEndTime;

    @ApiModelProperty( "提货开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliveryEffectStartDate;

    @ApiModelProperty( "提货结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliveryEffectEndDate;

    @ApiModelProperty( "创建人")
    private String createUserName;

    @ApiModelProperty( "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("最新竞价开始时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date lastBiddingStartTime;
    @ApiModelProperty("最新竞价结束时间")
    @JsonFormat(pattern = DatePatternConstant.NORMAL_FORMAT, timezone = DatePatternConstant.TIME_ZONE)
    @DateTimeFormat(pattern = DatePatternConstant.NORMAL_FORMAT)
    private Date lastBiddingEndTime;

    @ApiModelProperty("轮次")
    private Integer currentRound;
    @ApiModelProperty("轮次（中文）")
    private String currentRoundName;
}
