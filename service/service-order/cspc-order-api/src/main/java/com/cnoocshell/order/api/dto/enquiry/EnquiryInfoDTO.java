package com.cnoocshell.order.api.dto.enquiry;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EnquiryInfoDTO implements Serializable {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("询价场次编号")
    private String enquiryNo;

    @ApiModelProperty("询价场次名称")
    private String enquiryName;

    @ApiModelProperty("询价开始时间")
    private String enquiryStartTime;

    @ApiModelProperty("询价结束时间")
    private String enquiryEndTime;

    @ApiModelProperty("状态：DRAFT草稿、TO_START待开始、ENQUIRING询价中、END已结束、CANCELLED已作废")
    private String status;
}
