package com.cnoocshell.order.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import java.util.Date;
import java.util.List;

/**
 * 买家询价
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnquiryBuyerDTO {

    private String id; // 主键ID

    private String enquiryNo; // 询价场次编号

    private String memberCode; // 买家编码

    private String memberName; // 买家名称

    private String goodsCode; // 商品编码

    private String goodsName; // 商品名称

    private String participationStatus; // 参与状态：TODO未参与、DONE已参与

    private boolean delFlg; // 删除标志

    private String createUser; // 创建人

    private Date createTime; // 创建时间

    private String updateUser; // 修改人

    private Date updateTime; // 修改时间

    private String operator;

    //询价详情
    private EnquiryBuyerDetailDTO enquiryBuyerDetailDTO;

    //询价次数
    private Integer enquiryCount;

    private String clientType;

}