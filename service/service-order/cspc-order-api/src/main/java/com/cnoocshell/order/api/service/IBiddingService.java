package com.cnoocshell.order.api.service;

import com.cnoocshell.common.dto.ExportExcelDTO;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.order.api.dto.bidding.*;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.BiddingReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportQueryDTO;
import com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportResultDTO;
import com.cnoocshell.order.api.dto.bidding.reverse.BiddingReverseDTO;
import com.cnoocshell.order.api.dto.bidding.reverse.RemoveBiddingDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "cspc-service-order")
public interface IBiddingService {

    @ApiOperation("批量创建竞价场次")
    @PostMapping(value = "/bidding/batch/creation")
    ItemResult<BiddingCreateReturnDTO> batchCreateBidding(@RequestBody BiddingCreateBiddingDTO biddingEnquiryIdsDTO);

    @ApiOperation("卖家竞价场次列表")
    @PostMapping(value = "/bidding/sales/list")
    ItemResult<PageInfo<BiddingSalesListViewDTO>> salesBiddingList(@RequestBody BiddingSalesListDTO biddingEnquiryIdsDTO);

    @ApiOperation("买家报量")
    @PostMapping("/bidding/joinBiddingByBuyer")
    ItemResult<Boolean> joinBiddingByBuyer(@RequestBody BiddingBuyerDetailDTO param);

    @ApiOperation("销售计划-创建竞价场次")
    @PostMapping("/bidding/single/creation")
    ItemResult<BiddingCreateReturnDTO> singleCreateBidding(@RequestBody BiddingCreateBiddingDTO biddingCreateBiddingDTO);

    @ApiOperation("销售计划-创建竞价场次信息查询")
    @GetMapping("/bidding/query/create/data")
    ItemResult<BiddingQueryCreateDataDTO> queryCreateBiddingData(@RequestParam("saleId") String saleId);

    @ApiOperation("添加客户列表信息查询")
    @PostMapping("/bidding/query/customer/list")
    ItemResult<List<BiddingCustomerListDTO>> queryCustomerInfoList(@RequestBody BiddingCustomerInfoDTO dto);

    @ApiOperation("草稿-编辑竞价场次查询")
    @GetMapping("/bidding/edit/query")
    ItemResult<BiddingEditBiddingDTO> queryEditQueryData(@RequestParam("biddingId") String biddingId);

    @ApiOperation("草稿-提交/保存竞价场次")
    @PostMapping("/bidding/submit/single")
    ItemResult<Boolean> submitSingleData(@RequestBody BiddingCreateBiddingSaveDTO dto);

    @ApiOperation("提交竞价策略")
    @PostMapping("/bidding/submit/strategy")
    ItemResult<Boolean> submitStrategyData(@RequestBody BiddingSubmitStrategySaveDTO dto);

    @ApiOperation("竞价场次审批列表")
    @PostMapping("/bidding/approve/list")
    ItemResult<PageInfo<BiddingSalesListViewDTO>> queryApproveList(@RequestBody BiddingSalesListDTO dto);

    @ApiOperation("竞价场次批量审批通过驳回")
    @PostMapping("/bidding/batch/approval/rejected")
    ItemResult<Boolean> batchApprovalOrRejected(@RequestBody BiddingApprovalDTO dto);

    @ApiOperation("竞价场次撤回")
    @GetMapping("/bidding/single/withdraw")
    ItemResult<Boolean> singleWithdrawData(@RequestParam("biddingId") String biddingId);

    @ApiOperation("竞价策略查看")
    @GetMapping("/bidding/query/strategy")
    ItemResult<BiddingStrategyViewDTO> queryStrategy(@RequestParam("biddingId") String biddingId);

    @ApiOperation("导出调价记录")
    @GetMapping("/bidding/exportAdjustRecord")
    ItemResult<ExportExcelDTO> exportAdjustRecord(@RequestParam("biddingNo") String biddingNo);

    @ApiOperation(value = "导出竞价结果")
    @GetMapping("/bidding/exportBiddingBuyerDetail")
    ItemResult<ExportExcelDTO> exportBiddingBuyerDetail(@RequestParam("biddingNo") String biddingNo);

    @ApiOperation(value = "价格调整")
    @PostMapping("/bidding/biddingAdjust")
    ItemResult<Boolean> biddingAdjust(@RequestBody BiddingAdjustDTO param);

    @ApiOperation(value = "竞价成交结果不成交")
    @PostMapping("/bidding/biddingNotDeal")
    ItemResult<Boolean> biddingNotDeal(@RequestBody BiddingDealConfirmDTO param);

    @ApiOperation(value = "竞价确认成交")
    @PostMapping("/bidding/biddingDeal")
    ItemResult<Boolean> biddingDeal(@RequestBody BiddingDealConfirmDTO param);

    @ApiOperation(value = "竞价成交结果审批撤回")
    @PostMapping("/bidding/withdrawDealResult")
    ItemResult<Boolean> withdrawDealResult(@RequestBody WithdrawDealResultDTO param);

    @ApiOperation(value = "竞价详情")
    @PostMapping("/bidding/biddingDetail")
    ItemResult<BiddingDetailDTO> biddingDetail(@RequestBody QueryBiddingDetailDTO param);

    @ApiOperation(value = "买家竞价场次列表")
    @PostMapping("/bidding/buyer/list")
    ItemResult<PageInfo<BiddingBuyerListViewDTO>> buyerBiddingList(@RequestBody BiddingBuyerQueryDTO biddingBuyerListDTO);

    @ApiOperation(value = "竞价审批列表导出接口")
    @PostMapping("/bidding/export/check/excel")
    List<BiddingStrategyDetailExportExcelDTO> exportExcel(@RequestBody BiddingExportDataDTO dto);

    @ApiOperation(value = "竞价策略导入接口")
    @PostMapping("/bidding/import/excel")
    ItemResult<String> importExcel(@RequestBody BiddingImportExcelInfoDTO dto);

    @ApiOperation("买家竞价场次列表-竞价产品选择")
    @PostMapping(value = "/bidding/buyer/goods/list")
    ItemResult<List<String>> buyerGoodsBiddingList(@RequestBody BiddingBuyerGoodsDTO dto);

    @ApiOperation("竞价场次审批列表数量查询")
    @PostMapping(value = "/bidding/approve/number")
    ItemResult<Integer> queryApproveNumberList(@RequestBody BiddingSalesListDTO dto);

    @ApiOperation("竞价场次已添加买家查询")
    @GetMapping(value = "/bidding/query/customer/info")
    ItemResult<List<BiddingCustomerListDTO>> queryCustomerData(@RequestParam("biddingNo") String biddingNo);

    @ApiOperation("判断客户是否存在竞价场次状态=竞价中且参与状态=已参与的竞价场次")
    @GetMapping(value = "/bidding/judge/bool")
    Boolean judgeBool(@RequestParam("memberCode") String memberCode);

    @ApiOperation("查询竞价信息")
    @GetMapping(value = "/bidding/queryBiddingInfo")
    ItemResult<BiddingInfoDTO> queryBiddingInfo(@RequestParam("id") String id);

    @ApiOperation("卖家竞价场次列表-竞价产品选择")
    @PostMapping(value = "/bidding/seller/goods/list")
    ItemResult<List<String>> sellerGoodsBiddingList(@RequestBody BiddingBuyerGoodsDTO dto);

    @ApiOperation("手动创建SAP合同")
    @GetMapping(value = "/bidding/createSapContractBySeller")
    ItemResult<Boolean> createSapContractBySeller(@RequestParam("dealNo")String dealNo);

    @ApiOperation("竞价列表勾选数据查询接口")
    @PostMapping(value = "/bidding/query/check/data")
    List<BiddingQueryCheckDataExcelDTO> queryBiddingCheckData(BiddingListCheckDataDTO dto);

    @ApiOperation(value = "竞价策略待提交、竞价策略已驳回 撤回草稿",notes = "CR 2025.4.22")
    @GetMapping(value = "/bidding/biddingStrategyWithdrawDraft")
    ItemResult<Boolean> biddingStrategyWithdrawDraft(@RequestParam("biddingNo") String biddingNo,
                                                     @RequestParam(value = "operatorId",required = false)String operatorId);

    @ApiOperation("撤回竞价策略")
    @PostMapping("/bidding/withDrawStrategy")
    ItemResult<Void> withDrawStrategy(@RequestBody BiddingReverseDTO param);

    @ApiOperation("取消竞价场次")
    @PostMapping("/bidding/cancelBidding")
    ItemResult<Void> cancelBidding(@RequestBody BiddingReverseDTO param);

    @ApiOperation("删除草稿")
    @PostMapping("/bidding/removeDraft")
    ItemResult<Void> removeDraft(@RequestBody RemoveBiddingDTO param);

    @ApiOperation("周经营会汇报报表 竞价报表")
    @PostMapping("/bidding/biddingReport")
    PageInfo<BiddingReportResultDTO> biddingReport(@RequestBody BiddingReportQueryDTO param);

    @ApiOperation("活跃度报表")
    @PostMapping("/bidding/memberActivityReport")
    PageInfo<MemberActivityReportResultDTO> memberActivityReport(@RequestBody MemberActivityReportQueryDTO param);
}
