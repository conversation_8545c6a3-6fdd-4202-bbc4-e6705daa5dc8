package com.cnoocshell.order.api.dto.analysis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class BiddingAnalysisSaleChannelDTO {
    @ApiModelProperty(value = "销售渠道",notes = "销售渠道字典 其它：OTHER")
    private String type;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "会员编码")
    private List<String> memberCodes;

    @ApiModelProperty(value = "客户数量")
    private Integer count;

    @ApiModelProperty(value = "销售渠道总报量")
    private BigDecimal totalQuantity = BigDecimal.ZERO;
}
