package com.cnoocshell.order.api.service;

import com.cnoocshell.order.api.dto.salePlanApprove.SalesPlanApprovePageDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanInfoDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanPageRequestDTO;
import com.cnoocshell.order.api.dto.salesPlan.SalesPlanPageResponseDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name ="cspc-service-order")
public interface ISalesPlanApproveService {


    @ApiOperation("分页查询销售计划审批列表")
    @PostMapping("/salesPlanApprove/findSalePlanApproveByCondition")
    PageInfo<SalesPlanPageResponseDTO> findSalePlanApproveByCondition(@RequestBody SalesPlanPageRequestDTO requestDTO);

    @ApiOperation("审批")
    @PostMapping("/salesPlanApprove/approveSalesPlan")
    String approveSalesPlan(@RequestBody SalesPlanInfoDTO requestDTO);

    @ApiOperation("审批数量")
    @PostMapping("/salesPlanApprove/countApprove")
    Long countApprove(@RequestBody SalesPlanPageRequestDTO requestDTO);

    @ApiOperation("审批记录")
    @GetMapping("/salesPlanApprove/approveSalesPlanList")
    List<SalesPlanApprovePageDTO> approveSalesPlanList(@RequestParam("id") String id);
}
