package com.cnoocshell.order.api.enums;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum BiddingStatusEnum {
    DRAFT("DRAFT", "草稿"),
    TO_SUBMIT_STRATEGY("TO_SUBMIT_STRATEGY", "竞价策略待提交"),
    TO_APPROVE_STRATEGY("TO_APPROVE_STRATEGY", "竞价策略待审批"),
    APPROVING_STRATEGY("APPROVING_STRATEGY", "竞价策略审批中"),
    APPROVED_STRATEGY("APPROVED_STRATEGY", "竞价策略已通过"),
    REJECTED_STRATEGY("REJECTED_STRATEGY", "竞价策略已驳回"),
    ADJUSTED_PRICE("ADJUSTED_PRICE", "已调价"),
    BIDDING("BIDDING", "竞价中"),
    TO_CONFIRM_RESULT("TO_CONFIRM_RESULT", "成交结果待确认"),
    TO_APPROVE_RESULT("TO_APPROVE_RESULT", "成交结果待审批"),
    APPROVING_RESULT("APPROVING_RESULT", "成交结果审批中"),
    REJECTED_RESULT("REJECTED_RESULT", "成交结果已驳回"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已作废"),

    //CR 竞价策略审批通过数据需要撤回和取消进行逆向流程，新增以下审批状态
    WITHDRAW_TO_APPROVAL("WITHDRAW_TO_APPROVAL","撤回待审批"),
    WITHDRAWING_APPROVING("WITHDRAWING_APPROVING","撤回审批中"),

    /**
     *竞价策略已撤回 等同于 竞价策略待提交
     */
    WITHDRAWN("WITHDRAWN","竞价策略已撤回"),
    CANCEL_TO_APPROVAL("CANCEL_TO_APPROVAL","取消待审批"),
    CANCEL_APPROVING("CANCEL_APPROVING","取消审批中"),
    BIDDING_CANCELLED("BIDDING_CANCELLED","已取消")
    ;

    private String status;

    private String name;

    public static BiddingStatusEnum getByStatus(String status) {
        return Arrays.stream(values())
                .filter(v -> StringUtils.equals(v.getStatus(), status))
                .findFirst()
                .orElse(null);
    }

    public static Boolean hasStatus(String status) {
        for (BiddingStatusEnum value : BiddingStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return true;
            }
        }
        return false;
    }

    public static final String getNameByStatus(String status) {
        BiddingStatusEnum statusEnum = Arrays.stream(values())
                .filter(v-> CharSequenceUtil.equals(v.getStatus(),status))
                .findFirst().orElse(null);
        return Objects.nonNull(statusEnum) ? statusEnum.getName() : null;
    }
}
