package com.cnoocshell.order.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 买家询价详情
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnquiryBuyerDetailDTO {

    private String id; // 主键ID

    private String enquiryNo; // 询价场次编号

    private String memberCode; // 买家编码

    private String memberName; // 买家名称

    private String goodsCode; // 商品编码

    private String goodsName; // 商品名称

    private BigDecimal price; // 报价

    private BigDecimal quantity; // 报量

    private String deliveryMode; // 运输方式 1自提，2配送

    private String pack; // 包装方式

    private Boolean delFlg; // 删除标志

    private String createUser; // 创建人ID

    private String createUserName; // 创建人名称

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime; // 创建时间

    private String updateUser; // 修改人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime; // 修改时间

    private BigDecimal maxPrice;//最高报价

    private BigDecimal minPrice;//最低报价

    private BigDecimal totalQuantity;//报价总量

    //销售渠道
    private String saleChannel;

    @ApiModelProperty("客户编码 CRM code")
    private String crmCode;

}