package com.cnoocshell.order.api.dto.salesPlan;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesPlanInfoDTO implements Serializable {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("id")
    private List<String> ids;

    @ApiModelProperty("工厂编码")
    private String factoryCode;

    @ApiModelProperty("产品")
    private String categoryCode;

    @ApiModelProperty("产品")
    private String goodsCode;

    @ApiModelProperty("产品名称")
    private String goodsName;

    @ApiModelProperty("提货开始日期")
    private Date deliveryEffectStartDate;

    @ApiModelProperty("提货结束日期")
    private Date deliveryEffectEndDate;

    @ApiModelProperty( "是否需要询价")
    private Integer isNeedInquiry;

    @ApiModelProperty("是否创建询价")
    private Integer isCreatedInquiry;

    @ApiModelProperty("支持运输方式")
    private String deliveryMode;

    @ApiModelProperty("期初库存")
    private String storageQuantity;

    @ApiModelProperty("发货期产量")
    private String deliveryProductionQuantity;

    @ApiModelProperty("LTC")
    private String ltcQuantity;

    @ApiModelProperty("Spot Carryover量")
    private String spotCarryoverQuantity;

    @ApiModelProperty("系统计算可售量")
    private String systemSellableQuantity;

    @ApiModelProperty("预计竞价场次次数")
    private String expectBiddingCount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("目标库存量")
    private String targetStorageQuantity;

    @ApiModelProperty("others量")
    private String othersQuantity;

    @ApiModelProperty("申请预计可售量")
    private String applySellableQuantity;

    @ApiModelProperty("配送收费标准（附件）")
    private String deliveryCostStandard;

    @ApiModelProperty("自提指南（附件）")
    private String selfPickupGuide;

    @ApiModelProperty("自提承运商（附件））")
    private String selfPickupCarrier;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("创建人id")
    private String createUser;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("更新人名称")
    private String updateUserName;

    @ApiModelProperty("更新人id")
    private String updateUser;

    @ApiModelProperty("拒绝原因")
    @Size(max = 500,message = "原因/备注限制输入500字符")
    private String rejectedReason;

    @ApiModelProperty("剩余可售量")
    private String remainSellAbleQuantity;

}
