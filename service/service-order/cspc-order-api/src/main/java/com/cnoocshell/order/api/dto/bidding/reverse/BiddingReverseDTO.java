package com.cnoocshell.order.api.dto.bidding.reverse;

import com.cnoocshell.common.dto.OperatorDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 竞价逆向操作
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BiddingReverseDTO extends OperatorDTO {
    @ApiModelProperty("竞价场次编号")
    @NotBlank(message = "竞价场次编号不能为空")
    private String biddingNo;

    @ApiModelProperty("原因")
    @NotBlank(message = "原因不能为空")
    @Size(max = 500, message = "原因限制输入500字符")
    private String reason;
}
