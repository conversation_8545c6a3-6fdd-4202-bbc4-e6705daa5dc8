package com.cnoocshell.order.api.dto.salePlanApprove;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class SalesPlanApprovePageDTO implements Serializable {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("销售计划编号")
    private String planNo;

    @ApiModelProperty("审批人")
    private String approveUser;

    @ApiModelProperty("审批结果")
    private String approveResult;

    @ApiModelProperty("审批时间")
    private LocalDateTime approveDate;

    @ApiModelProperty("驳回原因")
    private String rejectedReason;
}
