package com.cnoocshell.order.api.dto.bidding.report;

import com.cnoocshell.common.excel.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * NEW 客户活跃度报表出参
 */
@Data
public class MemberActivityReportResultDTO {
    @ApiModelProperty(value = "产品一级分类", notes = "编码")
    private String categoryCodeLevelOne;
    @ApiModelProperty(value = "产品一级分类", notes = "名称")
    @Excel(index = 1, title = "产品一级分类")
    private String categoryNameLevelOne;

    @ApiModelProperty(value = "产品二级分类", notes = "编码")
    private String categoryCodeLevelTwo;
    @ApiModelProperty(value = "产品二级分类", notes = "名称")
    @Excel(index = 5, title = "产品二级分类")
    private String categoryNameLevelTwo;

    @ApiModelProperty(value = "产品编码")
    private String goodsCode;
    @ApiModelProperty(value = "产品名称")
    @Excel(index = 10, title = "产品名称")
    private String goodsName;

    @ApiModelProperty(value = "SAP物料编码")
    @Excel(index = 15, title = "SAP物料编号")
    private String sapMaterialCode;

    @ApiModelProperty(value = "客户编码")
    @Excel(index = 20, title = "客户编码")
    private String crmCode;

    @ApiModelProperty(value = "会员编码")
    private String memberCode;

    @ApiModelProperty(value = "客户名称")
    @Excel(index = 25, title = "客户名称")
    private String memberName;

    @ApiModelProperty(value = "销售渠道", notes = "销售渠道编码")
    private String saleChannel;
    @ApiModelProperty(value = "销售渠道", notes = "销售渠道名称")
    @Excel(index = 30, title = "销售渠道")
    private String saleChannelName;

    @ApiModelProperty(value = "开设询价场次次数")
    @Excel(index = 35, title = "开设询价场次次数")
    private Integer enquiryCount;
    @ApiModelProperty(value = "参与询价次数")
    @Excel(index = 40, title = "参与询价次数")
    private Integer joinEnquiryCount;

    @ApiModelProperty(value = "开设竞价场次次数")
    @Excel(index = 45, title = "开设竞价场次次数")
    private Integer biddingCount;
    @ApiModelProperty(value = "参与竞价次数")
    @Excel(index = 50, title = "参与竞价次数")
    private Integer joinBiddingCount;

    @ApiModelProperty(value = "成交次数")
    @Excel(index = 55, title = "成交次数")
    private Integer biddingDealCount;
    @ApiModelProperty(value = "成交量")
    @Excel(index = 60, title = "成交量")
    private BigDecimal biddingDealTotalQuantity;
}
