package com.cnoocshell.order.api.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BiddingDealQuantityDTO {
    @ApiModelProperty("竞价编号")
    private String biddingNo;

    @ApiModelProperty("成交总量")
    private BigDecimal totalQuantity;
}
