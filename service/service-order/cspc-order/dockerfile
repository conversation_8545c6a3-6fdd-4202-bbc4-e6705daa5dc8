# 基础镜像
FROM  openjdk:11-jre-slim

# 修复缺失的shell环境
RUN apt-get update && apt-get install -y --no-install-recommends \
    bash \
    && rm -rf /var/lib/apt/lists/*

# 挂载目录
VOLUME /home/<USER>
# 创建目录
RUN mkdir -p /home/<USER>/cspc-order

# 指定路径
WORKDIR /home/<USER>/cspc-order

# 复制jar文件到路径
COPY target/cspc-order.jar /home/<USER>/cspc-order/cspc-order.jar

ENV TZ=Asia/Shanghai

EXPOSE 8084
# 启动
# CMD java -jar cspc-order.jar
CMD ["java", "-jar", "cspc-order.jar"]