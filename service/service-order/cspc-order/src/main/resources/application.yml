server:
  port: 8090
  session:
    cookie:
      http-only: true
      secure: true
logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    com.cnoocshell: INFO
    com.netflix.discovery.DiscoveryClient: WARN
spring:
  application:
    name: cspc-service-order
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  profiles:
    active: ${ENV:dev}

order:
  taketime:
    mode: 0
  exception:
    retryCount: 3

mybatis:
  typeAliasesPackage: com.cnoocshell.order.dao
  mapperScanPackage: com.cnoocshell.order.dao
  mapperLocations: "classpath:/mapper/*.xml"
  configLocation: "classpath:/mybatis-config.xml"
xxl:
  job:
    accessToken: default_token
    admin:
      addresses: http://localhost:8090/xxl-job-admin
    executor:
      appname: ${spring.application.name}-job
      ip: 127.0.0.1
      port: 9999
      logpath: C:/Users/<USER>/DeloitteSpace/IdeaSpace/ec-asset/xxlJob/log
      logretentiondays: 7
