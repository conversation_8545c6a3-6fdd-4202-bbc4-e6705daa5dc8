<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.order.dao.mapper.EnquiryBuyerDetailMapper">
    <resultMap id="BaseResultMap" type="com.cnoocshell.order.dao.vo.EnquiryBuyerDetail">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="enquiry_no" property="enquiryNo" jdbcType="VARCHAR"/>
        <result column="member_code" property="memberCode" jdbcType="VARCHAR"/>
        <result column="member_name" property="memberName" jdbcType="VARCHAR"/>
        <result column="goods_code" property="goodsCode" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DECIMAL"/>
        <result column="quantity" property="quantity" jdbcType="DECIMAL"/>
        <result column="delivery_mode" property="deliveryMode" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="del_flg" property="delFlg" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="selectMaxAndLowPrice" resultType="com.cnoocshell.order.api.dto.EnquiryBuyerDetailDTO">
        SELECT max(price) as maxPrice,min(price) as minPrice, sum(quantity) as totalQuantity
        FROM enquiry_buyer_detail t1
                 JOIN (
            SELECT enquiry_no, MAX(create_time) AS latest_created_at, member_code
            FROM enquiry_buyer_detail
            where enquiry_no =  #{enquiryNo}
            GROUP BY member_code,enquiry_no
        ) t2
        ON t1.enquiry_no = t2.enquiry_no AND t1.create_time = t2.latest_created_at and t1.member_code=t2.member_code
    </select>

    <select id="selectByEnquiryAndMemberIds" resultType="com.cnoocshell.order.dao.vo.EnquiryBuyerDetail">
        SELECT t1.*
        FROM enquiry_buyer_detail t1
        JOIN (
            SELECT enquiry_no, member_code,MAX(create_time) AS latest_created_at
            FROM enquiry_buyer_detail
            where 1=1
            <if test="enquiryNo != null and enquiryNo !=''">
                and enquiry_no = #{enquiryNo}
            </if>
            <if test="memberCodes != null and memberCodes.size() > 0">
                and  member_code  in
                <foreach item="memberCode" index="index" collection="memberCodes" open="(" separator="," close=" )">
                    #{memberCode}
                </foreach>
            </if>
            GROUP BY member_code,enquiry_no
        ) t2
        ON t1.enquiry_no = t2.enquiry_no AND t1.create_time = t2.latest_created_at and t1.member_code = t2.member_code
        order by t1.create_time desc
    </select>


</mapper>
