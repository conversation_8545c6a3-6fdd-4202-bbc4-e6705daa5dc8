<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.order.dao.mapper.SalesPlanMapper">

    <select id="selectCreateBidding" resultType="com.cnoocshell.order.api.dto.bidding.BiddingCreateDataDTO">
        SELECT
        sp.id,
        sp.plan_no,
        sp.plan_name,
        sp.goods_code,
        sp.goods_name,
        sp.category_code,
        sp.delivery_effect_start_date ,
        sp.delivery_effect_end_date,
        sp.remain_sellable_quantity,
        bd.status currentStatus
        FROM
        sales_plan sp
        left join bidding bd on bd.sales_plan_no = sp.plan_no
        where
        sp.id = #{salesID}
    </select>

    <update id="updateRemainSellAbleQuantity">
        UPDATE sales_plan
        SET remain_sellable_quantity = (remain_sellable_quantity - #{dealTotalQuantity})
        WHERE plan_no = #{salePlanNo}
          AND del_flg = 0
    </update>

    <update id="resetCreateEnquiryFlag">
        update sales_plan tb1
        set tb1.is_created_inquiry =0
        where tb1.del_flg = 0
          AND tb1.is_created_inquiry = 1
          AND tb1.plan_no = #{salePlanNo}
          AND NOT EXISTS (select 1
                          from enquiry tb2
                          where tb1.plan_no = tb2.sales_plan_no
                            and tb2.del_flg = 0
                            and tb2.status NOT IN ('CANCELLED', 'ENQUIRY_CANCEL'))
    </update>
</mapper>