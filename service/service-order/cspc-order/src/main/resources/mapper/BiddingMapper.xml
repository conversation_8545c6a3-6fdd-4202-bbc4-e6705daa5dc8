<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.order.dao.mapper.BiddingMapper">

    <update id="updateStatus">
        UPDATE
        bidding
        SET
        status = #{biddingApproveStatus},
        update_user = #{realName},
        update_time = #{date},
        WHERE
        <if test="biddingIdList != null ">
            id in
            <foreach item="item" index="index" collection="biddingIdList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="withdrawData">
        UPDATE
        bidding
        SET
        status = #{status}
        WHERE
        id =#{bidding}
    </update>

    <select id="salesBiddingList" resultType="com.cnoocshell.order.api.dto.bidding.BiddingSalesListViewDTO">
        SELECT
        id,
        bidding_no,
        bidding_name,
        enquiry_no,
        enquiry_name,
        sales_plan_no,
        sales_plan_name,
        goods_code,
        goods_name,
        category_code,
        bidding_start_time,
        bidding_end_time,
        delivery_effect_start_date,
        delivery_effect_end_date,
        status,
        standard_price,
        current_week_expect_price,
        cost_price,
        remain_sellable_quantity,
        curr_apply_sellable_quantity,
        min_sellable_quantity,
        market_situation,
        description,
        support_document,
        current_round,
        last_standard_price,
        last_bidding_start_time,
        last_bidding_end_time,
        deal_time,
        del_flg,
        create_user,
        create_user_name,
        create_time,
        update_user,
        update_time
        FROM
        bidding
        where
        del_flg=0
        <if test="dto.biddingNo != null  and dto.biddingNo !=''">
            and bidding_no LIKE CONCAT('%', #{dto.biddingNo}, '%')
        </if>
        <if test="dto.detailBiddingNo != null  and dto.detailBiddingNo !=''">
            and bidding_no = #{dto.detailBiddingNo}
        </if>
        <if test="dto.biddingName != null  and dto.biddingName !=''">
            and bidding_name LIKE CONCAT('%', #{dto.biddingName}, '%')
        </if>
        <if test="dto.createUserName != null  and dto.createUserName !=''">
            and create_user_name LIKE CONCAT('%', #{dto.createUserName}, '%')
        </if>
        <if test="dto.statusParams != null ">
            and status in
            <foreach item="item" index="index" collection="dto.statusParams" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.goodsNameList != null ">
            and goods_name in
            <foreach item="item" index="index" collection="dto.goodsNameList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.statusList != null ">
            and status in
            <foreach item="item" index="index" collection="dto.statusList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.goodsCodeList != null ">
            and goods_code in
            <foreach item="item" index="index" collection="dto.goodsCodeList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.biddingNoList != null and dto.biddingNoList.size > 0">
            and bidding_no in
            <foreach item="item" index="index" collection="dto.biddingNoList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.createStartTime != null  and dto.createEndTime !=null and dto.createStartTime !=  dto.createEndTime" >
            and create_time between #{dto.createStartTime} and #{dto.createEndTime}
        </if>
        <if test="dto.createStartTime != null  and dto.createEndTime !=null and dto.createStartTime ==  dto.createEndTime" >
            and DATE(create_time) = #{dto.createStartTime}
        </if>
        <if test="dto.createStartTime != null  and dto.createEndTime ==null">
            and create_time >= #{dto.createStartTime}
        </if>
        <if test="dto.createStartTime == null  and dto.createEndTime !=null">
            and #{createEndTime} >= create_time
        </if>
        ORDER BY create_time desc
    </select>

    <select id="queryEditQueryData" resultType="com.cnoocshell.order.api.dto.bidding.BiddingEditBiddingDTO">
        SELECT
        bd.*,
        sp.id salesId,
        ey.id enquiryId
        FROM
        bidding bd
        left join sales_plan sp on bd.sales_plan_no = sp.plan_no
        left join enquiry ey on ey.enquiry_no = bd.enquiry_no
        where bd.id = #{biddingId}
    </select>

    <select id="getSimpleBidding" resultType="com.cnoocshell.order.api.dto.bidding.SimpleBiddingDTO">
        select *
        from bidding
        where del_flg = 0
        and status = #{status}
        and last_bidding_start_time &gt;= #{startDateTime}
        and last_bidding_end_time &lt;= #{endDateTime}
    </select>


    <select id="queryCheckCustomerInfo" resultType="com.cnoocshell.order.api.dto.bidding.BiddingCustomerListDTO">
        SELECT
        bb.member_code ,
        bb.member_name,
        bb.bidding_no ,
        eb.participation_status,
        eb.id enquiryId
        FROM
        bidding b
        inner join bidding_buyer bb on  b.bidding_no = bb.bidding_no
        left join enquiry_buyer eb on eb.enquiry_no = b.enquiry_no
        where
        b.id = #{biddingId}
    </select>

    <select id="queryStrategyDetail" resultType="com.cnoocshell.order.api.dto.bidding.BiddingStrategyDetailDTO">
        SELECT
        *
        FROM
        bidding where id = #{biddingId}
    </select>

    <select id="queryCheckCustomerForCreate" resultType="com.cnoocshell.order.api.dto.bidding.BiddingCustomerListDTO">
        SELECT
        mm.member_code,
        mm.member_name,
        eb.participation_status,
        MAX(CASE WHEN mmpgi.intention_type = 1 THEN gg.goods_name END) AS mainGoods,
        MAX(CASE WHEN mmpgi.intention_type = 2 THEN gg.goods_name END) AS minorGoods
        FROM
        qa_cnoocshell_order.enquiry_buyer eb
        inner join qa_cnoocshell_member.mb_member mm on
        mm.member_code = eb.member_code
        inner join qa_cnoocshell_member.mb_member_purchase_goods_intention mmpgi on
        mmpgi.member_id = mm.member_id
        inner join qa_cnoocshell_goods.go_goods gg on gg.goods_id = mmpgi.goods_id
        where
        eb.id = #{id}
        GROUP BY
        mm.member_name,
        mm.member_code,
        eb.participation_status
    </select>

    <select id="querySendEmailUserInfo" resultType="com.cnoocshell.order.api.dto.bidding.BiddingEmailDTO">
        SELECT
        DISTINCT
        bd.bidding_no,
        bd.bidding_name,
        category_code,
        goods_code
        FROM
        bidding bd
        where bd.id in
        <foreach item="item" index="index" collection="idList" open="(" separator="," close=" )">
            #{item}
        </foreach>
    </select>

    <select id="queryCategoryCodeList" resultType="java.lang.String">
        SELECT
        goods_code
        FROM
        bidding
        where id in
        <foreach item="item" index="index" collection="biddingIdList" open="(" separator="," close=" )">
            #{item}
        </foreach>
    </select>

    <select id="selectCreateBiddingList" resultType="com.cnoocshell.order.api.dto.bidding.BiddingCreateDataDTO">
        SELECT
        ey.id,
        ey.enquiry_no,
        ey.enquiry_name,
        sp.plan_no salesPlanNo,
        sp.plan_name salesPlanName,
        sp.goods_code,
        sp.goods_name,
        sp.category_code,
        sp.delivery_effect_start_date ,
        sp.delivery_effect_end_date,
        sp.remain_sellable_quantity,
        GROUP_CONCAT(bd.status) AS currentStatus,
        ey.pay_condition ,
        ey.price_trade_term ,
        ey.trade_currency
        FROM
        sales_plan sp
        left join enquiry ey on ey.sales_plan_no = sp.plan_no and ey.del_flg=0
        left join bidding bd on bd.sales_plan_no = sp.plan_no and bd.del_flg=0
        where
        ey.id in
        <foreach item="item" index="index" collection="enquiryIdList" open="(" separator="," close=" )">
            #{item}
        </foreach>
        or
        sp.id in
        <foreach item="item" index="index" collection="enquiryIdList" open="(" separator="," close=" )">
            #{item}
        </foreach>
        group by
        ey.id,
        ey.enquiry_no,
        ey.enquiry_name,
        sp.plan_no ,
        sp.plan_name ,
        sp.goods_code,
        sp.goods_name,
        sp.category_code,
        sp.delivery_effect_start_date ,
        sp.delivery_effect_end_date,
        sp.remain_sellable_quantity,
        ey.pay_condition ,
        ey.price_trade_term ,
        ey.trade_currency
    </select>

    <select id="queryEnquiryIdBySalesId" resultType="java.lang.String">
        SELECT
        eq.id
        FROM
        enquiry eq
        inner join sales_plan sp on
        sp.plan_no = eq.sales_plan_no
        where
        sp.id = #{id}
    </select>

    <select id="queryDownloadDataList" resultType="com.cnoocshell.order.api.dto.bidding.BiddingStrategyDetailExportExcelDTO">
        select
        b.bidding_no biddingNo,
        b.category_code categoryName,
        b.goods_name goodsName,
        b.gross_margin grossMargin,
        b.cost_price costPrice,
        b.market_price marketPrice,
        e.max_price maxPrice,
        e.min_price minPrice,
        b.current_week_expect_price currentWeekExpectPrice,
        b.standard_price standardPrice,
        e.total_quantity enquiryTotal,
        b.curr_apply_sellable_quantity currApplySellableQuantity,
        b.min_sellable_quantity minSellableQuantity,
        b.delivery_effect_start_date deliveryEffectStartDate,
        b.delivery_effect_end_date deliveryEffectEndDate,
        b.market_situation marketSituation,
        b.description description
        from
        bidding b
        left join enquiry e on
        e.enquiry_no = b.enquiry_no
        where
        1=1
        <if test="dto.biddingNo != null  and dto.biddingNo !=''">
            and b.bidding_no LIKE CONCAT('%', #{dto.biddingNo}, '%')
        </if>
        <if test="dto.biddingName != null  and dto.biddingName !=''">
            and b.bidding_name LIKE CONCAT('%', #{dto.biddingName}, '%')
        </if>
        <if test="dto.createUserName != null  and dto.createUserName !=''">
            and b.create_user_name LIKE CONCAT('%', #{dto.createUserName}, '%')
        </if>
        <if test="dto.statusParams != null ">
            and b.status in
            <foreach item="item" index="index" collection="dto.statusParams" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.goodsNameList != null ">
            and b.goods_name in
            <foreach item="item" index="index" collection="dto.goodsNameList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.statusList != null ">
            and b.status in
            <foreach item="item" index="index" collection="dto.statusList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.goodsCodeList != null ">
            and b.goods_code in
            <foreach item="item" index="index" collection="dto.goodsCodeList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.createStartTime != null  and dto.createEndTime !=null and dto.createStartTime !=  dto.createEndTime" >
            and b.create_time between #{dto.createStartTime} and #{dto.createEndTime}
        </if>
        <if test="dto.createStartTime != null  and dto.createEndTime !=null and dto.createStartTime ==  dto.createEndTime" >
            and DATE(b.create_time) = #{dto.createStartTime}
        </if>
        <if test="dto.createStartTime != null  and dto.createEndTime ==null">
            and b.create_time >= #{dto.createStartTime}
        </if>
        <if test="dto.createStartTime == null  and dto.createEndTime !=null">
            and #{createEndTime} >= b.create_time
        </if>
        ORDER BY b.create_time desc
    </select>

    <select id="queryBiddingInfoByExcelBiddingNo" resultType="com.cnoocshell.order.dao.vo.Bidding">
        SELECT
        bidding_no ,
        id,
        status,
        goods_code
        FROM
        bidding
        where
        del_flg = 0
        and bidding_no  in
        <foreach item="item" index="index" collection="biddingNoList" open="(" separator="," close=" )">
            #{item}
        </foreach>
    </select>

    <update id="updateBiddingQuantity">
        UPDATE bidding
        SET total_deal_quantity=#{totalDealQuantity},
            end_remain_sellable_quantity =(curr_apply_sellable_quantity - #{totalDealQuantity})
        WHERE del_flg = 0
          AND bidding_no = #{biddingNo}
    </update>

    <update id="updateImportData">
        UPDATE bidding
        SET
        gross_margin = #{item.grossMargin},
        cost_price = #{item.costPrice},
        market_price = #{item.marketPrice},
        current_week_expect_price = #{item.currentWeekExpectPrice},
        standard_price = #{item.standardPrice},
        curr_apply_sellable_quantity = #{item.currApplySellableQuantity},
        min_sellable_quantity = #{item.minSellableQuantity},
        market_situation = #{item.marketSituation},
        description = #{item.description},
        update_user = #{item.updateUser},
        update_time = #{item.updateTime},
        submit_user = #{item.submitUser},
        submit_time = #{item.submitTime},
        submit_user_name = #{item.submitUserName},
        last_standard_price = #{item.lastStandardPrice},
        status = #{item.status}
        WHERE bidding_no = #{item.biddingNo}
    </update>

    <select id="selectBiddingExportData" resultType="com.cnoocshell.order.api.dto.bidding.export.ExportSellNumDataDTO">
        select t1.bidding_no,t1.bidding_name,t1.goods_name, t1.curr_apply_sellable_quantity as applySellableQuantity,
               t1.total_deal_quantity as nowSellableQuantity,t1.end_remain_sellable_quantity as surplusSellableQuantity,
               t2.apply_sellable_quantity as expectedSellableQuantity, t2.system_sellable_quantity as systemComputingSellableQuantity
        from bidding t1
                 left join sales_plan t2 on t1.sales_plan_no = t2.plan_no
        where t1.del_flg = 0 and t1.status = 'COMPLETED'
        <if test="biddingNo != null and biddingNo !=''">
            and t1.bidding_no LIKE CONCAT('%', #{biddingNo}, '%')
        </if>
        <if test="biddingName != null and biddingName !=''">
            and t1.bidding_name LIKE CONCAT('%', #{biddingName}, '%')
        </if>
        <if test="startDate!=null and startDate!='' ">
            and t1.create_time &gt;= DATE_FORMAT(str_to_date(#{startDate},'%Y-%m-%d'),'%Y-%m-%d 00:00:00')
        </if>
        <if test="endDate!=null and endDate!='' ">
            and t1.create_time &lt;= DATE_FORMAT(str_to_date(#{endDate},'%Y-%m-%d'),'%Y-%m-%d 23:59:59')
        </if>
        order by t1.create_time desc
    </select>

    <select id="selectBiddingTimesByMemberCode"  resultType="java.lang.Integer">
        select count(bidding_no) from (
                                          select t1.bidding_no as bidding_no from bidding t1
                                                                                      left join bidding_buyer t2 on t1.bidding_no  = t2.bidding_no
                                          where t1.del_flg  = 0 and t1.status  = 'BIDDING'  and t2.member_code= #{memberCode}
                                            <if test="goodsCodes !=null and goodsCodes.size>0">
                                                and t1.goods_code in
                                                <foreach collection="goodsCodes" item="goodsCode" open="(" separator="," close=")">
                                                    #{goodsCode}
                                                </foreach>
                                            </if>
                                          group by t1.bidding_no
                                      ) as t2
    </select>

    <select id="judgeBool" resultType="java.lang.Boolean">
        SELECT
        CASE
        WHEN EXISTS (
        SELECT
        1
        FROM
        bidding_buyer bb
        INNER JOIN bidding b ON
        b.bidding_no = bb.bidding_no
        WHERE
        bb.member_code = #{memberCode}
        AND b.status in
        <foreach collection="status" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND bb.participation_status = #{participationStatus}
        ) THEN TRUE
        ELSE FALSE
        END AS result;
    </select>

    <select id="queryBiddingInfo" resultType="com.cnoocshell.order.api.dto.bidding.BiddingInfoDTO">
        SELECT
        b.id,
        b.bidding_no
        FROM
        bidding b
        left join sales_plan sp on   sp.plan_no = b.sales_plan_no and sp.del_flg = 0
        left join enquiry e on  e.enquiry_no = b.enquiry_no and e.del_flg = 0
        where
        (sp.id = #{id} or e.id = #{id})
        and b.del_flg = 0
        order by
        b.create_time desc
        limit 1
    </select>

    <update id="updateStatusByIds">
        UPDATE bidding SET status=#{status} where del_flg = 0 and
        id IN
        <foreach collection="biddingIds" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
    </update>

    <select id="biddingReport" resultType="com.cnoocshell.order.api.dto.bidding.report.BiddingReportResultDTO">
        select
        bidding_report.*
        from
        (
        select
        tb2.category_code AS 'categoryCodeLevelTwo',
        tb2.goods_code,
        tb2.goods_name ,
        tb2.sales_plan_no ,
        tb9.apply_sellable_quantity ,
        tb2.delivery_effect_start_date,
        tb2.delivery_effect_end_date ,
        tb2.last_bidding_start_time,
        tb2.last_bidding_end_time ,
        tb2.last_bidding_start_time 'biddingStartDate',
        tb2.bidding_no,
        tb2.curr_apply_sellable_quantity,
        CASE
        WHEN tb1.current_round is null THEN tb2.current_round
        ELSE tb1.current_round
        END AS 'round',
        CASE
        WHEN tb3.old_standard_price is null
        THEN tb2.last_standard_price
        ELSE tb3.old_standard_price
        END AS 'standardPrice',
        tb0.member_code ,
        tb0.member_name ,
        CASE
        WHEN tb1.want_quantity is null THEN '/'
        ELSE tb1.want_quantity
        END AS 'want_quantity',
        tb11.deal_no ,
        CASE
        WHEN tb2.status = 'COMPLETED'
        AND tb11.deal_no is not null THEN 'DEAL'
        ELSE CASE
        WHEN tb2.status = 'CANCELLED'
        AND tb2.current_round = tb1.current_round
        and tb1.want_quantity is not null THEN 'NOT_DEAL'
        ELSE '/'
        END
        END AS 'dealStatus',
        tb2.status,
        tb1.create_time
        from
        bidding_buyer tb0
        left join (
        select
        tb19.*
        from
        bidding_buyer_detail tb19
        left join (
        select
        tb20.bidding_no ,
        tb20.current_round ,
        tb20.member_code ,
        MAX(create_time) 'max_create_time'
        from
        bidding_buyer_detail tb20
        <!-- 优化查询 过滤符合数据权限的数据-->
        <if test="param.dataPermissionGoodsCodes != null and param.dataPermissionGoodsCodes.size > 0">
        where exists(select 1 from bidding tb201 where
            tb201.del_flg =0
            and tb201.bidding_no=tb20.bidding_no
            and tb201.goods_code in
            <foreach collection="param.dataPermissionGoodsCodes" item="item10" open="(" separator="," close=")">
                #{item10}
            </foreach>
            )
        </if>
        GROUP by
        tb20.bidding_no,
        tb20.current_round ,
        tb20.member_code
        ) tb21 on
        tb19.bidding_no = tb21.bidding_no
        and tb19.current_round = tb21.current_round
        and tb19.member_code = tb21.member_code
        and tb19.create_time = tb21.max_create_time
        where
        tb21.max_create_time is not null) tb1 on
        tb0.bidding_no = tb1.bidding_no
        and tb0.member_code = tb1.member_code
        left join bidding tb2 on
        tb0.bidding_no = tb2.bidding_no
        left join bidding_adjust_record tb3
        on
        tb1.bidding_no = tb3.bidding_no
        and tb1.current_round = tb3.old_current_round
        left join sales_plan tb9 on
        tb2.sales_plan_no = tb9.plan_no
        left join bidding_buyer_deal tb11 on
        tb1.id = tb11.bidding_buyer_detail_id
        and tb11.del_flg = 0
        where
        tb0.del_flg = 0
        <!-- 默认限制查询条件 -->
        and tb2.status in ('COMPLETED','CANCELLED')
        and tb2.last_bidding_start_time &gt;= DATE_SUB(NOW(),INTERVAL 1 YEAR)
        and tb2.last_bidding_start_time &lt;= NOW()
        and (
        <!-- 存在报量的 -->
        EXISTS (
        select
        1
        from
        bidding_buyer_detail tb12
        where
        tb12.bidding_no = tb2.bidding_no
        and tb12.member_code = tb0.member_code)
        OR
        <!-- 一次都没报量的 -->
        EXISTS (
        select
        tb12.*
        from
        bidding_buyer tb12
        left join bidding tb13 on
        tb12.bidding_no = tb13.bidding_no
        where
        tb12.bidding_no = tb2.bidding_no
        and tb13.bidding_no = tb2.bidding_no
        and tb12.member_code = tb0.member_code
        <!-- and tb13.status = 'CANCELLED' -->
        AND NOT EXISTS (
        select
        1
        from
        bidding_buyer_detail tb14
        where
        tb14.bidding_no = tb13.bidding_no
        and tb12.member_code = tb14.member_code
        )))

        <!-- 列表查询条件以及数据权限限制 在子查询中处理 -->
        <if test="param.salesPlanNo != null and param.salesPlanNo != ''">
            AND tb2.sales_plan_no like CONCAT('%',#{param.salesPlanNo},'%')
        </if>
        <if test="param.categoryCodes != null and param.categoryCodes.size > 0">
            AND tb2.category_code in
            <foreach collection="param.categoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.goodsName != null and param.goodsName != ''">
            AND tb2.goods_name like CONCAT('%',#{param.goodsName},'%')
        </if>
        <if test="param.memberName != null and param.memberName != ''">
            AND tb0.member_name like CONCAT('%',#{param.memberName},'%')
        </if>
        <if test="param.biddingNo != null and param.biddingNo != ''">
            AND tb2.bidding_no like CONCAT('%',#{param.biddingNo},'%')
        </if>
        <if test="param.startLastBiddingLastStartTime != null">
            AND tb2.last_bidding_start_time &gt;= #{param.startLastBiddingLastStartTime}
        </if>
        <if test="param.endLastBiddingLastStartTime != null">
            AND tb2.last_bidding_start_time &lt;= #{param.endLastBiddingLastStartTime}
        </if>
        <if test="param.status != null and param.status.size > 0">
            AND tb2.status in
            <foreach collection="param.status" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <if test="param.categoryCodeByCategoryName != null and param.categoryCodeByCategoryName.size > 0">
            AND tb2.category_code in
            <foreach collection="param.categoryCodeByCategoryName" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>
        <if test="param.goodsCodeBySapMaterialCode != null and param.goodsCodeBySapMaterialCode.size > 0">
            AND tb2.goods_code in
            <foreach collection="param.goodsCodeBySapMaterialCode" item="item3" open="(" separator="," close=")">
                #{item3}
            </foreach>
        </if>
        <if test="param.memberCodeByCrmCode != null and param.memberCodeByCrmCode.size > 0">
            AND tb0.member_code in
            <foreach collection="param.memberCodeByCrmCode" item="item4" open="(" separator="," close=")">
                #{item4}
            </foreach>
        </if>
        <!-- 数据权限限制 -->
        <if test="param.dataPermissionGoodsCodes != null and param.dataPermissionGoodsCodes.size > 0">
            AND tb0.goods_code in
            <foreach collection="param.dataPermissionGoodsCodes" item="item5" open="(" separator="," close=")">
                #{item5}
            </foreach>
        </if>
        ) bidding_report<!-- 查询条件 在外层中处理 -->
        <where>
            <if test="param.dealStatus != null and param.dealStatus !=''">
                AND bidding_report.dealStatus= #{param.dealStatus}
            </if>
        </where>
        order by
        bidding_report.bidding_no desc,
        bidding_report.round desc,
        bidding_report.create_time desc
    </select>

    <select id="biddingAnalysisInfo" resultType="com.cnoocshell.order.api.dto.analysis.BiddingAnalysisInfoDTO">
        select tb1.bidding_no,
               tb2.goods_code,
               tb1.current_round,
               CASE
                   WHEN tb3.old_standard_price is null
                       THEN tb2.last_standard_price
                   ELSE tb3.old_standard_price
                   END AS 'price', tb1.member_code,
               tb1.want_quantity
        from (select tb19.*
              from bidding_buyer_detail tb19
                       left join (select tb20.bidding_no,
                                         tb20.current_round,
                                         tb20.member_code,
                                         MAX(create_time) 'max_create_time'
                                  from bidding_buyer_detail tb20
                                  where tb20.bidding_no=#{biddingNo}
                                  GROUP by tb20.bidding_no,
                                           tb20.current_round,
                                           tb20.member_code) tb21 on
                  tb19.bidding_no = tb21.bidding_no
                      and tb19.current_round = tb21.current_round
                      and tb19.member_code = tb21.member_code
                      and tb19.create_time = tb21.max_create_time
              where tb21.max_create_time is not null
                and tb19.bidding_no = #{biddingNo}) tb1
                 left join bidding tb2 on tb1.bidding_no = tb2.bidding_no and tb2.del_flg = 0
                 left join bidding_adjust_record tb3
                           on tb1.bidding_no = tb3.bidding_no and tb1.current_round = tb3.old_current_round and
                              tb3.del_flg = 0
        where tb1.del_flg = 0
          and tb1.bidding_no = #{biddingNo}
        order by tb1.current_round asc
    </select>

    <select id="memberActivityReport"
            resultType="com.cnoocshell.order.api.dto.bidding.report.MemberActivityReportResultDTO">
        <!-- 分页基础 -->
        select
        tb1.goods_code,
        tb1.member_code,
        IFNULL(tb2.total,0) AS 'enquiry_count',
        IFNULL(tb3.total,0) AS 'bidding_count',
        IFNULL(tb4.total,0) AS 'join_enquiry_count' ,
        IFNULL(tb5.total,0) AS 'join_bidding_count',
        IFNULL(tb6.total,0) AS 'bidding_deal_count',
        IFNULL(tb6.total_want_quantity,0) AS 'bidding_deal_total_quantity'
        from(
        select DISTINCT tb0.* from(
        select
        tb1.goods_code ,
        tb2.member_code
        from
        enquiry tb1
        left join enquiry_buyer tb2 on tb1.enquiry_no =tb2.enquiry_no and tb2.del_flg =0
        where tb1.del_flg =0
        and tb2.member_code is not null
        <!-- 询价状态访问限制 -->
        <if test="param.defaultEnquiryStatus != null and param.defaultEnquiryStatus.size>0">
            and tb1.status in
            <foreach collection="param.defaultEnquiryStatus" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <!-- 数据权限限制 -->
        <if test="param.dataPermissionGoodsCodes != null and param.dataPermissionGoodsCodes.size > 0">
            AND tb1.goods_code IN
            <foreach collection="param.dataPermissionGoodsCodes" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>
        <!-- 客户名称查询 -->
        <if test="param.memberName != null and param.memberName != ''">
            AND tb2.member_name LIKE CONCAT('%', #{param.memberName}, '%')
        </if>
        <!-- CRM客户编码转换查询 -->
        <if test="param.memberCodeByCrmCode != null and param.memberCodeByCrmCode.size > 0">
            AND tb2.member_code IN
            <foreach collection="param.memberCodeByCrmCode" item="item3" open="(" separator="," close=")">
                #{item3}
            </foreach>
        </if>
        <!-- 时间范围查询 -->
        <if test="param.startTime != null">
            and tb1.enquiry_start_time &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and tb1.enquiry_start_time &lt;= #{param.endTime}
        </if>
        <!-- 产品分类转换查询 -->
        <if test="param.goodsCodeByCategoryCode != null and param.goodsCodeByCategoryCode.size > 0">
            AND tb1.goods_code IN
            <foreach collection="param.goodsCodeByCategoryCode" item="item4" open="(" separator="," close=")">
                #{item4}
            </foreach>
        </if>
        <!-- 产品名称查询 -->
        <if test="param.goodsName != null and param.goodsName != ''">
            AND tb1.goods_name LIKE CONCAT('%', #{param.goodsName}, '%')
        </if>
        <!-- SAP物料号转换查询 -->
        <if test="param.goodsCodeBySapMaterialCode != null and param.goodsCodeBySapMaterialCode.size > 0">
            AND tb1.goods_code IN
            <foreach collection="param.goodsCodeBySapMaterialCode" item="item5" open="(" separator="," close=")">
                #{item5}
            </foreach>
        </if>
        <!-- 销售渠道转换查询 -->
        <if test="param.goodsCodeConcatMemberCodeBySaleChannel != null and param.goodsCodeConcatMemberCodeBySaleChannel.size > 0">
            AND CONCAT(tb1.goods_code,'_',tb2.member_code) IN
            <foreach collection="param.goodsCodeConcatMemberCodeBySaleChannel" item="item6" open="(" separator=","
                     close=")">
                #{item6}
            </foreach>
        </if>

        UNION ALL

        SELECT
        tb1.goods_code ,
        tb2.member_code
        FROM bidding tb1
        LEFT JOIN bidding_buyer tb2 ON tb1.bidding_no = tb2.bidding_no AND tb2.del_flg = 0
        WHERE tb1.del_flg = 0
        AND tb2.member_code IS NOT NULL
          <!-- 竞价状态限制访问 -->
        <if test="param.defaultBiddingStatus != null and param.defaultBiddingStatus.size>0">
            and tb1.status in
            <foreach collection="param.defaultBiddingStatus" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <!-- 数据权限限制 -->
        <if test="param.dataPermissionGoodsCodes != null and param.dataPermissionGoodsCodes.size > 0">
            AND tb1.goods_code IN
            <foreach collection="param.dataPermissionGoodsCodes" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>
        <!-- 客户名称查询 -->
        <if test="param.memberName != null and param.memberName != ''">
            AND tb2.member_name LIKE CONCAT('%', #{param.memberName}, '%')
        </if>
        <!-- CRM客户编码转换查询 -->
        <if test="param.memberCodeByCrmCode != null and param.memberCodeByCrmCode.size > 0">
            AND tb2.member_code IN
            <foreach collection="param.memberCodeByCrmCode" item="item3" open="(" separator="," close=")">
                #{item3}
            </foreach>
        </if>
        <!-- 时间范围查询 -->
        <if test="param.startTime != null">
            and tb1.last_bidding_start_time &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and tb1.last_bidding_start_time &lt;= #{param.endTime}
        </if>
        <!-- 产品分类转换查询 -->
        <if test="param.goodsCodeByCategoryCode != null and param.goodsCodeByCategoryCode.size > 0">
            AND tb1.goods_code IN
            <foreach collection="param.goodsCodeByCategoryCode" item="item4" open="(" separator="," close=")">
                #{item4}
            </foreach>
        </if>
        <!-- 产品名称查询 -->
        <if test="param.goodsName != null and param.goodsName != ''">
            AND tb1.goods_name LIKE CONCAT('%', #{param.goodsName}, '%')
        </if>
        <!-- SAP物料号转换查询 -->
        <if test="param.goodsCodeBySapMaterialCode != null and param.goodsCodeBySapMaterialCode.size > 0">
            AND tb1.goods_code IN
            <foreach collection="param.goodsCodeBySapMaterialCode" item="item5" open="(" separator="," close=")">
                #{item5}
            </foreach>
        </if>
        <!-- 销售渠道转换查询 -->
        <if test="param.goodsCodeConcatMemberCodeBySaleChannel != null and param.goodsCodeConcatMemberCodeBySaleChannel.size > 0">
            AND CONCAT(tb1.goods_code,'_',tb2.member_code) IN
            <foreach collection="param.goodsCodeConcatMemberCodeBySaleChannel" item="item6" open="(" separator=","
                     close=")">
                #{item6}
            </foreach>
        </if>
        ) tb0) tb1

        <!-- 统计询价开设次数 -->
        left join (
        select
        tb1.goods_code,
        COUNT(DISTINCT tb1.enquiry_no) AS 'total'
        from enquiry tb1
        where tb1.del_flg =0
        <if test="param.defaultEnquiryStatus != null and param.defaultEnquiryStatus.size>0">
            and tb1.status in
            <foreach collection="param.defaultEnquiryStatus" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <!-- 在该时间范围内开设次数查询 -->
        <if test="param.startTime != null">
            and tb1.enquiry_start_time &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and tb1.enquiry_start_time &lt;= #{param.endTime}
        </if>
        GROUP by tb1.goods_code
        )tb2 on tb1.goods_code=tb2.goods_code
        <!-- 统计竞价开设次数 -->
        left join (
        select
        tb1.goods_code,
        COUNT(DISTINCT tb1.bidding_no) AS 'total'
        from bidding tb1
        where tb1.del_flg =0
        <if test="param.defaultBiddingStatus != null and param.defaultBiddingStatus.size>0">
            and tb1.status in
            <foreach collection="param.defaultBiddingStatus" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <!-- 在该时间范围内开设次数查询 -->
        <if test="param.startTime != null">
            and tb1.last_bidding_start_time &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and tb1.last_bidding_start_time &lt;= #{param.endTime}
        </if>
        GROUP by tb1.goods_code
        )tb3 on tb1.goods_code=tb3.goods_code
        <!-- 统计参与询价的次数 -->
        left join (
        select
        tb2.goods_code,
        tb1.member_code,
        COUNT(*) AS 'total'
        from enquiry_buyer tb1
        left join enquiry tb2 on tb1.enquiry_no =tb2.enquiry_no and tb2.del_flg =0
        where tb1.del_flg =0
        <if test="param.defaultEnquiryStatus != null and param.defaultEnquiryStatus.size>0">
            and tb2.status in
            <foreach collection="param.defaultEnquiryStatus" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <!-- 已参与 -->
        and tb1.participation_status ='DONE'
        <!-- 在该时间范围内参与询价次数查询 -->
        <if test="param.startTime != null">
            and tb2.enquiry_start_time &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and tb2.enquiry_start_time &lt;= #{param.endTime}
        </if>
        GROUP by tb2.goods_code ,tb1.member_code
        ) tb4 on tb1.goods_code=tb4.goods_code and tb1.member_code=tb4.member_code
        <!-- 统计参与竞价的次数 -->
        left join (
        select
        tb2.goods_code,
        tb1.member_code,
        COUNT(*) AS 'total'
        from bidding_buyer tb1
        left join bidding tb2 on tb1.bidding_no =tb2.bidding_no and tb2.del_flg =0
        where tb1.del_flg =0
        <if test="param.defaultBiddingStatus != null and param.defaultBiddingStatus.size>0">
            and tb2.status in
            <foreach collection="param.defaultBiddingStatus" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <!-- 已参与 -->
        and tb1.participation_status ='DONE'
        <!-- 在该时间范围内参与竞价次数查询 -->
        <if test="param.startTime != null">
            and tb2.last_bidding_start_time &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and tb2.last_bidding_start_time &lt;= #{param.endTime}
        </if>
        GROUP by tb2.goods_code ,tb1.member_code
        ) tb5 on tb1.goods_code=tb5.goods_code and tb1.member_code=tb5.member_code
        <!-- 统计成交次数及成交总量 -->
        left join (
        SELECT
        tb2.goods_code,
        tb1.member_code,
        COUNT(*) AS 'total',
        SUM(tb1.want_quantity) AS 'total_want_quantity'
        FROM
        bidding_buyer_deal tb1
        left join bidding tb2 on tb1.bidding_no =tb2.bidding_no and tb2.del_flg =0
        where tb1.del_flg =0
        and tb1.deal_no is not NULL
        and tb2.status ='COMPLETED'
        <!-- 在该时间范围内的成交数据 -->
        <if test="param.startTime != null">
            and tb2.last_bidding_start_time &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and tb2.last_bidding_start_time &lt;= #{param.endTime}
        </if>
        GROUP by tb2.goods_code ,tb1.member_code
        )tb6 on tb1.goods_code=tb6.goods_code and tb1.member_code=tb6.member_code
        where 1=1
        order by tb1.goods_code
    </select>
</mapper>