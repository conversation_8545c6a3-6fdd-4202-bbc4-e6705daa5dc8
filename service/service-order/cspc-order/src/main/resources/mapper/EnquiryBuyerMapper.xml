<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.order.dao.mapper.EnquiryBuyerMapper">
    <resultMap id="BaseResultMap" type="com.cnoocshell.order.dao.vo.EnquiryBuyer">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="enquiry_no" property="enquiryNo" jdbcType="VARCHAR"/>
        <result column="member_code" property="memberCode" jdbcType="VARCHAR"/>
        <result column="member_name" property="memberName" jdbcType="VARCHAR"/>
        <result column="goods_code" property="goodsCode" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="participation_status" property="participationStatus" jdbcType="VARCHAR"/>
        <result column="del_flg" property="delFlg" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="selectNotiryBuyer" resultMap="BaseResultMap">
        SELECT t2.member_code FROM enquiry t1
        left join enquiry_buyer t2 on t1.enquiry_no = t2.enquiry_no
        where t1.enquiry_start_time BETWEEN #{startTime} AND #{endTime} and t1.del_flg = false
    </select>

    <select id="selectBuyerList" resultType="com.cnoocshell.order.api.dto.bidding.BiddingBuyerDTO">
        SELECT
        DISTINCT
        en.goods_code,
        en.goods_name,
        eb.member_code,
        eb.member_name,
        'ENQUIRY' as buyerSource
        from
        enquiry en left join enquiry_buyer eb on en.enquiry_no  = eb.enquiry_no and eb.del_flg = 0
        where
        en.id in
        <foreach item="item" index="index" collection="idList" open="(" separator="," close=" )">
        #{item}
         </foreach>
        and eb.participation_status = #{enquiryStatus}
        and en.del_flg = 0
    </select>

    <select id="queryBuyerUserInfo" resultType="com.cnoocshell.order.api.dto.bidding.BiddingCustomerListDTO">
        SELECT
        eb.id enquiryId,
        eb.member_name ,
        eb.member_code,
        eb.goods_code,
        eb.participation_status
        FROM
        enquiry e
        inner join enquiry_buyer eb on  e.enquiry_no = eb.enquiry_no
        left join bidding bd on eb.enquiry_no = bd.enquiry_no and bd.id = #{dto.enquiryId}
        where
        eb.participation_status = #{status}
        <if test="dto.memberName != null  and dto.memberName !=''">
            and eb.member_name like CONCAT('%',#{dto.memberName},'%')
        </if>
        and ( bd.id = #{dto.enquiryId} or e.id = #{dto.enquiryId})
    </select>

    <select id="selectMemberEnquiryDataByMemberCodes" resultType="com.cnoocshell.order.api.dto.EnquiryBuyerDTO">
        SELECT COUNT(*) as enquiryCount, member_code FROM enquiry_buyer
                 where participation_status ='DONE' and del_flg = 0
          and  member_code in
        <foreach item="memberCode" index="index" collection="memberCodes" open="(" separator="," close=" )">
            #{memberCode}
        </foreach>
        <if test="startDate!=null and startDate!='' ">
            and create_time &gt;  str_to_date(#{startDate},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endDate!=null and endDate!='' ">
            and create_time &lt;=  str_to_date(#{endDate},'%Y-%m-%d %H:%i:%s')
        </if>
        GROUP BY member_code
    </select>

    <select id="selectByEnquiryMemberCode" resultType="com.cnoocshell.order.api.dto.EnquiryBuyerDTO">
        select enquiry_no,participation_status
        from enquiry_buyer
        where  member_code = #{memberCode}
        <if test="enquiryNos != null and enquiryNos.size()>0">
            and enquiry_no in
            <foreach item="enquiryNo" index="index" collection="enquiryNos" open="(" separator="," close=" )">
                #{enquiryNo}
            </foreach>
        </if>
        and del_flg = 0
    </select>

    <select id="selectByEnquiryNoMemberCode" resultType="com.cnoocshell.order.api.dto.EnquiryBuyerDTO">
        select enquiry_no,participation_status
        from enquiry_buyer
        where  member_code = #{memberCode}
            and enquiry_no = #{enquiryNo}
            and del_flg = 0
    </select>


    <select id="queryCheckBuyerUserInfo" resultType="com.cnoocshell.order.api.dto.bidding.BiddingBuyerDTO">
        SELECT
        DISTINCT
        en.goods_code,
        en.goods_name,
        eb.member_code,
        eb.member_name,
        'ENQUIRY' as buyerSource
        from
        enquiry en left join enquiry_buyer eb on en.enquiry_no  = eb.enquiry_no and eb.del_flg = 0
        where
        eb.member_code in
        <foreach item="item" index="index" collection="buyerList" open="(" separator="," close=" )">
            #{item}
        </foreach>
        and en.id  in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=" )">
            #{item}
        </foreach>
        and eb.participation_status = #{enquiryTODOStatus}
        and en.del_flg = 0
    </select>

    <select id="selectTODOList" resultMap="BaseResultMap">
        SELECT * FROM enquiry_buyer where del_flg = false and member_code = #{memberCode} and participation_status = 'TODO'

    </select>

    <select id="buyerQueryEnquiryList" resultType="com.cnoocshell.order.api.dto.EnquiryBuyerListViewDTO">
        SELECT
        e.id,
        e.enquiry_no,
        e.enquiry_name,
        e.goods_code,
        e.goods_name,
        eb.participation_status,
        e.enquiry_start_time,
        e.enquiry_end_time,
        e.status
        FROM enquiry_buyer eb
        inner join enquiry e on eb.enquiry_no = e.enquiry_no

        where e.del_flg=0 and eb.del_flg=0
        and eb.member_code = #{dto.memberCode}
        <if test="dto.enquiryNo != null  and dto.enquiryNo !=''">
            and e.enquiry_no LIKE CONCAT('%', #{dto.enquiryNo}, '%')
        </if>
        <if test="dto.enquiryName != null  and dto.enquiryName !=''">
            and e.enquiry_name LIKE CONCAT('%', #{dto.enquiryName}, '%')
        </if>
        <if test="dto.goodsName != null and dto.goodsName !=''">
            and e.goods_name = #{dto.goodsName}
        </if>
        <if test="dto.statusList != null and dto.statusList.size>0">
            and e.status in
            <foreach item="item" index="index" collection="dto.statusList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.participationStatus != null  and dto.participationStatus !=''">
            and eb.participation_status = #{dto.participationStatus}
        </if>
        <if test="dto.createStartTime != null and dto.createStartTime !='' ">
            and e.create_time &gt;= #{dto.createStartTime}
        </if>
        <if test="dto.createEndTime != null and dto.createEndTime !='' ">
            and e.create_time &lt;= #{dto.createEndTime}
        </if>
        <if test="dto.goodsCodeList != null and dto.goodsCodeList.size()>0 ">
            and e.goods_code in
            <foreach item="item" index="index" collection="dto.goodsCodeList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        ORDER BY e.create_time desc
    </select>

    <select id="queryEnquiryBuyer" resultType="com.cnoocshell.order.dao.vo.EnquiryBuyer">
        select tb1.*
        from enquiry_buyer tb1
                 left join enquiry tb2 on tb1.enquiry_no = tb2.enquiry_no
        where tb1.del_flg = 0
          and tb2.id = #{enquiryId}
          and tb1.participation_status = #{participationStatus}
    </select>

</mapper>
