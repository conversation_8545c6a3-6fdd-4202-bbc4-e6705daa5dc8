<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.order.dao.mapper.EnquiryMapper">
  <resultMap id="EnquiryResultMap" type="com.cnoocshell.order.dao.vo.Enquiry">
    <id property="id" column="id"/>
    <result property="enquiryNo" column="enquiry_no"/>
    <result property="enquiryName" column="enquiry_name"/>
    <result property="payCondition" column="pay_condition"/>
    <result property="priceTradeTerm" column="price_trade_term"/>
    <result property="tradeCurrency" column="trade_currency"/>
    <result property="enquiryStartTime" column="enquiry_start_time"/>
    <result property="enquiryEndTime" column="enquiry_end_time"/>
    <result property="status" column="status"/>
    <result property="goodsCode" column="goods_code"/>
    <result property="goodsName" column="goods_name"/>
    <result property="categoryCode" column="category_code"/>
    <result property="maxPrice" column="max_price"/>
    <result property="minPrice" column="min_price"/>
    <result property="totalQuantity" column="total_quantity"/>
    <result property="delFlg" column="del_flg"/>
    <result property="createUser" column="create_user"/>
    <result property="createUserName" column="create_user_name"/>
    <result property="createTime" column="create_time"/>
    <result property="updateUser" column="update_user"/>
    <result property="updateUserName" column="update_user_name"/>
    <result property="updateTime" column="update_time"/>
    <result property="salesPlanNo" column="sales_plan_no"/>
    <result property="salesPlanName" column="sales_plan_name"/>
  </resultMap>

  <select id="selectList" resultMap="EnquiryResultMap">
    SELECT * FROM enquiry t1
      <where>
          t1.del_flg=0
          <if test="id !=null and id!='' ">
              and t1.id = #{id}
          </if>
          <if test="enquiryNo!=null and enquiryNo!='' ">
              and t1.enquiry_no LIKE CONCAT('%', #{enquiryNo}, '%')
          </if>
          <if test="enquiryName!=null and enquiryName!='' ">
              and t1.enquiry_name LIKE CONCAT('%', #{enquiryName}, '%')
          </if>
          <if test="status!=null and status!='' ">
              and t1.status = #{status}
          </if>
          <if test="createUserName!=null and createUserName!='' ">
              and t1.create_user_name = #{createUserName}
          </if>
          <if test="createStartTime!=null and createStartTime!='' ">
              and t1.create_time &gt;=  str_to_date(#{createStartTime},'%Y-%m-%d %H:%i:%s')
          </if>
          <if test="createEndTime!=null and createEndTime!='' ">
              and t1.create_time &lt;=  str_to_date(#{createEndTime},'%Y-%m-%d %H:%i:%s')
          </if>
          <if test="goodsName !=null and goodsName!='' ">
              and t1.goods_name = #{goodsName}
          </if>
          <if test="memberCode != null and memberCode !=''">
              and t1.enquiry_no in
             (select enquiry_no from enquiry_buyer where member_code = #{memberCode} and del_flg=0)
          </if>
          <if test="categoryCodes != null and categoryCodes.size() > 0">
              and t1.category_code in
              <foreach item="categoryCode" collection="categoryCodes" open="(" separator="," close=")">
                  #{categoryCode}
              </foreach>
          </if>

          <if test="goodsCodes != null and goodsCodes.size() > 0">
              and t1.goods_code in
              <foreach item="goodsCode" collection="goodsCodes" open="(" separator="," close=")">
                  #{goodsCode}
              </foreach>
          </if>

          <if test="statusArrayList != null and statusArrayList.size() > 0">
              and t1.status in
              <foreach item="status" collection="statusArrayList" open="(" separator="," close=")">
                  #{status}
              </foreach>
          </if>

          <if test="enquiryNos != null and enquiryNos.size() > 0">
              and t1.enquiry_no in
              <foreach item="enquiryNo" collection="enquiryNos" open="(" separator="," close=")">
                  #{enquiryNo}
              </foreach>
          </if>
      </where>
      order by t1.create_time desc
  </select>

    <select id="selectTODOList" resultMap="EnquiryResultMap">
    SELECT * FROM enquiry where del_flg = false and

    </select>


    <select id="buyerSelectList" resultMap="EnquiryResultMap">
        SELECT * FROM enquiry t1
        left join enquiry_buyer t2 on t1.enquiry_no = t2.enquiry_no
        <where>
            1=1
            <if test="enquiryNo!=null and enquiryNo!='' ">
                and t1.enquiry_no = #{enquiryNo}
            </if>
            <if test="enquiryName!=null and enquiryName!='' ">
                and t1.enquiry_name LIKE CONCAT('%', #{enquiryName}, '%')
            </if>
            <if test="status!=null and status!='' ">
                and t1.status = #{status}
            </if>
            <if test="createUserName!=null and createUserName!='' ">
                and t1.create_user_uame = #{createUserName}
            </if>
            <if test="createStartTime!=null and createStartTime!='' ">
                and t1.create_time &gt;=  str_to_date(#{createStartTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="createEndTime!=null and createEndTime!='' ">
                and t1.create_time &lt;=  str_to_date(#{createEndTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="memberCode!=null and memberCode!='' ">
                and t2.member_code = #{memberCode}
            </if>
        </where>
        order by t1.create_time desc
    </select>

    <select id="selectStartList" resultMap="EnquiryResultMap">
        SELECT * FROM enquiry
        where status = 'TO_START' and del_flg = 0
    </select>

    <select id="selectEndList" resultMap="EnquiryResultMap">
        SELECT * FROM enquiry
        where status = 'ENQUIRING' and del_flg = 0
    </select>

    <update id="updateStatusByIds" parameterType="map">
        UPDATE enquiry
        <set>
            status = #{status}
        </set>
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdatePrice" parameterType="map">
        UPDATE enquiry
        <set>
            max_price = CASE
            <foreach collection="enquiryList" item="enquiry" separator=" ">
                WHEN enquiry_no = #{enquiry.enquiryNo} THEN #{enquiry.maxPrice}
            </foreach>
            END,
            min_price = CASE
            <foreach collection="enquiryList" item="enquiry" separator=" ">
                WHEN enquiry_no = #{enquiry.enquiryNo} THEN #{enquiry.minPrice}
            </foreach>
            END,
            total_quantity = CASE
            <foreach collection="enquiryList" item="enquiry" separator=" ">
                WHEN enquiry_no = #{enquiry.enquiryNo} THEN #{enquiry.totalQuantity}
            </foreach>
            END
        </set>
        WHERE enquiry_no IN
        <foreach collection="enquiryList" item="enquiry" open="(" separator="," close=")">
            #{enquiry.enquiryNo}
        </foreach>
    </update>

    <select id="selectGoodsSimpleList"  resultType="com.cnoocshell.order.api.dto.GoodsSimpleDTO">
        select max(t1.goods_code) as goodsCode ,max(t1.goods_name) as goodsName
        from enquiry t1
        left join enquiry_buyer t2 on t1.enquiry_no  = t2.enquiry_no
        where t1.del_flg = 0
        <if test="memberCode != null and memberCode != ''">
           and t2.member_code = #{memberCode}
        </if>
        group by t1.goods_code,t1.goods_name
    </select>

    <select id="selectEnquiryTimesByMemberCode"  resultType="java.lang.Integer">
        select count(enquiry_no) from (
          select t1.enquiry_no as enquiry_no  from enquiry t1 left join enquiry_buyer t2 on t1.enquiry_no = t2.enquiry_no
          where t1.del_flg = 0 and t1.status = 'ENQUIRING'  and t2.member_code = #{memberCode}
            <if test="goodsCodes !=null and goodsCodes.size>0">
                and t2.goods_code in
                <foreach collection="goodsCodes" item="goodsCode" open="(" separator="," close=")">
                    #{goodsCode}
                </foreach>
            </if>
          group by t1.enquiry_no
      ) as t1
    </select>

    <select id="selectEnquiryBySaleId"  resultType="java.lang.Integer">

        select count(1) from enquiry t1
             left join sales_plan t2 on t1.sales_plan_no = t2.plan_no
        where t2.id  = #{planId}
    </select>

    <select id="queryEnquiryInfo" resultType="com.cnoocshell.order.api.dto.analysis.EnquiryAnalysisInfoDTO">
        select tb1.enquiry_no,
               tb1.goods_code,
               tb1.member_code,
               IFNULL(tb2.price, 0)    as 'price',
               IFNULL(tb2.quantity, 0) AS 'quantity',
               tb2.create_time
        from enquiry_buyer tb1
                 left join (select tb3.enquiry_no,
                                   tb3.member_code,
                                   tb3.create_time,
                                   tb5.price,
                                   tb5.quantity,
                                   tb5.del_flg
                            from (select tb4.enquiry_no,
                                         tb4.member_code,
                                         MAX(tb4.create_time) AS 'create_time'
                                  from enquiry_buyer_detail tb4
                                  where tb4.del_flg = 0
                                    and tb4.enquiry_no = #{enquiryNo}
                                  group by tb4.enquiry_no,
                                           tb4.member_code) tb3
                                     left join enquiry_buyer_detail tb5 on
                                tb3.enquiry_no = tb5.enquiry_no
                                    and tb3.member_code = tb5.member_code
                                    and tb3.create_time = tb5.create_time
                            where tb5.enquiry_no = #{enquiryNo}) tb2 on
            tb1.member_code = tb2.member_code
                and tb1.enquiry_no = tb2.enquiry_no
                and tb2.del_flg = 0
                 left join enquiry tb3 on
            tb2.enquiry_no = tb3.enquiry_no
                and tb3.del_flg = 0
        where tb1.del_flg = 0
          and tb3.enquiry_no = #{enquiryNo}
    </select>

</mapper>
