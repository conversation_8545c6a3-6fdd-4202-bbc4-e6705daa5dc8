<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.order.dao.mapper.BiddingBuyerDealMapper">
    <select id="getBiddingDealTotalQuantity" resultType="com.cnoocshell.order.api.dto.bidding.BiddingDealQuantityDTO">
        select bidding_no ,SUM(want_quantity) AS 'total_quantity' from bidding_buyer_deal
        where del_flg =0
        and want_quantity is not null
        and bidding_no in
        <foreach collection="biddingNos" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
        GROUP BY bidding_no
    </select>

    <select id="selectMemberBiddingDealDataByMemberCodes" resultType="com.cnoocshell.order.api.dto.bidding.BiddingBuyerDealDTO">
        SELECT COUNT(*) as dealCount, member_code FROM bidding_buyer_deal
        where del_flg = 0 and contract_no is not null
        and member_code in
        <foreach item="memberCode" index="index" collection="memberCodes" open="(" separator="," close=" )">
            #{memberCode}
        </foreach>
        <if test="startDate!=null and startDate!='' ">
            and create_time &gt;  str_to_date(#{startDate},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endDate!=null and endDate!='' ">
            and create_time &lt;=  str_to_date(#{endDate},'%Y-%m-%d %H:%i:%s')
        </if>
        GROUP BY member_code
    </select>

    <update id="updateBiddingDealContractNo">
        UPDATE bidding_buyer_deal
        SET contract_no = #{param.sapContractNo},
            create_contract_status=#{param.createContractStatus},
            create_contract_fail_reason=#{param.createContractFailReason}
        WHERE del_flg=0
        AND deal_no = #{param.dealNo}
    </update>

</mapper>