<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.order.dao.mapper.BiddingApproveRecordMapper">

    <select id="queryApproveList" resultType="com.cnoocshell.order.api.dto.bidding.BiddingApproveListDTO">
        SELECT
        bar.*
        FROM
        qa_cnoocshell_order.bidding bd
        inner join qa_cnoocshell_order.bidding_approve_record bar on bar.bidding_no = bd.bidding_no
        where
        bd.id = #{biddingId}
        and bar.approve_type = #{status}
        ORDER BY
        bar.update_time desc
    </select>
</mapper>