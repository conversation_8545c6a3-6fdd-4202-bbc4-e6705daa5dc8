<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.order.dao.mapper.BiddingBuyerMapper">

    <select id="selectBuyerList" resultType="com.cnoocshell.order.api.dto.bidding.BiddingBuyerDTO">
        SELECT
        sp.goods_code,
        sp.goods_name,
        mm.member_code,
        mm.member_name,
        'SALES_PLAN' as buyerSource
        FROM
        qa_cnoocshell_order.sales_plan sp
        inner join qa_cnoocshell_goods.go_goods gg on
        gg.goods_code = sp.goods_code
        inner join qa_cnoocshell_member.mb_member_purchase_goods_intention mmpgi on
        gg.goods_id = mmpgi.goods_id
        inner join qa_cnoocshell_member.mb_member mm on
        mm.member_id = mmpgi.member_id
        where
        sp.id in
        <foreach item="item" index="index" collection="idList" open="(" separator="," close=" )">
            #{item}
        </foreach>
    </select>

    <select id="queryBiddingBuyer" resultType="com.cnoocshell.order.dao.vo.BiddingBuyer">
        SELECT
        DISTINCT
        goods_code,
        goods_name
        FROM
        sales_plan
        where
        id = #{id}
        and del_flg = 0
    </select>

    <select id="buyerBiddingList" resultType="com.cnoocshell.order.api.dto.bidding.BiddingBuyerListViewDTO">
        SELECT
        DISTINCT
        bd.id,
        bd.bidding_no,
        bd.bidding_name,
        bd.enquiry_no,
        bd.enquiry_name,
        bd.sales_plan_no,
        bd.sales_plan_name,
        bd.goods_code,
        bd.goods_name,
        bd.category_code,
        bd.bidding_start_time,
        bd.bidding_end_time,
        bd.delivery_effect_start_date,
        bd.delivery_effect_end_date,
        bd.status,
        bd.standard_price,
        bd.current_week_expect_price,
        bd.cost_price,
        bd.remain_sellable_quantity,
        bd.curr_apply_sellable_quantity,
        bd.min_sellable_quantity,
        bd.market_situation,
        bd.description,
        bd.support_document,
        bd.current_round,
        bd.last_standard_price,
        bd.last_bidding_start_time,
        bd.last_bidding_end_time,
        bd.deal_time,
        bd.del_flg,
        bd.create_user,
        bd.create_user_name,
        bd.create_time,
        bd.update_user,
        bd.update_time,
        bb.participation_status,
        CASE
        WHEN bbd.id IS NOT NULL THEN true
        ELSE false
        END AS transactionStatus
        FROM
        bidding_buyer bb
        inner join bidding bd on bb.bidding_no = bd.bidding_no
        left join bidding_buyer_deal bbd on bbd.bidding_no = bb.bidding_no and bd.status = #{dto.lastStatus} and bbd.del_flg  = 0 and bbd.member_code = #{dto.memberCode}
        where bd.del_flg=0
        and bb.member_code = #{dto.memberCode}
        <if test="dto.biddingNo != null  and dto.biddingNo !=''">
            and bd.bidding_no LIKE CONCAT('%', #{dto.biddingNo}, '%')
        </if>
        <if test="dto.biddingName != null  and dto.biddingName !=''">
            and bd.bidding_name LIKE CONCAT('%', #{dto.biddingName}, '%')
        </if>
        <if test="dto.createUserName != null  and dto.createUserName !=''">
            and bd.create_user_name LIKE CONCAT('%', #{dto.createUserName}, '%')
        </if>
        <if test="dto.participationStatus != null  and dto.participationStatus !=''">
            and bb.participation_status = #{dto.participationStatus}
        </if>
        <if test="dto.status != null ">
            and bd.status in
            <foreach item="item" index="index" collection="dto.status" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.cannotLookStatus != null ">
            and bd.status not in
            <foreach item="item" index="index" collection="dto.cannotLookStatus" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.goodsNameList != null ">
            and bd.goods_name in
            <foreach item="item" index="index" collection="dto.goodsNameList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.goodsCodeList != null ">
            and bd.goods_code in
            <foreach item="item" index="index" collection="dto.goodsCodeList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.statusList != null ">
            and bd.status in
            <foreach item="item" index="index" collection="dto.statusList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </if>
        <if test="dto.createStartTime != null  and dto.createEndTime != null and dto.createStartTime != dto.createEndTime">
            and bd.bidding_start_time >= #{dto.createStartTime} and #{dto.createEndTime} >= bd.bidding_end_time
        </if>
        <if test="dto.createStartTime != null  and dto.createEndTime == null">
            and bd.bidding_start_time >= #{dto.createStartTime}
        </if>
        <if test="dto.createStartTime == null  and dto.createEndTime != null">
            and #{createEndTime} >= bd.bidding_end_time
        </if>
        <if test="dto.createStartTime != null  and dto.createEndTime != null  and dto.createStartTime ==  dto.createEndTime">
            and (DATE(bd.bidding_start_time) = #{dto.createStartTime} or DATE(bd.bidding_end_time) = #{dto.createEndTime})
        </if>
        <if test="dto.transactionStatus == true">
            and  bd.bidding_no in(  select btemp.bidding_no from bidding_buyer_deal btemp where btemp.member_code = #{dto.memberCode} and btemp.del_flg  = 0)
        </if>
        <if test="dto.transactionStatus == false">
            and  bd.bidding_no not in (  select btemp.bidding_no from bidding_buyer_deal btemp where btemp.member_code = #{dto.memberCode} and btemp.del_flg  = 0)
        </if>
        ORDER BY bd.create_time desc
    </select>

    <select id="selectMemberBiddingDataByMemberCodes" resultType="com.cnoocshell.order.api.dto.bidding.BiddingBuyerDTO">
        SELECT COUNT(*) as biddingCount, member_code FROM bidding_buyer
        where participation_status ='DONE' and del_flg = 0
        and member_code in
        <foreach item="memberCode" index="index" collection="memberCodes" open="(" separator="," close=" )">
            #{memberCode}
        </foreach>
        <if test="startDate!=null and startDate!='' ">
            and create_time &gt;  str_to_date(#{startDate},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endDate!=null and endDate!='' ">
            and create_time &lt;=  str_to_date(#{endDate},'%Y-%m-%d %H:%i:%s')
        </if>
        GROUP BY member_code
    </select>

    <select id="buyerGoodsBiddingList" resultType="java.lang.String">
        SELECT
        DISTINCT
        goods_name
        FROM
        bidding_buyer
        where
        member_code = #{memberCode}
    </select>

    <select id="queryCustomerList" resultType="com.cnoocshell.order.dao.vo.BiddingBuyer">
        SELECT
        DISTINCT
        bb.id,
        bb.bidding_no,
        bb.member_code,
        bb.member_name,
        bb.goods_code,
        bb.goods_name,
        bb.buyer_source,
        bb.del_flg,
        bb.create_user,
        bb.create_time,
        bb.update_user,
        bb.update_time,
        eb.participation_status
        FROM
        bidding b
        inner join bidding_buyer bb on b.bidding_no = bb.bidding_no
        left join enquiry_buyer eb on b.enquiry_no = eb.enquiry_no and eb.member_code=bb.member_code and eb.del_flg = 0
        where
        b.bidding_no = #{biddingNo}
        and b.del_flg = 0
        and bb.del_flg = 0
    </select>

    <select id="queryMemberByBiddingId" resultType="java.lang.String">
        SELECT
        bb.member_code
        FROM
        bidding b
        inner join bidding_buyer bb on
        bb.bidding_no = b.bidding_no
        where
        b.id = #{id}
    </select>

    <update id="updateStatus">
        UPDATE bidding_buyer
        SET participation_status=#{status},
        update_user=#{operatorId},
        update_time=NOW()
        WHERE del_flg = 0
        AND bidding_no = #{biddingNo}
        <if test="memberCode != null and memberCode !=''">
            AND member_code = #{memberCode}
        </if>
    </update>

    <select id="queryLastBiddingBuyers" resultType="com.cnoocshell.order.dao.vo.BiddingBuyer">
        select DISTINCT tb1.*
        from bidding_buyer tb1
        left join bidding_buyer_detail tb2
        on tb1.bidding_no = tb2.bidding_no and tb1.member_code = tb2.member_code
        left join bidding tb3
        on tb3.bidding_no = tb2.bidding_no and (tb3.current_round - 1) = tb2.current_round
        where tb2.del_flg = 0
        and tb3.bidding_no IN
        <foreach collection="biddingNos" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
    </select>

</mapper>