package com.cnoocshell.goods;


import com.cnoocshell.order.CspcOrderApplication;
import com.cnoocshell.order.biz.IEnquiryBiz;
import com.cnoocshell.order.biz.impl.EnquiryBuyerBiz;
import com.cnoocshell.order.biz.impl.EnquiryBuyerDetailBiz;
import com.cnoocshell.order.common.SimpleDailyCounter;
import com.cnoocshell.order.dao.mapper.EnquiryBuyerDetailMapper;
import com.cnoocshell.order.dao.mapper.EnquiryBuyerMapper;
import com.cnoocshell.order.dao.mapper.EnquiryMapper;
import com.cnoocshell.order.dao.vo.EnquiryBuyerDetail;
import com.cnoocshell.order.tasks.EnquiryStartNotifyBuyerScheduler;
import com.cnoocshell.order.tasks.EnquiryStartScheduler;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CspcOrderApplication.class)
public class Enquiry {

    @Autowired
    private IEnquiryBiz enquiryBiz;

    @Autowired
    private SimpleDailyCounter simpleDailyCounter;

    @Autowired
    private EnquiryBuyerBiz enquiryBuyerBiz;

    @Autowired
    private EnquiryBuyerDetailBiz enquiryBuyerDetailBiz;

    @Resource
    private EnquiryBuyerMapper enquiryBuyerMapper;
    
    @Resource
    private EnquiryMapper enquiryMapper;

    @Resource
    private EnquiryBuyerDetailMapper enquiryBuyerDetailMapper;

    @Autowired
    private EnquiryStartScheduler enquiryStartScheduler;

    @Autowired
    private EnquiryStartNotifyBuyerScheduler enquiryStartNotifyBuyerScheduler;

    @Test
    public void insertEnquiryBuyerDetail(){
        EnquiryBuyerDetail enquiryBuyer = EnquiryBuyerDetail.builder()
                .id("123")
                .enquiryNo("2133")
                .goodsName("111")
                .memberCode("mecode")
                .memberName("mname")
                .goodsCode("goodscode")
                .price(new BigDecimal("12"))
                .quantity(new BigDecimal("2"))
                .pack("pack")
                .deliveryMode("2")
                .delFlg(false)
                .createTime(new Date())
                .createUser("123wsm")
                .build();
        int insert = enquiryBuyerDetailBiz.insert(enquiryBuyer);
        Assert.assertTrue(insert > 0);
    }

}
