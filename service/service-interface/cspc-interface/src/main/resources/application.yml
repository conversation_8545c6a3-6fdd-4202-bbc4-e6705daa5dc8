logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    com.netflix.discovery.DiscoveryClient: WARN
mybatis:
  typeAliasesPackage: com.cnoocshell.open.dao
  mapperScanPackage: com.cnoocshell.open.dao
  mapperLocations: classpath:/mapper/*.xml
  configLocation: classpath:/mybatis-config.xml
spring:
  application:
    name: cspc-service-interface
  profiles:
    active: ${ENV:dev}
  main:
    allow-bean-definition-overriding: true

  freemarker:
    template-loader-path: "classpath:/templates"
    cache: "false"
    charset: "utf-8"
    check-template-location: "true"
    content-type: "text/html"
    expose-request-attributes: "true"
    expose-session-attributes: "true"
    request-context-attribute: "request"
    suffix: ".ftl"

server:
  port: ${app.server.port:8088}

feign:
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  hystrix:
    enabled: true
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true

sms:
  # 联通短信发送配置
  unicom:
    connectTimeout: 15000
    socketTimeout: 15000
swagger:
  author: cnoocshell
  title: cnoocshell
  basePackage: com.cnoocshell.integration.controller

wechat:
  #微信公众号相关配置
  mp:
    #过期时间 秒
    expiredTime: 7199
    mini:
      program:
        state: trial #developer为开发版；trial为体验版；formal为正式版；默认为正式版


