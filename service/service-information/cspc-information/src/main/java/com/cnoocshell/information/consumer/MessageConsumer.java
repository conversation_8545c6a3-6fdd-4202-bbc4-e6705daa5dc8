package com.cnoocshell.information.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.information.api.dto.MessageSendDTO;
import com.cnoocshell.information.service.IMessageSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class MessageConsumer {
    private final IMessageSendService messageSendService;

    @KafkaListener(topics = MqTopicConstant.SEND_MESSAGE_TOPIC)
    public void sendMessageConsumer(ConsumerRecord<String, String> data) {
        String value = null;
        if (Objects.isNull(data) || CharSequenceUtil.isBlank(data.value())) {
            log.info("sendMessageConsumer 发送站内信消费 入参数据为空 data:{}", data);
        }else {
            value = data.value();
            log.info("发送站内信开始 data:{}", value);
        }

        MessageSendDTO message = null;
        try {
            message = JSONUtil.toBean(value, MessageSendDTO.class);
        } catch (Exception e) {
            log.error("sendMessageConsumer 解析json数据出错 data:{}", value, e);
            return;
        }
        if (Objects.isNull(message)) {
            log.error("sendMessageConsumer 解析json数据为空 data:{}", value);
            return;
        }
        MessageSendDTO send = BeanUtil.toBean(message,MessageSendDTO.class);
        try {
            log.info("开始发送站内信 send:{}", send);
            Boolean result = messageSendService.sendMessage(send);
            log.info("结束发送站内信 send:{} result:{}", send, result);
            if (BooleanUtil.isTrue(result)) {
                log.info("站内信发送成功 Message:{}",message.getMessageHistoryMqId());
                return;
            }
            log.error("发送站内信失败 send:{} result:{}", send, result);
        } catch (Exception e) {
            log.error("发送站内信异常 error:", e);
        }
    }
}
