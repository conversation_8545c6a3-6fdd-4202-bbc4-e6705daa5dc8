package com.cnoocshell.information.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 消息内容
 */
@Data
@Table(name = "ii_private_message_content")
@EqualsAndHashCode(callSuper = true)
public class PrivateMessageContent extends BaseEntity {
    /**
     * 消息ID
     */
    @Id
    @Column(name = "msg_id")
    private String msgId;

    /**
     * 消息类型
     */
    @Column(name = "msg_type")
    private Integer msgType;

    /**
     * 消息标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 消息附件url
     */
    @Column(name = "attachment_url")
    private String attachmentUrl;

    /**
     * 消息内容URL
     */
    @Column(name = "msg_content_url")
    private String msgContentUrl;

    /**
     * 消息内容
     */
    @Column(name = "msg_content")
    private String msgContent;

}