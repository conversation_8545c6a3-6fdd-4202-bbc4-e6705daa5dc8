package com.cnoocshell.information.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageDTO;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageQueryDTO;
import com.cnoocshell.information.dao.vo.PrivateMessageInbox;

import java.util.List;

public interface PrivateMessageInboxMapper extends IBaseMapper<PrivateMessageInbox> {
    List<InternalMessageDTO> findInboxMessage(InternalMessageQueryDTO queryDTO);
}