package com.cnoocshell.information.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.utils.BeanConvertUtils;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.information.api.dto.internalmessage.*;
import com.cnoocshell.information.biz.IPrivateMessageContentBiz;
import com.cnoocshell.information.biz.IPrivateMessageInboxBiz;
import com.cnoocshell.information.dao.vo.PrivateMessageContent;
import com.cnoocshell.information.dao.vo.PrivateMessageInbox;
import com.cnoocshell.information.service.IInternalMessageService;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.service.IAccountService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 30/08/2018 15:05
 * @DESCRIPTION:
 */
@Slf4j
@Service
public class InternalMessageService implements IInternalMessageService {

    @Autowired
    private IPrivateMessageInboxBiz privateMessageInboxBiz;
    @Autowired
    private IPrivateMessageContentBiz privateMessageContentBiz;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private UUIDGenerator uuidGenerator;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean sendMessage(InternalMessageCreateDTO internalMessageCreateDTO) {
        log.info("sendMessage:{}",internalMessageCreateDTO);
        if( CollectionUtils.isEmpty(internalMessageCreateDTO.getReceiveAccountIds()) && CollectionUtils.isEmpty(internalMessageCreateDTO.getReceiveMemberIds())){
            throw  new BizException(BasicCode.INVALID_PARAM,":receiveAccountIds与receiveMemberIds不能都为空");
        }
        Date now = new Date();
        String creteUser = internalMessageCreateDTO.getOperator();
        //保存消息内容
        PrivateMessageContent privateMessageContent = new PrivateMessageContent();
        privateMessageContent.setAttachmentUrl(internalMessageCreateDTO.getAttachmentUrl());
        privateMessageContent.setCreateTime(now);
        privateMessageContent.setCreateUser(creteUser);
        privateMessageContent.setDelFlg(Boolean.FALSE);
        privateMessageContent.setMsgContent(internalMessageCreateDTO.getContent());
        privateMessageContent.setMsgContentUrl(internalMessageCreateDTO.getContentUrl());
        privateMessageContent.setMsgId(uuidGenerator.gain());
        privateMessageContent.setTitle(internalMessageCreateDTO.getTitle());
        privateMessageContentBiz.save(privateMessageContent);
        List<AccountSimpleDTO> accountList = Lists.newArrayList();
        if( !CollectionUtils.isEmpty(internalMessageCreateDTO.getReceiveMemberIds()) ){
            //TODO 查询会员下的所有账号 需要accountService 添加一个方法
        }else if( !CollectionUtils.isEmpty(internalMessageCreateDTO.getReceiveAccountIds()) ){
            accountList.addAll(accountService.listSimpleByIds(internalMessageCreateDTO.getReceiveAccountIds()));
        }
        List<PrivateMessageInbox> list = Lists.newArrayList();
        for (AccountSimpleDTO account : accountList) {
            PrivateMessageInbox privateMessageInbox = new PrivateMessageInbox();
            privateMessageInbox.setCreateTime(now);
            privateMessageInbox.setCreateUser(creteUser);
            privateMessageInbox.setStatus(0);
            privateMessageInbox.setDelFlg(Boolean.FALSE);
            privateMessageInbox.setMessageHistoryMqId(internalMessageCreateDTO.getMessageHistoryMqId());
            privateMessageInbox.setId(uuidGenerator.gain());
            privateMessageInbox.setMsgId(privateMessageContent.getMsgId());
            privateMessageInbox.setReceiveUserId(account.getAccountId());
            privateMessageInbox.setReceiveMemberId(account.getMemberId());
            privateMessageInbox.setSendTime(now);
            privateMessageInbox.setSendUserId(creteUser);
            //冗余存储，方便查询
            privateMessageInbox.setTitle(internalMessageCreateDTO.getTitle());
            list.add(privateMessageInbox);
        }
        privateMessageInboxBiz.batchInsert(list);
        return true;
    }

    @Override
    public Boolean delete(InternalMessageDeleteDTO internalMessageDeleteDTO) {
        return privateMessageInboxBiz.delete(internalMessageDeleteDTO);
    }

    @Override
    public Boolean updateReadStatus(InternalMessageReadStatusUpdateDTO internalMessageReadStatusUpdateDTO) {
        return privateMessageInboxBiz.updateReadStatus(internalMessageReadStatusUpdateDTO);
    }

    @Override
    public PageInfo<InternalMessageDTO> findAll(InternalMessageQueryDTO internalMessageQueryDTO) {
        PageInfo<PrivateMessageInbox> pageInfo = privateMessageInboxBiz.findAll(internalMessageQueryDTO);
        PageInfo<InternalMessageDTO> result = BeanConvertUtils.convert(pageInfo, PageInfo.class);
        if (CollUtil.isNotEmpty(pageInfo.getList())) {
            List<String> msgIds = CommonUtils.getListValueByDistinctAndFilterBank(pageInfo.getList(), PrivateMessageInbox::getMsgId);
            List<String> inboxIds = CommonUtils.getListValueByDistinctAndFilterBank(pageInfo.getList(),PrivateMessageInbox::getId);

            Map<String, PrivateMessageContent> messageContentMap = CommonUtils.getMap(privateMessageContentBiz.findByIds(msgIds), PrivateMessageContent::getMsgId);
            List<InternalMessageDTO> resultList = pageInfo.getList().stream().map(v -> {
                InternalMessageDTO target = BeanUtil.toBean(v, InternalMessageDTO.class);
                PrivateMessageContent content = CommonUtils.getByKey(messageContentMap, v.getMsgId());
                if (Objects.nonNull(content)) {
                    target.setContent(content.getMsgContent());
                    target.setReleaseTime(content.getCreateTime());
                    target.setContentUrl(content.getMsgContentUrl());
                }
                return target;
            }).collect(Collectors.toList());

            //消除未读
            InternalMessageReadStatusUpdateDTO readMsg = new InternalMessageReadStatusUpdateDTO();
            readMsg.setIds(inboxIds);
            readMsg.setOperator(internalMessageQueryDTO.getAccountId());
            readMsg.setAllRead(Boolean.FALSE);
            readMsg.setMemberId(internalMessageQueryDTO.getMemberId());
            readMsg.setAccountId(internalMessageQueryDTO.getAccountId());
            this.updateReadStatus(readMsg);

            result.setList(resultList);
        }
        return result;
    }

    @Override
    public Integer count(String accountId) {
        return privateMessageInboxBiz.countByAccountId(accountId);
    }

    @Override
    public InternalMessageDTO findById(String id) {
        return convert(privateMessageInboxBiz.get(id));
    }

    private InternalMessageDTO convert(PrivateMessageInbox privateMessageInbox) {
        if (privateMessageInbox == null || BooleanUtil.isTrue(privateMessageInbox.getDelFlg())) {
            return null;
        }
        //添加内容
        InternalMessageDTO internalMessageDTO = BeanConvertUtils.convert(privateMessageInbox, InternalMessageDTO.class);
        PrivateMessageContent privateMessageContent = privateMessageContentBiz.get(privateMessageInbox.getMsgId());
        if (privateMessageContent != null && !BooleanUtil.isTrue(privateMessageContent.getDelFlg())) {
            internalMessageDTO.setContent(privateMessageContent.getMsgContent());
            internalMessageDTO.setReleaseTime(privateMessageContent.getCreateTime());
            internalMessageDTO.setContentUrl(privateMessageContent.getMsgContentUrl());
        }
        return internalMessageDTO;
    }
}
