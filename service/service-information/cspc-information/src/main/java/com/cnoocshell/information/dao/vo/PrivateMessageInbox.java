package com.cnoocshell.information.dao.vo;

import com.cnoocshell.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 消息接收人记录表
 */
@Data
@Table(name = "ii_private_message_inbox")
@EqualsAndHashCode(callSuper = true)
public class PrivateMessageInbox extends BaseEntity{

    /**
     * ID
     */
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 收件人
     */
    @Column(name = "receive_user_id")
    private String receiveUserId;

    /**
     * 收件会员id
     */
    @Column(name = "receive_member_id")
    private String receiveMemberId;

    private String title;
    /**
     * 消息类型
     */
    @Column(name = "msg_type")
    private Integer msgType;

    /**
     * 消息ID
     */
    @Column(name = "msg_id")
    private String msgId;

    /**
     * 状态 0 未读 1 已读
     */
    private Integer status;

    /**
     * 发信人id
     */
    @Column(name = "send_user_id")
    private String sendUserId;

    /**
     * 发信时间
     */
    @Column(name = "send_time")
    private Date sendTime;

    /**
     * 读取时间
     */
    @Column(name = "read_time")
    private Date readTime;

    @Column(name = "message_history_mq_id")
    private String messageHistoryMqId;
}