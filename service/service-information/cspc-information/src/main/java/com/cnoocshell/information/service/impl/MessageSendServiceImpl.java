package com.cnoocshell.information.service.impl;

import com.cnoocshell.information.api.dto.MessageSendDTO;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageCreateDTO;
import com.cnoocshell.information.service.IInternalMessageService;
import com.cnoocshell.information.service.IMessageSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MessageSendServiceImpl implements IMessageSendService {

    @Autowired
    private IInternalMessageService iInternalMessageService;

    @Override
    public Boolean sendMessage(MessageSendDTO messageSendDTO) {
        InternalMessageCreateDTO internalMessageCreateDTO = new InternalMessageCreateDTO();
        BeanUtils.copyProperties(messageSendDTO,internalMessageCreateDTO);
       return iInternalMessageService.sendMessage(internalMessageCreateDTO);
    }
}
