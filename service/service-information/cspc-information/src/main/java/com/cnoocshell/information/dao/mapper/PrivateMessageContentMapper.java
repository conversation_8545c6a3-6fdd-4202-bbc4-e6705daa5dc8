package com.cnoocshell.information.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageDTO;
import com.cnoocshell.information.api.dto.internalmessage.InternalMessageQueryDTO;
import com.cnoocshell.information.dao.vo.PrivateMessageContent;

import java.util.List;

public interface PrivateMessageContentMapper extends IBaseMapper<PrivateMessageContent> {
     List<InternalMessageDTO> findDeleteMessage(InternalMessageQueryDTO internalMessageQueryDTO);
}