package com.cnoocshell.information.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.cnoocshell.information.service.IAnnouncementService;
import com.cnoocshell.information.api.dto.announcement.AnnouncementDTO;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import com.cnoocshell.information.api.dto.announcement.AnnouncementCreateDTO;
import com.cnoocshell.information.api.dto.announcement.AnnouncementQueryDTO;
import com.cnoocshell.information.api.dto.announcement.AnnouncementApprovalDTO;
import java.lang.String;
import com.cnoocshell.information.api.dto.announcement.AnnouncementIndexQueryDTO;
import com.cnoocshell.information.api.dto.announcement.AnnouncementDetailDTO;
import com.cnoocshell.information.api.dto.announcement.AnnouncementUpdateDTO;
import com.github.pagehelper.PageInfo;


/**
 * @Created：Wed Aug 29 16:12:44 CST 2018
 * @Author: <EMAIL>
 * @Version:2
 * @Description:咨询管理接口
*/

@Api(tags={"Announcement"},description = "null")
@RestController
@RequestMapping("/announcement")
public class AnnouncementController {

   @Autowired 
   private IAnnouncementService iAnnouncementService;

   @ApiOperation("修改一个公告（如果修改审批 通过的公共，审批状态应该改为未审批，等待再次审批）")
   @PostMapping(value="/update")
   public void update(@RequestBody AnnouncementUpdateDTO announcementUpdateDTO)throws Exception{
      iAnnouncementService.update(announcementUpdateDTO);

   }


   @ApiOperation("删除一个公告")
   @PostMapping(value="/delete")
   public void delete(@RequestParam("id") String id,@RequestParam("operator") String operator)throws Exception{
      iAnnouncementService.delete(id,operator);

   }


   @ApiOperation("添加一个公告")
   @PostMapping(value="/create")
   public void create(@RequestBody AnnouncementCreateDTO announcementCreateDTO)throws Exception{
      iAnnouncementService.create(announcementCreateDTO);

   }


   @ApiOperation("按条件翻页查询公告")
   @PostMapping(value="/findAll")
   public PageInfo<AnnouncementDTO> findAll(@RequestBody AnnouncementQueryDTO query)throws Exception{
      return iAnnouncementService.findAll(query);
   }

   @ApiOperation("首页公告")
   @GetMapping(value="/homePageList")
   public PageInfo<AnnouncementDTO> homePageList()throws Exception{
      AnnouncementQueryDTO query = new AnnouncementQueryDTO();
      query.setAnnouncementType(0);
      query.setApprovalStatus(1);
      query.setAvailableStatus(0);
      query.setStartTime(Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()));
      LocalDateTime endOfDay = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
      Date newDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
      query.setEndTime(newDate);
      query.setPageSize(5);
      return iAnnouncementService.findAll(query);
   }

   @ApiOperation("查询单条公告详情(不含审批记录)")
   @PostMapping(value="/findById")
   public AnnouncementDTO findById(@RequestParam("id") String id)throws Exception{
      return iAnnouncementService.findById(id);
   }


   @ApiOperation("查询单条公告详情(含审批记录)")
   @PostMapping(value="/findDetailById")
   public AnnouncementDetailDTO findDetailById(@RequestParam("id") String id)throws Exception{
      return iAnnouncementService.findDetailById(id);
   }


   @ApiOperation("禁止（暂停）公告")
   @PostMapping(value="/pause")
   public void pause(@RequestParam("id") String id,@RequestParam("operator") String operator)throws Exception{
      iAnnouncementService.pause(id,operator);

   }


   @ApiOperation("启用已暂停的公告(如果当前时间已经过期，当前公告也不会显示)")
   @PostMapping(value="/goon")
   public void goon(@RequestParam("id") String id,@RequestParam("operator") String operator)throws Exception{
      iAnnouncementService.goon(id,operator);

   }


   @ApiOperation("公告审批")
   @PostMapping(value="/approval")
   public void approval(@RequestBody AnnouncementApprovalDTO announcementApprovalDTO)throws Exception{
      iAnnouncementService.approval(announcementApprovalDTO);

   }


   @ApiOperation("查询主页显示的公共，查询对象可以为空")
   @PostMapping(value="/findForIndex")
   public List<AnnouncementDTO> findForIndex(@RequestBody AnnouncementIndexQueryDTO query)throws Exception{
      return iAnnouncementService.findForIndex(query);
   }



}
