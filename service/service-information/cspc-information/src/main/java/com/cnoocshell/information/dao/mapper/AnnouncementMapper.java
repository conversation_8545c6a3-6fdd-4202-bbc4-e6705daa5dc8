package com.cnoocshell.information.dao.mapper;

import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.information.dao.vo.Announcement;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AnnouncementMapper extends IBaseMapper<Announcement> {

    @Select("SELECT * FROM ii_announcement WHERE del_flg = 0 and available_status = 0 and approval_status=1 " +
            " and announcement_type=#{type}" +
            " and start_time <=#{time} and end_time >=#{time}" +
            " order by weight desc,update_time desc limit #{limitSize}")
    List<Announcement> findForIndex2(@Param("type") Integer type,@Param("time") String time1, @Param("limitSize") Integer limitSize);

    @Select("SELECT * FROM ii_announcement WHERE del_flg = 0 and available_status = 0 and approval_status=1 " +
            " and start_time <=#{time} and end_time >=#{time}" +
            " order by weight desc,update_time desc limit #{limitSize}")
    List<Announcement> findForIndex1(@Param("time") String time1, @Param("limitSize") Integer limitSize);
}