package com.cnoocshell.information.controller;

import com.cnoocshell.information.api.dto.internalmessage.*;
import com.cnoocshell.information.service.IInternalMessageService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @Created锛�Fri Sep 28 11:47:39 CST 2018
 * @Author: <EMAIL>
 * @Version:2
 * @Description:站内信服务
*/

@Api(tags={"InternalMessage"},description = "站内信服务")
@RestController
@RequestMapping("/internalMessage")
public class InternalMessageController {

   @Autowired 
   private IInternalMessageService internalMessageService;

   @ApiOperation("发送消息")
   @PostMapping(value="/sendMessage")
   public Boolean sendMessage(@RequestBody InternalMessageCreateDTO internalMessageCreateDTO) {
      return internalMessageService.sendMessage(internalMessageCreateDTO);
   }

   @ApiOperation("删除消息")
   @PostMapping(value="/delete")
   public Boolean delete(@RequestBody InternalMessageDeleteDTO internalMessageDeleteDTO) {
      return internalMessageService.delete(internalMessageDeleteDTO);
   }

   @ApiOperation("更新已读状态消息")
   @PostMapping(value="/updateReadStatus")
   public Boolean updateReadStatus(@RequestBody InternalMessageReadStatusUpdateDTO internalMessageReadStatusUpdateDTO) {
      return internalMessageService.updateReadStatus(internalMessageReadStatusUpdateDTO);
   }

   @ApiOperation("分页")
   @PostMapping(value="/findAll")
   public PageInfo<InternalMessageDTO> findAll(@RequestBody InternalMessageQueryDTO internalMessageQueryDTO) {
      return internalMessageService.findAll(internalMessageQueryDTO);
   }

   @ApiOperation("根据账号查询未读消息数量")
   @PostMapping(value="/count")
   public Integer count(@RequestParam("accountId") String accountId) {
      return internalMessageService.count(accountId);
   }

   @ApiOperation("根据消息id查询消息详情")
   @PostMapping(value="/findById")
   public InternalMessageDTO findById(@RequestParam("id") String id) {
      return internalMessageService.findById(id);
   }
}
