package com.cnoocshell.information.service.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.cnoocshell.base.api.dto.cloud.AttachmentinfoDTO;
import com.cnoocshell.base.api.service.IAttachmentService;
import com.cnoocshell.information.api.dto.announcement.*;
import com.cnoocshell.information.biz.IAnnouncementBiz;
import com.cnoocshell.information.dao.vo.Announcement;
import com.cnoocshell.information.service.IAnnouncementService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AnnouncementService implements IAnnouncementService {

    @Autowired
    IAnnouncementBiz announcementBiz;

    @Autowired
    IAttachmentService attachmentService;

    @Override
    public void create(AnnouncementCreateDTO announcementCreateDTO) {
        announcementBiz.create(announcementCreateDTO);
    }

    @Override
    public void delete(String id, String operator) {
        announcementBiz.delete(id, operator);
    }

    @Override
    public void update(AnnouncementUpdateDTO announcementUpdateDTO) {
        announcementBiz.update(announcementUpdateDTO);
    }

    @Override
    public void approval(AnnouncementApprovalDTO announcementApprovalDTO) {
        announcementBiz.approval(announcementApprovalDTO);
    }

    @Override
    public void pause(String id, String operator) {
        announcementBiz.pause(id, operator);
    }

    @Override
    public void goon(String id, String operator) {
        announcementBiz.goon(id, operator);
    }

    @Override
    public AnnouncementDTO findById(String id) {
        List<AnnouncementAttachmentDTO> attachmentDTOList = new ArrayList<>();
        AnnouncementDTO resDTO = announcementBiz.findById(id);
        if (StringUtils.isNotBlank(resDTO.getAttachmentIds())){
            List<AttachmentinfoDTO> attachmentInfoDTOList = attachmentService.getByIds(resDTO.getAttachmentIds());
            attachmentInfoDTOList.forEach(attachment -> {
                AnnouncementAttachmentDTO attachmentDTO = new AnnouncementAttachmentDTO();
                attachmentDTO.setAttachmentId(attachment.getAttachmentId());
                attachmentDTO.setAttachmentName(attachment.getAttcName());
                attachmentDTOList.add(attachmentDTO);
            });
            resDTO.setAttachmentList(attachmentDTOList);
        }
        return resDTO;
    }

    @Override
    public AnnouncementDetailDTO findDetailById(String id) {
        return announcementBiz.findDetailById(id);
    }

    @Override
    public PageInfo<AnnouncementDTO> findAll(AnnouncementQueryDTO query) {
        PageInfo<AnnouncementDTO> page = new PageInfo<>();
        PageInfo<Announcement> result = announcementBiz.findAll(query);
        if( result != null ){
            BeanUtils.copyProperties(result,page);
            if( result.getList() != null && !result.getList().isEmpty() ) {
                page.setList(result.getList().stream().map(this::vo2Dto).collect(Collectors.toList()));
            }
        }
        return page;
    }

    @Override
    public List<AnnouncementDTO> findForIndex(AnnouncementIndexQueryDTO query) {
        return vo2Dto(announcementBiz.findForIndex(query));
    }

    private AnnouncementDTO vo2Dto(Announcement announcement) {
        if (announcement == null) {
            return null;
        }
        AnnouncementDTO dto = new AnnouncementDTO();
        BeanUtils.copyProperties(announcement, dto);
        if(dto.getStartTime() != null && dto.getEndTime() != null){
            long now = System.currentTimeMillis();
            long begin = dto.getStartTime().getTime();
            long end = dto.getEndTime().getTime();
            //@ApiModelProperty("进行状态０未开始　3 进行中，4 已关闭")
            if( begin <= now && now <= end ){
                if(dto.getApprovalStatus()== null || dto.getApprovalStatus() == 0 || dto.getApprovalStatus() ==2){
                    dto.setProcessStatus(0);
                }else{
                    dto.setProcessStatus(3);
                }
            }else if(end <= now ){
                dto.setProcessStatus(4);
            }else{
                dto.setProcessStatus(0);
            }
        }
        return dto;
    }


    public List<AnnouncementDTO> vo2Dto(List<Announcement> announcementList) {
        List<AnnouncementDTO> dtoList = Lists.newArrayList();
        if (announcementList != null && !announcementList.isEmpty()) {
            announcementList.forEach(item -> dtoList.add(vo2Dto(item)));
        }
        return dtoList;
    }
}
