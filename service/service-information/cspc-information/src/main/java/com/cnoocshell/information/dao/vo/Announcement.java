package com.cnoocshell.information.dao.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ii_announcement")
public class Announcement implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "announcement_id")
    private String announcementId;

    /**
     * 审批状态 0待审批 1审批通过 2审批未通过
     */
    @Column(name = "approval_status")
    private Integer approvalStatus;

    /**
     * 可用状态 0启用  1暂停
     */
    @Column(name = "available_status")
    private Integer availableStatus;

    /**
     * 公告类型 0 默认  1 弹框公告 2轮播公告
     */
    @Column(name = "announcement_type")
    private Integer announcementType;

    /**
     * 标题
     */
    private String title;

    /**
     * 生效开始时间
     */
    @Column(name = "start_time")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 公告内容url
     */
    @Column(name = "content_url")
    private String contentUrl;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Integer delFlg;
    /**
     * 排序序号
     */
    private Integer weight;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建人/审批人
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 附件id
     */
    @Column(name = "attachment_ids")
    private String attachmentIds;

    /**
     * 公告内容
     */
    private String content;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return announcement_id - 主键
     */
    public String getAnnouncementId() {
        return announcementId;
    }

    /**
     * 设置主键
     *
     * @param announcementId 主键
     */
    public void setAnnouncementId(String announcementId) {
        this.announcementId = announcementId == null ? null : announcementId.trim();
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    /**
     * 获取审批状态 0待审批 1审批通过 2审批未通过
     *
     * @return approval_status - 审批状态 0待审批 1审批通过 2审批未通过
     */
    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    /**
     * 设置审批状态 0待审批 1审批通过 2审批未通过
     *
     * @param approvalStatus 审批状态 0待审批 1审批通过 2审批未通过
     */
    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    /**
     * 获取可用状态 0启用  1暂停
     *
     * @return available_status - 可用状态 0启用  1暂停
     */
    public Integer getAvailableStatus() {
        return availableStatus;
    }

    /**
     * 设置可用状态 0启用  1暂停
     *
     * @param availableStatus 可用状态 0启用  1暂停
     */
    public void setAvailableStatus(Integer availableStatus) {
        this.availableStatus = availableStatus;
    }

    /**
     * 获取公告类型 0 默认  1 弹框公告 2轮播公告
     *
     * @return announcement_type - 公告类型 0 默认  1 弹框公告 2轮播公告
     */
    public Integer getAnnouncementType() {
        return announcementType;
    }

    /**
     * 设置公告类型 0 默认  1 弹框公告 2轮播公告
     *
     * @param announcementType 公告类型 0 默认  1 弹框公告 2轮播公告
     */
    public void setAnnouncementType(Integer announcementType) {
        this.announcementType = announcementType;
    }

    /**
     * 获取标题
     *
     * @return title - 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 设置标题
     *
     * @param title 标题
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * 获取生效开始时间
     *
     * @return start_time - 生效开始时间
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * 设置生效开始时间
     *
     * @param startTime 生效开始时间
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 获取生效结束时间
     *
     * @return end_time - 生效结束时间
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * 设置生效结束时间
     *
     * @param endTime 生效结束时间
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取公告内容url
     *
     * @return content_url - 公告内容url
     */
    public String getContentUrl() {
        return contentUrl;
    }

    /**
     * 设置公告内容url
     *
     * @param contentUrl 公告内容url
     */
    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl == null ? null : contentUrl.trim();
    }

    /**
     * 获取关键字
     *
     * @return keywords - 关键字
     */
    public String getKeywords() {
        return keywords;
    }

    /**
     * 设置关键字
     *
     * @param keywords 关键字
     */
    public void setKeywords(String keywords) {
        this.keywords = keywords == null ? null : keywords.trim();
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return update_user - 修改人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人
     *
     * @param updateUser 修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取公告内容
     *
     * @return content - 公告内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置公告内容
     *
     * @param content 公告内容
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", announcementId=").append(announcementId);
        sb.append(", approvalStatus=").append(approvalStatus);
        sb.append(", availableStatus=").append(availableStatus);
        sb.append(", announcementType=").append(announcementType);
        sb.append(", title=").append(title);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", contentUrl=").append(contentUrl);
        sb.append(", keywords=").append(keywords);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", content=").append(content);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}