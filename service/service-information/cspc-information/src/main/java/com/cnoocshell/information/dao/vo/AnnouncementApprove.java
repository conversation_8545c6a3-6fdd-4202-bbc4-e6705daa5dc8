package com.cnoocshell.information.dao.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@Table(name = "ii_announcement_approve")
public class AnnouncementApprove implements Serializable {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 公告id
     */
    @Column(name = "announcement_id")
    private String announcementId;

    /**
     * 审批状态 0待审批 1审批通过 2审批未通过
     */
    @Column(name = "approval_status")
    private Integer approvalStatus;

    /**
     * 可用状态 0启用  1停用 2暂停
     */
    private String comment;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建人/审批人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间/审批时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取公告id
     *
     * @return announcement_id - 公告id
     */
    public String getAnnouncementId() {
        return announcementId;
    }

    /**
     * 设置公告id
     *
     * @param announcementId 公告id
     */
    public void setAnnouncementId(String announcementId) {
        this.announcementId = announcementId == null ? null : announcementId.trim();
    }

    /**
     * 获取审批状态 0待审批 1审批通过 2审批未通过
     *
     * @return approval_status - 审批状态 0待审批 1审批通过 2审批未通过
     */
    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    /**
     * 设置审批状态 0待审批 1审批通过 2审批未通过
     *
     * @param approvalStatus 审批状态 0待审批 1审批通过 2审批未通过
     */
    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    /**
     * 获取可用状态 0启用  1停用 2暂停
     *
     * @return comment - 可用状态 0启用  1停用 2暂停
     */
    public String getComment() {
        return comment;
    }

    /**
     * 设置可用状态 0启用  1停用 2暂停
     *
     * @param comment 可用状态 0启用  1停用 2暂停
     */
    public void setComment(String comment) {
        this.comment = comment == null ? null : comment.trim();
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人/审批人
     *
     * @return create_user - 创建人/审批人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人/审批人
     *
     * @param createUser 创建人/审批人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间/审批时间
     *
     * @return create_time - 创建时间/审批时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间/审批时间
     *
     * @param createTime 创建时间/审批时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return update_user - 修改人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人
     *
     * @param updateUser 修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", announcementId=").append(announcementId);
        sb.append(", approvalStatus=").append(approvalStatus);
        sb.append(", comment=").append(comment);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}