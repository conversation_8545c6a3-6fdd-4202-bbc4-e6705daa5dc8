server:
  port: 8086
  session:
    cookie:
      http-only: true
      secure: true
logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    com.netflix.discovery.DiscoveryClient: WARN
spring:
  application:
    name: cspc-service-information
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  profiles:
    active: ${ENV:dev}

order:
  taketime:
    mode: 0
  exception:
    retryCount: 3

mybatis:
  typeAliasesPackage: com.cnoocshell.order.dao
  mapperScanPackage: com.cnoocshell.order.dao
  mapperLocations: "classpath:/mapper/*.xml"
  configLocation: "classpath:/mybatis-config.xml"