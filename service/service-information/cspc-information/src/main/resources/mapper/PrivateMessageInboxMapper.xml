<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.information.dao.mapper.PrivateMessageInboxMapper">
  <resultMap id="BaseResultMap" type="com.cnoocshell.information.dao.vo.PrivateMessageInbox">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="receive_user_id" jdbcType="VARCHAR" property="receiveUserId" />
    <result column="receive_member_id" jdbcType="VARCHAR" property="receiveMemberId" />
    <result column="msg_type" jdbcType="VARCHAR" property="msgType" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="send_user_id" jdbcType="VARCHAR" property="sendUserId" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="read_time" jdbcType="TIMESTAMP" property="readTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap id="InboxMessageResultMap" type="com.cnoocshell.information.api.dto.internalmessage.InternalMessageDTO">
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="send_user_id" jdbcType="VARCHAR" property="senderId" />
      <result column="receive_user_id" jdbcType="VARCHAR" property="receiveUserId" />
      <result column="receive_member_id" jdbcType="VARCHAR" property="receiveMemberId" />
    <result column="status" jdbcType="INTEGER" property="readStatus" />
    <result column="update_time" jdbcType="TIMESTAMP" property="time" />
  </resultMap>
  <select id="findInboxMessage" parameterType="com.cnoocshell.information.api.dto.internalmessage.InternalMessageQueryDTO" resultMap="InboxMessageResultMap">
   select * from ii_private_message_inbox as a
      left join ii_private_message_content as b
      on a.msg_id=b.msg_id
      where a.del_flg=0 and a.msg_type=0 and (receive_user_id = #{operator} or receive_member_id = #{memberId})
    <if test="readStatus != null">
    and status = #{readStatus}
    </if>
    <if test="title != null and title!=''">
      and title like concat('%',#{title},'%')
    </if>
    ORDER by a.update_time desc
  </select>

</mapper>