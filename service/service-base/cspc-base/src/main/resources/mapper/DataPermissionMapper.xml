<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.base.dao.mapper.DataPermissionMapper">
    <resultMap id="BaseResultMap" type="com.cnoocshell.base.dao.vo.DataPermission">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="category_id" property="categoryId" jdbcType="VARCHAR"/>
        <result column="category_code" property="categoryCode" jdbcType="VARCHAR"/>
        <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
        <result column="account_id" property="accountId" jdbcType="VARCHAR"/>
        <result column="account_real_name" property="accountRealName" jdbcType="VARCHAR"/>
        <result column="goods_code" property="goodsCode" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="member_id" property="memberId" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="del_flg" property="delFlg" jdbcType="BIT"/>
    </resultMap>

    <update id="changeGoodsNameByGoodsCode">
        UPDATE
        sys_data_permission
        set
        goods_name = #{param.goodsName}
        where
        goods_code = #{param.goodsCode}
        and del_flg = 0
    </update>

    <select id="queryAccountIdsByGoodsCode" resultType="java.lang.String">
        SELECT
        account_id
        FROM
        sys_data_permission
        where
        del_flg = 0
        and goods_code = #{goodsCode}
    </select>

    <select id="queryDataPermission" resultType="com.cnoocshell.base.api.dto.DataPermissionDTO">
        select DISTINCT tb1.* from sys_data_permission tb1
        left join sys_account_role tb2 on tb1.account_id = tb2.account_id
        where tb1.del_flg = 0 and tb2.del_flg = 0
        <if test="param.memberIds != null and param.memberIds.size>0">
            AND tb1.member_id IN
            <foreach collection="param.memberIds" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>

        <if test="param.goodsCodes != null and param.goodsCodes.size>0">
            AND tb1.goods_code IN
            <foreach collection="param.goodsCodes" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>

        <if test="param.accountIds != null and param.accountIds.size>0">
            AND tb1.account_id IN
            <foreach collection="param.accountIds" item="item3" open="(" separator="," close=")">
                #{item3}
            </foreach>
        </if>

        <if test="param.roleCodes != null and param.roleCodes.size>0">
            AND tb2.role_code IN
            <foreach collection="param.roleCodes" item="item4" open="(" separator="," close=")">
                #{item4}
            </foreach>
        </if>
    </select>

    <select id="accountReport" resultType="com.cnoocshell.base.api.dto.report.AccountReportResultDTO">
        select
        tb1.member_id ,
        tb1.account_id ,
        tb1.category_id AS 'categoryIdLevelTwo',
        tb1.category_code AS 'categoryCodeLevelTwo',
        tb1.goods_code ,
        tb1.goods_name
        from
        sys_data_permission tb1
        where
        tb1.del_flg = 0
        <!-- 客户编码 -->
        <if test="param.memberIds != null and param.memberIds.size > 0">
            and tb1.member_id IN
            <foreach collection="param.memberIds" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <!-- 客户名称 -->
        <if test="param.accountIds != null and param.accountIds.size > 0">
            and tb1.account_id IN
            <foreach collection="param.accountIds" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>
        <!-- 角色查询 -->
        <if test="param.role != null and param.role !=''">
            and EXISTS (select 1
            from sys_account_role tb2
            left join sys_role tb3 on tb2.role_code =tb3.role_code and tb3.del_flg =0
            where tb2.del_flg =0
            and tb2.account_id =tb1.account_id
            and (tb2.role_code like CONCAT('%',#{param.role},'%') or
            tb3.role_name like CONCAT('%',#{param.role},'%')
            )
            )
        </if>
        <if test="param.categoryCodes != null and param.categoryCodes.size > 0">
            AND tb1.category_code IN
            <foreach collection="param.categoryCodes" item="item3" open="(" separator="," close=")">
                #{item3}
            </foreach>
        </if>
        <if test="param.goodsName != null and param.goodsName !=''">
            AND tb1.goods_name like CONCAT('%',#{param.goodsName},'%')
        </if>
        <if test="param.goodsCodeBySapMaterialCode != null and param.goodsCodeBySapMaterialCode.size > 0">
            AND tb1.goods_code IN
            <foreach collection="param.goodsCodeBySapMaterialCode" item="item4" open="(" separator="," close=")">
                #{item4}
            </foreach>
        </if>
        <if test="param.memberIdConcatGoodsCodeBySaleChannel != null and param.memberIdConcatGoodsCodeBySaleChannel.size > 0">
            AND CONCAT(tb1.member_id,'_',tb1.goods_code) IN
            <foreach collection="param.memberIdConcatGoodsCodeBySaleChannel" item="item5" open="(" separator="," close=")">
                #{item5}
            </foreach>
        </if>
    </select>

</mapper>