<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.base.dao.mapper.SysInterfacePermissionMapper">
    <select id="listByPublic" resultType="com.cnoocshell.common.dto.InterfacePermissionDTO">
        select * from sys_interface_permission
        where del_flg=0
        <if test="isPublic != null">
            AND is_public=#{isPublic}
        </if>
    </select>
</mapper>