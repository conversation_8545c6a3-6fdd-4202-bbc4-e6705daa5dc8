server:
  port: 8084
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: cspc-service-base
  profiles:
    active: ${ENV:local}
  cloud:
    nacos:
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
      config:
        profile: ${spring.application.active:local}
        namespace: ${NACOS_NAMESPACE:cnoocshell-local}
        server-addr: ${NACOS_SERVER_ADDR:27.40.98.108:8848} # Nacos 服务器地址
        file-extension: yaml          # 配置文件扩展名
        file—prefix: ${spring.application.name:local}
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}  # Nacos 服务器地址

mybatis:
  typeAliasesPackage: com.cnoocshell.base.dao
  mapperScanPackage: com.cnoocshell.base.dao
  mapperLocations: "classpath:/mapper/*.xml"
  configLocation: "classpath:/mybatis-config.xml"

logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    root: info
swagger:
  author: cnoocshell
  title: cnoocshell
  basePackage: com.cnoocshell.web.base.controller
management:
  endpoint:
    health:
      show-details: always
  security:
    enabled: false
  endpoints:
    health:
      sensitive: false
    web:
      exposure:
        include: "*"

