package com.cnoocshell.base.controller;

import cn.hutool.core.bean.BeanUtil;
import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.base.api.dto.role.AccountRoleDTO;
import com.cnoocshell.base.biz.impl.DataPermissionBiz;
import com.cnoocshell.base.dao.vo.DataPermission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.cnoocshell.base.exception.DuplicateString.DEL_FLG;

@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest(classes = RoleControllerTest.class,webEnvironment = SpringBootTest.WebEnvironment.NONE)
public class RoleControllerTest {

    @Resource
    private DataPermissionBiz dataPermissionBiz;

    @Test
    public void test() {
        AccountRoleDTO arg0 = new AccountRoleDTO();
        arg0.setAccountId("test");
        List<DataPermissionDTO> ddd = new ArrayList<>();
        DataPermissionDTO dto1 = new DataPermissionDTO();
        dto1.setAccountId("test");
        dto1.setCategoryCode("categorycode1");
        dto1.setCategoryId("categoryid1");
        dto1.setCategoryName("categoryname1");
        dto1.setAccountRealName("realname");
        ddd.add(dto1);

        DataPermissionDTO dto2 = new DataPermissionDTO();
        dto2.setAccountId("test");
        dto2.setCategoryCode("categorycode2");
        dto2.setCategoryId("categoryid2");
        dto2.setCategoryName("categoryname2");
        dto2.setAccountRealName("realname");
        ddd.add(dto2);
        arg0.setDataPermissionDTOList(ddd);

        // 先清理之前的数据，（如果，DataPermissionDTOList 空依然删除，原有数据，表示不对任何分类授权）
        DataPermission dataPermission = new DataPermission();
        dataPermission.setDelFlg(true);
        Example example = new Example(DataPermission.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(DEL_FLG, Boolean.FALSE)
                .andEqualTo("accountId", arg0.getAccountId());
        // 逻辑删除原有分类，保存原有授权数据
        dataPermissionBiz.getMapper().updateByExampleSelective(dataPermission,criteria);

        // 添加新的分类授权
        if (CollectionUtils.isNotEmpty(arg0.getDataPermissionDTOList())){
            List<DataPermission> dataPermissionList = BeanUtil.copyToList(arg0.getDataPermissionDTOList(),
                    DataPermission.class);
            Assert.assertNotNull(dataPermissionBiz.batchInsert(dataPermissionList));
        }
    }
}
