spring:
  main:
    #该项配置 似乎配置中心配置无效
    allow-bean-definition-overriding: true
logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    com.netflix.discovery.DiscoveryClient: WARN
mybatis:
  typeAliasesPackage: com.cnoocshell.goods.dao
  mapperScanPackage: com.cnoocshell.goods.dao
  mapperLocations: "classpath:/mapper/*.xml"
  configLocation: "classpath:/mybatis-config.xml"