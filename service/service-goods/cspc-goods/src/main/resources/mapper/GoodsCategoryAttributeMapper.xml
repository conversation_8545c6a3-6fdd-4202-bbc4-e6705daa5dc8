<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.GoodsCategoryAttributeMapper">
  <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.GoodsCategoryAttribute">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="category_attribute_id" jdbcType="VARCHAR" property="categoryAttributeId" />
    <result column="goods_attri_id" jdbcType="VARCHAR" property="goodsAttriId" />
    <result column="attri_name" jdbcType="VARCHAR" property="attriName" />
    <result column="value_type" jdbcType="TINYINT" property="valueType" />
    <result column="multi_select" jdbcType="TINYINT" property="multiSelect" />
    <result column="editable" jdbcType="TINYINT" property="editable" />
    <result column="spu" jdbcType="BIT" property="spu" />
    <result column="sort" jdbcType="TINYINT" property="sort" />
    <result column="is_require" jdbcType="BIT" property="isRequire" />
    <result column="table_field" jdbcType="VARCHAR" property="tableField" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>