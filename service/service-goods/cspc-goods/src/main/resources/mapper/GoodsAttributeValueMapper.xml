<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.GoodsAttributeValueMapper">
  <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.GoodsAttributeValue">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="value_id" jdbcType="VARCHAR" property="valueId" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_attri_id" jdbcType="VARCHAR" property="goodsAttriId" />
    <result column="attri_name" jdbcType="VARCHAR" property="attriName" />
    <result column="attri_value_code" jdbcType="VARCHAR" property="attriValueCode" />
    <result column="attri_value" jdbcType="VARCHAR" property="attriValue" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>