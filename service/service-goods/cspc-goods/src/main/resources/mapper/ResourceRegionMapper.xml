<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.ResourceRegionMapper">
  <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.ResourceRegion">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="resource_region_id" jdbcType="VARCHAR" property="resourceRegionId" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="resource_version" jdbcType="INTEGER" property="resourceVersion" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="country_name" jdbcType="VARCHAR" property="countryName" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="street_code" jdbcType="LONGVARCHAR" property="streetCode" />
    <result column="street_name" jdbcType="LONGVARCHAR" property="streetName" />
  </resultMap>
</mapper>