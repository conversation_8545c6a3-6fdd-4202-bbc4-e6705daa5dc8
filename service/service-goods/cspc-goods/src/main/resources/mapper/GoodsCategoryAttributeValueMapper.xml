<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.GoodsCategoryAttributeValueMapper">
  <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.GoodsCategoryAttributeValue">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="attribute_value_id" jdbcType="VARCHAR" property="attributeValueId" />
    <result column="attribute_id" jdbcType="VARCHAR" property="attributeId" />
    <result column="attribute_name" jdbcType="VARCHAR" property="attributeName" />
    <result column="attribute_value_code" jdbcType="VARCHAR" property="attributeValueCode" />
    <result column="attribute_value" jdbcType="VARCHAR" property="attributeValue" />
    <result column="sort" jdbcType="TINYINT" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>