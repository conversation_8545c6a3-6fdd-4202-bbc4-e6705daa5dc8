<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.GoodsResourceMapper">
  <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.GoodsResource">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="goods_resource_id" jdbcType="VARCHAR" property="goodsResourceId" />
    <result column="goods_attributes" jdbcType="VARCHAR" property="goodsAttributes" />
    <result column="goods_regions" jdbcType="VARCHAR" property="goodsRegions" />
    <result column="goods_regions2" jdbcType="VARCHAR" property="goodsRegions2" />
    <result column="goods_resources" jdbcType="VARCHAR" property="goodsResources" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_type" jdbcType="TINYINT" property="goodsType" />
    <result column="search_keywords" jdbcType="VARCHAR" property="searchKeywords" />
    <result column="goods_status" jdbcType="TINYINT" property="goodsStatus" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="measure_unit" jdbcType="VARCHAR" property="measureUnit" />
    <result column="pack" jdbcType="VARCHAR" property="pack" />
    <result column="specs" jdbcType="VARCHAR" property="specs" />
    <result column="mark" jdbcType="VARCHAR" property="mark" />
    <result column="concrete_class" jdbcType="VARCHAR" property="concreteClass" />
    <result column="strength" jdbcType="VARCHAR" property="strength" />
    <result column="slump" jdbcType="VARCHAR" property="slump" />
    <result column="quality_standard" jdbcType="VARCHAR" property="qualityStandard" />
    <result column="use_range" jdbcType="VARCHAR" property="useRange" />
    <result column="logistics" jdbcType="VARCHAR" property="logistics" />
    <result column="pricing_mode" jdbcType="TINYINT" property="pricingMode" />
    <result column="delivery_mode" jdbcType="VARCHAR" property="deliveryMode" />
    <result column="category_type" jdbcType="TINYINT" property="categoryType" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="category1" jdbcType="VARCHAR" property="category1" />
    <result column="category2" jdbcType="VARCHAR" property="category2" />
    <result column="category3" jdbcType="VARCHAR" property="category3" />
    <result column="category4" jdbcType="VARCHAR" property="category4" />
    <result column="imgs" jdbcType="VARCHAR" property="imgs" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="memo1" jdbcType="LONGVARCHAR" property="memo1" />
    <result column="memo2" jdbcType="LONGVARCHAR" property="memo2" />
    <result column="memo3" jdbcType="LONGVARCHAR" property="memo3" />
    <result column="memo4" jdbcType="LONGVARCHAR" property="memo4" />
    <result column="memo5" jdbcType="LONGVARCHAR" property="memo5" />
    <result column="memo6" jdbcType="LONGVARCHAR" property="memo6" />
    <result column="app_memo1" jdbcType="LONGVARCHAR" property="appMemo1" />
    <result column="app_memo2" jdbcType="LONGVARCHAR" property="appMemo2" />
    <result column="app_memo3" jdbcType="LONGVARCHAR" property="appMemo3" />
    <result column="app_memo4" jdbcType="LONGVARCHAR" property="appMemo4" />
    <result column="app_memo5" jdbcType="LONGVARCHAR" property="appMemo5" />
    <result column="app_memo6" jdbcType="LONGVARCHAR" property="appMemo6" />
  </resultMap>
</mapper>