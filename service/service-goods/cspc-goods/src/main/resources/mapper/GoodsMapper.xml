<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.GoodsMapper">
  <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.Goods">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="commodity_code" jdbcType="VARCHAR" property="commodityCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_type" jdbcType="TINYINT" property="goodsType" />
    <result column="search_keywords" jdbcType="VARCHAR" property="searchKeywords" />
    <result column="goods_status" jdbcType="TINYINT" property="goodsStatus" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="measure_unit" jdbcType="VARCHAR" property="measureUnit" />
    <result column="pack" jdbcType="VARCHAR" property="pack" />
    <result column="specs" jdbcType="VARCHAR" property="specs" />
    <result column="mark" jdbcType="VARCHAR" property="mark" />
    <result column="logistics" jdbcType="VARCHAR" property="logistics" />
    <result column="cartage_rule" jdbcType="VARCHAR" property="cartageRule" />
    <result column="pricing_mode" jdbcType="TINYINT" property="pricingMode" />
    <result column="delivery_mode" jdbcType="VARCHAR" property="deliveryMode" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="size" jdbcType="VARCHAR" property="size" />
    <result column="imgs" jdbcType="VARCHAR" property="imgs" />
    <result column="sap_factory_code" jdbcType="VARCHAR" property="sapFactoryCode" />
    <result column="sap_material_code" jdbcType="VARCHAR" property="sapMaterialCode" />
    <result column="deliver_cost_standard" jdbcType="VARCHAR" property="deliverCostStandard" />
    <result column="self_pickup_guide" jdbcType="VARCHAR" property="selfPickupGuide" />
    <result column="self_pickup_carrier" jdbcType="VARCHAR" property="selfPickupCarrier" />
    <result column="document_ids;" jdbcType="VARCHAR" property="documentIds" />
    <result column="pc_application_area" jdbcType="VARCHAR" property="pcApplicationArea" />
    <result column="app_application_area" jdbcType="VARCHAR" property="appApplicationArea" />
    <result column="sales_group" jdbcType="VARCHAR" property="salesGroup" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>

    <select id="selectGoodsById" resultType="com.cnoocshell.goods.dao.vo.Goods">
        SELECT * FROM go_goods
        WHERE goods_id =#{goodsId}
    </select>

  <select id="goodsCommonQuery" parameterType="com.cnoocshell.goods.api.dto.GoodsQueryCondDTO" resultMap="BaseResultMap">
    select
           gs.*
    from go_goods gs
    where gs.del_flg = 0
    <if test="goodsStatus != null">
      and gs.goods_status = #{goodsStatus}
    </if>
    <if test="goodsNameLike != null and goodsNameLike != ''">
      and gs.goods_name like concat('%', #{goodsNameLike}, '%')
    </if>
    <if test="goodsType != null">
      and gs.goods_type = #{goodsType}
    </if>
    <if test="categoryType != null">
      and gs.category_type = #{categoryType}
    </if>
    <if test="logistics != null and logistics !=''">
      and gs.logistics = #{logistics}
    </if>
    <if test="categoryCode != null and categoryCode != ''">
      and gs.category_code like concat(#{categoryCode}, '%')
    </if>
    <if test="categoryCodeNotLike != null ">
      <foreach collection="categoryCodeNotLike" item="item">
        and gs.category_code not like concat(#{item}, '%')
      </foreach>
    </if>
    order by gs.update_time desc
  </select>


  <!-- 经销商查询厂家商品，厂家商品不能是其他厂家的引用商品-->
  <select id="listSellerGoodsIdAndName" parameterType="com.cnoocshell.goods.api.dto.SellerGoodsIdAndNameDTO" resultType="com.cnoocshell.goods.api.dto.SellerGoodsIdAndNameDTO">
    select gs.goods_id as goodsId,gs.goods_name as goodsName from go_goods gs
    where gs.del_flg = 0 and gs.seller_id = #{sellerId}
    and (ref_goods_id is null or length(ref_goods_id) = 0 )
    <if test="goodsName != null and goodsName != ''">
      and gs.goods_name like concat('%', #{goodsName}, '%')
    </if>
    <if test="goodsType != null">
      and gs.goods_type = #{goodsType}
    </if>
    <if test="categoryCode != null and categoryCode != ''">
      and gs.category_code like concat(#{categoryCode}, '%')
    </if>
    order by gs.update_time desc limit 500
  </select>

  <!--获取商品种类下拉列表-->
  <select id="queryGoodsNameListDropDownBox" resultType="com.cnoocshell.goods.api.dto.GoodsBaseDTO">
    SELECT DISTINCT goods_name AS goodsName FROM go_goods
    WHERE del_flg = 0
    <if test="goodsName != null and goodsName != ''">
      AND goods_name LIKE CONCAT('%', #{goodsName}, '%')
    </if>
  </select>
    <select id="pageSellerGoods" resultType="com.cnoocshell.goods.api.dto.SellerGoodsDTO"
            parameterType="com.cnoocshell.goods.api.dto.PageSellerGoodsDTO">
        SELECT
            g.goods_id,
            g.goods_name,
            g.brand,
            g.unit,
            g.specs,
            g.goods_type,
            g.goods_status,
            g.sap_material_code,
            g.update_time,
            g.pack,
            GROUP_CONCAT(DISTINCT c.category_name) as category_name,
            GROUP_CONCAT(DISTINCT c.category_name) as spuCategories
        FROM
            go_goods g
            LEFT JOIN go_goods_category_relation r  ON g.goods_id = r.goods_id or g.spu_id = r.goods_id
            LEFT JOIN go_goods_category c ON r.category_id = c.category_id
        where
            g.del_flg = 0 and g.goods_type = 2
            <if test="goodsStatus != null">
                and g.goods_status = #{goodsStatus}
            </if>
            <if test="goodsCode != null and goodsCode != '' ">
                and g.goods_code = #{goodsCode}
            </if>
            <if test="searchKeywords != null and searchKeywords!= ''">
                and g.search_keywords LIKE CONCAT('%',#{searchKeywords},'%')
            </if>
            <if test="goodsName != null and goodsName!= ''">
                and g.goods_name LIKE CONCAT('%',#{goodsName},'%')
            </if>
            <if test="categoryCode != null and categoryCode!= ''">
                and INSTR( r.category_string, #{categoryCode} )
            </if>
            <if test="categoryCodeTwo != null and categoryCodeTwo!= ''">
                and INSTR( c.category_code, #{categoryCodeTwo} )
            </if>
        group by g.goods_id
        <choose>
            <when test="brandOrderFlag!= null and brandOrderFlag==0">
                order by CONVERT(g.brand USING GBK) ASC
            </when>
            <when test="brandOrderFlag!= null and brandOrderFlag==0">
                order by CONVERT(g.brand USING GBK) desc
            </when>
            <otherwise>
                ORDER BY g.update_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="pageGoods1" resultType="com.cnoocshell.goods.api.dto.SellerGoodsDTO">
        select
            g.goods_id,
            g.goods_name,
            g.brand,
            g.unit,
            g.specs,
            g.goods_type,
            g.goods_status,
            g.sap_material_code,
            g.update_time,
            g.pack,
            g.delivery_mode,
            GROUP_CONCAT(DISTINCT c.category_name) as category_name,
            GROUP_CONCAT(DISTINCT c.category_name) as spuCategories
        from go_resource t1
                 left join go_goods g on t1.goods_id  = g.goods_id
                 LEFT JOIN go_goods_category_relation r  ON g.goods_id = r.goods_id or g.spu_id = r.goods_id
                 LEFT JOIN go_goods_category c ON r.category_id = c.category_id
        where t1.status = 100 and g.del_flg = 0 and g.goods_type = 2
            <if test="goodsName != null and goodsName!= ''">
                and g.goods_name LIKE CONCAT('%',#{goodsName},'%')
            </if>
            <if test="categoryCodeTwo != null and categoryCodeTwo!= ''">
                and INSTR( c.category_code, #{categoryCodeTwo} )
            </if>
            <if test="goodsIds != null and goodsIds.size() > 0">
                and g.goods_id not in
                <foreach item="goodsId" collection="goodsIds" open="(" separator="," close=")">
                    #{goodsId}
                </foreach>
            </if>
        group by g.goods_id
        ORDER BY CONVERT( g.goods_name  USING gbk) COLLATE gbk_chinese_ci
    </select>

    <select id="pageGoods" resultType="com.cnoocshell.goods.api.dto.SellerGoodsDTO">
        select tb0.* from (
        select tb1.* from (select
        g.goods_id,
        g.goods_name,
        g.brand,
        g.unit,
        g.specs,
        g.goods_type,
        g.goods_status,
        g.sap_material_code,
        g.update_time,
        g.pack,
        g.delivery_mode,
        GROUP_CONCAT(DISTINCT c.category_name) as category_name,
        GROUP_CONCAT(DISTINCT c.category_name) as spuCategories
        from go_resource t1
        left join go_goods g on t1.goods_id = g.goods_id
        LEFT JOIN go_goods_category_relation r ON g.goods_id = r.goods_id or g.spu_id = r.goods_id
        LEFT JOIN go_goods_category c ON r.category_id = c.category_id
        where t1.status = 100 and g.del_flg = 0 and g.goods_type = 2
        and g.goods_id IN
        <foreach collection="param.allIntentionGoodsIds" item="item0" open="(" separator="," close=")">
            #{item0}
        </foreach>

        <if test="param.goodsName != null and param.goodsName!= ''">
            and g.goods_name LIKE CONCAT('%',#{param.goodsName},'%')
        </if>
        <if test="param.categoryCodeTwo != null and param.categoryCodeTwo!= ''">
            and INSTR( c.category_code, #{param.categoryCodeTwo} )
        </if>
        <if test="param.goodsIds != null and param.goodsIds.size() > 0">
            and g.goods_id not in
            <foreach item="item4" collection="param.goodsIds" open="(" separator="," close=")">
                #{item4}
            </foreach>
        </if>

        group by g.goods_id
        ORDER BY
        <if test="param.mainIntentionGoodsIds != null and param.mainIntentionGoodsIds.size>0">
            CASE WHEN g.goods_id IN
            <foreach collection="param.mainIntentionGoodsIds" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
            THEN 0
            ELSE 1
            END,
        </if>
        CONVERT( g.goods_name USING gbk) COLLATE gbk_chinese_ci ASC) tb1

        UNION ALL

        select tb2.* from (select
        g.goods_id,
        g.goods_name,
        g.brand,
        g.unit,
        g.specs,
        g.goods_type,
        g.goods_status,
        g.sap_material_code,
        g.update_time,
        g.pack,
        g.delivery_mode,
        GROUP_CONCAT(DISTINCT c.category_name) as category_name,
        GROUP_CONCAT(DISTINCT c.category_name) as spuCategories
        from go_resource t1
        left join go_goods g on t1.goods_id = g.goods_id
        LEFT JOIN go_goods_category_relation r ON g.goods_id = r.goods_id or g.spu_id = r.goods_id
        LEFT JOIN go_goods_category c ON r.category_id = c.category_id
        where t1.status = 100 and g.del_flg = 0 and g.goods_type = 2
        AND g.goods_id not IN
        <foreach collection="param.allIntentionGoodsIds" item="item3" open="(" separator="," close=")">
            #{item3}
        </foreach>

        <if test="param.goodsName != null and param.goodsName!= ''">
            and g.goods_name LIKE CONCAT('%',#{param.goodsName},'%')
        </if>
        <if test="param.categoryCodeTwo != null and param.categoryCodeTwo!= ''">
            and INSTR( c.category_code, #{param.categoryCodeTwo} )
        </if>
        <if test="param.goodsIds != null and param.goodsIds.size() > 0">
            and g.goods_id not in
            <foreach item="item5" collection="param.goodsIds" open="(" separator="," close=")">
                #{item5}
            </foreach>
        </if>
        group by g.goods_id
        ORDER BY CONVERT( g.goods_name USING gbk) COLLATE gbk_chinese_ci
        ) tb2
        ) tb0
    </select>




    <select id="selectGoodsByCategoryIds" resultType="com.cnoocshell.goods.api.dto.SellerGoodsDTO">
        select * from go_goods where goods_id in (
            select goods_id from go_goods_category_relation where category_id = #{categoryId}
        )
    </select>

    <select id="findGoodsSimpleByIds" resultType="com.cnoocshell.goods.api.dto.GoodsSimpleDTO">
        select goods_id,goods_code,goods_name,goods_type
        from go_goods
        where del_flg=0
        and goods_id in
        <foreach collection="goodsIds" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
    </select>

    <select id="findGoodsSimpleByCategoryId" resultType="com.cnoocshell.goods.api.dto.GoodsSimpleDTO">
        select * from go_goods where goods_id in (
            select goods_id from go_goods_category_relation
            where category_id in
            <foreach collection="categoryIds" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        ) and del_flg = false
    </select>

    <select id="queryGoodsNameByGoodsCode" resultType="com.cnoocshell.goods.api.dto.GoodsNameInfoDTO">
        SELECT
        goods_name,
        goods_code,
        goods_id
        FROM
        go_goods
        where
        goods_id in
        <foreach item="item" index="index" collection="dto.goodsIds" open="(" separator="," close=" )">
            #{item}
        </foreach>
        and del_flg =0
    </select>

    <select id="buyerPageGoods" resultType="com.cnoocshell.goods.api.dto.SellerGoodsDTO">
        select
        g.goods_id,
        g.goods_name,
        g.brand,
        g.unit,
        g.specs,
        g.goods_type,
        g.goods_status,
        g.sap_material_code,
        g.update_time,
        g.pack,
        g.delivery_mode,
        GROUP_CONCAT(DISTINCT c.category_name) as category_name,
        GROUP_CONCAT(DISTINCT c.category_name) as spuCategories
        from go_resource t1
        left join go_goods g on t1.goods_id = g.goods_id
        LEFT JOIN go_goods_category_relation r ON g.goods_id = r.goods_id or g.spu_id = r.goods_id
        LEFT JOIN go_goods_category c ON r.category_id = c.category_id
        where t1.status = 100 and g.del_flg = 0 and g.goods_type = 2
        <if test="goodsName != null and goodsName!= ''">
            and g.goods_name LIKE CONCAT('%',#{goodsName},'%')
        </if>
        <if test="categoryCodeTwo != null and categoryCodeTwo!= ''">
            and INSTR( c.category_code, #{categoryCodeTwo} )
        </if>
        <if test="goodsIds != null and goodsIds.size() > 0">
            and g.goods_id in
            <foreach item="goodsId" collection="goodsIds" open="(" separator="," close=")">
                #{goodsId}
            </foreach>
        </if>
        group by g.goods_id
        ORDER BY CONVERT( g.goods_name USING gbk) COLLATE gbk_chinese_ci
    </select>

    <select id="getPackSalesGroupByGoodsCode" resultType="com.cnoocshell.goods.api.dto.goods.GoodsDataListDTO">
        SELECT
        goods_code ,
        pack,
        sales_group
        FROM
        go_goods
        where
        del_flg = 0
        and goods_code in
        <foreach item="item" index="index" collection="goodsCodeList" open="(" separator="," close=" )">
            #{item}
        </foreach>
    </select>

    <select id="queryGoodsAndCategoryInfoByGoodsCodes"
            resultType="com.cnoocshell.goods.api.dto.goods.GoodsAndCategoryInfoResultDTO">
        select tb4.category_code as 'categoryCodeLevelOne',
        tb4.category_name as 'categoryNameLevelOne',
        tb3.category_code as 'categoryCodeLevelTwo',
        tb3.category_name as 'categoryNameLevelTwo',
        tb1.goods_code,
        tb1.goods_name,
        tb1.sap_material_code
        from go_goods tb1
        left join go_goods_category_relation tb2 on tb1.goods_id = tb2.goods_id
        left join go_goods_category tb3 on tb2.category_id = tb3.category_id and tb3.del_flg = 0
        left join go_goods_category tb4 on tb3.parent_id = tb4.category_id and tb4.del_flg = 0
        where tb1.del_flg = 0
        <if test="goodsCodes != null and goodsCodes.size > 0">
            and tb1.goods_code in
            <foreach collection="goodsCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>