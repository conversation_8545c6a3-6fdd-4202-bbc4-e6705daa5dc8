<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.ResourceHistoryMapper">
  <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.ResourceHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="resource_history_id" jdbcType="VARCHAR" property="resourceHistoryId" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="resource_attributes" jdbcType="VARCHAR" property="resourceAttributes" />
    <result column="resource_regions" jdbcType="VARCHAR" property="resourceRegions" />
    <result column="goods_resource_id" jdbcType="VARCHAR" property="goodsResourceId" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_type" jdbcType="VARCHAR" property="goodsType" />
    <result column="machine_rule_id" jdbcType="VARCHAR" property="machineRuleId" />
    <result column="empty_load_rule_id" jdbcType="VARCHAR" property="emptyLoadRuleId" />
    <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
    <result column="goods_describe" jdbcType="VARCHAR" property="goodsDescribe" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
    <result column="resource_code" jdbcType="VARCHAR" property="resourceCode" />
    <result column="effect_time" jdbcType="TIMESTAMP" property="effectTime" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="currency_symbol" jdbcType="VARCHAR" property="currencySymbol" />
    <result column="currency_name" jdbcType="VARCHAR" property="currencyName" />
    <result column="price_describe" jdbcType="VARCHAR" property="priceDescribe" />
    <result column="price_way" jdbcType="VARCHAR" property="priceWay" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="if_protocol_price" jdbcType="BIT" property="ifProtocolPrice" />
    <result column="price_unit" jdbcType="VARCHAR" property="priceUnit" />
    <result column="sale_unit" jdbcType="VARCHAR" property="saleUnit" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="store_address" jdbcType="VARCHAR" property="storeAddress" />
    <result column="resource_version" jdbcType="INTEGER" property="resourceVersion" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="approval_message" jdbcType="VARCHAR" property="approvalMessage" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="seller_nick_name" jdbcType="VARCHAR" property="sellerNickName" />
    <result column="sales_id" jdbcType="VARCHAR" property="salesId" />
    <result column="sales_name" jdbcType="VARCHAR" property="salesName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="sale_num" jdbcType="DECIMAL" property="saleNum" />
    <result column="cansale_num" jdbcType="DECIMAL" property="cansaleNum" />
    <result column="lock_num" jdbcType="DECIMAL" property="lockNum" />
    <result column="volume_num" jdbcType="DECIMAL" property="volumeNum" />
    <result column="tolerance_type" jdbcType="VARCHAR" property="toleranceType" />
    <result column="tolerance" jdbcType="DECIMAL" property="tolerance" />
    <result column="ordermax_num" jdbcType="DECIMAL" property="ordermaxNum" />
    <result column="ordermin_num" jdbcType="DECIMAL" property="orderminNum" />
    <result column="orderminchange_num" jdbcType="DECIMAL" property="orderminchangeNum" />
    <result column="daymax_num" jdbcType="DECIMAL" property="daymaxNum" />
    <result column="area_level" jdbcType="INTEGER" property="areaLevel" />
    <result column="if_take_self" jdbcType="BIT" property="ifTakeSelf" />
    <result column="if_platform_delivery" jdbcType="BIT" property="ifPlatformDelivery" />
    <result column="if_seller_delivery" jdbcType="BIT" property="ifSellerDelivery" />
    <result column="take_self_discounts" jdbcType="DECIMAL" property="takeSelfDiscounts" />
    <result column="logistics_weight" jdbcType="DECIMAL" property="logisticsWeight" />
    <result column="logistics_type" jdbcType="VARCHAR" property="logisticsType" />
    <result column="logistics_unit" jdbcType="VARCHAR" property="logisticsUnit" />
    <result column="logistics_price" jdbcType="DECIMAL" property="logisticsPrice" />
    <result column="cartage_rule_id" jdbcType="VARCHAR" property="cartageRuleId" />
    <result column="pay_way" jdbcType="VARCHAR" property="payWay" />
    <result column="ifup" jdbcType="BIT" property="ifup" />
    <result column="fix_uptime" jdbcType="TIMESTAMP" property="fixUptime" />
    <result column="ifdown" jdbcType="BIT" property="ifdown" />
    <result column="fix_downtime" jdbcType="TIMESTAMP" property="fixDowntime" />
    <result column="up_time" jdbcType="TIMESTAMP" property="upTime" />
    <result column="down_time" jdbcType="TIMESTAMP" property="downTime" />
    <result column="trade_starttime" jdbcType="TIMESTAMP" property="tradeStarttime" />
    <result column="trade_endtime" jdbcType="TIMESTAMP" property="tradeEndtime" />
    <result column="if_flowcontrol" jdbcType="BIT" property="ifFlowcontrol" />
    <result column="allow_partial" jdbcType="BIT" property="allowPartial" />
    <result column="sale_area" jdbcType="VARCHAR" property="saleArea" />
    <result column="sale_area_code" jdbcType="VARCHAR" property="saleAreaCode" />
    <result column="sale_area_code2" jdbcType="VARCHAR" property="saleAreaCode2" />
    <result column="sale_area_code3" jdbcType="VARCHAR" property="saleAreaCode3" />
    <result column="sale_area_code4" jdbcType="VARCHAR" property="saleAreaCode4" />
    <result column="sale_area_code5" jdbcType="VARCHAR" property="saleAreaCode5" />
    <result column="sale_area_name" jdbcType="VARCHAR" property="saleAreaName" />
    <result column="sale_area_name2" jdbcType="VARCHAR" property="saleAreaName2" />
    <result column="sale_area_name3" jdbcType="VARCHAR" property="saleAreaName3" />
    <result column="sale_area_name4" jdbcType="VARCHAR" property="saleAreaName4" />
    <result column="sale_area_name5" jdbcType="VARCHAR" property="saleAreaName5" />
    <result column="paydate_type" jdbcType="VARCHAR" property="paydateType" />
    <result column="paydate_limit" jdbcType="BIGINT" property="paydateLimit" />
    <result column="takedate_type" jdbcType="VARCHAR" property="takedateType" />
    <result column="takedate_limit" jdbcType="BIGINT" property="takedateLimit" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>