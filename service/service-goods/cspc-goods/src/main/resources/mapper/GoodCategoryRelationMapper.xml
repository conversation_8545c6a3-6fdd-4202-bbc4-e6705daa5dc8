<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.GoodCategoryRelationMapper">
    <select id="queryRelationByCategoryCodes"
            resultType="com.cnoocshell.goods.api.dto.relation.GoodsCategoryRelationSimpleDTO">
        select
        DISTINCT
        tb1.category_id ,tb1.goods_id ,tb2.category_code ,tb3.goods_code
        FROM
        go_goods_category_relation tb1
        left join go_goods_category tb2 on tb1.category_id =tb2.category_id and tb2.del_flg =0
        left join go_goods tb3 on tb1.goods_id =tb3.goods_id and tb3.del_flg =0
        where
        tb3.goods_code is not null
        <if test="categoryCodes != null and categoryCodes.size > 0">
            and tb2.category_code in
            <foreach collection="categoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>
</mapper>