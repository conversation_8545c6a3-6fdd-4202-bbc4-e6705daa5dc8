<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.ResourceMapper">
  <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.Resource">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="resource_attributes" jdbcType="VARCHAR" property="resourceAttributes" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_describe" jdbcType="VARCHAR" property="goodsDescribe" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
    <result column="resource_code" jdbcType="VARCHAR" property="resourceCode" />
    <result column="price_unit" jdbcType="VARCHAR" property="priceUnit" />
    <result column="sale_unit" jdbcType="VARCHAR" property="saleUnit" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="seller_nick_name" jdbcType="VARCHAR" property="sellerNickName" />
    <result column="ifup" jdbcType="BIT" property="ifup" />
    <result column="ifdown" jdbcType="BIT" property="ifdown" />
    <result column="up_time" jdbcType="TIMESTAMP" property="upTime" />
    <result column="down_time" jdbcType="TIMESTAMP" property="downTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

	<select id="searchResourceDetail" parameterType="com.cnoocshell.goods.api.dto.ResourceDetailSearchDTO" resultMap="BaseResultMap">
		SELECT
		gr.*
		FROM
		go_resource gr
		INNER JOIN go_resource_region rregion ON
		rregion.resource_id =
		gr.resource_id
		WHERE
		gr.del_flg = '0'
		<if test="goodsResourceId != null">
			AND gr.goods_resource_id = #{goodsResourceId}
		</if>
		<if test="countryName != null and countryName != ''">
			AND (rregion.country_name = #{countryName} or
			rregion.country_name is
			null)
		</if>
		<if test="provinceName != null and provinceName != ''">
			AND (rregion.province_name = #{provinceName} or
			rregion.province_name is
			null)
		</if>
		<if test="cityName != null and cityName != ''">
			AND (rregion.city_name = #{cityName} or rregion.city_name
			is null)
		</if>
		<if test="areaName != null and areaName != ''">
			AND (rregion.area_name = #{areaName} or rregion.area_name
			is null)
		</if>
		<if test="streetName != null and streetName != ''">
			AND (rregion.street_name = #{streetName} or
			rregion.street_name is null)
		</if>

		<if test="attributes != null">
			<foreach collection="attributes" item="attribute">
				<if test="attribute.attributeValueDTOs != null">
					<foreach collection="attribute.attributeValueDTOs" item="attributeValue"> AND gr.resource_attributes LIKE
						'%${attribute.attributeNo}:${attributeValue.attributeValueNo}%' </foreach>
				</if>
			</foreach>
		</if>
	</select>

    <select id="selectForUpdate" parameterType="java.lang.String" resultMap="BaseResultMap">
        select * from go_resource
        where resource_id = #{resourceId,jdbcType=VARCHAR} for update
    </select>

    <select id="pageQueryWithDataPerm" resultMap="BaseResultMap">
        SELECT
           re.*
        FROM
            `go_resource` re
        left join go_goods g on re.goods_id = g.goods_id
        LEFT JOIN go_goods_category_relation r ON g.goods_id = r.goods_id
        LEFT JOIN go_goods_category c ON r.category_id = c.category_id
        WHERE 1=1 and re.del_flg=0 and g.del_flg = 0 
        <if test="resource.categoryCode != null and resource.categoryCode!=''">
            and INSTR( c.category_code, #{resource.categoryCode} )
        </if>
        <if test="resource.goodsType != null and resource.goodsType!=''">
            AND re.goods_type=#{resource.goodsType}
        </if>
        <if test="resource.goodsDescribe != null and resource.goodsDescribe!=''">
            AND re.goods_name LIKE concat('%',#{resource.goodsDescribe},'%')
        </if>
        <if test="resource.saleArea != null and resource.saleArea!=''">
            AND re.sale_area LIKE concat('%',#{resource.saleArea},'%')
        </if>
        <if test="resource.saleAreaCode != null and resource.saleAreaCode!=''">
            AND re.sale_area_code=#{resource.saleAreaCode}
        </if>
        <if test="resource.saleAreaCode2 != null and resource.saleAreaCode2!=''">
            AND re.sale_area_code2=#{resource.saleAreaCode2}
        </if>
        <if test="resource.saleAreaCode3 != null and resource.saleAreaCode3!=''">
            AND re.sale_area_code3=#{resource.saleAreaCode3}
        </if>
        <if test="resource.saleAreaCode4 != null and resource.saleAreaCode4!=''">
            AND re.sale_area_code4=#{resource.saleAreaCode4}
        </if>
        <if test="resource.saleAreaCode5 != null and resource.saleAreaCode5!=''">
            AND re.sale_area_code5=#{resource.saleAreaCode5}
        </if>
        <if test="resource.resourceCode != null and resource.resourceCode!=''">
            AND re.resource_code LIKE concat('%',#{resource.resourceCode},'%')
        </if>
        <if test="resource.storeName != null and resource.storeName!=''">
            AND re.store_name LIKE concat('%',#{resource.storeName},'%')
        </if>
        <if test="resource.ifProtocolPrice != null">
            AND re.if_protocol_price=#{resource.ifProtocolPrice}
        </if>
        <if test="resource.startTime != null">
            AND re.up_time &gt;= #{resource.startTime}
        </if>
        <if test="resource.endTime != null">
            AND re.up_time &lt;= #{resource.endTime}
        </if>
        <if test="resource.status != null and resource.status!=''">
            AND re.status=#{resource.status}
        </if>
            ORDER BY re.update_time DESC
    </select>

    <select id="countQueryWithDataPerm" resultType="java.lang.Integer">
        SELECT
            count(re.resource_id)
        FROM
           `go_resource` re
        WHERE 1=1 and re.del_flg=0
        AND re.`sale_area_real_code` IN
            (
            SELECT
               bsra.region_son_id
            FROM
               `ba_sale_region_All` bsra
            WHERE bsra.`region_id` IN
            <foreach close=")" collection="resource.regionList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
            )
        <if test="resource.sellerId != null and resource.sellerId!=''">
            AND re.seller_id=#{resource.sellerId}
        </if>
        <if test="resource.goodsType != null and resource.goodsType!=''">
            AND re.goods_type=#{resource.goodsType}
        </if>
        <if test="resource.goodsDescribe != null and resource.goodsDescribe!=''">
            AND re.goods_describe LIKE concat('%',#{resource.goodsDescribe},'%')
        </if>
        <if test="resource.saleArea != null and resource.saleArea!=''">
            AND re.sale_area LIKE concat('%',#{resource.saleArea},'%')
        </if>
        <if test="resource.saleAreaCode != null and resource.saleAreaCode!=''">
            AND re.sale_area_code=#{resource.saleAreaCode}
        </if>
        <if test="resource.saleAreaCode2 != null and resource.saleAreaCode2!=''">
            AND re.sale_area_code2=#{resource.saleAreaCode2}
        </if>
        <if test="resource.saleAreaCode3 != null and resource.saleAreaCode3!=''">
            AND re.sale_area_code3=#{resource.saleAreaCode3}
        </if>
        <if test="resource.saleAreaCode4 != null and resource.saleAreaCode4!=''">
            AND re.sale_area_code4=#{resource.saleAreaCode4}
        </if>
        <if test="resource.saleAreaCode5 != null and resource.saleAreaCode5!=''">
            AND re.sale_area_code5=#{resource.saleAreaCode5}
        </if>
        <if test="resource.resourceCode != null and resource.resourceCode!=''">
            AND re.resource_code LIKE concat('%', #{resource.resourceCode}, '%')
        </if>
        <if test="resource.storeName != null and resource.storeName!=''">
            AND re.store_name= LIKE concat('%', #{resource.storeName}, '%')
        </if>
        <if test="resource.ifProtocolPrice != null">
            AND re.if_protocol_price=#{resource.ifProtocolPrice}
        </if>
        <if test="resource.startTime != null">
            AND re.up_time &gt;= #{resource.startTime}
        </if>
        <if test="resource.endTime != null">
            AND re.up_time &lt;= #{resource.endTime}
        </if>
        <if test="resource.status != null and resource.status!=''">
            AND re.status=#{resource.status}
        </if>
        ORDER BY re.update_time DESC
    </select>

    <select id="promptOnsaleResourceWithDataPerm" resultMap="BaseResultMap">
        SELECT
        re.*
        FROM
        `go_resource` re
        WHERE 1=1 and re.del_flg=0
        AND re.`sale_area_real_code` IN
        (
        SELECT
        bsra.region_son_id
        FROM
        `ba_sale_region_All` bsra
        WHERE bsra.`region_id` IN
        <foreach close=")" collection="resource.regionList" index="index" item="item" open="(" separator=",">
            #{item}
        </foreach>
        )
        <if test="resource.spuId != null and resource.spuId!=''">
            AND re.spu_id=#{resource.spuId}
        </if>
        <if test="resource.goodsId != null and resource.goodsId!=''">
            AND re.goods_id=#{resource.goodsId}
        </if>
    </select>

    <select id="selectPutOnAndOffResourceList" resultMap="BaseResultMap">
        SELECT
            re.*
        FROM
            go_resource re
        WHERE
            (re.ifup = true AND re.up_time &lt;= NOW())
           OR (re.ifdown = true AND re.down_time &lt;= NOW())
    </select>

</mapper>