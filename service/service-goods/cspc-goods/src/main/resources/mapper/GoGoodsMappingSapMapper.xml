<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.GoGoodsMappingSapMapper">
    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.GoGoodsMappingSap">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="goods_code" property="goodsCode" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="sap_material_code" property="sapMaterialCode" jdbcType="VARCHAR"/>
        <result column="mapping_sap_material_code" property="mappingSapMaterialCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="del_flg" property="delFlg" jdbcType="BIT"/>
    </resultMap>

    <select id="selectSapMapping" parameterType="com.cnoocshell.goods.api.dto.GoodsSapMappingDTO" resultType="com.cnoocshell.goods.api.dto.GoodsSapMappingDTO">
        select * from go_goods_mapping_sap
         where del_flg = 0
         <if test="pack != null and pack != ''">
             and pack = #{pack}
         </if>
        <if test="goodsName != null and goodsName != ''">
            and goods_name LIKE CONCAT('%',  #{goodsName} , '%')
        </if>
        <if test="mappingSapMaterialCode != null and mappingSapMaterialCode !=''">
            and mapping_sap_material_code LIKE CONCAT('%',  #{mappingSapMaterialCode} , '%')
        </if>
        order by create_time desc
    </select>

</mapper>