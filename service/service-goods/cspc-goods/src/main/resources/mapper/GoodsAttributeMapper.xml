<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.GoodsAttributeMapper">
  <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.GoodsAttribute">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="goods_attri_id" jdbcType="VARCHAR" property="goodsAttriId" />
    <result column="attri_name" jdbcType="VARCHAR" property="attriName" />
    <result column="attri_type" jdbcType="TINYINT" property="attriType" />
    <result column="value_type" jdbcType="TINYINT" property="valueType" />
    <result column="defalut" jdbcType="VARCHAR" property="defalut" />
    <result column="is_edit" jdbcType="CHAR" property="isEdit" />
    <result column="is_show" jdbcType="CHAR" property="isShow" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>