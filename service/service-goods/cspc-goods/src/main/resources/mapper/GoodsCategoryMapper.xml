<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnoocshell.goods.dao.mapper.GoodsCategoryMapper">
    <resultMap id="BaseResultMap" type="com.cnoocshell.goods.dao.vo.GoodsCategory">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="category_id" jdbcType="VARCHAR" property="categoryId"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="category_code" jdbcType="VARCHAR" property="categoryCode"/>
        <result column="category_name" jdbcType="VARCHAR" property="categoryName"/>
        <result column="imgs" jdbcType="VARCHAR" property="imgs"/>
        <result column="app_imgs" jdbcType="VARCHAR" property="appImgs"/>
        <result column="mini_imgs" jdbcType="VARCHAR" property="miniImgs"/>
        <result column="sap_code" jdbcType="VARCHAR" property="sapCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="del_flg" jdbcType="BIT" property="delFlg"/>
    </resultMap>

    <select id="findCategorySimpleList" resultType="com.cnoocshell.goods.api.dto.GoodsCategorySimpleDTO"
            parameterType="java.util.Set">
        SELECT
        category_code AS categoryCode,
        category_name AS categoryName
        FROM go_goods_category
        WHERE del_flg = 0
        <if test="set != null and set.size() > 0">
            AND category_code IN
            <foreach collection="set" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by categoryCode
    </select>

    <select id="queryCategoryAndParentCategory" resultType="com.cnoocshell.goods.api.dto.CategoryAndParentCategoryDTO">
        select
        tb1.category_id AS 'categoryIdLevelTwo',
        tb1.category_code AS 'categoryCodeLevelTwo',
        tb1.category_name AS 'categoryNameLevelTwo',
        tb2.category_id AS 'categoryIdLevelOne',
        tb2.category_code AS 'categoryCodeLevelOne',
        tb2.category_name AS 'categoryNameLevelOne'
        from go_goods_category tb1
        left join go_goods_category tb2 on tb1.parent_id = tb2.category_id and tb2.del_flg = 0
        where tb1.del_flg = 0
        <if test="categoryCodes != null and categoryCodes.size>0">
            and tb1.category_code in
            <foreach collection="categoryCodes" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
    </select>

    <select id="queryCategoryAndParentByCategoryId"
            resultType="com.cnoocshell.goods.api.dto.CategoryAndParentCategoryDTO">
        select
        tb1.category_id AS 'categoryIdLevelTwo',
        tb1.category_code AS 'categoryCodeLevelTwo',
        tb1.category_name AS 'categoryNameLevelTwo',
        tb2.category_id AS 'categoryIdLevelOne',
        tb2.category_code AS 'categoryCodeLevelOne',
        tb2.category_name AS 'categoryNameLevelOne'
        from go_goods_category tb1
        left join go_goods_category tb2 on tb1.parent_id = tb2.category_id and tb2.del_flg = 0
        where tb1.del_flg = 0
        <if test="categoryIds != null and categoryIds.size>0">
            and tb1.category_id in
            <foreach collection="categoryIds" item="item1" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
    </select>
</mapper>