server:
  port: 8087
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: cspc-service-goods
  profiles:
    active: ${ENV:dev}
  cloud:
    nacos:
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
      config:
        profile: ${spring.application.active:dev}
        namespace: ${NACOS_NAMESPACE:cnoocshell-dev}
        server-addr: ${NACOS_SERVER_ADDR:27.40.98.108:8848} # Nacos 服务器地址
        file-extension: yaml          # 配置文件扩展名
        file—prefix: ${spring.application.name:dev}
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}  # Nacos 服务器地址
  cache:
    type: CAFFEINE
    caffeine:
      spec: initialCapacity=50,maximumSize=500,expireAfterWrite=5s


cement-code: "002001001"

mybatis:
  typeAliasesPackage: com.cnoocshell.goods.dao
  mapperScanPackage: com.cnoocshell.goods.dao
  mapperLocations: "classpath:/mapper/*.xml"
  configLocation: "classpath:/mybatis-config.xml"

logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    root: info
swagger:
  author: cnoocshell
  title: cnoocshell
  basePackage: com.cnoocshell.good.controller
management:
  endpoint:
    health:
      show-details: always
  security:
    enabled: false
  endpoints:
    health:
      sensitive: false
    web:
      exposure:
        include: "*"

redis:
  lock:
    acquire:
      timeout: 30000

# 的分类code
#合同批量调价前6小时，提醒买家尽快提货，注意：时间长短与job执行间隔任务保持一致,否则会重复提醒
contract:
  batchAdjustPrice:
    remaindTimeHour: 6
xxl:
  job:
    accessToken: default_token
    admin:
      addresses: http://localhost:8090/xxl-job-admin
    executor:
      appname: ${spring.application.name}-job
      ip: 127.0.0.1
      port: 9999
      logpath: C:/Users/<USER>/DeloitteSpace/IdeaSpace/ec-asset/xxlJob/log
      logretentiondays: 7