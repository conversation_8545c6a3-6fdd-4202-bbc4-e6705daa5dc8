<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="javaFileEncoding" value="UTF-8"/>
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin" />
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin" />

        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="com.cnoocshell.common.service.IBaseMapper"/>
        </plugin>

        <!--<jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"-->
        <!--connectionURL="*************************************************"-->
        <!--userId="root"-->
        <!--password="111111">-->
        <!--</jdbcConnection>-->

        <!--<jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"-->
                        <!--connectionURL="*************************************"-->
                        <!--userId="root"-->
                        <!--password="cqnews">-->
        <!--</jdbcConnection>-->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="*************************************"
                        userId="root"
                        password="crc123456">
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.cnoocshell.goods.dao.vo" targetProject="../goods/src/main/java">
            <property name="enableSubPackages" value="true" />
            <property name="trimStrings" value="true" />
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true" />
        </sqlMapGenerator>

        <javaClientGenerator targetPackage="com.cnoocshell.goods.dao.mapper" targetProject="../goods/src/main/java"
                             type="XMLMAPPER"/>

        <!--<table tableName="go_stock_seller" domainObjectName="StockSeller" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>
        <table tableName="go_stock_agent" domainObjectName="StockAgent" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>
        <table tableName="go_stock_item" domainObjectName="StockItem" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>-->
        <!--<table tableName="go_resource" domainObjectName="Resource" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->

        <!--<table tableName="go_resource" domainObjectName="Resource" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="go_resource_certificate" domainObjectName="ResourceCertificate" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="go_resource_history" domainObjectName="ResourceHistory" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="go_resource_region" domainObjectName="ResourceRegion" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="go_resource_uneffective" domainObjectName="ResourceUneffective" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="go_goods_resource" domainObjectName="GoodsResource" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <table tableName="tr_resource_additem" domainObjectName="GoodsAddItem" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>
        <!--<table tableName="go_purchase" domainObjectName="Purchase" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="go_purchase_history" domainObjectName="PurchaseHistory" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="tr_resource_additem" domainObjectName="GoodsAddItem" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="go_goods_attribute_value" domainObjectName="GoodsAttributeValue" enableCountByExample="true"-->
            <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
            <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->

        <!--<table tableName="go_goods_category_attribute" domainObjectName="GoodsCategoryAttribute" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->

        <!--<table tableName="go_goods_category" domainObjectName="GoodsCategory" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
       <!-- <table tableName="go_materials" domainObjectName="Materials" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>-->
        <!--<table tableName="go_goods" domainObjectName="Goods" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->

        <!--<table tableName="tr_resource_additem" domainObjectName="GoodsAddItem" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->

        <!--<table tableName="tr_resource_additem_history" domainObjectName="GoodsAddItemHistory" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->

        <!--<table tableName="go_batch_price_rule" domainObjectName="BatchPriceRule" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="go_price_change_plan" domainObjectName="PriceChangePlan" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="go_batch_price_rule_history" domainObjectName="BatchPriceRuleHistory" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="tr_contract_goods" domainObjectName="TrContractGoods" enableCountByExample="true"-->
                <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
                <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="tr_contract" domainObjectName="TrContract" enableCountByExample="true"-->
            <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
            <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="tr_contract_goods_history" domainObjectName="TrContractGoodsHistory" enableCountByExample="true"-->
            <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
            <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="tr_contract_additem_history" domainObjectName="TrContractAdditemHistory" enableCountByExample="true"-->
            <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
            <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
    </context>
</generatorConfiguration>