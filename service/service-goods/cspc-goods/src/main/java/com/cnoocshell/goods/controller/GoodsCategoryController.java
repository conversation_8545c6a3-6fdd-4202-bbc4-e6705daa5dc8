package com.cnoocshell.goods.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.service.IGoodsCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * @Author: wangshunmin
 * @Description GoodsCategoryController
 * @date 2025.2.12
 */
@Slf4j
@RestController
@RequestMapping("/goodsCategory")
@Api(tags = {"GoodsCategoryController"}, description = "商品分类服务")
public class GoodsCategoryController {

    @Autowired
    private IGoodsCategoryService goodsCategoryService;

    @ApiOperation("添加商品分类--新接口")
    @PostMapping(value = "/addCategory")
    public void addCategory(@RequestBody @ApiParam("分类DTO") CategoryDTO arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1) {
        goodsCategoryService.addCategory(arg0, arg1);
    }

    @ApiOperation("查询商品分类树--新接口")
    @PostMapping(value = "/findCategoryTree")
    public List<CategoryTreeDTO> findCategoryTree(@ApiParam("分类Code") String arg0) {
        return goodsCategoryService.findCategoryTree(arg0, false);
    }

    @ApiOperation("查询商品分类树--新接口")
    @PostMapping(value = "/findCategoryTreeByMp")
    public List<CategoryTreeDTO> findCategoryTreeByMp(@ApiParam("分类Code") String arg0) {
        return goodsCategoryService.findCategoryTreeByMp(arg0, false);
    }

    @ApiOperation("查询商品分类树--新接口")
    @PostMapping(value = "/findCategoryTreeGoods")
    public List<CategoryTreeDTO> findCategoryTreeGoods(@ApiParam("分类Code") String arg0) {
        return goodsCategoryService.findCategoryTree(arg0, true);
    }

    @ApiOperation("查询商品分类树--根据角色权限获取")
    @GetMapping(value = "/findCategoryTreeGoodsByRole")
    public List<CategoryTreeDTO> findCategoryTreeGoodsByRole(@RequestParam String memberId,
                                                             @RequestParam String accountId) {
        return goodsCategoryService.findCategoryTreeGoodsByRole(null, memberId, accountId,true);
    }

    @ApiOperation("删除商品分类--新接口")
    @PostMapping(value = "/delCategory")
    public void delCategory(@RequestParam("arg0") @ApiParam("分类ID") String arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1) {
        goodsCategoryService.delCategory(arg0, arg1);
    }

    @ApiOperation("修改商品分类--新接口")
    @PostMapping(value = "/uptCategory")
    public void uptCategory(@RequestBody @ApiParam("分类DTO") CategoryDTO arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1) {
        goodsCategoryService.uptCategory(arg0, arg1);
    }

    @ApiOperation("商品分类")
    @PostMapping(value = "/goodsCategoryTree")
    public List<CategoryTreeDTO> goodsCategoryTree(@RequestParam("arg0") @ApiParam("code") String arg0) {
        return goodsCategoryService.goodsCategoryTree(arg0);
    }

    @ApiOperation("获取标准商品属性配置--新接口")
    @PostMapping(value = "/getCategory")
    public CategoryDTO getCategory(@RequestParam("arg0") @ApiParam("arg0") String arg0) {
        if (StringUtils.isEmpty(arg0)) {
            return null;
        }
        return goodsCategoryService.getCategory(arg0);
    }

    @ApiOperation("根据查询商品分类树--新接口")
    @PostMapping(value = "/findCategoryTreeByCode")
    public List<CategoryTreeDTO> findCategoryTreeByCode(@RequestParam("arg0") @ApiParam("分类Code") String arg0) {
        return goodsCategoryService.findCategoryTreeByCode(arg0);
    }

    @ApiOperation("刷新缓存,redisKey:category_tree_cache")
    @PostMapping(value = "/refreshCategoryTreeCache")
    public void refreshCategoryTreeCache(@RequestParam("operator") @ApiParam("操作人id") String operator) {
        goodsCategoryService.refreshCategoryTreeCache(operator);
    }

    @PostMapping(value = "/findCategorySimpleByIds", consumes = "application/json")
    List<GoodsCategorySimpleDTO> findCategorySimpleByIds(@RequestBody List<String> categoryIds){
        return goodsCategoryService.findCategorySimple(categoryIds);
    }


    @PostMapping(value = "/getSimpleList", consumes = "application/json")
    List<GoodsCategorySimpleDTO> getSimpleList(@RequestBody Set<String> categoryCodeSet){
        return goodsCategoryService.getSimpleList(categoryCodeSet);
    }

    @PostMapping(value = "/findCategoryByCountId")
    List<GoodsCategorySimpleDTO> findCategoryByCountId(@RequestBody GoodsCategorySimpleDTO categorySimpleDTO){
        return goodsCategoryService.findCategoryByCountId(categorySimpleDTO);
    }

    @PostMapping(value = "/findGoodsByCategoryAccountId")
    List<GoodsSimpleDTO> findGoodsByCategoryAccountId(@RequestBody GoodsCategorySimpleDTO categorySimpleDTO){
        return goodsCategoryService.findGoodsByCategoryCountId(categorySimpleDTO);
    }

    @PostMapping("/getByLikeName")
    ItemResult<List<GoodsCategoryDTO>> getByLikeName(@RequestParam("name") String name){
        return ItemResult.success(goodsCategoryService.getByLikeName(name));
    }

    @ApiOperation("查询分类名称与其上一级的父级分类名称信息")
    @PostMapping("/queryCategoryAndParentCategory")
    public List<CategoryAndParentCategoryDTO> queryCategoryAndParentCategory(@RequestBody List<String> goodsCategoryCodes){
        return goodsCategoryService.queryCategoryAndParentCategory(goodsCategoryCodes);
    }

    @ApiOperation("查询分类名称与其上一级的父级分类名称信息")
    @PostMapping("/queryCategoryAndParentByCategoryId")
    List<CategoryAndParentCategoryDTO> queryCategoryAndParentByCategoryId(@RequestBody List<String> categoryIds){
        return goodsCategoryService.queryCategoryAndParentByCategoryId(categoryIds);
    }

}