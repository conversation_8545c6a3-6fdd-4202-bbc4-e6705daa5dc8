package com.cnoocshell.goods.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.result.PageData;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.dto.base.PageQuery;
import com.cnoocshell.goods.api.dto.goods.GoodsAndCategoryInfoResultDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsDataListDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 商品管理服务
 *
 * @Author: <EMAIL>
 * @Description IGoodsService
 * @date 2018年8月16日 下午4:34:59
 */
public interface IGoodsService {

    /**
     * 根据主键查询商品
     *
     * @param goodsId 商品ID
     * @return 商品对象
     */
    GoodsDTO findGoodsById(String goodsId);

    /**
     * 根据主键查询商品
     *
     * @param goodsId 商品ID
     * @return 商品对象
     */
    GoodsDTO findGoodsByCode(String goodsId);

    /**
     * 修改商品状态
     *
     * @param goodsId     商品ID
     * @param goodsStatus 商品状态 1草稿 2启用（审核通过）3禁用 4审核中 5审核失败
     * @param operator    操作人
     * @return
     */
    ItemResult<String> updateStatus(String goodsId, Integer goodsStatus, String operator);

    /**
     * 删除商品（逻辑删除，支持批量删除）
     *
     * @param goodsIds 多ID以","分开
     * @param operator 操作人
     * @return
     */
    void deleteGoods(String goodsIds, String operator);

    /**
     * 获取商品（SPU,SKU）分类属性
     *
     * @param goodsId 商品ID
     * @return List<GoodsCategoryAttrDTO>
     */
    List<GoodsCategoryAttrDTO> getCategoryAttrByGoodsId(String goodsId);

    DynamicAttributeDTO findDynamicAttribute(String goodsId);

    /**
     * * 分页查看标准商品列表
     *
     * @param pageBaseGoodsDTO 分页入参
     * @return PageInfo<BaseGoodsDTO>
     */
    PageInfo<BaseGoodsDTO> pageBaseGoods(PageBaseGoodsDTO pageBaseGoodsDTO);


    /**
     * 创建卖家商品
     *
     * @param sellerGoodsDTO 入参
     * @param operator       操作人
     */
    void createSellerGoods(SellerGoodsDTO sellerGoodsDTO, String operator);

    /**
     * 修改卖家商品
     *
     * @param sellerGoodsDTO 入参
     * @param operator       操作人
     */
    void updateSellerGoods(SellerGoodsDTO sellerGoodsDTO, String operator);

    /**
     * 查询卖家商品详情
     *
     * @param goodsId 商品Id
     * @return SellerGoodsDTO
     */
    SellerGoodsDTO getSellerGoodsDetail(String goodsId);

    /**
     * 分页查询卖家商品
     *
     * @param pageSellerGoodsDTO 分页对象
     * @return PageInfo<SellerGoodsDTO>
     */
    PageInfo<SellerGoodsDTO> pageSellerGoods(PageSellerGoodsDTO pageSellerGoodsDTO);

    /**
     * 首页上架商品
     *
     * @param pageSellerGoodsDTO 分页对象
     * @return PageInfo<SellerGoodsDTO>
     */
    PageInfo<SellerGoodsDTO> pageGoods(PageSellerGoodsDTO pageSellerGoodsDTO);


    /**
     * 查询商品特殊属性
     *
     * @param goodsId 商品ID
     * @return SpecialGoodsAttributeDTO
     */
    SpecialGoodsAttributeDTO getSpecialGoodsAttribute(String goodsId);

    List<GoodsDTO> findBaseGoods(String name, Integer categoryType, String seller);

    /**
     * 查询标准商品属性
     *
     * @param goodsId 标准商品ID
     * @return List<String>
     */
    List<CategoryAttributeDTO> findBaseGoodsAttribute(String goodsId);



    /**
     * 商品通用查询
     *
     * @param pageQuery
     * @return
     */
    PageData<GoodsDTO> goodsCommonQuery(PageQuery<GoodsQueryCondDTO> pageQuery);


    /**
     * 根据物料商品编码查询查询商品,不带商品属性
     *
     * @param commodityCode
     * @return
     */
    GoodsDTO getGoodsInfoByCommodityCode(String commodityCode, String sellerId);

    List<GoodsSimpleDTO> findGoodsSimpleByIds(List<String> goodsIds);

    List<GoodsSimpleDTO> findGoodsSimpleByCategoryId(String categoryId);

    List<GoodsSimpleDTO> findGoodsSimpleByCodes(List<String> goodsCodes);


    List<GoodsNameInfoDTO> queryGoodsNameByGoodsCode(GoodsCodeInfoDTO goodsCodeInfoDTO);

    List<GoodsDataListDTO> getPackSalesGroupByGoodsCode(List<String> goodsCodeList);

    List<GoodsSimpleDTO> findGoodsSimpleByLikeSapMaterialCode(String sapMaterialCode);


    /**
     * 根据商品名称模糊匹配商品信息
     * @param name 商品名称 为空则返回null
     * @param goodsStatus 商品状态 为空则不匹配商品状态 参考GoodsStatusEnum
     * @return 商品简单信息
     */
    List<GoodsSimpleDTO> queryGoodsByLikeName(String name, Integer goodsStatus);

    List<GoodsAndCategoryInfoResultDTO> queryGoodsAndCategoryInfoByGoodsCodes(List<String> goodsCodes);
}


