package com.cnoocshell.goods.tasks;


import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.service.IXxlJobService;
import com.cnoocshell.goods.api.enums.ResourceStatusEnum;
import com.cnoocshell.goods.biz.IResourceBiz;
import com.cnoocshell.goods.dao.mapper.ResourceMapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 资源定时上架
 */
@Component
@Slf4j
public class PutOnAndOffResourceScheduler {

    @Resource
    private ResourceMapper resourceMapper;

    @Autowired
    private IResourceBiz resourceBiz;

    @Autowired
    private IXxlJobService iXxlJobService;

    @XxlJob("putOnResource")
    public void doJob(String param) {
        XxlJobHelper.log("PutOnAndOffResourceScheduler is start");
        Long jobId = XxlJobHelper.getJobId();
        try {
            Condition condition = resourceBiz.newCondition();
            condition.createCriteria().andEqualTo("resourceId", param).andEqualTo("delFlg", false);
            List<com.cnoocshell.goods.dao.vo.Resource> resourceList = resourceBiz.findByCondition(condition);
            XxlJobHelper.log("PutOnAndOffResourceScheduler resourceList：{}", resourceList);
            if (CollectionUtils.isEmpty(resourceList)) {
                return;
            }

            com.cnoocshell.goods.dao.vo.Resource resource1 = resourceList.get(0);
            if (Boolean.TRUE.equals(resource1.getIfup())) {
                resource1.setStatus(ResourceStatusEnum.RES_STATUS100.getCode());
                resource1.setIfup(false);
            }else {
                resource1.setStatus(ResourceStatusEnum.RES_STATUS200.getCode());
                resource1.setIfdown(false);
            }
            List<com.cnoocshell.goods.dao.vo.Resource> updateList = Lists.newArrayList();
            updateList.add(resource1);
            resourceBiz.batchUpdateResources(updateList);
        }catch (Exception e) {
            XxlJobHelper.log("PutOnAndOffResourceScheduler is error:"+ CharSequenceUtil.sub(ExceptionUtil.stacktraceToString(e),0,1024));
        }
        XxlJobHelper.log("PutOnAndOffResourceScheduler is end");
        //屏蔽销毁逻辑
//        destroyJob(jobId, param);
    }


    private void destroyJob(Long jobId, String resourceCode) {
        XxlJobHelper.log(CharSequenceUtil.format("定时上架自动任务销毁开始 jobId:{} enquiryNo：{}", jobId, resourceCode));
        iXxlJobService.destroyXxlJob(jobId);
    }

}
