package com.cnoocshell.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.GoodsDTO;
import com.cnoocshell.goods.api.dto.GoodsSapMappingDTO;
import com.cnoocshell.goods.api.dto.GoodsSapMappingSimpleDTO;
import com.cnoocshell.goods.biz.IGoodsMappingSapBiz;
import com.cnoocshell.goods.dao.mapper.GoGoodsMappingSapMapper;
import com.cnoocshell.goods.dao.vo.GoGoodsMappingSap;
import com.cnoocshell.goods.service.IGoodsMappingSapService;
import com.cnoocshell.goods.service.IGoodsService;
import com.cnoocshell.member.api.dto.account.AccountDTO;
import com.cnoocshell.member.api.service.IAccountService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsMappingSapBizService implements IGoodsMappingSapService {
    private final IGoodsMappingSapBiz iGoodsMappingSapBiz;
    @Resource
    private GoGoodsMappingSapMapper goGoodsMappingSapMapper;

    @Autowired
    private IGoodsService goodsService;

    @Autowired
    private IAccountService accountService;

    @Override
    public List<GoodsSapMappingSimpleDTO> querySimple(List<String> goodsCode, List<String> packList) {
        return BeanUtil.copyToList(iGoodsMappingSapBiz.queryGoodsMapping(goodsCode,packList),GoodsSapMappingSimpleDTO.class);
    }

    @Override
    public ItemResult<PageInfo<GoodsSapMappingDTO>> selectGoodsSapMapping(GoodsSapMappingDTO sapMappingDTO) {
        PageMethod.startPage(Objects.isNull(sapMappingDTO.getPageNum()) ? 1 : sapMappingDTO.getPageNum(), Objects.isNull(sapMappingDTO.getPageSize()) ? 10 : sapMappingDTO.getPageSize());
        List<GoodsSapMappingDTO> goodsSapMappingDTOS = goGoodsMappingSapMapper.selectSapMapping(sapMappingDTO);
        return new ItemResult<>(new PageInfo<>(goodsSapMappingDTOS));
    }

    @Override
    public ItemResult<String> insertGoodsSapMapping(GoodsSapMappingDTO sapMappingDTO) {
        //检查参数
        if (Objects.isNull(sapMappingDTO)) {
            return new ItemResult<>(BasicCode.PARAM_NULL.getCode(), "参数为空！");
        }
        String checked = checkParam(sapMappingDTO);
        if (StringUtils.isNotEmpty(checked)) {
            return new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), checked);
        }
        //校验是否插入过
        Condition condition = iGoodsMappingSapBiz.newCondition();
        condition.createCriteria().andEqualTo("goodsName", sapMappingDTO.getGoodsName())
                .andEqualTo("pack", sapMappingDTO.getPack())
                .andEqualTo("delFlg", false);
        List<GoGoodsMappingSap> byCondition = iGoodsMappingSapBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(byCondition)) {
            return new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "已存在该商品和包装方式的映射关系，请您重新操作!");
        }

        AccountDTO accountDTO = accountService.findById(sapMappingDTO.getCreateUser());

        GoodsDTO goods = goodsService.findGoodsByCode(sapMappingDTO.getGoodsCode());
        GoGoodsMappingSap goGoodsMappingSap = GoGoodsMappingSap.builder().build();
        BeanUtils.copyProperties(sapMappingDTO, goGoodsMappingSap);
        goGoodsMappingSap.setCreateTime(new Date());
        goGoodsMappingSap.setCreateUser(accountDTO.getAccountId());
        goGoodsMappingSap.setCreateUserName(accountDTO.getRealName());
        goGoodsMappingSap.setUpdateTime(new Date());
        goGoodsMappingSap.setUpdateUser(accountDTO.getAccountId());
        goGoodsMappingSap.setUpdateUserName(accountDTO.getRealName());
        goGoodsMappingSap.setSapMaterialCode(goods.getSapMaterialCode());
        goGoodsMappingSap.setDelFlg(false);
        try {
            goGoodsMappingSap.setId(IdUtil.simpleUUID());
            iGoodsMappingSapBiz.insert(goGoodsMappingSap);
        }catch (Exception e){
            log.error("insertGoodsSapMapping is error",e);
            new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "保存失败！");
        }
        return new ItemResult<>("保存成功！");
    }

    private String checkParam(GoodsSapMappingDTO sapMappingDTO) {
        if (StringUtils.isEmpty(sapMappingDTO.getGoodsCode())) {
            return "商品code为空";
        }
        if (StringUtils.isEmpty(sapMappingDTO.getGoodsName())) {
            return "商品名称为空";
        }
        if (StringUtils.isEmpty(sapMappingDTO.getPack())) {
            return "包装方式为空";
        }
        if (StringUtils.isEmpty(sapMappingDTO.getMappingSapMaterialCode())) {
            return "映射sap物料编码为空";
        }
        return null;
    }

    @Override
    public ItemResult<String> updateGoodsSapMapping(GoodsSapMappingDTO sapMappingDTO) {
        if (StringUtils.isEmpty(sapMappingDTO.getId())) {
            return new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "id为空");
        }
        if (StringUtils.isEmpty(sapMappingDTO.getMappingSapMaterialCode())) {
            return new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "映射sap物料编码为空");
        }

        GoGoodsMappingSap goGoodsMappingSap1 = iGoodsMappingSapBiz.get(sapMappingDTO.getId());
        if (Objects.isNull(goGoodsMappingSap1)) {
            return new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "映射关系不存在！");
        }

        AccountDTO accountDTO = accountService.findById(sapMappingDTO.getUpdateUser());
        goGoodsMappingSap1.setUpdateTime(new Date());
        goGoodsMappingSap1.setUpdateUser(accountDTO.getAccountId());
        goGoodsMappingSap1.setUpdateUserName(accountDTO.getRealName());
        goGoodsMappingSap1.setMappingSapMaterialCode(sapMappingDTO.getMappingSapMaterialCode());
        try {
            iGoodsMappingSapBiz.save(goGoodsMappingSap1);
        }catch (Exception e){
            log.error("updateGoodsSapMapping is error",e);
            new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "修改失败！");
        }
        return new ItemResult<>("修改成功！");
    }

    @Override
    public ItemResult<String> deleteGoodsSapMapping(GoodsSapMappingDTO sapMappingDTO) {
        if (StringUtils.isEmpty(sapMappingDTO.getId())) {
            return new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "id为空");
        }
        GoGoodsMappingSap goGoodsMappingSap1 = iGoodsMappingSapBiz.get(sapMappingDTO.getId());
        AccountDTO accountDTO = accountService.findById(sapMappingDTO.getUpdateUser());
        goGoodsMappingSap1.setUpdateTime(new Date());
        goGoodsMappingSap1.setUpdateUser(accountDTO.getAccountId());
        goGoodsMappingSap1.setUpdateUserName(accountDTO.getRealName());
        goGoodsMappingSap1.setDelFlg(true);
        try {
            iGoodsMappingSapBiz.save(goGoodsMappingSap1);
        }catch (Exception e){
            log.error("deleteGoodsSapMapping is error",e);
            new ItemResult<>(BasicCode.UNDEFINED_ERROR.getCode(), "删除失败！");
        }
        return new ItemResult<>("删除失败！");
    }

}
