package com.cnoocshell.goods.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.BatchUpdateResourceDTO;
import com.cnoocshell.goods.api.dto.CreateResourceDTO;
import com.cnoocshell.goods.api.dto.ManualOnSaleDTO;
import com.cnoocshell.goods.api.dto.UpdateResourceDTO;

import java.util.List;

/**
 * 资源挂牌服务
 */
public interface IResourceListingService {

    /**
     * 资源挂牌
     * @param createResourceDTO 挂牌详情
     * @param operator 操作员
     * @return void
     */
    void createResource(CreateResourceDTO createResourceDTO, String operator);

    /**
     * 手动下架
     * @param resourceIds 资源编号列表
     * @param operator 操作员
     * @return void
     */
    ItemResult<String> manualOffSale(List<String> resourceIds, String operator);

    /**
     * 手动上架
     * @return void
     */
    void manualOnSale(ManualOnSaleDTO manualOnSaleDTO);

    /**
     * 批量修改挂牌资源
     * @param batchUpdateResourceDTO 批量修改对象
     */
    void batchUpdateResource(BatchUpdateResourceDTO batchUpdateResourceDTO) throws Exception;


    /**
     * 更新资源挂牌
     * @param updateResourceDTO 更新挂牌详情
     * @param operator 操作员
     * @return void
     */
    void updateResource(UpdateResourceDTO updateResourceDTO, String operator);


}
