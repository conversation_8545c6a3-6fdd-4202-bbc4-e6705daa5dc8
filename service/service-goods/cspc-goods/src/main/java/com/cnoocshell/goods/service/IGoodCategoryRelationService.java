package com.cnoocshell.goods.service;

import com.cnoocshell.goods.api.dto.GoodCategoryRelationDto;
import com.cnoocshell.goods.api.dto.relation.GoodsCategoryRelationSimpleDTO;

import java.util.List;

public interface IGoodCategoryRelationService {
    boolean save(List<GoodCategoryRelationDto> dtos);

    List<GoodsCategoryRelationSimpleDTO> queryRelationByCategoryCodes(List<String> categoryCodes);

}
