package com.cnoocshell.goods.service.attibute.impl;

import com.cnoocshell.goods.biz.IGoodsBiz;
import com.cnoocshell.goods.biz.IGoodsCategoryBiz;
import com.cnoocshell.goods.dao.vo.Goods;
import com.cnoocshell.goods.service.attibute.ICategoryAttributeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CategoryArributeService implements ICategoryAttributeService{

    @Autowired
    private IGoodsBiz goodsBiz;

    @Autowired
    private IGoodsCategoryBiz goodsCategoryBiz;

    @Override
    public Boolean getSplitBillNode(String goodsId) {
        Goods goodsDTO = goodsBiz.get(goodsId);
        return goodsCategoryBiz.getSplitBillNode(null);
    }

}
