package com.cnoocshell.goods.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.BaseGoodDetailAuditDto;
import com.cnoocshell.goods.api.dto.BaseGoodsAuditDTO;
import com.cnoocshell.goods.service.IBaseGoodAuditService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("base-good")
public class BaseGoodAuditController {
    private final IBaseGoodAuditService baseGoodAuditService;

    public BaseGoodAuditController(IBaseGoodAuditService baseGoodAuditService) {
        this.baseGoodAuditService = baseGoodAuditService;
    }

    @PostMapping("/audit/apply")
    public ItemResult<Boolean> applyGoodsAudit(@RequestBody BaseGoodsAuditDTO dto) {
        boolean result = baseGoodAuditService.applyGoodsAudit(dto, dto.getAction(), dto.getOperator());
        return new ItemResult<>(result);
    }

    // todo ??? body
    @PostMapping("/audit/audit")
    public ItemResult<Boolean> doAudit(String goodId, boolean pass, String msg, String operator) {
        boolean result = baseGoodAuditService.doAudit(goodId, pass, msg, operator);
        return new ItemResult<>(result);
    }

    /**
     * 删除
     * @param goodIds
     * @param operator
     * @return
     */
    @PostMapping("/audit/apply/batchDelete")
    public ItemResult<Boolean> applyBatchDelete(@RequestBody List<String> goodIds,String operator){
        boolean deleteGoods = baseGoodAuditService.applyDeleteGoods(goodIds, operator);
        return new ItemResult<>(deleteGoods);
    }

    @GetMapping("/audit")
    public ItemResult<BaseGoodDetailAuditDto> baseGoodDetails(String goodId) {
        BaseGoodDetailAuditDto details = baseGoodAuditService.baseGoodDetails(goodId);
        return new ItemResult<>(details);
    }

}
