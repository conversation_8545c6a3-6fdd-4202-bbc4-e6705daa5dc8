package com.cnoocshell.goods.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.cnoocshell.base.api.dto.region.RegionDTO;
import com.cnoocshell.base.api.service.IRegionService;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.goods.api.dto.GoodsShippingFee.*;
import com.cnoocshell.goods.api.enums.GoodsShippingFeePackEnum;
import com.cnoocshell.goods.biz.IGoodsShippingFeeBiz;
import com.cnoocshell.goods.common.enums.PackEnum;
import com.cnoocshell.goods.dao.mapper.GoodsMapper;
import com.cnoocshell.goods.dao.mapper.GoodsShippingFeeMapper;
import com.cnoocshell.goods.dao.vo.Goods;
import com.cnoocshell.goods.dao.vo.GoodsShippingFee;
import com.cnoocshell.goods.service.IGoodsShippingFeeService;
import com.cnoocshell.member.api.service.IMemberService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GoodsShippingFeeService implements IGoodsShippingFeeService {

    public static final String DEL_FLG = "delFlg";
    @Autowired
    private IMemberService memberService;

    @Autowired
    private IRegionService regionService;

    @Autowired
    private IGoodsShippingFeeBiz goodsShippingFeeBiz;

    @Autowired
    private GoodsShippingFeeMapper goodsShippingFeeMapper;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    private GoodsMapper goodsMapper;

    @Override
    public PageInfo<GoodsShippingFeeInfoDTO> findAllByCondition(GoodsShippingFeePageReqDTO reqDTO) {
        PageInfo<GoodsShippingFeeInfoDTO> resDTO = new PageInfo<>();
        PageInfo<GoodsShippingFee> pageByCondition = goodsShippingFeeBiz.findAllByCondiftion(reqDTO);
        if (null != pageByCondition) {
            BeanUtils.copyProperties(pageByCondition, resDTO);
            if (CollectionUtils.isNotEmpty(pageByCondition.getList())) {
                List<GoodsShippingFeeInfoDTO> pageList = pageByCondition.getList().stream()
                        .map(page -> {
                            GoodsShippingFeeInfoDTO res = new GoodsShippingFeeInfoDTO();
                            BeanUtils.copyProperties(page, res);
                            toLocalDateAndTime(page, res);
                            return res;
                        }).collect(Collectors.toList());
                resDTO.setList(pageList);
            }
        }
        return resDTO;
    }

    @Override
    public PageInfo<GoodsShippingFeeInfoDTO> findAllByConditionOfBuyer(GoodsShippingFeePagOfBuyerReqDTO reqDTO) {
        PageInfo<GoodsShippingFeeInfoDTO> resDTO = new PageInfo<>();
        List<String> goodsCodeList = memberService.queryGoodsCodeByMemberId(reqDTO.getMemberId());
        if (CollectionUtils.isEmpty(goodsCodeList)) {
            return new PageInfo<>();
        }
        PageInfo<GoodsShippingFee> pageByCondition = goodsShippingFeeBiz.findAllByConditionOfBuyer(reqDTO, goodsCodeList);
        if (null != pageByCondition) {
            BeanUtils.copyProperties(pageByCondition, resDTO);
            if (CollectionUtils.isNotEmpty(pageByCondition.getList())) {
                List<GoodsShippingFeeInfoDTO> pageList = pageByCondition.getList().stream()
                        .map(page -> {
                            GoodsShippingFeeInfoDTO res = new GoodsShippingFeeInfoDTO();
                            BeanUtils.copyProperties(page, res);
                            toLocalDateAndTime(page,res);
                            return res;
                        }).collect(Collectors.toList());
                resDTO.setList(pageList);
            }
        }
        return resDTO;
    }

    @Override
    public Boolean deleteGoodsShippingFee(String id,String accountId,String userName) {
        GoodsShippingFee goodsShippingFee = goodsShippingFeeMapper.selectByPrimaryKey(id);
        goodsShippingFee.setDelFlg(true);
        goodsShippingFee.setUpdateTime(new Date());
        goodsShippingFee.setUpdateUser(accountId);
        goodsShippingFee.setUpdateUserName(userName);
        goodsShippingFeeMapper.updateByPrimaryKeySelective(goodsShippingFee);
        return true;
    }

    @Override
    public GoodsShippingFeeInfoDTO findDetailById(String id) {
        GoodsShippingFee goodsShippingFee = goodsShippingFeeMapper.selectByPrimaryKey(id);
        GoodsShippingFeeInfoDTO resDTO = new GoodsShippingFeeInfoDTO();
        BeanUtils.copyProperties(goodsShippingFee,resDTO);
        toLocalDateAndTime(goodsShippingFee,resDTO);
        return resDTO;
    }

    @Override
    @Transactional
    public Boolean updateGoodsShippingFee(GoodsShippingFeeCreateDTO reqDTO) {
        GoodsShippingFee goodsShippingFee = goodsShippingFeeMapper.selectByPrimaryKey(reqDTO.getId());
        BeanUtils.copyProperties(reqDTO,goodsShippingFee);
        //验重
        GoodsShippingFee shippingFee = new GoodsShippingFee();
        BeanUtils.copyProperties(reqDTO,shippingFee);
        shippingFee.setId(goodsShippingFee.getId());
        if (checkCount(shippingFee) > 0) {
            return false;
        }
        goodsShippingFee.setUpdateTime(new Date());
        goodsShippingFee.setUpdateUser(reqDTO.getUpdateUser());
        goodsShippingFee.setUpdateUserName(reqDTO.getUpdateUserName());
        goodsShippingFeeMapper.updateByPrimaryKey(goodsShippingFee);
        return true;
    }

    @Override
    @Transactional
    public Boolean insertGoodsShippingFee(GoodsShippingFeeCreateDTO reqDTO) {
        GoodsShippingFee goodsShippingFee = new GoodsShippingFee();
        BeanUtils.copyProperties(reqDTO,goodsShippingFee);
        goodsShippingFee.setDelFlg(false);
        goodsShippingFee.setId(uuidGenerator.gain());
        goodsShippingFee.setCreateTime(new Date());
        goodsShippingFee.setCreateUser(reqDTO.getCreateUser());
        goodsShippingFee.setCreateUserName(reqDTO.getCreateUserName());
        goodsShippingFee.setUpdateTime(new Date());
        goodsShippingFee.setUpdateUser(reqDTO.getCreateUser());
        goodsShippingFee.setUpdateUserName(reqDTO.getCreateUserName());
        Condition condition = new Condition(Goods.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false)
                .andEqualTo("goodsCode",goodsShippingFee.getGoodsCode());
        List<Goods> goods = goodsMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(goods)){
            goodsShippingFee.setSapMaterialCode(goods.get(0).getSapMaterialCode());
        }
        //验重
        GoodsShippingFee shippingFee = new GoodsShippingFee();
        BeanUtils.copyProperties(reqDTO,shippingFee);
        shippingFee.setSapMaterialCode(goodsShippingFee.getSapMaterialCode());
        if (checkCount(shippingFee) > 0) {
            return false;
        }
        goodsShippingFeeMapper.insert(goodsShippingFee);
        return true;
    }

    @Override
    @Transactional
    public ItemResult<String> importExcel(GoodsShippingFeeImportExcelInfoDTO reqDTO) {
        List<String> sapCodeList = reqDTO.getDataList().stream().map(GoodsShippingFeeImportExcelDTO::getSapMaterialCode).collect(Collectors.toList());
        Condition condition = new Condition(GoodsShippingFee.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false)
                .andIn("sapMaterialCode",sapCodeList);
        List<Goods> goodsList = goodsMapper.selectByCondition(condition);
        String checkExcel = checkExcel(reqDTO.getDataList(),goodsList);
        if ( checkExcel != null) {
            return  ItemResult.fail(null, checkExcel);
        }
        List<GoodsShippingFee> resDTOList = new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat("MM/dd/yyyy");
        reqDTO.getDataList().forEach(data -> {
            GoodsShippingFee goodsShippingFee = new GoodsShippingFee();
            BeanUtils.copyProperties(data,goodsShippingFee);
            try {
                goodsShippingFee.setEffectStartDate(formatter.parse(data.getEffectStartDate()));
                goodsShippingFee.setEffectEndDate(formatter.parse(data.getEffectEndDate()));
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            if (StringUtils.isNotBlank(data.getPackFee())){
                goodsShippingFee.setPackFee(new BigDecimal(data.getPackFee()));
            }
            goodsShippingFee.setShippingFee(new BigDecimal(data.getShippingFee()));
            goodsShippingFee.setDelFlg(false);
            goodsShippingFee.setId(uuidGenerator.gain());
            goodsShippingFee.setCreateTime(new Date());
            goodsShippingFee.setCreateUser(reqDTO.getAccountId());
            goodsShippingFee.setCreateUserName(reqDTO.getRealName());
            goodsShippingFee.setUpdateTime(new Date());
            goodsShippingFee.setUpdateUser(reqDTO.getAccountId());
            goodsShippingFee.setUpdateUserName(reqDTO.getRealName());
            if (CollectionUtils.isNotEmpty(goodsList)){
                goodsList.forEach(goods -> {
                    if (goods.getSapMaterialCode().equals(goodsShippingFee.getSapMaterialCode())){
                        goodsShippingFee.setGoodsCode(goods.getGoodsCode());
                        goodsShippingFee.setGoodsName(goods.getGoodsName());
                    }
                });
            }
            resDTOList.add(goodsShippingFee);
        });
        return new ItemResult<>("导入" + goodsShippingFeeMapper.insertList(resDTOList) + "条");
    }

    private String checkExcel(List<GoodsShippingFeeImportExcelDTO> dataList, List<Goods> goodsList) {
        List<RegionDTO> all = regionService.findAll();
        for (int i = 0; i < dataList.size(); i++) {
            GoodsShippingFeeImportExcelDTO data = dataList.get(i);
            //检查sap编码
            String sapCheck = checkSap(data, goodsList, i);
            if (sapCheck != null){
                return sapCheck;
            }
            //检查省市区
            String checkCity = checkCity(all, data, i);
            if (checkCity != null){
                return checkCity;
            }
            //包装方式格式
            String checkPack = checkPack(data, i);
            if (checkPack != null){
                return checkPack;
            }
            data.setPack(PackEnum.getCodeByName(data.getPack()));
            //检查运费和包装费不超过两位小数
            String checkFee = checkFee(data, i);
            if (checkFee != null){
                return checkFee;
            }
            //检查日期
            String checkDate = checkDate(data, i);
            if (checkDate != null){
                return checkDate;
            }
            //校验唯一性
            GoodsShippingFee shippingFee = new GoodsShippingFee();
            BeanUtils.copyProperties(data,shippingFee);
            if (checkCount(shippingFee) > 0) {
                return "第" + (i + 12) + "行，SAP物料编号+省名称+市名称+区/县/街道名称+包装方式不是唯一一行数据";
            }

        }
        return null;
    }

    private String checkSap(GoodsShippingFeeImportExcelDTO data , List<Goods> goodsList,int i) {
        Boolean sapFlag = false;
        //检查sap编码
        for (Goods goods : goodsList) {
            if (goods.getSapMaterialCode().equals(data.getSapMaterialCode())){
                sapFlag = true;
            }
        }
        if (!sapFlag){
            return  "第" + (i + 12) + "行，SAP物料号不存在";
        }
        return null;
    }

    private int checkCount(GoodsShippingFee data) {
        Condition condition = new Condition(GoodsShippingFee.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false)
                .andEqualTo("sapMaterialCode",data.getSapMaterialCode())
                .andEqualTo("provinceName",data.getProvinceName())
                .andEqualTo("cityName",data.getCityName())
                .andEqualTo("districtName",data.getDistrictName())
                .andEqualTo("pack",data.getPack());
        if (StringUtils.isNotBlank(data.getId()) ){
            criteria.andNotEqualTo("id",data.getId());
        }
        int count = goodsShippingFeeMapper.selectCountByCondition(condition);
        return count;
    }

    private String checkCity(List<RegionDTO> all, GoodsShippingFeeImportExcelDTO data,int i) {
        Boolean provinceFlag = false;
        Boolean cityFlag = false;
        Boolean districtFlag = false;
        for (RegionDTO regionDTO : all) {
            if (regionDTO.getName().equals(data.getProvinceName())) {
                data.setProvinceCode(regionDTO.getAdcode());
                provinceFlag = true;
            }
            if (regionDTO.getName().equals(data.getCityName())){
                data.setCityCode(regionDTO.getAdcode());
                cityFlag = true;
            }
            if (regionDTO.getName().equals(data.getDistrictName())){
                data.setDistrictCode(regionDTO.getAdcode());
                districtFlag = true;
            }
        }
        if (!provinceFlag){
            return  "第" + (i + 12) + "行，省填写错误";
        }
        if (!cityFlag){
            return "第" + (i + 12) + "行，市名称填写错误";
        }
        if (!districtFlag){
            return "第" + (i + 12) + "行，区/县/镇/街道名称填写错误";
        }
        return null;
    }

    private String checkPack(GoodsShippingFeeImportExcelDTO data,int i){
        Boolean packFlag = true;
        for (GoodsShippingFeePackEnum value : GoodsShippingFeePackEnum.values()) {
            if (value.getKey().equals(data.getPack())){
                packFlag = false;
            }
        }
        if (packFlag) {
            return "第" + (i + 12) + "行，包装方式填写错误";
        }
        return null;
    }

    private String checkFee(GoodsShippingFeeImportExcelDTO data,int i) {
        String regex = "^\\d+(\\.\\d{1,2})?$";
        if (StringUtils.isNotBlank(data.getPackFee()) && !Pattern.matches(regex, data.getPackFee())) {
            return "第" + (i + 12) + "行，包装费填写错误，请填写数字且不超过两位小数";
        }
        if (!Pattern.matches(regex, data.getShippingFee())) {
            return "第" + (i + 12) + "行，运费填写错误，请填写数字且不超过两位小数";
        }
        return null;
    }

    private String checkDate(GoodsShippingFeeImportExcelDTO data,int i) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");
            dateFormat.setLenient(false);
            // 解析字符串为日期对象
            Date startDate = dateFormat.parse(data.getEffectStartDate());
            Date endDate = dateFormat.parse(data.getEffectEndDate());
            if (startDate.after(endDate) && !startDate.equals(endDate)){
                return  "第" + (i + 12) + "行，有效开始日期大于截止日期格式";
            }
        } catch (ParseException e) {
            return "第" + (i + 12) + "行，有效开始或截止日期格式无效";
        }
        return null;
    }

    private static LocalDate toLocalDate(Date date){
        if(Objects.isNull(date))
            return null;
        LocalDateTime localDateTime = DateUtil.toLocalDateTime(date);
        if(Objects.isNull(localDateTime))
            return null;
        return localDateTime.toLocalDate();
    }

    private static void toLocalDateAndTime(GoodsShippingFee source, GoodsShippingFeeInfoDTO target) {
        target.setEffectStartDate(toLocalDate(source.getEffectStartDate()));
        target.setEffectEndDate(toLocalDate(source.getEffectEndDate()));
        target.setCreateTime(DateUtil.toLocalDateTime(source.getCreateTime()));
        target.setUpdateTime(DateUtil.toLocalDateTime(source.getUpdateTime()));
    }

}
