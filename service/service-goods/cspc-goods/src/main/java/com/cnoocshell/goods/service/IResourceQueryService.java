package com.cnoocshell.goods.service;

import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.dao.vo.Resource;
import com.github.pagehelper.PageInfo;

import java.util.Collection;
import java.util.List;

/**
 * 资源挂牌查询服务
 */
public interface IResourceQueryService {

    /**
     * 卖家-挂牌列表
     * @param reqResourceSellerDTO 查询DTO
     * @return 分页卖家挂牌列表
     */
    PageInfo<ResourceSellerDTO> pageResourceSeller(ReqResourceSellerDTO reqResourceSellerDTO);


    List<Resource> getResource(Collection<String> ids);

    /**
     * 查询当前商品当前卖家有效的挂牌的资源列表
     * @param sellerId
     * @param goodsId
     * @return
     */
    List<Resource> getEffectiveResourceList(String sellerId, String goodsId, Collection<String> statusList);


    List<Resource> getEffectiveResourceList(Collection<String> sellerIds, Collection<String> statusList);

    /**
     * 复杂挂牌详情
     * @param resourceId 资源编号
     * @return 复杂挂牌详情
     */
    ResourceDTO getComplexResourceDetail(String resourceId);


}
