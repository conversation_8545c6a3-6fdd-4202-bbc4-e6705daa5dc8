package com.cnoocshell.goods.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.result.PageData;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.dto.base.PageQuery;
import com.cnoocshell.goods.api.dto.goods.GoodCodesListDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsAndCategoryInfoResultDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsDataListDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsListDTO;
import com.cnoocshell.goods.api.enums.GoodsStatusEnum;
import com.cnoocshell.goods.service.IGoodsService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * @Author: <EMAIL>
 * @Description GoodsController
 * @date 2018年8月20日 上午10:29:31
 */
@Slf4j
@RestController
@RequestMapping("/goods")
@Api(tags = {"GoodsController"}, description = "商品服务")
public class GoodsController {

    @Autowired
    private IGoodsService goodsService;


    @ApiOperation(value = "分页查看卖家商品列表")
    @PostMapping(value = "/pageSellerGoods")
    public ItemResult<PageInfo<SellerGoodsDTO>> pageSellerGoods(@RequestBody @ApiParam("卖家商品分页DTO") PageSellerGoodsDTO arg0)  {
        return new ItemResult<>(goodsService.pageSellerGoods(arg0));
    }

    @ApiOperation(value = "首页商品列表-已上架")
    @PostMapping(value = "/pageGoods")
    public ItemResult<PageInfo<SellerGoodsDTO>> pageGoods(@RequestBody @ApiParam("卖家商品分页DTO") PageSellerGoodsDTO arg0)  {
        return new ItemResult<>(goodsService.pageGoods(arg0));
    }

    @ApiOperation(value = "创建卖家商品")
    @PostMapping(value = "/createSellerGoods")
    public ItemResult<Boolean> createSellerGoods(@RequestBody @ApiParam("卖家商品DTO") SellerGoodsDTO arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1)  {
        ItemResult<Boolean> result = new ItemResult<Boolean>();
        goodsService.createSellerGoods(arg0, arg1);
        result.setData(true);
        result.setSuccess(true);
        return new ItemResult<>(true);
    }

    @ApiOperation(value = "修改卖家商品")
    @PostMapping(value = "/updateSellerGoods")
    public ItemResult<Boolean> updateSellerGoods(@RequestBody @ApiParam("卖家商品DTO") SellerGoodsDTO arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1)  {
        ItemResult<Boolean> result = new ItemResult<Boolean>();
        goodsService.updateSellerGoods(arg0, arg1);
        result.setData(true);
        result.setSuccess(true);
        return new ItemResult<>(true);
    }


    @ApiOperation(value = "删除商品（逻辑删除，支持批量删除）")
    @PostMapping(value = "/delete")
    public ItemResult<Boolean> delete(@ApiParam("商品ID") String goodsIds, @ApiParam("操作人") String operator) {
        ItemResult<Boolean> result = new ItemResult<Boolean>();
        goodsService.deleteGoods(goodsIds, operator);
        result.setData(true);
        result.setSuccess(true);
        return result;
    }

    @ApiOperation(value = "更新商品状态")
    @PostMapping(value = "/updateStatus")
    public ItemResult<String> updateStatus(@ApiParam("商品ID") String goodsId, @ApiParam("商品状态") Integer goodsStatus, @ApiParam("操作人") String operator) {
        return goodsService.updateStatus(goodsId, goodsStatus, operator);
    }

    @ApiOperation(value = "查看卖家商品详情")
    @PostMapping(value = "/getSellerGoodsDetail")
    public ItemResult<SellerGoodsDTO> getSellerGoodsDetail(@RequestParam("arg0") @ApiParam("商品ID") String arg0)  {
        return new ItemResult<>(goodsService.getSellerGoodsDetail(arg0));
    }

    @ApiOperation(value = "分页查看标准商品列表")
    @PostMapping(value = "/pageBaseGoods")
    public ItemResult<PageInfo<BaseGoodsDTO>> pageBaseGoods(@RequestBody @ApiParam("标准商品分页DTO") PageBaseGoodsDTO arg0)  {
        return new ItemResult<>(goodsService.pageBaseGoods(arg0));
    }

    @ApiOperation(value = "选择品类（平台商品）")
    @GetMapping(value = "/findBaseGoodsLikeName")
    public ItemResult<List<GoodsDTO>> findBaseGoodsLikeName(
            @ApiParam("名称") String name,
            @ApiParam("分类类型") Integer categoryType,
            @RequestParam(value = "seller",required = false)String seller) {
        ItemResult result = new ItemResult<>(goodsService.findBaseGoods(name, categoryType,seller));
        result.setSuccess(true);
        return result;
    }

    @ApiOperation(value = "查询标准商品属性")
    @PostMapping(value = "/findBaseGoodsAttribute")
    public ItemResult<List<CategoryAttributeDTO>> findBaseGoodsAttribute(@RequestParam("arg0") @ApiParam("商品ID") String arg0)  {
        return new ItemResult<>(goodsService.findBaseGoodsAttribute(arg0));
    }

    @ApiOperation(value = "商品通用查询")
    @PostMapping(value = "/goodsCommonQuery")
    public ItemResult<PageData<GoodsDTO>> goodsCommonQuery(@RequestBody @ApiParam("商品分页查询DTO") PageQuery<GoodsQueryCondDTO> pageQuery)  {
        GoodsQueryCondDTO dto = pageQuery.getQueryDTO();
        dto = Optional.ofNullable(dto).orElse(new GoodsQueryCondDTO());
        dto.setCategoryCode(null);
        pageQuery.setQueryDTO(dto);
        Integer number = Optional.ofNullable(pageQuery.getPageNum()).orElse(1);
        pageQuery.setPageNum(number);
        Integer size = Optional.ofNullable(pageQuery.getPageSize()).orElse(50);
        pageQuery.setPageSize(size);
        return new ItemResult<>(goodsService.goodsCommonQuery(pageQuery));
    }

    @ApiOperation(value = "根据物料商品编码查询查询商品,不带商品属性")
    @PostMapping(value = "/getGoodsInfoByCommodityCode")
    public ItemResult<GoodsDTO> getGoodsInfoByCommodityCode(@RequestParam("arg0") @ApiParam("商品ID") String arg0, @RequestParam("arg1") @ApiParam("卖家ID") String arg1) {
        GoodsDTO info = goodsService.getGoodsInfoByCommodityCode(arg0, arg1);
        ItemResult result = new ItemResult<>(info);
        result.setSuccess(true);
        return result;
    }

    @ApiOperation(value = "取得商品详情")
    @GetMapping(value = "/getGoodsInfo")
    public ItemResult<GoodsDTO> getGoodsInfo(@ApiParam("商品ID") String goodsId) {
        GoodsDTO info = goodsService.findGoodsById(goodsId);
        ItemResult result = new ItemResult<>(info);
        result.setSuccess(true);
        return result;
    }

    @ApiOperation(value = "取得商品详情通过goodsCode")
    @GetMapping(value = "/getGoodsInfoByGoodsCode")
    public ItemResult<GoodsDTO> getGoodsInfoByGoodsCode(@ApiParam("商品ID") String goodsCode) {
        GoodsDTO info = goodsService.findGoodsByCode(goodsCode);
        return new ItemResult<>(info);
    }

    @PostMapping(value= "/findGoodsSimpleByIds", consumes = "application/json")
    ItemResult<List<GoodsSimpleDTO>> findGoodsSimpleByIds(@RequestBody List<String> goodsIds){
        return ItemResult.success(goodsService.findGoodsSimpleByIds(goodsIds));
    }

    @GetMapping(value= "/findGoodsSimpleByCategoryId")
    ItemResult<List<GoodsSimpleDTO>> findGoodsSimpleByCategoryId(@RequestParam String categoryId){
        return ItemResult.success(goodsService.findGoodsSimpleByCategoryId(categoryId));
    }

    @PostMapping(value= "/findGoodsSimpleByCodes", consumes = "application/json")
    ItemResult<List<GoodsSimpleDTO>> findGoodsSimpleByCodes(@RequestBody List<String> goodsCodes){
        return ItemResult.success(goodsService.findGoodsSimpleByCodes(goodsCodes));
    }

    @PostMapping(value= "/queryGoodsNameByGoodsCode")
    public List<GoodsNameInfoDTO> queryGoodsNameByGoodsCode(@RequestBody GoodsCodeInfoDTO goodsCodeInfoDTO){
        return goodsService.queryGoodsNameByGoodsCode(goodsCodeInfoDTO);
    }

    @PostMapping(value= "/getGoodsListByGoodsCode")
    ItemResult<GoodsListDTO> getGoodsListByGoodsCode(@RequestBody GoodsListDTO dto){
        List<GoodsSimpleDTO> goodsSimpleByCodes = goodsService.findGoodsSimpleByCodes(dto.getGoodsCodeList());
        GoodsListDTO reDto = new GoodsListDTO();
        reDto.setGoodsList(goodsSimpleByCodes);
        reDto.setGoodsCodeList(dto.getGoodsCodeList());
        return ItemResult.success(reDto);
    }

    @PostMapping(value= "/getPackSalesGroupByGoodsCode")
    ItemResult<List<GoodsDataListDTO>> getPackSalesGroupByGoodsCode(@RequestBody GoodCodesListDTO dto){
        List<GoodsDataListDTO> list = goodsService.getPackSalesGroupByGoodsCode(dto.getGoodsCodeList());
        return ItemResult.success(list);
    }

    @GetMapping(value= "/findGoodsSimpleByLikeSapMaterialCode")
    ItemResult<List<GoodsSimpleDTO>> findGoodsSimpleByLikeSapMaterialCode(@RequestParam("sapMaterialCode")String sapMaterialCode){
        return ItemResult.success(goodsService.findGoodsSimpleByLikeSapMaterialCode(sapMaterialCode));
    }

    /**
     * {@link GoodsStatusEnum}
     */
    @ApiOperation(value = "根据商品名称模糊匹配商品信息",notes = "goodsStatus 商品状态 为空则不匹配商品状态 参考GoodsStatusEnum")
    @GetMapping("/queryGoodsByLikeName")
    List<GoodsSimpleDTO> queryGoodsByLikeName(@RequestParam String name,@RequestParam(required = false) Integer goodsStatus){
        return goodsService.queryGoodsByLikeName(name,goodsStatus);
    }

    @PostMapping("/queryGoodsAndCategoryInfoByGoodsCodes")
    public List<GoodsAndCategoryInfoResultDTO> queryGoodsAndCategoryInfoByGoodsCodes(@RequestBody List<String> goodsCodes){
        return goodsService.queryGoodsAndCategoryInfoByGoodsCodes(goodsCodes);
    }
}