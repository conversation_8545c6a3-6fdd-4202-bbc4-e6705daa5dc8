package com.cnoocshell.goods.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.GoodsSapMappingDTO;
import com.cnoocshell.goods.api.dto.GoodsSapMappingSimpleDTO;
import com.cnoocshell.goods.api.dto.QueryGoodsMappingSimpleDTO;
import com.cnoocshell.goods.service.IGoodsMappingSapService;
import com.cnoocshell.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/goodsMappingSap")
@Api(tags = {"GoodsController"}, description = "商品与SAP映射服务")
public class GoodsMappingSapController {
    private final IGoodsMappingSapService iGoodsMappingSapService;

    @PostMapping("/querySimple")
    @ApiOperation(value = "查询简单映射列表", notes = "查询简单映射列表")
    public List<GoodsSapMappingSimpleDTO> querySimple(@RequestBody QueryGoodsMappingSimpleDTO param){
        return iGoodsMappingSapService.querySimple(param.getGoodsCodes(),param.getPackList());
    }

    @PostMapping("/selectSapGoodsMappingList")
    @ApiOperation(value = "查询商品映射sap商品", notes = "查询商品映射sap商品")
    public ItemResult<PageInfo<GoodsSapMappingDTO>> selectSapGoodsMappingList(@RequestBody GoodsSapMappingDTO goodsSapMappingDTO) {
        return iGoodsMappingSapService.selectGoodsSapMapping(goodsSapMappingDTO);
    }

    @PostMapping("/createSapGoodsMapping")
    @ApiOperation(value = "保存商品映射sap商品", notes = "保存商品映射sap商品")
    public ItemResult<String> createSapGoodsMapping(@RequestBody GoodsSapMappingDTO goodsSapMappingDTO) {
        return iGoodsMappingSapService.insertGoodsSapMapping(goodsSapMappingDTO);
    }

    @PostMapping("/updateSapGoodsMapping")
    @ApiOperation(value = "修改商品映射sap商品", notes = "修改商品映射sap商品")
    public ItemResult<String> updateSapGoodsMapping(@RequestBody GoodsSapMappingDTO goodsSapMappingDTO) {
        return iGoodsMappingSapService.updateGoodsSapMapping(goodsSapMappingDTO);
    }

    @PostMapping("/deleteSapGoodsMapping")
    @ApiOperation(value = "删除商品映射sap商品", notes = "删除商品映射sap商品")
    public ItemResult<String> deleteSapGoodsMapping(@RequestBody GoodsSapMappingDTO goodsSapMappingDTO) {
        return iGoodsMappingSapService.deleteGoodsSapMapping(goodsSapMappingDTO);
    }

}
