package com.cnoocshell.goods;

import com.cnoocshell.common.annotation.ExcludeFromComponetScan;
import com.cnoocshell.kafka.annotation.EnableKafka;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@SpringBootApplication
@EnableFeignClients({
		"com.cnoocshell.goods.api",
		"com.cnoocshell.member.api",
		"com.cnoocshell.base.api",
})
@ComponentScan(value = {
		"com.cnoocshell.goods",
		"com.cnoocshell.common",
        "com.cnoocshell.kafka"},
		excludeFilters = {@ComponentScan.Filter(type = FilterType.ANNOTATION, value = ExcludeFromComponetScan.class)})
@MapperScan("com.cnoocshell.goods.dao.mapper")
@EnableKafka
@EnableAsync
@EnableCaching
@EnableDiscoveryClient
@Slf4j
public class CspcGoodsApplication {

	public static void main(String[] args) {
		SpringApplication.run(CspcGoodsApplication.class, args);
		log.info("----------------------CspcGoodsApplication is started----------------------");
	}

	@Bean(destroyMethod = "shutdown")
	public ThreadPoolExecutor threadPoolExecutor() {
		return new ThreadPoolExecutor(
				16, 32,
				60, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(1000),
				Executors.defaultThreadFactory(),
				new ThreadPoolExecutor.AbortPolicy()
		);
	}

}
