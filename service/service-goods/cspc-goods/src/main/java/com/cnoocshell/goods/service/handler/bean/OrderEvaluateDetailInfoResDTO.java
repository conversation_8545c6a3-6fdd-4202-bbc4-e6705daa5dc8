package com.cnoocshell.goods.service.handler.bean;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OrderEvaluateDetailInfoResDTO {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 买家名称
     */
    private String buyerName;

    /**
     * 卖家名称
     */
    private String sellerName;

    /**
     * 订单创建时间
     */
    private Date createTime;

    /**
     * 订单完成时间
     */
    private Date completeTime;

    /**
     * 订单评价时间
     */
    private Date evaluateDate;

    /**
     * 商品评价
     */
    private List<EvaluateGoodsListDTO> evaluateGoodsList;
}
