package com.cnoocshell.goods.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.cron.pattern.CronPatternBuilder;
import cn.hutool.cron.pattern.Part;
import com.alibaba.fastjson.JSON;
import com.cnoocshell.common.annotation.PrintArgs;
import com.cnoocshell.common.dto.XxlJobDTO;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.service.IXxlJobService;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.cnoocshell.common.utils.DateUtil;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.enums.*;
import com.cnoocshell.goods.biz.IGoodCategoryRelationBiz;
import com.cnoocshell.goods.biz.IGoodsBiz;
import com.cnoocshell.goods.biz.IResourceBiz;
import com.cnoocshell.goods.config.XxlJobConfig;
import com.cnoocshell.goods.dao.vo.GoodCategoryRelation;
import com.cnoocshell.goods.dao.vo.Goods;
import com.cnoocshell.goods.dao.vo.Resource;
import com.cnoocshell.goods.exception.ResourceCode;
import com.cnoocshell.goods.service.IGoodsService;
import com.cnoocshell.goods.service.IResourceListingService;
import com.cnoocshell.goods.service.IResourceQueryService;
import com.cnoocshell.member.api.dto.member.MemberDetailDTO;
import com.cnoocshell.member.api.service.IMemberService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ResourceListingService implements IResourceListingService {

    private static final String RESOURCE_CODE_LIST = "资源编号列表";

    @Autowired
    private IGoodCategoryRelationBiz goodCategoryRelationBiz;
    @Autowired
    private IResourceBiz resourceBiz;
    @Autowired
    private IGoodsBiz goodsBiz;
    @Autowired
    private UUIDGenerator uuidGenerator;
    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private IResourceQueryService resourceQueryService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IXxlJobService iXxlJobService;

    @Autowired
    private XxlJobConfig xxlJobConfig;

    private SecureRandom random = new SecureRandom();

    /**
     * 创建资源
     *
     * @param createResourceDTO 入参
     * @param operator          操作员
     */
    @PrintArgs
    @Override
    public void createResource(CreateResourceDTO createResourceDTO, String operator) {
        checkOperator(operator);
        String goodsId = createResourceDTO.getGoodsId();
        //查询商品信息
        Goods goods = goodsBiz.get(goodsId);
        if (goods == null) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, "查询商品信息异常");
        }
        Integer goodsType = goods.getGoodsType();

        if (GoodsTypeEnum.BASE.getCode() == goodsType) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "不能挂牌标准商品");
        }
        Condition newCondition = goodCategoryRelationBiz.newCondition();
        newCondition.createCriteria().andIn("goodId", Arrays.asList(goods.getGoodsId(), goods.getSpuId()));
        List<GoodCategoryRelation> relations = goodCategoryRelationBiz.findByCondition(newCondition);
        if (CollectionUtils.isEmpty(relations)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "该商品未关联分类");
        }

        // 检查是否已挂牌
        Condition condition = resourceBiz.newCondition();
        condition.and()
                .andEqualTo("goodsId", goodsId)
                .andIn("status", Lists.newArrayList(ResourceStatusEnum.RES_STATUS100.code(), ResourceStatusEnum.RES_STATUS200.code()));
        List<Resource> exitsResources = resourceBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(exitsResources)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "该资源已创建");
        }
        //获取挂牌规则
        List<Resource> resources = Lists.newArrayList();

        log.info("定价规则：{}", (createResourceDTO.getPriceRule()));
        Map<String, Object> newRuleMap = JSON.parseObject(createResourceDTO.getPriceRule());
        newRuleMap.forEach((key, value) -> {
            Resource prepareResource = new Resource();
            //参数组装
            prepareResource.setResourceId(uuidGenerator.gain());
            prepareResource.setResourceCode(generatorResourceCode()); //生成资源编号
            //设置商品信息
            convertGoodsResource(prepareResource, goods);
            //设置卖家信息
            updateSellerInfo(createResourceDTO.getSellerId(), prepareResource);
            //转换创建参数信息
            convertCreateResourceDTO(prepareResource, createResourceDTO);
            resources.add(prepareResource);
        });

        //批量挂牌
        if (CollectionUtils.isNotEmpty(resources)) {
            resourceBiz.batchInsert(resources, operator);
        }

        //定时挂牌修改状态
        try {
            if (createResourceDTO.getIfDown() || createResourceDTO.getIfUp()) {
                Date startTime = Objects.nonNull(createResourceDTO.getFixUptime()) ? createResourceDTO.getFixUptime() : createResourceDTO.getFixDowntime();
                createXllJob(resources.get(0), startTime);
            }
        }catch (Exception e) {
            log.error("createResource createXllJob is error", e);
        }
    }



    /**
     * 手动下架
     *
     * @param resourceIds 资源编号列表
     * @param operator    操作员
     */
    @Override
    public ItemResult<String> manualOffSale(List<String> resourceIds, String operator) {
        // 参数检查
        if (CollectionUtils.isEmpty(resourceIds)) {
            return new ItemResult<>(ResourceCode.PARAM_NULL.getCode(), "参数为空");
        }
        if (StringUtils.isBlank(operator)) {
            return new ItemResult<>(ResourceCode.PARAM_NULL.getCode(), "操作人");

        }
        //下架处理
        List<Resource> resources = Lists.newArrayList();
        String errorMsg = "";
        for (String resourceId : resourceIds) {
            Resource resource = resourceBiz.get(resourceId);
            if (resource == null) {
                throw new BizException(ResourceCode.DATA_NOT_FOUND, "资源");
            }
            if (!resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())) {
                errorMsg = "只能下架已上架状态的商品！";
            }

            resource.setStatus(ResourceStatusEnum.RES_STATUS200.code());
            resource.setUpdateUser(operator);
            resource.setDownTime(new Date());
            resource.setUpdateTime(new Date());
            resources.add(resource);
        }
        if (StringUtils.isNotEmpty(errorMsg)) {
            return new ItemResult<>(BasicCode.INVALID_PARAM.getCode(), errorMsg);
        }
        //批量修改资源状态
        if (CollectionUtils.isNotEmpty(resources)) {
            resourceBiz.batchUpdateByPrimaryKeySelective(resources);
        }

        return new ItemResult<>("下架成功");
    }

    /**
     * 手动上架
     */
    @Override
    public void manualOnSale(ManualOnSaleDTO manualOnSaleDTO) {
        log.info("manualOnSale:{}", manualOnSaleDTO);
        List<String> resourceIds = manualOnSaleDTO.getResourceIds();
        String operator = manualOnSaleDTO.getOperator();
        // 参数检查
        if (StringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        if (CollectionUtils.isEmpty(resourceIds)) {
            throw new BizException(ResourceCode.PARAM_NULL, RESOURCE_CODE_LIST);
        }
        //上架处理
        List<Resource> resources = Lists.newArrayList();
        List<Resource> beOnSaleResourceList = resourceQueryService.getResource(resourceIds);
        for (Resource resource : beOnSaleResourceList) {
            if (!resource.getStatus().equals(ResourceStatusEnum.RES_STATUS200.code()) &&
                    !resource.getStatus().equals(ResourceStatusEnum.RES_STATUS400.code())) {
                String message = StringUtils.join(Lists.newArrayList(
                        resource.getGoodsName()), "-");
                throw new BizException(ResourceCode.UNKNOWN_ERROR, "只能上架已下架状态、未上架的商品。以下商品不能上架：" + message);
            }
//            checkIfDownTime(resource);
            resource.setUpdateUser(operator);
            resource.setUpTime(new Date());
            resource.setUpdateTime(new Date());
            resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
            resources.add(resource);
        }
        //批量修改资源状态
        if (CollectionUtils.isNotEmpty(resources)) {
            resourceBiz.batchUpdateByPrimaryKeySelective(resources);
        }
    }

    /**
     * 操作人检查
     *
     * @param operator 操作员
     */
    private void checkOperator(String operator) {
        if (StringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }

    /**
     * 转换商品资源数据
     *
     * @param resource 资源对象
     * @param goods    商品对象
     */
    private void convertGoodsResource(Resource resource, Goods goods) {
        resource.setGoodsId(goods.getGoodsId());
        resource.setGoodsName(goods.getGoodsName());
        resource.setGoodsDescribe(goods.getSearchKeywords());

        resource.setResourceName(goods.getGoodsName());
        //袋 -> 吨 (换算率)
        resource.setPriceUnit(goods.getMeasureUnit());
        resource.setSaleUnit(goods.getUnit());
        // 设置商品属性
        String resourceAttributes = "";
        List<String> attributesList = new ArrayList<>();
        // 查询商品属性
        List<GoodsCategoryAttrDTO> attrVars = goodsService.getCategoryAttrByGoodsId(goods.getGoodsId());
        attributesListAddValue(attrVars, attributesList);
        Collections.sort(attributesList);
        if (CollectionUtils.isNotEmpty(attributesList)) {
            for (String attributeStr : attributesList) {
                resourceAttributes = resourceAttributes.concat(attributeStr)
                        .concat(MergeAttrPrefixEnum.OUTSIDE.getCode());
            }
        }
        resource.setResourceAttributes(resourceAttributes);
    }


    private static void attributesListAddValue(List<GoodsCategoryAttrDTO> attrVars, List<String> attributesList) {
        if (attrVars != null && attrVars.size() > 0) {
            for (GoodsCategoryAttrDTO goodsCategoryAttrDTO : attrVars) {
                String attrValueCode = goodsCategoryAttrDTO.getValueCode();
                String goodsAttrId = goodsCategoryAttrDTO.getGoodsAttriId();
                if (StringUtils.isNotBlank(attrValueCode)
                        && StringUtils.isNotBlank(goodsAttrId)) {
                    String attributeStr = goodsAttrId.concat(MergeAttrPrefixEnum.INSTIDE.getCode())
                            .concat(attrValueCode);
                    if (!attributesList.contains(attributeStr)) {
                        attributesList.add(attributeStr);
                    }
                }
            }
        }
    }

    /**
     * 转换外部数据
     *
     * @param resource          资源对象
     * @param createResourceDTO 资源创建对象
     */
    private void convertCreateResourceDTO(Resource resource, CreateResourceDTO createResourceDTO) {
        //定价模式
        //已销售数量

        resource.setSpuId(createResourceDTO.getSpuId());
        //设置空载费和台班费规则

        //支持的配送方式(默认支持自提)

        //优先配送方式默认为卖家配送

        //设置是否可议价
//        if (createResourceDTO.getIfPlatformDelivery() &&
//                PriceWayEnum.PRICE_TYPE1.code().equals(createResourceDTO.getPriceWay())) {
//            resource.setArrivePrice(null);
//            resource.setIfPlatformDelivery(false);
//        }
        //支付方式
        //生效时间，新增立即生效
        //资源版本号
        resource.setSellerId(createResourceDTO.getSellerId());
        //设置上下架时间
        boolean ifUp = (resource.getIfup() != null && resource.getIfup()) || (createResourceDTO.getIfUp() != null && createResourceDTO.getIfUp());
        log.info("ifUp:{},resource.getIfup():{},createResourceDTO.getIfUp():{}", ifUp, resource.getIfup(), createResourceDTO.getIfUp());
        resource.setIfup(ifUp);
        if (!ifUp) {
            resource.setUpTime(new Date());
            resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
        } else {
            resource.setStatus(ResourceStatusEnum.RES_STATUS400.code());
        }
        resource.setIfdown(createResourceDTO.getIfDown());
        //交易时间、(支付有效期、提货/发货有效期,存到数据库采用毫秒形式)
    }


    private static void checkIfDownTime(Resource resource) {
        log.info("checkIfDownTime:{}", resource);
        if (resource != null && BooleanUtils.isTrue(resource.getIfdown())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品挂牌失败：定时下架时间为过去的时间！");
        }
    }

    @PrintArgs
    @Override
    public void batchUpdateResource(BatchUpdateResourceDTO batchUpdateResourceDTO) throws Exception {
        //参数检查
        batchUpdateParamCheckout(batchUpdateResourceDTO);
        //获取当前商品的所有未上架资源
        List<Resource> currentResourceList = resourceQueryService.getEffectiveResourceList(
                batchUpdateResourceDTO.getSellerId(),
                batchUpdateResourceDTO.getGoodsId(),
                Lists.newArrayList(ResourceStatusEnum.RES_STATUS200.getCode(),
                        ResourceStatusEnum.RES_STATUS300.getCode(),
                        ResourceStatusEnum.RES_STATUS400.getCode(),
                        ResourceStatusEnum.RES_STATUS500.getCode()));
        if (CollectionUtils.isEmpty(currentResourceList)) {
            return;
        }
        List<Resource> updateResourceList = Lists.newArrayList();

        for (Resource resource : currentResourceList) {
            //未上架的资源（即定时上架的资源） 不参与批量修改
            if (!ResourceStatusEnum.RES_STATUS400.getCode().equals(resource.getStatus())) {
                continue;
            }
            //更新销售区域的行政区域归属
            resource.setUpdateTime(new Date());
            resource.setUpdateUser(batchUpdateResourceDTO.getOperator());
            updateResourceList.add(resource);
        }
        //批量修改资源状态
        if (CollectionUtils.isNotEmpty(updateResourceList)) {
            resourceBiz.batchUpdateByPrimaryKeySelective(updateResourceList);
        }
    }

    /**
     * 参数检查
     *
     * @param batchUpdateResourceDTO 批量修改资源对象
     */
    private void batchUpdateParamCheckout(BatchUpdateResourceDTO batchUpdateResourceDTO) {
        if (StringUtils.isBlank(batchUpdateResourceDTO.getGoodsId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "卖家商品ID");
        }
        if (StringUtils.isBlank(batchUpdateResourceDTO.getSellerId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "卖家ID");
        }
        if (StringUtils.isBlank(batchUpdateResourceDTO.getOperator())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作人");
        }
    }

    /**
     * 更新资源
     *
     * @param req      入参
     * @param operator 操作员
     */
    @PrintArgs
    @Override
    public void updateResource(UpdateResourceDTO req, String operator) {
        //参数检查
        checkUpdateResource(req, OperateTypeEnum.CREATE.code());
        checkOperator(operator);
        //查询需要修改的资源
        List<Resource> resources = getResourcesForUpdate(req);
        if (CollectionUtils.isEmpty(resources)) {
            log.info("要更新的资源没有找到");
            return;
        }
        List<Resource> onSaleResourceList = resourceQueryService.getEffectiveResourceList(resources.stream().map(Resource::getSellerId).collect(Collectors.toSet()), Lists.newArrayList(ResourceStatusEnum.RES_STATUS100.getCode()));
        //如果前端编辑的是已上架的资源，且是定时上架的，则需要新增其副本来保存，不能立即更新
        List<Resource> addResources = Lists.newArrayList();
        //原来的逻辑，需要更新的资源
        List<Resource> updateResources = Lists.newArrayList();
        Date now = new Date();
        //更新参数
        resourceSetValue(req, resources, now, updateResources, onSaleResourceList, addResources);

        if (CollectionUtils.isNotEmpty(updateResources)) {
            log.info("批量修改资源并生成历史");
            //批量修改
            resourceBiz.batchUpdateResources(updateResources);
            //生成资源镜像
        }
        //如果是修改已上架的资源，且是定时上架，则将这种数据保存为新增资源
//        if (CollectionUtils.isNotEmpty(addResources)) {
//            log.info("批量新增资源并生成历史");
//            //批量新增 该方法会设置新的资源id的
//            resourceBiz.batchInsert(addResources, operator);
//        }
        //定时挂牌修改状态
        try {
            if (req.getIfup() || req.getIfdown()) {
                Date startTime = Objects.nonNull(req.getFixUptime()) ? req.getFixUptime() : req.getFixDowntime();
                createXllJob(resources.get(0), startTime);
            }
        }catch (Exception e) {
            log.error("createResource createXllJob is error", e);
        }

    }

    private void resourceSetValue(UpdateResourceDTO req, List<Resource> resources, Date now, List<Resource> updateResources, List<Resource> onSaleResourceList, List<Resource> addResources) {
        resources.forEach(resource -> {
            //立即上架
            if (Boolean.TRUE.equals(req.getIfEffectNow())) {
                resource.setUpTime(now);
                resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
                updateResources.add(resource);
            }
            resource.setUpdateTime(new Date());
            //设置空载费和台班费规则
            // 商品定价信息
            //设置是否可议价
            // 商品数量信息
            // 物流价格
            //支持的配送方式(默认支持自提)
            //优先配送方式默认为卖家配送
            // 商品交付信息
            // 商品其他信息
            Boolean oldIfup = resource.getIfup();
            // 设置上下架时间
            resource.setIfup(req.getIfup());
            //如果是立即上架
            if (Boolean.FALSE.equals(req.getIfup())) {
                updateResourcesAddValue(resource, now, oldIfup, onSaleResourceList, updateResources);
            } else {
                resouceAddValue(req, resource, addResources, updateResources);
            }
            resource.setIfdown(req.getIfdown());
            resource.setUpTime(req.getFixUptime());
            resource.setDownTime(req.getFixDowntime());
            if (req.getIfdown()) {
                if (req.getFixDowntime() == null) {
                    throw new BizException(ResourceCode.UNKNOWN_ERROR, "定时下架需要选择定时下架时间！");
                }
//                checkIfDownTime(resource);
            }
            // 交易时间、(支付有效期、提货/发货有效期,存到数据库采用毫秒形式)
        });
    }


    /**
     * 更新资源参数检测
     *
     * @param req         入参
     * @param operateType 操作类型
     */
    private void checkUpdateResource(UpdateResourceDTO req, String operateType) {
        if (StringUtils.isBlank(req.getGoodsId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        if (StringUtils.isNotBlank(req.getAutoCompleteType())) {
            if (req.getAutoCompleteThreshold() == null) {
                throw new BizException(ResourceCode.PARAM_NULL, "自动完成阀值");
            }
        }
        //浮动比例
        if (StringUtils.equals(req.getAutoCompleteType(), "floatScale")) {
            if (req.getAutoCompleteThreshold().compareTo(new BigDecimal(100)) >= 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "自动完成浮动比例不能超过100%");
            }
        }
    }


    /**
     * 根据资源id或者商品id查询资源
     */
    private List<Resource> getResourcesForUpdate(UpdateResourceDTO req) {
        List<Resource> resources = Lists.newArrayList();
        if (StringUtils.isNotEmpty(req.getResourceId())) {
            Resource resource = resourceBiz.get(req.getResourceId());
            resources.add(resource);
        } else {
            Condition condition = new Condition(Resource.class);
            Example.Criteria criteria = condition.createCriteria();
            criteria.andEqualTo("goodsId", req.getGoodsId());
            criteria.andEqualTo("delFlg", false);
            resources = resourceBiz.findByCondition(condition);
        }
        return resources;
    }




    private void updateResourcesAddValue(Resource resource, Date now, Boolean oldIfup, List<Resource> onSaleResourceList, List<Resource> updateResources) {
        resource.setUpTime(now);
        resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
        updateResources.add(resource);
    }

    private static void resouceAddValue(UpdateResourceDTO req, Resource resource, List<Resource> addResources, List<Resource> updateResources) {
        if (req.getFixUptime() == null) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, "定时上架需要选择定时上架时间！");
        }
        //如果编辑的是已上架资源的修改
//        if (ResourceStatusEnum.RES_STATUS100.code().equals(resource.getStatus())) {
//            addResources.add(resource);
//        } else {
//            updateResources.add(resource);
//        }
        updateResources.add(resource);
        resource.setStatus(ResourceStatusEnum.RES_STATUS400.code());
    }

    /**
     * 生成资源CODE
     *
     * @return 资源CODE
     */
    private String generatorResourceCode() {
        String defaultDateTimeStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            result.append(this.random.nextInt(10));
        }
        return "SP" + defaultDateTimeStr + result.toString();
    }

    private void updateSellerInfo(String sellerId, Resource resource) {
        MemberDetailDTO member = memberService.findMemberById(sellerId);
        if (member != null) {
            resource.setSellerName(member.getMemberName());
            resource.setSellerNickName(member.getMemberShortName());
        }
    }

    public void createXllJob(Resource resource, Date startTime) {
        String notifyJobDesc = CharSequenceUtil.format("商品:{}即将上架开始自动任务", resource.getResourceCode());
        log.info("ResourceListingService createXllJob resource:{}", resource);

        //通知客户自动任务
        CronPatternBuilder notifyCron = CronPatternBuilder.of();
        //定时上架
        notifyCron.set(Part.SECOND, String.valueOf(cn.hutool.core.date.DateUtil.second(startTime)));
        notifyCron.set(Part.MINUTE, String.valueOf(cn.hutool.core.date.DateUtil.minute(startTime)));
        notifyCron.set(Part.HOUR, String.valueOf(cn.hutool.core.date.DateUtil.hour(startTime, true)));
        notifyCron.set(Part.DAY_OF_MONTH, String.valueOf(cn.hutool.core.date.DateUtil.dayOfMonth(startTime)));
        notifyCron.set(Part.MONTH, String.valueOf(cn.hutool.core.date.DateUtil.month(startTime) + 1));
        notifyCron.set(Part.DAY_OF_WEEK, "?");
        Integer year = cn.hutool.core.date.DateUtil.year(startTime);
        notifyCron.setRange(Part.YEAR, year, year);
        XxlJobDTO notifyJob = new XxlJobDTO(xxlJobConfig.getAppName(),
                notifyJobDesc,
                notifyCron.build(),
                "putOnResource",
                resource.getResourceId());
        iXxlJobService.createXxlJob(notifyJob);
        log.info("ResourceListingService createXllJob resourceName:{} putOnResource is end", resource.getResourceCode());
    }


}
