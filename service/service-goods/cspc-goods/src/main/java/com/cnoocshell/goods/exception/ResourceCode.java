package com.cnoocshell.goods.exception;

import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.CodeMeta;

public class ResourceCode extends BasicCode{
    public static final CodeMeta PARAM_NULL = new CodeMeta("0360101", "PARAM_NULL", "{}不能为空",
            "{} can not be empty");
    public static final CodeMeta PARAM_ERROR = new CodeMeta("0360102", "PARAM_ERROR", "参数错误，{}",
            "param error {}");
    public static final CodeMeta DATA_NOT_FOUND = new CodeMeta("0360103", "DATA_NOT_FOUND", "未查询到{}",
            "data not find {}");
    public static final CodeMeta CAN_NOT_CREATE = new CodeMeta("0360104", "CAN_NOT_CREATE", "{}",
            "can not create {}");
    public static final CodeMeta CAN_NOT_UPDATE = new CodeMeta("0360105", "CAN_NOT_UPDATE", "{}",
            "can not update {}");
    public static final CodeMeta NOT_CHANGE = new CodeMeta("0360106", "NOT_CHANGE", "{}",
            "not change {}");
    public static final CodeMeta UNKNOWN_ERROR = new CodeMeta("0360107", "", "{}",
            "{}");

    public static final CodeMeta PRICE_CHANGE = new CodeMeta("0360108", "PRICE_CHANGE_EXCEPTION", "资源价格调整异常{}",
            "price change exception{}");
//    public static final CodeMeta VALUE_ERROR = new CodeMeta("0360104", "VALUE_ERROR", "{}错误", "{} error");
//
//    public static final CodeMeta TAKE_TIME_LIMIT = new CodeMeta("0360105", "TAKE_TIME_LIMIT", "提货有效期已到");
//
//    public static final CodeMeta LOGISTIC_ERROR_CREATE = new CodeMeta("0360106", "LOGISTIC_ERROR_CREATE", "物流处理发货单失败 {} ") ;
//
//    public static final CodeMeta LOGISTIC_ERROR_CLOSE = new CodeMeta("0360107", "LOGISTIC_ERROR_CLOSE", "物流关闭发货单失败 {} ");
//
//    public static final CodeMeta LOGISTIC_ERROR_SENDINONE = new CodeMeta("0360108", "LOGISTIC_ERROR_SENDINONE", "{}");

}
