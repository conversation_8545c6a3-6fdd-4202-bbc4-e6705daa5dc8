package com.cnoocshell.goods.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.GoodsSapMappingDTO;
import com.cnoocshell.goods.api.dto.GoodsSapMappingSimpleDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface IGoodsMappingSapService {
    List<GoodsSapMappingSimpleDTO> querySimple(List<String> goodsCode, List<String> packList);

    ItemResult<PageInfo<GoodsSapMappingDTO>> selectGoodsSapMapping(GoodsSapMappingDTO sapMappingDTO);
    ItemResult<String> insertGoodsSapMapping(GoodsSapMappingDTO sapMappingDTO);
    ItemResult<String> updateGoodsSapMapping(GoodsSapMappingDTO sapMappingDTO);
    ItemResult<String> deleteGoodsSapMapping(GoodsSapMappingDTO sapMappingDTO);

}
