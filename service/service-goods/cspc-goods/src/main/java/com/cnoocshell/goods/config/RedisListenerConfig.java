package com.cnoocshell.goods.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

/**
 * @Description: RedisListenerConfig
 * @Author: <EMAIL>
 * @Date: 2021-05-19 11:07
 */
@Configuration
public class RedisListenerConfig {

    @Bean("expiredRedisMessageListenerContainer")
    public RedisMessageListenerContainer container(@Qualifier("bizRedisConnectionFactory") RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
//        container.addMessageListener(new RedisExpiredListener(), new PatternTopic("__keyevent@0__:expired"));
        return container;
    }

}
