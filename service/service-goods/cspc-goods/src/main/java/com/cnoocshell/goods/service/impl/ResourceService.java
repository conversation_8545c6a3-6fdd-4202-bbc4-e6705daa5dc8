package com.cnoocshell.goods.service.impl;

import com.cnoocshell.common.annotation.AddLog;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.utils.BeanConvertUtils;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.enums.ResourceStatusEnum;
import com.cnoocshell.goods.biz.IResourceBiz;
import com.cnoocshell.goods.dao.vo.Resource;
import com.cnoocshell.goods.exception.ResourceCode;
import com.cnoocshell.goods.service.IGoodsService;
import com.cnoocshell.goods.service.IResourceService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ResourceService implements IResourceService {

    @Autowired
    private IResourceBiz iResourceBiz;

    @Override
    public PageInfo<ResourceSellerDTO> pageResourceSeller(ReqResourceSellerDTO reqDto) {
//        if(reqDto.getRegionList()==null||reqDto.getRegionList().size()==0){
//            return iResourceBiz.pageResourceSeller(reqDto);
//        }
        return iResourceBiz.pageResourceSellerWithDataPerm(reqDto);
    }

    @AddLog(operatorIndex = 0,operatorFieldName = "operator")
    @Override
    public void createResource(ReqCreateResourceDTO reqCreateResourceDTO) {
        iResourceBiz.createResource(reqCreateResourceDTO);
    }

    @AddLog(operatorIndex = 0,operatorFieldName = "operator")
    @Override
    public void updateResource(ReqUpdateResourceDTO reqUpdateResourceDTO) {
        iResourceBiz.updateResource(reqUpdateResourceDTO);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void deleteResource(List<String> resourceIds, String operatorId) {
        iResourceBiz.deleteResource(resourceIds, operatorId);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void offSaleResource(String resourceId, String operator) {
        iResourceBiz.offSaleResource(resourceId, operator);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void offSaleResourceBatch(List<String> resourceIds, String operator) {
        iResourceBiz.offSaleResourceBatch(resourceIds, operator);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void onSaleResource(String resourceId, String operator) {
        iResourceBiz.onSaleResource(resourceId, operator);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void onSaleResourceBatch(List<String> resourceIds, String operator) {
        iResourceBiz.onSaleResourceBatch(resourceIds, operator);
    }


    @Override
    public PageInfo<GoodsResourceListDTO> searchGoodsResourceEmall(ReqGoodsResourceDTO reqGoodsResourceDTO) {
        return iResourceBiz.searchGoodsResourceEmall(reqGoodsResourceDTO);
    }


}
