package com.cnoocshell.goods.service.impl;

import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.utils.CommonConstants;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.enums.TradeStatusEnum;
import com.cnoocshell.goods.biz.IGoodsBiz;
import com.cnoocshell.goods.biz.IResourceBiz;
import com.cnoocshell.goods.dao.mapper.ResourceMapper;
import com.cnoocshell.goods.dao.vo.Goods;
import com.cnoocshell.goods.dao.vo.Resource;
import com.cnoocshell.goods.exception.ResourceCode;
import com.cnoocshell.goods.service.IResourceQueryService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ResourceQueryService implements IResourceQueryService {

    private static final Double HOUR = 3600d;
    private static final Double DAY = 86400d;

    private static final String CATEGORY_CODE = "categoryCode";
    private static final String RESOURCE_REGIONS = "resourceRegions";
    private static final String STATUS = "status";
    private static final String DEL_FLG = "delFlg";
    private static final String SELLER_ID = "sellerId";
    private static final String GOODS_ID = "goodsId";
    private static final String RESOURCE_ID = "resourceId";
    private static final String GOODS_RESOURCE_ID = "goodsResourceId";
    private static final String COUNTRY_NAME = "countryName";
    private static final String CITY_NAME = "cityName";
    private static final String PROVINCE_NAME = "provinceName";
    private static final String AREA_NAME = "areaName";
    private static final String STREET_NAME = "streetName";
    private static final String COUNTRY_CODE = "countryCode";
    private static final String PROVINCE_CODE = "provinceCode";
    private static final String CITY_CODE = "cityCode";
    private static final String AREA_CODE = "areaCode";
    private static final String STREET_CODE = "streetCode";
    private static final String PAGE_NUM_BEGIN_FROM_1 = "页码从1开始";
    private static final String PAGE_SIZE_MUST_MORE_THAN_0 = "页面大小必须大于0";
    private static final String RESOURCE_CODE_LIST = "资源编号列表";
    private static final String SALE_AREA_REAL_CODE = "saleAreaRealCode";

    //内部服务
    private final IResourceBiz resourceBiz;

    private final IGoodsBiz goodsBiz;

    private final GoodsService goodsService;

    @javax.annotation.Resource
    private ResourceMapper resourceMapper;


    /**
     * 卖家-挂牌列表
     *
     * @param reqDto 查询DTO
     * @return
     */
    @Override
    public PageInfo<ResourceSellerDTO> pageResourceSeller(ReqResourceSellerDTO reqDto) {
        // 参数检测
        checkParams1(reqDto);
        log.info("访问数据权限查挂牌列表");
        return PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize())
                .doSelectPageInfo(() -> resourceMapper.pageQueryWithDataPerm(reqDto).stream()
                        .collect(Collectors.toList()));
    }

    private void checkParams1(ReqResourceSellerDTO reqResourceSellerDTO) {
        if (reqResourceSellerDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        if (StringUtils.isBlank(reqResourceSellerDTO.getSellerId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "卖家编号");
        }
        if (reqResourceSellerDTO.getPageNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页码");
        }
        if (reqResourceSellerDTO.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_NUM_BEGIN_FROM_1);
        }
        if (reqResourceSellerDTO.getPageSize() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页面大小");
        }
        if (reqResourceSellerDTO.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_SIZE_MUST_MORE_THAN_0);
        }
    }


    private void listAddItems(PageInfo<Resource> page, List<ResourceSellerDTO> list) {
        for (Resource resource : page.getList()) {
            ResourceSellerDTO dto = new ResourceSellerDTO();
            BeanUtils.copyProperties(resource, dto);
            Goods goods = goodsBiz.get(resource.getGoodsId());
            if (goods != null && StringUtils.isNotBlank(goods.getImgs())) {
                dto.setImgs(goods.getImgs().split(","));
            }
            // 交易状态
            // 查询是否有资源历史记录
            list.add(dto);
        }
    }

    @Override
    public List<Resource> getEffectiveResourceList(Collection<String> sellerIds, Collection<String> statusList) {
        return getEffectiveResourceList2(sellerIds, null, statusList);
    }

    private List<Resource> getEffectiveResourceList2(Collection<String> sellerIds, String goodsId, Collection<String> statusList) {
        if (CollectionUtils.isEmpty(sellerIds) && CollectionUtils.isEmpty(statusList)) {
            return Lists.newArrayList();
        }
        Condition condition = new Condition(Resource.class);
        Example.Criteria criteria = condition.createCriteria().andEqualTo(GOODS_ID, goodsId);
        if (sellerIds.size() == 1) {
            criteria.andEqualTo(SELLER_ID, sellerIds.iterator().next());
        } else {
            criteria.andIn(SELLER_ID, sellerIds);
        }
        if (CollectionUtils.isNotEmpty(statusList)) {
            if (statusList.size() == 1) {
                criteria.andEqualTo(STATUS, statusList.iterator().next());
            } else {
                criteria.andIn(STATUS, statusList);
            }
        }
        criteria.andEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_FALSE);
        return resourceBiz.findByCondition(condition);
    }

    @Override
    public List<Resource> getResource(Collection<String> ids) {
        List<Resource> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(ids)) {
            return result;
        }
        Condition condition = new Condition(Resource.class);
        Example.Criteria criteria = condition.createCriteria();
        if (ids.size() == 1) {
            criteria.andEqualTo(RESOURCE_ID, ids.iterator().next());
        } else {
            criteria.andIn(RESOURCE_ID, ids);
        }
        criteria.andEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_FALSE);
        return resourceBiz.findByCondition(condition);
    }


    /**
     * 复杂挂牌详情
     *
     * @param resourceId 资源编号
     * @return
     */
    @Override
    public ResourceDTO getComplexResourceDetail(String resourceId) {
        //初始化结果
        ResourceDTO resourceDTO = new ResourceDTO();
        //查询资源
        Resource resource = resourceBiz.get(resourceId);
        //封装资源
        BeanUtils.copyProperties(resource, resourceDTO);
        resourceDTO.setFixUptime(resource.getUpTime());
        resourceDTO.setFixDowntime(resource.getDownTime());
        // 查询商品聚合信息
        GoodsDTO goodsInfo = goodsBiz.getGoodsInfo(resource.getGoodsId());
        SpecialGoodsAttributeDTO specialGoodsAttributeDTO = goodsService.getSpecialGoodsAttribute(goodsInfo.getGoodsId());
        goodsInfo.setConcreteFlag(specialGoodsAttributeDTO.getConcreteFlag());
        goodsInfo.setAddItemFlag(specialGoodsAttributeDTO.getAddItemFlag());
        goodsInfo.setSupportCarryFlag(specialGoodsAttributeDTO.getSupportCarryFlag());
        resourceDTO.setGoodsDTO(goodsInfo);
        // 处理其他信息
        List<String> payWays = new ArrayList<>();
        resourceDTO.setPayWay(payWays);
        // 设置交易状态
        boolean tradeStatus = resourceBiz.checkResourceTradeStatus(resource);
        if (tradeStatus) {
            resourceDTO.setTradeStatus(TradeStatusEnum.TRADE_STATUS100.code());
        } else {
            resourceDTO.setTradeStatus(TradeStatusEnum.TRADE_STATUS200.code());
        }
        return resourceDTO;
    }


    @Override
    public List<Resource> getEffectiveResourceList(String sellerId, String goodsId, Collection<String> statusList) {
        if (StringUtils.isBlank(sellerId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "卖家编号");
        }
        if (StringUtils.isBlank(goodsId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        return getEffectiveResourceList2(Lists.newArrayList(sellerId), goodsId, statusList);
    }

}
