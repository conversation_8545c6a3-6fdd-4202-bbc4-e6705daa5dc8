package com.cnoocshell.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.base.api.dto.DataPermissionDTO;
import com.cnoocshell.base.api.dto.role.AccountRoleDTO;
import com.cnoocshell.base.api.service.IRoleService;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.common.utils.CsStringUtils;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.biz.IGoodsCategoryAttrBiz;
import com.cnoocshell.goods.biz.IGoodsCategoryAttrValueBiz;
import com.cnoocshell.goods.biz.IGoodsCategoryBiz;
import com.cnoocshell.goods.cache.CategoryTreeCache;
import com.cnoocshell.goods.cache.ICategoryTreeCacheService;
import com.cnoocshell.goods.cache.ICodeNumCacheService;
import com.cnoocshell.goods.common.ColumnConstant;
import com.cnoocshell.goods.dao.mapper.GoodCategoryRelationMapper;
import com.cnoocshell.goods.dao.mapper.GoodsCategoryMapper;
import com.cnoocshell.goods.dao.mapper.GoodsMapper;
import com.cnoocshell.goods.dao.vo.*;
import com.cnoocshell.goods.exception.GoodsCode;
import com.cnoocshell.goods.service.IGoodsCategoryService;
import com.cnoocshell.member.api.dto.account.AccountSimpleDTO;
import com.cnoocshell.member.api.service.IAccountService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Description GoodsCategoryService
 * @date 2018年8月15日 下午5:20:51
 */
@Slf4j
@EnableAsync
@Service
@RequiredArgsConstructor
public class GoodsCategoryService implements IGoodsCategoryService {

    private static final String DEL_FLG = "delFlg";
    private static final String CATEGORY_TYPE = "categoryType";
    private static final String PARENT_ID = "parentId";
    private static final String CATEGORY_CODE = "categoryCode";
    private static final String GOODS_CATEGORY_ID = "商品分类ID";
    private static final String GOODS_CATEGORY_CODE = "商品分类编号";

    private final IGoodsCategoryBiz goodsCategoryBiz;

    private final IGoodsCategoryAttrBiz goodsCategoryAttrBiz;

    private final ICodeNumCacheService codeNumCacheService;

    private final ICategoryTreeCacheService categoryTreeCacheService;

    private final IGoodsCategoryAttrValueBiz goodsCategoryAttrValueBiz;


    @Resource
    private GoodCategoryRelationMapper goodCategoryRelationMapper;

    @Resource
    private GoodsMapper goodsMapper;

    @Autowired
    private IRoleService roleService;
    @Resource
    private GoodsCategoryMapper goodsCategoryMapper;

    /**
     * 的分类code
     */
    @Value("${cement-code}")
    private String cementCode;

    @Autowired
    private IAccountService accountService;

    private static final String SELLER = "SELLER";

    /**
     * generateCategoryCode
     */
    private String generateCategoryCode(GoodsCategory parent) {
        String categoryCode;
        Condition condition = new Condition(GoodsCategory.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(PARENT_ID, parent.getCategoryId());
        criteria.andEqualTo(DEL_FLG, Boolean.FALSE);
        condition.orderBy(CATEGORY_CODE).desc();
        List<GoodsCategory> byCondition = goodsCategoryBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(byCondition)) {
            categoryCode = parent.getCategoryCode() + "01";
        } else {
            GoodsCategory goodsCategory = byCondition.get(0);
            String code = goodsCategory.getCategoryCode();
            String substring = code.substring(code.length() - 2);
            int i = Integer.parseInt(substring) + 1;
            String xs = String.format("%02d", i);
            categoryCode = parent.getCategoryCode() + xs;
        }
        return categoryCode;
    }


    /**
     * batchInsertCategoryAttribute
     */
    private void batchInsertCategoryAttribute(List<CategoryAttributeDTO> categoryAttrs, String operator) {
        List<GoodsCategoryAttribute> goodsCategoryAttributes = Lists.newArrayList();
        Condition condition = new Condition(GoodsCategoryAttribute.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        List<GoodsCategoryAttribute> goodsCategoryAttributeList = goodsCategoryAttrBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(goodsCategoryAttributeList)) {
            goodsCategoryAttributeList.forEach(goodsCategoryAttribute -> {
                if (CollectionUtils.isNotEmpty(categoryAttrs)) {
                    categoryAttrs.forEach(categoryAttr -> {
                        if (categoryAttr.getAttriName().equals(goodsCategoryAttribute.getAttriName())) {
                            goodsCategoryAttributes.add(goodsCategoryAttribute);
                        }
                    });
                }
            });
            //插入商品分类基本属性
            if (CollectionUtils.isNotEmpty(goodsCategoryAttributes)) {
                goodsCategoryAttrBiz.batchInsert(goodsCategoryAttributes, operator);
            }
        }
    }

    @Override
    public void addCategory(CategoryDTO categoryDTO, String operator) {
        if (StringUtils.isBlank(categoryDTO.getParentId())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "商品分类父ID");
        }
        if (StringUtils.isBlank(categoryDTO.getCategoryName())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "商品分类名称");
        }
        if (StringUtils.isBlank(operator)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "操作人");
        }
        GoodsCategory father = goodsCategoryBiz.get(categoryDTO.getParentId());
        if (father == null) {
            throw new BizException(GoodsCode.DATA_NOT_FOUND, "商品父类");
        }
        if (StringUtils.isBlank(categoryDTO.getCategoryName())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "分类名称");
        }
        if (BooleanUtil.isTrue(goodsCategoryBiz.existCategoryBySapCode(categoryDTO.getSapCode()))) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "已存在SAP编码");
        }

        if (BooleanUtil.isTrue(goodsCategoryBiz.existCategoryByParentId(categoryDTO.getParentId(),categoryDTO.getCategoryName()))) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "已存在相同的分类名称");
        }

        //插入商品分类
        GoodsCategory goodsCategory = new GoodsCategory();
        BeanUtils.copyProperties(categoryDTO, goodsCategory);
        goodsCategory.setCategoryCode(generateCategoryCode(father));
        goodsCategoryBiz.save(goodsCategory, operator);
        //异步缓存
        categoryTreeCacheService.setCategoryTreeCache();
        // 插入分类和账号的绑定关系
//        batchInsertCategoryAcoountRelation(goodsCategory, categoryDTO, operator);
        //插入商品分类基本属性
//        batchInsertCategoryAttribute(categoryDTO.getCategoryAttributeDTOS(), operator);
    }

    @Override
    public void uptCategory(CategoryDTO categoryDTO, String operator) {
        if (StringUtils.isBlank(categoryDTO.getCategoryId())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_CATEGORY_ID);
        }
        if (StringUtils.isBlank(categoryDTO.getCategoryName())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "商品分类名称");
        }
        GoodsCategory goodsCategory = goodsCategoryBiz.get(categoryDTO.getCategoryId());
        if (!goodsCategory.getParentId().equals(categoryDTO.getParentId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品父级id不能修改");
        }
        if (!goodsCategory.getCategoryCode().equals(categoryDTO.getCategoryCode())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品分类编码不能修改");
        }
        String origin = goodsCategory.getCategoryName();
        //更新商品分类
        BeanUtils.copyProperties(categoryDTO, goodsCategory);
        goodsCategory.setCategoryName(categoryDTO.getCategoryName());
        goodsCategory.setImgs(categoryDTO.getImgs());
        goodsCategory.setAppImgs(categoryDTO.getAppImgs());
        goodsCategory.setMiniImgs(categoryDTO.getMiniImgs());
        goodsCategoryBiz.save(goodsCategory, operator);
        // 分类名称变更时 修改关系表冗余字段
        if (!Objects.equals(origin, categoryDTO.getCategoryName())) {
            String categoryId = goodsCategory.getCategoryId();
            GoodCategoryRelation relation = new GoodCategoryRelation();
            relation.setCategoryId(categoryId);
            for (GoodCategoryRelation goodCategoryRelation : goodCategoryRelationMapper.select(relation)) {
                goodCategoryRelation.setCategoryName(goodsCategory.getCategoryName());
                String[] fullIds = goodCategoryRelation.getCategoryString().split(",");
                String[] fullNames = goodCategoryRelation.getCategoryFullName().split(",");
                for (int i = 0; i < fullIds.length; i++) {
                    if (Objects.equals(fullIds[i], goodsCategory.getCategoryId())) {
                        fullNames[i] = goodsCategory.getCategoryName();
                    }
                }
                goodCategoryRelation.setCategoryFullName(String.join(",", fullNames));
                goodCategoryRelationMapper.updateByPrimaryKey(goodCategoryRelation);
            }
        }
        //异步缓存
        categoryTreeCacheService.setCategoryTreeCache();
        //更新商品分类基本属性
        batchInsertCategoryAttribute(categoryDTO.getCategoryAttributeDTOS(), operator);
        //更新分类和账户关系
    }

    @Override
    public CategoryDTO getCategory(String spuType) {
        CategoryDTO categoryDTO = new CategoryDTO();
        List<CategoryAttributeDTO> categoryAttributeDTOS = Lists.newArrayList();
        Condition conditionAll = new Condition(GoodsCategoryAttribute.class);
        Criteria criteriaAll = conditionAll.createCriteria();
        criteriaAll.andEqualTo(DEL_FLG, false);
        criteriaAll.andEqualTo("spu", spuType.equals("1"));//1标品 0单品
        conditionAll.orderBy("sort").asc();
        List<GoodsCategoryAttribute> goodsCategoryAttributeAllList = goodsCategoryAttrBiz.findByCondition(conditionAll);
        if (CollectionUtils.isNotEmpty(goodsCategoryAttributeAllList)) {
            goodsCategoryAttributeAllList.forEach(attribute -> {
                CategoryAttributeDTO categoryAttrDTO = new CategoryAttributeDTO();
                BeanUtils.copyProperties(attribute, categoryAttrDTO);
                //属性值填充
                List<CategoryAttributeValueDTO> categoryAttributeValueDTOS = Lists.newArrayList();
                Condition attrValueCondition = new Condition(GoodsCategoryAttributeValue.class);
                Criteria attrValueConditionCriteria = attrValueCondition.createCriteria();
                attrValueConditionCriteria.andEqualTo(DEL_FLG, false);
                attrValueConditionCriteria.andEqualTo("attributeId", attribute.getCategoryAttributeId());
                attrValueCondition.orderBy("sort").asc();
                List<GoodsCategoryAttributeValue> categoryAttributeValues = goodsCategoryAttrValueBiz.findByCondition(attrValueCondition);
                categoryAttributeValues.forEach(goodsCategoryAttributeValue -> {
                    CategoryAttributeValueDTO categoryAttributeValueDTO = new CategoryAttributeValueDTO();
                    BeanUtils.copyProperties(goodsCategoryAttributeValue, categoryAttributeValueDTO);
                    categoryAttributeValueDTOS.add(categoryAttributeValueDTO);
                });
                categoryAttrDTO.setCategoryAttributeValueDTOs(categoryAttributeValueDTOS);
                //属性值填充-end
                categoryAttributeDTOS.add(categoryAttrDTO);
                categoryDTO.setCategoryAttributeDTOS(categoryAttributeDTOS);
                //返回分类和账户关系
            });
        }
        return categoryDTO;
    }


    /**
     * 删除商品分类检测
     */
    private void checkDelCategory(GoodsCategory goodsCategory) {
        Condition condition = new Condition(GoodsCategory.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(PARENT_ID, goodsCategory.getCategoryId());
        criteria.andEqualTo(DEL_FLG, false);
        List<GoodsCategory> categories = goodsCategoryBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(categories)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "该商品分类包含子分类，不允许删除！");
        }
        List<GoodCategoryRelation> relations = goodCategoryRelationMapper.findGoodCategoryRelationByCategoryId(goodsCategory.getCategoryId());
        if (CollectionUtils.isNotEmpty(relations)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "该商品分类已创建商品，不允许删除！");
        }
    }

    @Override
    public void delCategory(String categoryId, String operator) {
        if (StringUtils.isBlank(categoryId)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_CATEGORY_ID);
        }
        if (StringUtils.isBlank(operator)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "操作人");
        }
        GoodsCategory goodsCategory = goodsCategoryBiz.get(categoryId);
        if (goodsCategory == null) {
            throw new BizException(GoodsCode.DATA_NOT_FOUND, "商品分类");
        }
        //商品分类删除检测
        checkDelCategory(goodsCategory);
        goodsCategory.setDelFlg(true);
        goodsCategoryBiz.save(goodsCategory, operator);
        //异步缓存
        categoryTreeCacheService.setCategoryTreeCache();
    }

    @Override
    public void refreshCategoryTreeCache(String operator) {
        log.info("refreshCategoryTreeCache operator:{}", operator);
        categoryTreeCacheService.setCategoryTreeCache();
    }

    @Override
    public List<CategoryTreeDTO> findCategoryTree(String categoryCode, boolean isGoods) {
        //获取缓存
        CategoryTreeCache categoryTreeCache = categoryTreeCacheService.getCategoryTreeCache(isGoods);
        return categoryTreeCache.getList();
    }

    @Override
    public List<CategoryTreeDTO> findCategoryTreeByMp(String categoryCode, boolean isGoods) {
        //获取缓存
        CategoryTreeCache categoryTreeCache = categoryTreeCacheService.getCategoryTreeCache(isGoods);
        return categoryTreeCache.getList().get(0).getChilds();
    }

    @Override
    public List<CategoryTreeDTO> findCategoryTreeGoodsByRole(String categoryCode,String memberId,
                                                             String accountId, boolean isGoods) {
        List<DataPermissionDTO> dataPermissionList = getDataPermissionList(memberId, accountId);
        List<String> categoryIds = dataPermissionList.stream().map(DataPermissionDTO::getCategoryId).collect(Collectors.toList());
        List<String> goodsCodes = dataPermissionList.stream().map(DataPermissionDTO::getGoodsCode).collect(Collectors.toList());
        log.info("findCategoryTreeGoodsByRole accountId:{} dataPermissionList:{}", accountId, dataPermissionList.size());
        List<CategoryTreeDTO>  categoryTreeDTOList = CollectionUtils.isNotEmpty(dataPermissionList) ? categoryTreeCacheService.getCategoryTreeGoodsByRole(categoryIds, goodsCodes, isGoods).getList() : Collections.emptyList();
        if (CollectionUtils.isEmpty(categoryTreeDTOList)) {
            return Collections.emptyList();
        }
        log.info("findCategoryTreeGoodsByRole accountId:{} categoryTreeDTOList:{}", accountId, categoryTreeDTOList.size());
        List<CategoryTreeDTO> returnChilds = Lists.newArrayList();
        List<CategoryTreeDTO> childs = categoryTreeDTOList.get(0).getChilds();
        CategoryTreeDTO father = categoryTreeDTOList.get(0);
        father.setChilds(null);
        childs.forEach(categoryTreeDTO -> {
            CategoryTreeDTO child = new CategoryTreeDTO();
            BeanUtils.copyProperties(categoryTreeDTO, child);
            List<CategoryTreeDTO> childsTwo = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(categoryTreeDTO.getChilds())) {
                for (CategoryTreeDTO categoryTreeDTO1 : categoryTreeDTO.getChilds()) {
                    if (CollectionUtils.isNotEmpty(categoryTreeDTO1.getGoodsDTOS())) {
                        categoryTreeDTO1.setChild(categoryTreeDTO1.getChilds());
                        categoryTreeDTO1.setChilds(null);
                        childsTwo.add(categoryTreeDTO1);
                    }
                }
                if (CollectionUtils.isNotEmpty(childsTwo)) {
                    child.setChild(childsTwo);
                    child.setChilds(null);
                    returnChilds.add(child);
                }
            }
        });
        father.setChild(returnChilds);
        return Lists.newArrayList(father);
    }

    @Override
    public List<CategoryTreeDTO> findCategoryTreeByCode(String categoryCode) {
        //
        if (CsStringUtils.isNullOrBlank(categoryCode)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_CATEGORY_CODE);
        }
        //初始化结果
        List<CategoryTreeDTO> list = Lists.newArrayList();
        Condition condition = new Condition(GoodsCategory.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        criteria.andEqualTo(CATEGORY_CODE, categoryCode);
        condition.orderBy(CATEGORY_CODE).asc();
        List<GoodsCategory> fathers = goodsCategoryBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(fathers)) {
            fathers.forEach(father -> {
                CategoryTreeDTO dto = new CategoryTreeDTO();
                BeanUtils.copyProperties(father, dto);
                childBuild(father, dto);
                list.add(dto);
            });
        }
        return list;
    }


    /**
     * 构建子分类
     */
    public void childBuild(GoodsCategory father, CategoryTreeDTO dto) {
        Condition condition = new Condition(GoodsCategory.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        criteria.andEqualTo(PARENT_ID, father.getCategoryId());
        condition.orderBy(CATEGORY_CODE).asc();
        List<GoodsCategory> childCategories = goodsCategoryBiz.findByCondition(condition);
        List<CategoryTreeDTO> childs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(childCategories)) {
            childCategories.forEach(child -> {
                CategoryTreeDTO childDto = new CategoryTreeDTO();
                BeanUtils.copyProperties(child, childDto);
                childDto.setParentName(father.getCategoryName());
                childs.add(childDto);
                childBuild(child, childDto);
            });
            dto.setChilds(childs);
        }
    }

    /**
     * 生成编码 格式001001001
     *
     * @param parentId
     * @return
     */
    public String generateCategoryCode(String parentId) {
        String code = "";
        int baseLeng = 3;
        int parentCodeLeng = 0;
        String parentCode = "";
        if (!StringUtils.isBlank(parentId)) {
            GoodsCategory parentCategory = goodsCategoryBiz.get(parentId);
            parentCode = parentCategory.getCategoryCode();
            parentCodeLeng = parentCategory.getCategoryCode().length();
        }

        String codeNumKey = "parentCategory_" + parentCode;
        Long codeNum = codeNumCacheService.getIncrCodeNum(codeNumKey);
        if (codeNum == null) {
            /**
             * 取得当前最大的code
             */
            Condition condition = new Condition(GoodsCategory.class);
            Criteria criteria = condition.createCriteria();
            criteria.andCondition("LENGTH(category_code) = " + (parentCodeLeng + baseLeng));
            condition.orderBy(CATEGORY_CODE).desc();
            List<GoodsCategory> categorys = goodsCategoryBiz.findByCondition(condition);
            if (CollectionUtils.isNotEmpty(categorys)) {
                String maxCode = categorys.get(0).getCategoryCode();
                codeNumCacheService.initCodeNum(codeNumKey, Long.parseLong(maxCode.substring(parentCodeLeng, maxCode.length())));
            } else {
                codeNumCacheService.initCodeNum(codeNumKey, 0L);
            }
            codeNum = codeNumCacheService.getIncrCodeNum(codeNumKey);
        }

        code = parentCode + StringUtils.leftPad(codeNum + "", baseLeng, "0");
        return code;
    }

    @Override
    public List<GoodsCategoryDTO> findCategoryAttrList(GoodsCategoryAttrDTO queryDTO) {
        return Lists.newArrayList();
    }

    @PostConstruct
    public void init() {
        codeNumCacheService.clearAllCodeNum();
    }


    @Override
    public List<CategoryTreeDTO> goodsCategoryTree(String code) {
        if (CsStringUtils.isNullOrBlank(code)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_CATEGORY_CODE);
        }
        List<CategoryTreeDTO> list = Lists.newArrayList();
        //获取缓存
        CategoryTreeCache categoryTreeCache = categoryTreeCacheService.getCategoryTreeCache(false);
        List<CategoryTreeDTO> fathers = categoryTreeCache.getList();
        if (CollectionUtils.isEmpty(fathers)) {
            return list;
        }
        for (CategoryTreeDTO father : fathers) {
            List<CategoryTreeDTO> childs = father.getChilds();
            if (CollectionUtils.isNotEmpty(childs)) {
                for (CategoryTreeDTO child : childs) {
                    if ("002001".equals(child.getCategoryCode())) {
                        list = child.getChilds();
                    }
                }
            }
        }
        return list;
    }

    @Override
    public List<CategoryTreeDTO> goodsCategoryTreeAndGoods(String code) {
        return List.of();
    }

    @Override
    public List<GoodsCategorySimpleDTO> getSimpleList(Set<String> categoryCodeSet) {
        if(CollUtil.isEmpty(categoryCodeSet))
            return Collections.emptyList();
        Condition condition = new Condition(GoodsCategory.class);
        condition.createCriteria()
                .andEqualTo(DEL_FLG,0)
                .andIn(CATEGORY_CODE,categoryCodeSet);
        return BeanUtil.copyToList(goodsCategoryBiz.findByCondition(condition),GoodsCategorySimpleDTO.class);
    }

    @Override
    public List<GoodsCategorySimpleDTO> findCategorySimple(List<String> categoryIds) {
        if(CollUtil.isEmpty(categoryIds))
            return Collections.emptyList();
        Condition condition = new Condition(GoodsCategory.class);
        condition.createCriteria()
                .andIn("categoryId", categoryIds)
                .andEqualTo(DEL_FLG, false);
        List<GoodsCategory> goodsCategories = goodsCategoryBiz.findByCondition(condition);
        if(CollUtil.isEmpty(goodsCategories))
            return Collections.emptyList();
        return BeanUtil.copyToList(goodsCategories,GoodsCategorySimpleDTO.class);
    }

    @Override
    public List<GoodsCategorySimpleDTO> findCategoryByCountId(GoodsCategorySimpleDTO categorySimpleDTO) {
//        List<GoCategoryAccountRelation> goodCategoryRelationByAccountId = goCategoryAccountRelationMapper.findGoodCategoryRelationByAccountId(categorySimpleDTO.getAccountId());
//        return BeanUtil.copyToList(goodCategoryRelationByAccountId, GoodsCategorySimpleDTO.class);
        return List.of();
    }

    @Override
    public List<GoodsSimpleDTO> findGoodsByCategoryCountId(GoodsCategorySimpleDTO categorySimpleDTO) {
        AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
        accountRoleDTO.setAccountId(categorySimpleDTO.getAccountId());
        List<DataPermissionDTO> categoryByCountId = roleService.getDataPermissionList(accountRoleDTO);
        log.info("findGoodsByCategoryCountId accountId:{}, DataPermissionDTOS:{}", categorySimpleDTO.getAccountId(), categoryByCountId);
        if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(categoryByCountId)){
            return Collections.emptyList();
        }
        List<String> categoryIds = categoryByCountId.stream().map(DataPermissionDTO::getCategoryId).collect(Collectors.toList());
        return goodsMapper.findGoodsSimpleByCategoryId(Lists.newArrayList(categoryIds));
    }

    @Override
    public List<GoodsCategoryDTO> getByLikeName(String categoryName) {
        Condition condition = new Condition(GoodsCategory.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(ColumnConstant.DEL_FLG, false);
        CommonUtils.andLikeIfNotBank(criteria, ColumnConstant.CATEGORY_NAME, categoryName);
        return BeanUtil.copyToList(goodsCategoryBiz.findByCondition(condition), GoodsCategoryDTO.class);
    }

    @Override
    public List<CategoryAndParentCategoryDTO> queryCategoryAndParentCategory(List<String> goodsCategoryCodes) {
        if(CollUtil.isEmpty(goodsCategoryCodes))
            return Collections.emptyList();
        return goodsCategoryMapper.queryCategoryAndParentCategory(goodsCategoryCodes);
    }

    @Override
    public List<CategoryAndParentCategoryDTO> queryCategoryAndParentByCategoryId(List<String> categoryIds) {
        if(CollUtil.isEmpty(categoryIds))
            return Collections.emptyList();
        return goodsCategoryMapper.queryCategoryAndParentByCategoryId(categoryIds);
    }

    public List<DataPermissionDTO> getDataPermissionList(String memberId, String accountId) {
        AccountRoleDTO dto = new AccountRoleDTO();
        dto.setAccountId(accountId);
        dto.setMemberId(memberId);
        dto.setPlatform(SELLER);
        AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(accountId);
        dto = roleService.getRoleByAccountId2(dto);
        if (accountSimpleDTO != null) {
            dto.setAccountId(accountId);
            dto.setAccountName(accountSimpleDTO.getAccountName());
            dto.setMemberName(accountSimpleDTO.getMemberName());
            dto.setRealName(accountSimpleDTO.getRealName());
            dto.setEmployeeId(accountSimpleDTO.getEmployeeId());
            dto.setMemberCode(accountSimpleDTO.getMemberCode());
            dto.setAccountCode(accountSimpleDTO.getAccountCode());
            dto.setMobile(accountSimpleDTO.getMobile());
            dto.setAccountType(accountSimpleDTO.getAccountType());
            dto.setDepartment(accountSimpleDTO.getDepartment());
            dto.setPosition(accountSimpleDTO.getPosition());
        }
        // 获取用户商品分类授权list
        return roleService.getDataPermissionList(dto);
    }

}
