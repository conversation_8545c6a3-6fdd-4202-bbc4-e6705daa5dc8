package com.cnoocshell.goods.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.GoodsShippingFee.*;
import com.cnoocshell.goods.service.IGoodsShippingFeeService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/goodsShippingFee")
@Api(tags = {"GoodsShippingFeeController"}, description = "商品运费服务")
public class GoodsShippingFeeController {

    @Autowired
    private IGoodsShippingFeeService goodsShippingFeeService;

    @ApiOperation("商品运费列表")
    @PostMapping(value = "/findAllByCondition")
    public PageInfo<GoodsShippingFeeInfoDTO> findAllByCondition(@RequestBody GoodsShippingFeePageReqDTO reqDTO)  {
        return goodsShippingFeeService.findAllByCondition(reqDTO);
    }

    @ApiOperation("商品运费列表-买家")
    @PostMapping(value = "/findAllByConditionOfBuyer")
    public PageInfo<GoodsShippingFeeInfoDTO> findAllByConditionOfBuyer(@RequestBody GoodsShippingFeePagOfBuyerReqDTO reqDTO)  {
        return goodsShippingFeeService.findAllByConditionOfBuyer(reqDTO);
    }

    @ApiOperation("删除商品运费信息")
    @GetMapping(value = "/deleteGoodsShippingFee")
    public Boolean deleteGoodsShippingFee(@RequestParam String id, @ApiIgnore String accountId, @ApiIgnore String userName)  {
        return goodsShippingFeeService.deleteGoodsShippingFee(id,accountId,userName);
    }

    @ApiOperation("商品运费信息详情")
    @GetMapping(value = "/findDetailById")
    public GoodsShippingFeeInfoDTO findDetailById(@RequestParam String id)  {
        return goodsShippingFeeService.findDetailById(id);
    }

    @ApiOperation("更新商品运费")
    @PostMapping(value = "/updateGoodsShippingFee")
    public Boolean updateGoodsShippingFee(@RequestBody GoodsShippingFeeCreateDTO reqDTO)  {
        return goodsShippingFeeService.updateGoodsShippingFee(reqDTO);
    }

    @ApiOperation("新增商品运费")
    @PostMapping(value = "/insertGoodsShippingFee")
    public Boolean insertGoodsShippingFee(@RequestBody GoodsShippingFeeCreateDTO reqDTO)  {
        return goodsShippingFeeService.insertGoodsShippingFee(reqDTO);
    }

    @ApiOperation("导入")
    @PostMapping(value = "/importExcel")
    public ItemResult<String> importExcel(@RequestBody GoodsShippingFeeImportExcelInfoDTO reqDTO)  {
        return goodsShippingFeeService.importExcel(reqDTO);
    }
}
