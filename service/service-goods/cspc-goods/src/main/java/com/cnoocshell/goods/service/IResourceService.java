package com.cnoocshell.goods.service;

import com.cnoocshell.goods.api.dto.*;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 资源服务
 */
public interface IResourceService {

	/**
	 * 卖家-挂牌管理列表查询
	 * @param reqDto ReqResourceSellerDTO入参对象
	 * @return PageInfo<ResourceSellerDTO>
	 */
	PageInfo<ResourceSellerDTO> pageResourceSeller(ReqResourceSellerDTO reqDto);

	/**
	 * 创建资源
	 * @param reqCreateResourceDTO 创建资源DTO
	 * @return
	 */
	void createResource(ReqCreateResourceDTO reqCreateResourceDTO);

	/**
	 * 修改资源
	 * @param reqUpdateResourceDTO 修改资源DTO
	 * @return
	 */
	void updateResource(ReqUpdateResourceDTO reqUpdateResourceDTO);

	/**
	 * 删除资源
	 * @param resourceIds 资源ID集
	 * @param operatorId 操作人
	 */
	void deleteResource(List<String> resourceIds, String operatorId);

	/**
	 * 下架/撤牌资源
	 * @param resourceId 资源ID
	 * @param operator 操作人
	 */
	void offSaleResource(String resourceId, String operator);

	/**
	 * 批量下架/撤牌资源
	 * @param resourceIds 资源ID集
	 * @param operator 操作人
	 */
	void offSaleResourceBatch(List<String> resourceIds, String operator);

	/**
	 * 上架资源
	 * @param resourceId 资源ID
	 * @param operator 操作人
	 */
	void onSaleResource(String resourceId, String operator);

	/**
	 * 批量上架资源
	 * @param resourceIds 资源ID集
	 * @param operator 操作人
	 */
	void onSaleResourceBatch(List<String> resourceIds, String operator);

	/**
	 * 交查询交易大厅商品列表
	 * @param reqGoodsResourceDTO 入参
	 * @return PageInfo<GoodsResourceListDTO>
	 */
	PageInfo<GoodsResourceListDTO> searchGoodsResourceEmall(ReqGoodsResourceDTO reqGoodsResourceDTO);


}
