package com.cnoocshell.goods.controller;


import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.BatchUpdateResourceDTO;
import com.cnoocshell.goods.api.dto.CreateResourceDTO;
import com.cnoocshell.goods.api.dto.ManualOnSaleDTO;
import com.cnoocshell.goods.api.dto.UpdateResourceDTO;
import com.cnoocshell.goods.service.IResourceListingService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/resourceListing")
@Api(tags={"ResourceListingController"},description = "资源挂牌服务")
public class ResourceListingController {

    @Autowired
    private IResourceListingService resourceListingService;

    @ApiOperation("资源挂牌")
    @PostMapping(value = "/createResource")
    public void createResource(@RequestBody @ApiParam("资源挂牌DTO") CreateResourceDTO arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1)  {
        resourceListingService.createResource(arg0,arg1);
    }

    @ApiOperation("手动下架")
    @PostMapping(value = "/manualOffSale")
    public ItemResult<String> manualOffSale(@RequestBody @ApiParam("资源ID列表") List<String> arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1)  {
        return resourceListingService.manualOffSale(arg0, arg1);
    }

    @ApiOperation("手动上架")
    @PostMapping(value = "/manualOnSale")
    public void manualOnSale(@RequestBody ManualOnSaleDTO manualOnSaleDTO)  {
        resourceListingService.manualOnSale(manualOnSaleDTO);
    }

    @ApiOperation("批量更新资源挂牌")
    @PostMapping(value = "/batchUpdateResource")
    public void batchUpdateResource(@RequestBody @ApiParam("批量更新挂牌资源DTO") BatchUpdateResourceDTO batchUpdateResourceDTO) throws Exception {
        resourceListingService.batchUpdateResource(batchUpdateResourceDTO);
    }


    @ApiOperation("更新资源挂牌")
    @PostMapping(value = "/updateResource")
    public void updateResource(@RequestBody @ApiParam("更新挂牌资源DTO") UpdateResourceDTO arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1)  {
        resourceListingService.updateResource(arg0,arg1);
    }

}
