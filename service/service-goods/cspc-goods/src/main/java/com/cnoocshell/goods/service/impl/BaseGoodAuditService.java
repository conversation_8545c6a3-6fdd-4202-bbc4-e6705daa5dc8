package com.cnoocshell.goods.service.impl;

import com.cnoocshell.goods.api.dto.BaseGoodDetailAuditDto;
import com.cnoocshell.goods.api.dto.BaseGoodsAuditDTO;
import com.cnoocshell.goods.api.enums.AuditAction;
import com.cnoocshell.goods.biz.IGoodsAuditBiz;
import com.cnoocshell.goods.service.IBaseGoodAuditService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BaseGoodAuditService implements IBaseGoodAuditService {

    private final IGoodsAuditBiz goodsAuditBiz;

    public BaseGoodAuditService(IGoodsAuditBiz goodsAuditBiz) {
        this.goodsAuditBiz = goodsAuditBiz;
    }

    @Override
    public boolean applyGoodsAudit(BaseGoodsAuditDTO dto, AuditAction action, String operator) {
        return goodsAuditBiz.applyGoodsAudit(dto, action, operator);
    }

    @Override
    public boolean applyDeleteGoods(List<String> goodsId, String operator) {
        return goodsAuditBiz.applyDeleteGoods(goodsId, operator);
    }

    @Override
    public boolean doAudit(String goodId, boolean pass, String msg, String operator) {
        return goodsAuditBiz.doAudit(goodId, pass, msg, operator);
    }

    @Override
    public BaseGoodDetailAuditDto baseGoodDetails(String goodId){
        return goodsAuditBiz.baseGoodDetails(goodId);
    }

}
