package com.cnoocshell.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.PageUtil;
import com.cnoocshell.common.annotation.AddLog;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.result.PageData;
import com.cnoocshell.common.utils.BeanConvertUtils;
import com.cnoocshell.common.utils.CommonConstants;
import com.cnoocshell.common.utils.CommonUtils;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.dto.base.PageQuery;
import com.cnoocshell.goods.api.dto.goods.GoodsAndCategoryInfoResultDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsDataListDTO;
import com.cnoocshell.goods.api.enums.DynamicAttributeEnum;
import com.cnoocshell.goods.api.enums.GoodsStatusEnum;
import com.cnoocshell.goods.api.enums.GoodsTypeEnum;
import com.cnoocshell.goods.biz.*;
import com.cnoocshell.goods.common.ColumnConstant;
import com.cnoocshell.goods.dao.mapper.GoodCategoryRelationMapper;
import com.cnoocshell.goods.dao.mapper.GoodsMapper;
import com.cnoocshell.goods.dao.vo.GoodCategoryRelation;
import com.cnoocshell.goods.dao.vo.Goods;
import com.cnoocshell.goods.dao.vo.GoodsAttributeValue;
import com.cnoocshell.goods.dao.vo.GoodsCategory;
import com.cnoocshell.goods.service.IGoodsCategoryService;
import com.cnoocshell.goods.service.IGoodsService;
import com.cnoocshell.goods.util.PageUtils;
import com.cnoocshell.member.api.dto.member.MemberDetailDTO;
import com.cnoocshell.member.api.dto.member.MemberPurchaseGoodsIntentionDTO;
import com.cnoocshell.member.api.dto.member.QueryIntentionInfoDTO;
import com.cnoocshell.member.api.service.IMemberService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GoodsService implements IGoodsService {

    private static final String SELLER_ID = "sellerId";
    private static final String DEL_FLG = "delFlg";
    private static final String GOODS_TYPE = "goodsType";
    private static final String GOODS_NAME = "goodsName";
    private static final String GOODS_STATUS = "goodsStatus";
    private static final String CATEGORY_CODE3 = "002001003";
    private static final String ATTRIBUTE_ID = "caq9fde75wppshg7umc17ss17";

    @Autowired
    private IGoodsBiz goodsBiz;
    @Autowired
    private IGoodsCategoryBiz goodsCategoryBiz;
    @Autowired
    private IGoodsAttributeValueBiz goodsAttributeValueBiz;
    @Resource
    private GoodCategoryRelationMapper goodCategoryRelationMapper;

    @Resource
    private GoodsMapper goodsMapper;

    @Autowired
    private IMemberService memberService;

    @Override
    public PageInfo<BaseGoodsDTO> pageBaseGoods(PageBaseGoodsDTO pageBaseGoodsDTO) {
        return goodsBiz.pageBaseGoods(pageBaseGoodsDTO);
    }


    @Override
    public void createSellerGoods(SellerGoodsDTO sellerGoodsDTO, String operator) {
        goodsBiz.createSellerGoods(sellerGoodsDTO, operator);
    }

    @Override
    public void updateSellerGoods(SellerGoodsDTO sellerGoodsDTO, String operator) {
        goodsBiz.updateSellerGoods(sellerGoodsDTO, operator);
    }

    @Override
    public SellerGoodsDTO getSellerGoodsDetail(String goodsId) {
        //        SpecialGoodsAttributeDTO specialGoodsAttributeDTO = getSpecialGoodsAttribute(sellerGoodsDTO.getGoodsId());
//        sellerGoodsDTO.setConcreteFlag(specialGoodsAttributeDTO.getConcreteFlag());
        return goodsBiz.getSellerGoodsDetail(goodsId);
    }

    @Override
    public PageInfo<SellerGoodsDTO> pageSellerGoods(PageSellerGoodsDTO pageSellerGoodsDTO) {
        return goodsBiz.pageSellerGoods(pageSellerGoodsDTO);
    }

    @Override
    public PageInfo<SellerGoodsDTO> pageGoods(PageSellerGoodsDTO pageSellerGoodsDTO) {
        MemberDetailDTO member = memberService.findMemberById(pageSellerGoodsDTO.getCurrentMemberId());
        if (Objects.isNull(member)) {
            return new PageInfo<>();
        }

        //1.先查分类下所有商品数据
        //客户下存在意向商品 步骤1中的所有商品中意向商品 考前排列  所有商品 统一名称排序

        //获取意向商品
        List<MemberPurchaseGoodsIntentionDTO> intentionDTOS = memberService.getIntentionsByMemberId(member.getMemberId());
        if(CollUtil.isNotEmpty(intentionDTOS)){
            List<String> mainIntentionGoodsIds = intentionDTOS.stream()
                    .filter(v->Objects.equals(1,v.getIntentionType()))
                    .map(v->v.getGoodsId())
                    .filter(CharSequenceUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> allIntentionGoodsIds = intentionDTOS.stream()
                    .map(v->v.getGoodsId())
                    .filter(CharSequenceUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            if(CollUtil.isNotEmpty(mainIntentionGoodsIds))
                pageSellerGoodsDTO.setMainIntentionGoodsIds(mainIntentionGoodsIds);
            if(CollUtil.isNotEmpty(allIntentionGoodsIds))
                pageSellerGoodsDTO.setAllIntentionGoodsIds(allIntentionGoodsIds);
        }

        PageInfo<SellerGoodsDTO> page = PageMethod
                .startPage(pageSellerGoodsDTO.getPageNum(), pageSellerGoodsDTO.getPageSize())
                .doSelectPageInfo(() -> goodsMapper.pageGoods(pageSellerGoodsDTO));

        return page;
    }


    @Override
    public GoodsDTO findGoodsById(String goodsId) {
        GoodsDTO goodsInfo = goodsBiz.getGoodsInfo(goodsId);
        if (goodsInfo == null) {
            return null;
        }
        DynamicAttributeDTO dynamicAttributeDTO = findDynamicAttribute(goodsId);
        goodsInfo.setSignType(dynamicAttributeDTO.getValueCode(DynamicAttributeEnum.SIGN_TYPE.getCode()));
        goodsInfo.setDynamicAttributeDTO(dynamicAttributeDTO);
        SpecialGoodsAttributeDTO specialGoodsAttributeDTO = getSpecialGoodsAttribute(goodsInfo.getGoodsId());
        goodsInfo.setSupportCarryFlag(0);
        if (specialGoodsAttributeDTO != null) {
            goodsInfo.setSupportCarryFlag(specialGoodsAttributeDTO.getSupportCarryFlag());
        }

        return goodsInfo;
    }

    @Override
    public GoodsDTO findGoodsByCode(String goodsCode) {
        Condition condition = goodsBiz.newCondition();
        condition.createCriteria().andEqualTo("goodsCode", goodsCode)
                .andEqualTo(DEL_FLG, false);
        List<Goods> goodsList = goodsBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }
        GoodsDTO goodsDTO = new GoodsDTO();
        BeanUtils.copyProperties(goodsList.get(0), goodsDTO);
        return goodsDTO;
    }


    @AddLog(operatorIndex = 1)
    @Override
    public void deleteGoods(String goodsIds, String operator) {
        goodsBiz.deleteGoods(goodsIds, operator);
    }

    @AddLog(operatorIndex = 2)
    @Override
    public ItemResult<String> updateStatus(String goodsId, Integer goodsStatus, String operator) {
        log.info("updateGoodsStatus:{},{},{}", goodsId, goodsStatus, operator);
        return goodsBiz.updateStatus(goodsId, goodsStatus, operator);
    }


    @Override
    public List<GoodsCategoryAttrDTO> getCategoryAttrByGoodsId(String goodsId) {

//		return getCategoryAttrByGoodsId(goodsId, null);
        return goodsBiz.getCategoryAttrByGoodsId(goodsId);
    }

    @Override
    public DynamicAttributeDTO findDynamicAttribute(String goodsId) {
        List<GoodsAttributeValueDTO> list = new ArrayList<>();
        DynamicAttributeDTO dynamicAttributeDTO = new DynamicAttributeDTO();
        Condition condition = new Condition(GoodsAttributeValue.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("goodsId", goodsId);
        criteria.andEqualTo(DEL_FLG, false);
        List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(goodsAttributeValues)) {
            goodsAttributeValues.forEach(goodsAttributeValue -> {
                GoodsAttributeValueDTO goodsAttributeValueDTO = new GoodsAttributeValueDTO();
                BeanUtils.copyProperties(goodsAttributeValue, goodsAttributeValueDTO);
                list.add(goodsAttributeValueDTO);
            });
        }
        dynamicAttributeDTO.setGoodsAttributeValueDTOList(list);
        return dynamicAttributeDTO;
    }

    private void setDynamicAttributeByIds(List<GoodsDTO> goodsDTOS) {
        if (CollectionUtils.isNotEmpty(goodsDTOS)) {
            List<String> idList = goodsDTOS.stream().map(GoodsDTO::getGoodsId).collect(Collectors.toList());
            Condition condition = new Condition(GoodsAttributeValue.class);
            condition.createCriteria()
                    .andIn("goodsId", idList)
                    .andEqualTo(DEL_FLG, false);
            List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueBiz.findByCondition(condition);
            if (CollectionUtils.isNotEmpty(goodsAttributeValues)) {
                Map<String, List<GoodsAttributeValueDTO>> goodsId2GoodsAttributeValue = goodsAttributeValues.stream()
                        .map(item -> {
                            GoodsAttributeValueDTO goodsAttributeValueDTO = new GoodsAttributeValueDTO();
                            BeanUtils.copyProperties(item, goodsAttributeValueDTO);
                            return goodsAttributeValueDTO;
                        })
                        .collect(Collectors.groupingBy(GoodsAttributeValueDTO::getGoodsId));
                goodsDTOS.forEach(goodsDTO -> {
                    List<GoodsAttributeValueDTO> goodsAttributeValueDTOList = goodsId2GoodsAttributeValue.get(goodsDTO.getGoodsId());
                    DynamicAttributeDTO dynamicAttributeDTO = new DynamicAttributeDTO();
                    dynamicAttributeDTO.setGoodsAttributeValueDTOList(goodsAttributeValueDTOList);
                    goodsDTO.setDynamicAttributeDTO(dynamicAttributeDTO);
                });
            }
        }

    }

    @Override
    public SpecialGoodsAttributeDTO getSpecialGoodsAttribute(String goodsId) {
        if (StringUtils.isEmpty(goodsId)) {
            throw new BizException(BasicCode.INVALID_PARAM, "商品ID");
        }
        Goods goods = goodsBiz.get(goodsId);
        if (goods == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "商品");
        }
        //获取商品属性
//        GoodsCategory queryGoodsCategory = new GoodsCategory();
//        List<GoodsCategory> goodsCategoryList = goodsCategoryBiz.find(queryGoodsCategory);
//        if (CollectionUtils.isEmpty(goodsCategoryList)) {
//            throw new BizException(BasicCode.DATA_NOT_EXIST, "商品属性");
//        }
//        GoodsCategory goodsCategory = goodsCategoryList.get(0);
        SpecialGoodsAttributeDTO specialGoodsAttributeDTO = new SpecialGoodsAttributeDTO();
//        specialGoodsAttributeDTO.setConcreteFlag(0);
//        if (StringUtils.contains(goodsCategory.getCategoryCode(), CATEGORY_CODE3)) {
//            specialGoodsAttributeDTO.setConcreteFlag(1);
//        }
//        specialGoodsAttributeDTO.setAddItemFlag(0);
        //仅袋装支持搬运
//        specialGoodsAttributeDTO.setSupportCarryFlag(0);
        return specialGoodsAttributeDTO;
    }

    @Override
    public List<GoodsDTO> findBaseGoods(String name, Integer categoryType, String seller) {
        List<GoodsDTO> dtoList = null;

        Condition condition = new Condition(Goods.class);
        Criteria criteria = condition.createCriteria();
        if (!StringUtils.isBlank(name)) {
            criteria.andLike(GOODS_NAME, "%" + name + "%");
        }

        if (categoryType != null) {
            criteria.andEqualTo("categoryType", categoryType);
        }

        if (seller != null) {
            criteria.andEqualTo(SELLER_ID, seller);
        }
        criteria.andEqualTo(GOODS_STATUS, GoodsStatusEnum.ENABLE.getCode());
        criteria.andEqualTo(GOODS_TYPE, GoodsTypeEnum.BASE.getCode());
        criteria.andNotEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_TRUE);
        condition.orderBy(GOODS_NAME).asc();
        List<Goods> goodsList = goodsBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(goodsList)) {
            dtoList = new ArrayList<GoodsDTO>();
            for (Goods goods : goodsList) {
                dtoList.add(BeanConvertUtils.convert(goods, GoodsDTO.class));
            }
        }

        return dtoList;
    }

    @Override
    public List<CategoryAttributeDTO> findBaseGoodsAttribute(String goodsId) {
        return goodsBiz.findBaseGoodsAttribute(goodsId);
    }

    @Override
    public PageData<GoodsDTO> goodsCommonQuery(PageQuery<GoodsQueryCondDTO> pageQuery) {
        pageQuery.setPageSize(Integer.MAX_VALUE);
        PageData<GoodsDTO> pageData = goodsBiz.goodsCommonQuery(pageQuery);
        List<GoodsDTO> dtos = pageData.getList();
        if (CollectionUtils.isEmpty(dtos)) {
            return pageData;
        }
        //设置商品特殊属性
        Set<String> categoryCodeSet = new HashSet<>();
        Map<GoodsDTO, Set<String>> goodCodeMap = new HashMap<>();
        dtos = dtos.stream().filter(goodsDTO -> {
            String goodsId = goodsDTO.getGoodsId();
            Condition condition = new Condition(GoodCategoryRelation.class);
            String spuId = goodsDTO.getSpuId();
            condition.or().andEqualTo("goodId", goodsId)
                    .orEqualTo("goodId", spuId);
            List<GoodCategoryRelation> relations = goodCategoryRelationMapper.selectByCondition(condition);
            Set<String> categories = relations.stream().map(GoodCategoryRelation::getCategoryId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(categories)) {
                // 跳过未关联分类的商品
                return false;
            } else {
                String idSql = categories.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
                List<GoodsCategory> goodsCategories = goodsCategoryBiz.getMapper().selectByIds(idSql);
                goodsCategories.stream().map(GoodsCategory::getCategoryCode).forEach(categoryCodeSet::add);
                DynamicAttributeDTO dynamicAttributeDTO = findDynamicAttribute(goodsId);
                goodsDTO.setDynamicAttributeDTO(dynamicAttributeDTO);
                goodCodeMap.put(goodsDTO, categories);
                return true;
            }
        }).collect(Collectors.toList());
        // 没有结果
        if (CollectionUtils.isEmpty(dtos)) {
            return new PageData<>(Collections.emptyList());
        }
        setDynamicAttributeByIds(dtos);
        dtosSetValues(categoryCodeSet, dtos, goodCodeMap);
        return pageData;
    }


    private void dtosSetValues(Set<String> categoryCodeSet, List<GoodsDTO> dtos, Map<GoodsDTO, Set<String>> goodCodeMap) {
        Map<String, SpecialGoodsAttributeDTO> goodsCategoryMap = getGoodsCategoryAttribute(categoryCodeSet);
        for (GoodsDTO dto : dtos) {
            Set<String> categories = goodCodeMap.get(dto);
            if (CollectionUtils.isEmpty(categories)) {
                continue;
            }
            for (String category : categories) {
                Optional.ofNullable(goodsCategoryMap.get(category))
                        .ifPresent(attr -> {
                            if (Objects.isNull(dto.getConcreteFlag()) || dto.getConcreteFlag() == 0) {
                                dto.setConcreteFlag(attr.getConcreteFlag());
                            }
                            if (Objects.isNull(dto.getAddItemFlag()) || dto.getAddItemFlag() == 0) {
                                dto.setAddItemFlag(attr.getAddItemFlag());
                            }
                        });
            }

        }
    }


    public Map<String, SpecialGoodsAttributeDTO> getGoodsCategoryAttribute(Collection<String> categoryCodeSet) {
        if (CollectionUtils.isEmpty(categoryCodeSet)) {
            throw new BizException(BasicCode.INVALID_PARAM, "商品分类编码");
        }
        //获取商品属性
        Condition goodsCategoryCondition = new Condition(GoodsCategory.class);
        Example.Criteria categoryCriteria = goodsCategoryCondition.createCriteria();
        categoryCriteria.andIn("categoryCode", categoryCodeSet);
        categoryCriteria.andEqualTo(DEL_FLG, Boolean.FALSE);
        List<GoodsCategory> goodsCategoryList = goodsCategoryBiz.findByCondition(goodsCategoryCondition);
        if (CollectionUtils.isEmpty(goodsCategoryList)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "商品属性");
        }
        Map<String, SpecialGoodsAttributeDTO> goodsCategoryMap = new HashMap<>();
        for (GoodsCategory goodsCategory : goodsCategoryList) {
            SpecialGoodsAttributeDTO specialGoodsAttributeDTO = new SpecialGoodsAttributeDTO();
            specialGoodsAttributeDTO.setConcreteFlag(0);
            if (StringUtils.contains(goodsCategory.getCategoryCode(), CATEGORY_CODE3)) {
                specialGoodsAttributeDTO.setConcreteFlag(1);
            }
            specialGoodsAttributeDTO.setAddItemFlag(0);
            goodsCategoryMap.put(goodsCategory.getCategoryCode(), specialGoodsAttributeDTO);
        }

        return goodsCategoryMap;
    }

    @Override
    public GoodsDTO getGoodsInfoByCommodityCode(String goodsId, String sellerId) {
        return goodsBiz.getGoodsInfoByCommodityCode(goodsId, sellerId);
    }

    @Override
    public List<GoodsSimpleDTO> findGoodsSimpleByIds(List<String> goodsIds) {
        return goodsBiz.findGoodsSimpleByIds(goodsIds);
    }

    @Override
    public List<GoodsSimpleDTO> findGoodsSimpleByCategoryId(String categoryId) {
        return goodsMapper.findGoodsSimpleByCategoryId(Lists.newArrayList(categoryId));
    }

    @Override
    public List<GoodsSimpleDTO> findGoodsSimpleByCodes(List<String> goodsCodes) {
        return goodsBiz.findGoodsSimpleByCodes(goodsCodes);
    }

    @Override
    public List<GoodsNameInfoDTO> queryGoodsNameByGoodsCode(GoodsCodeInfoDTO goodsCodeInfoDTO) {
      return   goodsMapper.queryGoodsNameByGoodsCode(goodsCodeInfoDTO);
    }

    @Override
    public List<GoodsDataListDTO> getPackSalesGroupByGoodsCode(List<String> goodsCodeList) {
        return goodsMapper.getPackSalesGroupByGoodsCode(goodsCodeList);
    }

    @Override
    public List<GoodsSimpleDTO> findGoodsSimpleByLikeSapMaterialCode(String sapMaterialCode) {
        Condition condition = new Condition(Goods.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(ColumnConstant.DEL_FLG, Boolean.FALSE);
        CommonUtils.andLikeIfNotBank(criteria, ColumnConstant.SAP_MATERIAL_CODE, sapMaterialCode);
        return BeanUtil.copyToList(goodsBiz.findByCondition(condition), GoodsSimpleDTO.class);
    }

    @Override
    public List<GoodsSimpleDTO> queryGoodsByLikeName(String name, Integer goodsStatus) {
        if (CharSequenceUtil.isBlank(name))
            return Collections.emptyList();

        Condition condition = new Condition(Goods.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo(DEL_FLG, Boolean.FALSE);
        CommonUtils.andLikeIfNotBank(criteria, GOODS_NAME, name);
        CommonUtils.andEqualToIfNotNull(criteria, GOODS_STATUS, goodsStatus);

        return BeanUtil.copyToList(goodsBiz.findByCondition(condition), GoodsSimpleDTO.class);
    }

    @Override
    public List<GoodsAndCategoryInfoResultDTO> queryGoodsAndCategoryInfoByGoodsCodes(List<String> goodsCodes) {
        return goodsBiz.queryGoodsAndCategoryInfoByGoodsCodes(goodsCodes);
    }
}
