package com.cnoocshell.goods.controller;

import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.service.IResourceService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Created：Fri Aug 31 18:47:03 CST 2018
 * @Author: <EMAIL>
 * @Version:2
 * @Description:null
 */

@Api(tags = { "Resource" }, description = "商品资源挂牌服务")
@RestController
@RequestMapping("/resource")
@Slf4j
public class ResourceController {

   @Autowired
   private IResourceService iResourceService;


   @Deprecated(since = "2.1.4-RELEASE")
   @ApiOperation("卖家-挂牌管理列表")
   @PostMapping(value = "/pageResourceSeller")
   public PageInfo<ResourceSellerDTO> pageResourceSeller(@RequestBody @ApiParam("卖家资源请求DTO") ReqResourceSellerDTO arg0)  {
      log.info("ResourceController pageResourceSeller param:{}", arg0);
      return iResourceService.pageResourceSeller(arg0);
   }

   @Deprecated(since = "2.1.4-RELEASE")
   @ApiOperation("创建资源")
   @PostMapping(value = "/createResource")
   public void createResource(@RequestBody @ApiParam("创建资源请求DTO") ReqCreateResourceDTO arg0)  {
      iResourceService.createResource(arg0);
   }

   @Deprecated(since = "2.1.4-RELEASE")
   @ApiOperation("上架/挂牌资源")
   @PostMapping(value = "/onSaleResource")
   public void onSaleResource(@RequestParam("arg0") @ApiParam("资源ID") String arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1)  {
      iResourceService.onSaleResource(arg0, arg1);

   }

   @Deprecated(since = "2.1.4-RELEASE")
   @ApiOperation("下架/撤牌资源")
   @PostMapping(value = "/offSaleResource")
   public void offSaleResource(@RequestParam("arg0") @ApiParam("资源ID") String arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1)  {
      iResourceService.offSaleResource(arg0, arg1);
   }

   @Deprecated(since = "2.1.4-RELEASE")
   @ApiOperation("修改资源")
   @PostMapping(value = "/updateResource")
   public void updateResource(@RequestBody @ApiParam("修改资源请求DTO") ReqUpdateResourceDTO arg0)  {
      iResourceService.updateResource(arg0);
   }


   @Deprecated(since = "2.1.4-RELEASE")
   @ApiOperation("删除资源")
   @PostMapping(value = "/deleteResource")
   public void deleteResource(@RequestBody @ApiParam("资源ID列表") List<String> arg0, @RequestParam("arg1") @ApiParam("操作人ID" ) String arg1)  {
      iResourceService.deleteResource(arg0, arg1);
   }

   @Deprecated(since = "2.1.4-RELEASE")
   @ApiOperation("批量下架/撤牌资源")
   @PostMapping(value = "/offSaleResourceBatch")
   public void offSaleResourceBatch(@RequestBody @ApiParam("资源ID列表") List<String> arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1) {
      iResourceService.offSaleResourceBatch(arg0, arg1);

   }

   @Deprecated(since = "2.1.4-RELEASE")
   @ApiOperation("批量上架/挂牌资源")
   @PostMapping(value = "/onSaleResourceBatch")
   public void onSaleResourceBatch(@RequestBody @ApiParam("资源ID列表") List<String> arg0, @RequestParam("arg1") @ApiParam("操作人") String arg1) {
      iResourceService.onSaleResourceBatch(arg0, arg1);

   }

}
