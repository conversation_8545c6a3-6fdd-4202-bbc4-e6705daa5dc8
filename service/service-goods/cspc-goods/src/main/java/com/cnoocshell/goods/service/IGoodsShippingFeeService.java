package com.cnoocshell.goods.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.GoodsShippingFee.*;
import com.github.pagehelper.PageInfo;

public interface IGoodsShippingFeeService {

    PageInfo<GoodsShippingFeeInfoDTO> findAllByCondition(GoodsShippingFeePageReqDTO reqDTO);

    PageInfo<GoodsShippingFeeInfoDTO> findAllByConditionOfBuyer(GoodsShippingFeePagOfBuyerReqDTO reqDTO);

    Boolean deleteGoodsShippingFee(String id,String accountId,String userName);

    GoodsShippingFeeInfoDTO findDetailById(String id);

    Boolean updateGoodsShippingFee(GoodsShippingFeeCreateDTO reqDTO);

    Boolean insertGoodsShippingFee(GoodsShippingFeeCreateDTO reqDTO);

    ItemResult<String> importExcel(GoodsShippingFeeImportExcelInfoDTO reqDTO);
}
