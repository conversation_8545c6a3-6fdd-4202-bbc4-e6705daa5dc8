package com.cnoocshell.goods.common.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum BooleanEnum {
    YES(1, "是"),
    NO(0, "否");

    /** 枚举值 */
    private final Integer code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>BooleanEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    private BooleanEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public Integer getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public Integer code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return BooleanEnum
     */
    public static BooleanEnum getByCode(Integer code) {
        for (BooleanEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }

    /**
     * 获取全部枚举
     *
     * @return List<BooleanEnum>
     */
    public List<BooleanEnum> getAllEnum() {
        return new ArrayList<>(Arrays.asList(values()));
    }

    /**
     * 获取全部枚举值
     *
     * @return List<Integer>
     */
    public List<Integer> getAllEnumCode() {
        List<Integer> list = new ArrayList<>();
        for (BooleanEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }
}
