package com.cnoocshell.goods.service;

import com.cnoocshell.goods.api.dto.BaseGoodDetailAuditDto;
import com.cnoocshell.goods.api.dto.BaseGoodsAuditDTO;
import com.cnoocshell.goods.api.enums.AuditAction;

import java.util.List;

public interface IBaseGoodAuditService {
    boolean applyGoodsAudit(BaseGoodsAuditDTO dto, AuditAction action, String operator);

    boolean applyDeleteGoods(List<String> goodsId, String operator);

    boolean doAudit(String auditId, boolean pass, String msg, String operator);

    BaseGoodDetailAuditDto baseGoodDetails(String goodId);
}
