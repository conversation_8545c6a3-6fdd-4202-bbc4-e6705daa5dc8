package com.cnoocshell.goods.service.impl;

import com.cnoocshell.goods.api.dto.GoodCategoryRelationDto;
import com.cnoocshell.goods.api.dto.relation.GoodsCategoryRelationSimpleDTO;
import com.cnoocshell.goods.biz.IGoodCategoryRelationBiz;
import com.cnoocshell.goods.service.IGoodCategoryRelationService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GoodCategoryRelationService implements IGoodCategoryRelationService {

    private final IGoodCategoryRelationBiz goodCategoryRelationBiz;

    public GoodCategoryRelationService(IGoodCategoryRelationBiz goodCategoryRelationBiz) {
        this.goodCategoryRelationBiz = goodCategoryRelationBiz;
    }

    @Override
    public boolean save(List<GoodCategoryRelationDto> dtos) {
        return goodCategoryRelationBiz.save(dtos);
    }

    @Override
    public List<GoodsCategoryRelationSimpleDTO> queryRelationByCategoryCodes(List<String> categoryCodes) {
        return goodCategoryRelationBiz.queryRelationByCategoryCodes(categoryCodes);
    }


}
