package com.cnoocshell.goods.exception;

import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.CodeMeta;

/**
 * <AUTHOR>
 * @Description goods模块异常代码定义，code以03开头
 * @Date 09/08/2018 14:22
 */
public class GoodsCode extends BasicCode {
    public static final CodeMeta DATA_NOT_FOUND = new CodeMeta("030001", "DATA_NOT_FOUND", "未查询到记录{}", "DATA_NOT_FOUND error, see: {}");
    public static final CodeMeta VALUE_NOT_EMPTY = new CodeMeta("030002", "VALUE_NOT_EMPTY", "{}不能为空", "{} can not be empty");
    public static final CodeMeta VALUE_ERROR = new CodeMeta("030004", "VALUE_ERROR", "{}错误", "{} error");
    public static final CodeMeta SALE_UNIT_NOT_RIGHT = new CodeMeta("030008", "SALE_UNIT_NOT_RIGHT", "商品销售单位不正确：{}", "SALE_UNIT_NOT_RIGHT error, see: {}");
    public static final CodeMeta SALE_CATEGORY_FAIL = new CodeMeta("030009", "SALE_CATEGORY_FAIL", "保存商品分类失败：{}", "SALE_CATEGORY_FAIL error, see: {}");
}
