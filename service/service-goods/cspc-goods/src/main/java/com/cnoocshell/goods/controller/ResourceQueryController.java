package com.cnoocshell.goods.controller;


import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.service.IResourceQueryService;
import com.cnoocshell.goods.service.IResourceService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/resourceQuery")
@Api(tags={"ResourceQueryController"},description = "资源挂牌查询服务")
public class ResourceQueryController {

    @Autowired
    private IResourceQueryService resourceQueryService;

    @Autowired
    private IResourceService iResourceService;

    @ApiOperation("卖家-挂牌列表")
    @PostMapping(value = "/pageResourceSeller")
    public PageInfo<ResourceSellerDTO> pageResourceSeller(@RequestBody @ApiParam("卖家挂牌信息请求") ReqResourceSellerDTO arg0)  {
        return resourceQueryService.pageResourceSeller(arg0);
    }

    @ApiOperation("复杂挂牌详情")
    @GetMapping(value = "/getComplexResourceDetail")
    public ResourceDTO getComplexResourceDetail(@RequestParam("arg0") @ApiParam("资源ID") String arg0)  {
        return resourceQueryService.getComplexResourceDetail(arg0);
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @ApiOperation("查询上架商品列表-EMALL")
    @PostMapping(value = "/searchGoodsResourceEmall")
    public PageInfo<GoodsResourceListDTO> searchGoodsResourceEmall(@RequestBody @ApiParam("商品资源请求DTO") ReqGoodsResourceDTO arg0)  {
        return iResourceService.searchGoodsResourceEmall(arg0);
    }


}
