package com.cnoocshell.goods.common.enums;

public enum PackEnum {

    VO_PACKAGE_METHOD_BULK("VO_PACKAGE_METHOD_BULK", "散装"),
    VO_PACKAGE_METHOD_IBC("VO_PACKAGE_METHOD_IBC", "吨桶"),
    VO_PACKAGE_METHOD_BUCKET_TRAY("VO_PACKAGE_METHOD_BUCKET_TRAY", "桶装托盘"),
    VO_PACKAGE_METHOD_APRON("VO_PACKAGE_METHOD_APRON", "散水"),
    VO_PACKAGE_METHOD_25KGBAG("VO_PACKAGE_METHOD_25KGBAG", "25公斤/包装袋"),
    VO_PACKAGE_METHOD_25KGBAG_HOLDER("VO_PACKAGE_METHOD_25KGBAG_HOLDER", "25KG袋装带托"),
    NULL("NONE", "无"),
    VO_PACKAGE_METHOD_BARREL("VO_PACKAGE_METHOD_BARREL", "桶装");

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String name;

    PackEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getCodeByName(String name) {
        for (PackEnum packEnum : values()) {
            if (packEnum.getName().equals(name)) {
                return packEnum.getCode();
            }
        }
        return null;
    }
}
