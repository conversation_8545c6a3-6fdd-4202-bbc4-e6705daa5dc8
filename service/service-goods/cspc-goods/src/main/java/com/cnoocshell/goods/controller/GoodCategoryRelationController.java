package com.cnoocshell.goods.controller;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.GoodCategoryRelationDto;
import com.cnoocshell.goods.api.dto.relation.GoodsCategoryRelationSimpleDTO;
import com.cnoocshell.goods.service.IGoodCategoryRelationService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("base-good/category/relation")
public class GoodCategoryRelationController {

    private final IGoodCategoryRelationService goodCategoryRelationService;

    @PostMapping("save")
    public ItemResult<Boolean> save(@RequestBody List<GoodCategoryRelationDto> dtos) {
        return new ItemResult<>(goodCategoryRelationService.save(dtos));
    }

    @PostMapping("/queryRelationByCategoryCodes")
    public List<GoodsCategoryRelationSimpleDTO> queryRelationByCategoryCodes(@RequestBody List<String> categoryCodes){
        return goodCategoryRelationService.queryRelationByCategoryCodes(categoryCodes);
    }

}
