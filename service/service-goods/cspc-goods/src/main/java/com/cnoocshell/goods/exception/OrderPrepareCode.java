package com.cnoocshell.goods.exception;

import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.CodeMeta;

public class OrderPrepareCode extends BasicCode {

	public static final CodeMeta PARAM_NULL = new CodeMeta("0360101", "PARAM_NULL", "{}不能为空",
			"{} can not be empty");
	public static final CodeMeta PARAM_ERROR = new CodeMeta("0360102", "PARAM_ERROR", "参数错误，{}",
			"param error {}");
	public static final CodeMeta DATA_NOT_FOUND = new CodeMeta("0360103", "DATA_NOT_FOUND", "未查询到{}",
			"data not find {}");
	public static final CodeMeta CAN_NOT_CREATE = new CodeMeta("0360104", "CAN_NOT_CREATE", "{}",
			"can not create {}");
	public static final CodeMeta CAN_NOT_UPDATE = new CodeMeta("0360105", "CAN_NOT_UPDATE", "{}",
			"can not update {}");
	public static final CodeMeta NOT_CHANGE = new CodeMeta("0360106", "NOT_CHANGE", "{}",
			"not change {}");
	public static final CodeMeta UNKNOWN_ERROR = new CodeMeta("0360107", "", "{}",
			"{}");

}
