package com.cnoocshell.goods.service;

import com.cnoocshell.goods.api.dto.*;

import java.util.List;
import java.util.Set;

/**
 * 商品分类管理服务
 *
 * @Author: <EMAIL>
 * @Description IGoodsCategoryService
 * @date 2018年8月15日 下午5:19:18
 */
public interface IGoodsCategoryService {


    /**
     * 查询商品分类属性
     *
     * @param queryDTO 查询商品分类属性入参对象
     * @return List<GoodsCategoryDTO>
     */
    List<GoodsCategoryDTO> findCategoryAttrList(GoodsCategoryAttrDTO queryDTO);



    /**
     * 添加商品分类--新接口
     *
     * @param categoryDTO 商品分类入参
     * @param operator    操作人
     */
    void addCategory(CategoryDTO categoryDTO, String operator);

    /**
     * 修改商品分类--新接口
     *
     * @param categoryDTO 商品分类入参
     * @param operator    操作人
     */
    void uptCategory(CategoryDTO categoryDTO, String operator);

    /**
     * 查看商品分类详情--新接口
     */
    CategoryDTO getCategory(String spuType);

    /**
     * 删除商品分类--新接口
     *
     * @param categoryId 商品分类ID
     * @param operator   操作人
     * @return
     */
    void delCategory(String categoryId, String operator);

    void refreshCategoryTreeCache(String operator);

    /**
     * 查询商品分类树--新接口
     *
     * @param categoryCode 类别编码
     * @return
     */
    List<CategoryTreeDTO> findCategoryTree(String categoryCode, boolean isGoods);

    /**
     * 查询商品分类树--通过小程序
     *
     * @param categoryCode 类别编码
     * @return
     */
    List<CategoryTreeDTO> findCategoryTreeByMp(String categoryCode, boolean isGoods);


    /**
     * 查询商品分类树--根据角色权限获取
     *
     * @param categoryCode 类别编码
     * @return
     */
    List<CategoryTreeDTO> findCategoryTreeGoodsByRole(String categoryCode, String memberId,
                                                      String accountId, boolean isGoods);


    /**
     * 根据查询商品分类树--新接口
     *
     * @param categoryCode 类别编码
     * @return
     */
    List<CategoryTreeDTO> findCategoryTreeByCode(String categoryCode);

    /**
     * 商品分类
     *
     * @param code 默认000
     * @return List<CategoryTreeDTO>
     */
    List<CategoryTreeDTO> goodsCategoryTree(String code);

    /**
     * 商品分类包含商品信息
     *
     * @param code 默认000
     * @return List<CategoryTreeDTO>
     */
    List<CategoryTreeDTO> goodsCategoryTreeAndGoods(String code);

    List<GoodsCategorySimpleDTO> findCategorySimple(List<String> categoryIds);
    List<GoodsCategorySimpleDTO> getSimpleList(Set<String> categoryCodeSet);

    List<GoodsCategorySimpleDTO> findCategoryByCountId(GoodsCategorySimpleDTO categorySimpleDTO);

    List<GoodsSimpleDTO> findGoodsByCategoryCountId(GoodsCategorySimpleDTO categorySimpleDTO);

    List<GoodsCategoryDTO>  getByLikeName(String categoryName);

    List<CategoryAndParentCategoryDTO> queryCategoryAndParentCategory(List<String> goodsCategoryCodes);

    List<CategoryAndParentCategoryDTO> queryCategoryAndParentByCategoryId(List<String> categoryIds);
}


