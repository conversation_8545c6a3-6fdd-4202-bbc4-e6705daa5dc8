package com.cnoocshell.goods;


import com.cnoocshell.goods.biz.IGoodsBiz;
import com.cnoocshell.goods.dao.vo.Goods;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertNotNull;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CspcGoodsApplication.class)
public class GoodsTest {

    @Autowired
    private IGoodsBiz goodsBiz;


    @Test
    public void test() {
        Goods xx = goodsBiz.get("xx");
        assertNotNull(xx);
    }

}
