package com.cnoocshell.goods;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CspcGoodsApplication.class)
@Slf4j
public class KafkaApplicationsendTests {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Test
    public void testSendMessage() {
        long time = new Date().getTime();
        sendMessage("sysLogQueue", "{\"moduleName\":\"1\",\"operMethod\":\"1\",\"operStartTime\":\""+time+"\"}");
        redisTemplate.opsForValue().set("test", time+"RedisTemplate");
        String s = redisTemplate.opsForValue().get("test");
        log.info("redisTemplate.opsForValue().get(\"test\") = {}", s);
    }

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(String topic, String message) {
        kafkaTemplate.send(topic, message);
    }
}

