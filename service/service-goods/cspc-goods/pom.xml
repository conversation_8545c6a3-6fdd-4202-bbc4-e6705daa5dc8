<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>service-goods</artifactId>
		<groupId>com.cnoocshell</groupId>
		<version>2.1.4-RELEASE</version>
	</parent>
	<artifactId>cspc-goods</artifactId>
	<name>cspc-goods</name>


	<packaging>jar</packaging>
	<description>Goods Service</description>
	
	<properties>
        <docker.deploy.version>${project.version}</docker.deploy.version>
    </properties>

	<dependencies>
		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>service-common</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>

		<!--session同步-->
		<dependency>
			<groupId>org.springframework.session</groupId>
			<artifactId>spring-session-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>kafka-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-sleuth</artifactId>
		</dependency>

		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>kafka-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.2.3</version>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-core</artifactId>
			<version>1.2.3</version>
			<scope>runtime</scope>
		</dependency>

		<!--<dependency>-->
			<!--<groupId>org.flywaydb</groupId>-->
			<!--<artifactId>flyway-core</artifactId>-->
		<!--</dependency>-->

	 
		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>cspc-goods-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
		</dependency>
		
	    <dependency>
	      <groupId>org.apache.poi</groupId>
	      <artifactId>poi</artifactId>
	    </dependency>
	    <dependency>
	      <groupId>org.apache.poi</groupId>
	      <artifactId>poi-ooxml</artifactId>
	    </dependency>
		<dependency>
			<groupId>fr.opensagres.xdocreport</groupId>
			<artifactId>xdocreport</artifactId>
			<version>1.0.6</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>ooxml-schemas</artifactId>
			<version>1.4</version>
		</dependency>

		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>generator</artifactId>
			<version>2.1.4-RELEASE</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.xuxueli</groupId>
			<artifactId>xxl-job-core</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>

		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
			<version>2.8.5</version>
		</dependency>

		<!-- es -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-elasticsearch</artifactId>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-high-level-client</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>org.apache.logging.log4j</groupId>-->
<!--			<artifactId>log4j-core</artifactId>-->
<!--			<version>2.17.1</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.logging.log4j</groupId>-->
<!--			<artifactId>log4j-api</artifactId>-->
<!--			<version>2.17.1</version>-->
<!--		</dependency>-->
		<!-- es end -->

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.9.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<version>4.3.18.RELEASE</version>
		</dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>cspc-member-api</artifactId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.cnoocshell</groupId>
			<artifactId>cspc-base-api</artifactId>
			<scope>compile</scope>
		</dependency>
	</dependencies>

	<build>
		<finalName>cspc-goods</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-maven-plugin</artifactId>
				<version>1.3.5</version>
				<configuration>
					<configurationFile>${basedir}/src/main/resources/generator/generatorConfig.xml</configurationFile>
					<overwrite>true</overwrite>
					<verbose>true</verbose>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>mysql</groupId>
						<artifactId>mysql-connector-java</artifactId>
						<version>8.0.31</version>
					</dependency>
					<dependency>
						<groupId>org.mybatis.generator</groupId>
						<artifactId>mybatis-generator-core</artifactId>
						<version>1.3.5</version>
					</dependency>
					<dependency>
						<groupId>tk.mybatis</groupId>
						<artifactId>mapper</artifactId>
						<version>3.4.0</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<dependencies>
					<!-- spring热部署 -->
					<dependency>
						<groupId>org.springframework</groupId>
						<artifactId>springloaded</artifactId>
						<version>1.2.8.RELEASE</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>
</project>
