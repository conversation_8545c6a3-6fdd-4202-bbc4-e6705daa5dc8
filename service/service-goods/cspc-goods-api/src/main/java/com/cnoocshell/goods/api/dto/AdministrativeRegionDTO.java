package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("行政区域")
public class AdministrativeRegionDTO {
    /**
     * 资源行政区域id
     */
    @ApiModelProperty(value = "资源行政区域ID")
    private String resourceAdministrativeRegionId;

    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源ID")
    private String resourceId;

    /**
     * 行政区域国家编码
     */
    @ApiModelProperty(value = "行政区域国家编码")
    private String countryCode;

    /**
     * 行政区域省编码
     */
    @ApiModelProperty(value = "行政区域省编码")
    private String provinceCode;

    /**
     * 行政区域城市编码
     */
    @ApiModelProperty(value = "行政区域城市编码")
    private String cityCode;

    /**
     * 行政区域地区编码
     */
    @ApiModelProperty(value = "行政区域地区编码")
    private String areaCode;

    /**
     * 行政区域街道编码
     */
    @ApiModelProperty(value = "行政区域街道编码")
    private String streetCode;

    /**
     * 行政区域国家名称
     */
    @ApiModelProperty(value = "行政区域国家名称")
    private String countryName;

    /**
     * 行政区域省名称
     */
    @ApiModelProperty(value = "行政区域省名称")
    private String provinceName;

    /**
     * 行政区域城市名称
     */
    @ApiModelProperty(value = "行政区域城市名称")
    private String cityName;

    /**
     * 行政区域地区名称
     */
    @ApiModelProperty(value = "行政区域地区名称")
    private String areaName;

    /**
     * 行政区域街道名称
     */
    @ApiModelProperty(value = "行政区域街道名称")
    private String streetName;

    /**
     * 是否可以配送
     * 1 可以
     * 0 不可以
     */
    @ApiModelProperty(value = "是否可以配送")
    private Integer isCarriage;

    /**
     * 每公里运费
     */
    @ApiModelProperty(value = "每公里运费")
    private BigDecimal carriage;
}
