package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("查询资源")
public class SelectResourceDTO {
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;
    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    private String resourceId;
    /**
     * 购买数量
     */
    @ApiModelProperty(value = "购买数量")
    private BigDecimal quantity;
    /**
     * 购买单位
     */
    @ApiModelProperty(value = "购买单位")
    private String unit;

    /**
     * 钢筋选项
     */
    @ApiModelProperty(value = "钢筋选项")
    private List<SelectInfoDTO> selectInfos;
    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    @Valid
    List<AttributeDTO> attributes;
}
