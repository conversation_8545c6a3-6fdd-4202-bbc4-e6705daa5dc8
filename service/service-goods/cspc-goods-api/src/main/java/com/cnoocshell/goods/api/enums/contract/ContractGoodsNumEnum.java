package com.cnoocshell.goods.api.enums.contract;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum ContractGoodsNumEnum {

    EXPECT_NUM(1, "更新计划提货量"),
    ACTUAL_NUM(2, "更新实际提货量");

    /** 枚举值 */
    private final Integer code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>CartStatusEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    private ContractGoodsNumEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public Integer getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public Integer code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return CartStatusEnum
     */
    public static ContractGoodsNumEnum getByCode(Integer code) {
        for (ContractGoodsNumEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }

    /**
     * 获取全部枚举
     *
     * @return List<CartStatusEnum>
     */
    public List<ContractGoodsNumEnum> getAllEnum() {
        return new ArrayList<>(Arrays.asList(values()));
    }

    /**
     * 获取全部枚举值
     *
     * @return List<Integer>
     */
    public List<Integer> getAllEnumCode() {
        List<Integer> list = new ArrayList<>();
        for (ContractGoodsNumEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }
}
