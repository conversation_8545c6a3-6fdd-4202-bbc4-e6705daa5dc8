package com.cnoocshell.goods.api.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("资源详情搜索DTO")
public class ResourceDetailSearchDTO {
	
	/**
	 * 商品ID
	 */
	@ApiModelProperty(value = "商品ID")
	String goodsId;
	/**
	 * 卖家ID
	 */
	@ApiModelProperty(value = "卖家ID")
	String sellerId;
	/**
	 * 资源ID
	 */
	@ApiModelProperty(value = "资源ID")
	String resourceId;
	/**
	 * 数量
	 */
	@ApiModelProperty(value = "数量")
	String quantity;
	/**
	 * 买家ID
	 */
	@ApiModelProperty(value = "买家ID")
	String buyerId;
	/**
	 * 购买数量
	 */
	@ApiModelProperty(value = "购买数量")
	private BigDecimal buyNum;
	
    /**
     * 买家姓名
     */
	@ApiModelProperty(value = "买家姓名")
    private String buyerName;
	/**
	 * 商品资源ID
	 */
	@ApiModelProperty(value = "商品资源ID")
	String goodsResourceId;
	/**
	 * 行政区域国家编码
	 */
	@ApiModelProperty(value = "行政区域国家编码")
	private String countryCode;
	/**
	 * 行政区域省编码
	 */
	@ApiModelProperty(value = "行政区域省编码")
	private String provinceCode;
	/**
	 * 行政区域城市编码
	 */
	@ApiModelProperty(value = "行政区域城市编码")
	private String cityCode;
	/**
	 * 行政区域地区编码
	 */
	@ApiModelProperty(value = "行政区域地区编码")
	private String areaCode;
	/**
	 * 行政区域街道编码
	 */
	@ApiModelProperty(value = "行政区域街道编码")
	private String streetCode;
	/**
	 * 行政区域国家名称
	 */
	@ApiModelProperty(value = "行政区域国家名称")
	private String countryName;
	/**
	 * 行政区域省名称
	 */
	@ApiModelProperty(value = "行政区域省名称")
	private String provinceName;
	/**
	 * 行政区域城市名称
	 */
	@ApiModelProperty(value = "行政区域城市名称")
	private String cityName;
	/**
	 * 行政区域地区名称
	 */
	@ApiModelProperty(value = "行政区域地区名称")
	private String areaName;
	/**
	 * 行政区域街道名称
	 */
	@ApiModelProperty(value = "行政区域街道名称")
	private String streetName;

	@ApiModelProperty(value = "属性")
	@Valid
	List<AttributeDTO> attributes;
}
