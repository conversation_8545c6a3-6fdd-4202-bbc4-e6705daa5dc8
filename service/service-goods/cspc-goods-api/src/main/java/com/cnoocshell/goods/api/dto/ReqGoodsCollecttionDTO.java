package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("商品收藏请求DTO")
public class ReqGoodsCollecttionDTO {
    /**
     * 收藏对象ID
     */
    @ApiModelProperty(value = "收藏对象ID")
    private String objectId;
    /**
     * 收藏对象类型
     * list 商品
     * detail 资源
     */
    @ApiModelProperty(value = "收藏对象类型")
    private String objectType;
}
