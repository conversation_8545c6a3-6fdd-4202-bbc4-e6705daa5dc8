package com.cnoocshell.goods.api.dto.GoodsShippingFee;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("商品运费信息分页列表买家入参")
public class GoodsShippingFeePagOfBuyerReqDTO {

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "市编码")
    private String cityCode;

    @ApiModelProperty(value = "市名称")
    private String cityName;

    @ApiModelProperty(value = "区编码")
    private String districtCode;

    @ApiModelProperty(value = "区名称")
    private String districtName;

    @ApiModelProperty(value = "会员id")
    private String memberId;

    @ApiModelProperty("页码")
    private int pageNum;

    @ApiModelProperty("单页条数")
    private int pageSize;
}
