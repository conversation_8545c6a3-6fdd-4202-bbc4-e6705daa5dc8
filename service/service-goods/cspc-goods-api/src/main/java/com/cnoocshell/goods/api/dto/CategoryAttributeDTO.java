package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("分类属性")
public class CategoryAttributeDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String categoryAttributeId;

    /**
     * 商品类型 1 2
     */
    @ApiModelProperty(value = "商品类型")
    private Integer categoryType;

    /**
     * 商品分类id
     */
    @ApiModelProperty(value = "商品分类ID")
    private String categoryId;

    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private String categoryName;

    /**
     * 商品属性id
     */
    @ApiModelProperty(value = "商品属性ID")
    private String goodsAttriId;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String attriName;

    /**
     * 属性值类型,1-数字型，2-输入型，3-选项值，4-特殊值，5-日期
     */
    @ApiModelProperty(value = "属性值类型")
    private Integer valueType;

    /**
     * 是否生效
     */
    @ApiModelProperty(value = "是否生效")
    private Boolean effective;

    /**
     * 是否显示
     */
    @ApiModelProperty(value = "是否显示")
    private Boolean onShow;

    /**
     * 是否详情显示
     */
    @ApiModelProperty(value = "是否详情显示")
    private Boolean detailShow;

    /**
     * 是否可编辑
     */
    @ApiModelProperty(value = "是否可编辑")
    private Boolean editable;

    /**
     * 是否多选
     */
    @ApiModelProperty(value = "是否多选")
    private Boolean multiSelect;

    /**
     * 是否支持搜索
     */
    @ApiModelProperty(value = "是否支持搜索")
    private Boolean supportSearch;

    /**
     * 是否新增值
     */
    @ApiModelProperty(value = "是否新增值")
    private Boolean valueAdded;

    /**
     * 是否spu
     */
    @ApiModelProperty(value = "是否spu")
    private Boolean spu;

    /**
     * 是否拆分
     */
    @ApiModelProperty(value = "是否拆分")
    private Boolean splitUp;

    /**
     * 对应商品表字段
     */
    @ApiModelProperty(value = "对应商品表字段")
    private String tableField;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Boolean isRequire;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;

    /**
     * 商品分类属性值
     */
    @ApiModelProperty(value = "商品分类属性值")
    private List<CategoryAttributeValueDTO> categoryAttributeValueDTOs;

}