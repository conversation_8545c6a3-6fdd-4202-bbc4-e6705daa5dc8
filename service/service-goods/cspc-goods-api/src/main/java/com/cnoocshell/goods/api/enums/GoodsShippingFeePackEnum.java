package com.cnoocshell.goods.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GoodsShippingFeePackEnum {
    BIDDING_EXCEL_1("25公斤/包装袋"),
    BIDDING_EXCEL_2("25KG袋装带托"),
    BIDDING_EXCEL_3("散水"),
    BIDDING_EXCEL_4("桶装 托盘"),
    BIDDING_EXCEL_5("桶装"),
    BIDDING_EXCEL_6("吨桶"),
    BIDDING_EXCEL_7("散装"),
    BIDDING_EXCEL_8("无"),
    ;
    private final String key;
}
