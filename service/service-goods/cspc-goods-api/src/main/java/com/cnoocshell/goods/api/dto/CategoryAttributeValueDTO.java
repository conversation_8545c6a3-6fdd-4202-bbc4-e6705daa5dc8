package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("分类属性值")
public class CategoryAttributeValueDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String attributeValueId;

    /**
     * 分类属性ID
     */
    @ApiModelProperty(value = "分类属性ID")
    private String attributeId;

    /**
     * 商品属性名称
     */
    @ApiModelProperty(value = "商品属性名称")
    private String attributeName;

    /**
     * 分类属性值编号
     */
    @ApiModelProperty(value = "分类属性值编号")
    private String attributeValueCode;

    /**
     * 分类属性值
     */
    @ApiModelProperty(value = "分类属性值")
    private String attributeValue;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Byte sort;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;
}
