package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("请求合同资源DTO")
public class ReqContractResourceDTO {
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;
    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;
    /**
     * 购买单位
     */
    @ApiModelProperty(value = "购买单位")
    private String unit;

    /**
     * 所在国家名称
     */
    @ApiModelProperty(value = "所在国家名称")
    private String countryName;

    /**
     * 所在省名称
     */
    @ApiModelProperty(value = "所在省名称")
    private String provinceName;

    /**
     * 所在城市名称
     */
    @ApiModelProperty(value = "所在城市名称")
    private String cityName;

    /**
     * 所在地区名称
     */
    @ApiModelProperty(value = "所在地区名称")
    private String areaName;

    /**
     * 所在街道名称
     */
    @ApiModelProperty(value = "所在街道名称")
    private String streetName;

    /**
     * 所在国家Code
     */
    @ApiModelProperty(value = "所在国家Code")
    private String countryCode;

    /**
     * 所在省Code
     */
    @ApiModelProperty(value = "所在省Code")
    private String provinceCode;

    /**
     * 所在城市Code
     */
    @ApiModelProperty(value = "所在城市Code")
    private String cityCode;

    /**
     * 所在地区Code
     */
    @ApiModelProperty(value = "所在地区Code")
    private String areaCode;

    /**
     * 所在街道Code
     */
    @ApiModelProperty(value = "所在街道Code")
    private String streetCode;

}
