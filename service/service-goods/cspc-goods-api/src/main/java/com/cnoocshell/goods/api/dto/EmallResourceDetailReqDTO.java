package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("请求Emall资源详情DTO")
public class EmallResourceDetailReqDTO {
    /**
     * 商品聚合ID
     */
    @ApiModelProperty(value = "商品聚合ID")
    private String goodsResourceId;
    /**
     * 卖家商品ID
     */
    @ApiModelProperty(value = "卖家商品ID")
    private String goodsId;
    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    private String resourceId;
    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "仓库ID")
    private String storeId;
    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;
    /**
     * 购买数量
     */
    @ApiModelProperty(value = "购买数量")
    private BigDecimal quantity;
    /**
     * 买家ID
     */
    @ApiModelProperty(value = "买家ID")
    private String buyerId;
    /**
     * 买家姓名
     */
    @ApiModelProperty(value = "买家姓名")
    private String buyerName;

    /**
     * 收货地址ID
     */
    @ApiModelProperty(value = "收货地址ID")
    private String receiveAddressId;

    /**
     * 收货地址位置
     */
    @ApiModelProperty(value = "收货地址位置")
    private String receiveAddressLocation;

    /**
     * 行政区域国家编码
     */
    @ApiModelProperty(value = "行政区域国家编码")
    private String countryCode;
    /**
     * 行政区域省编码
     */
    @ApiModelProperty(value = "行政区域省编码")
    private String provinceCode;
    /**
     * 行政区域城市编码
     */
    @ApiModelProperty(value = "行政区域城市编码")
    private String cityCode;
    /**
     * 行政区域地区编码
     */
    @ApiModelProperty(value = "行政区域地区编码")
    private String areaCode;
    /**
     * 行政区域街道编码
     */
    @ApiModelProperty(value = "行政区域街道编码")
    private String streetCode;
    /**
     * 行政区域国家名称
     */
    @ApiModelProperty(value = "行政区域国家名称")
    private String countryName;
    /**
     * 行政区域省名称
     */
    @ApiModelProperty(value = "行政区域省名称")
    private String provinceName;
    /**
     * 行政区域城市名称
     */
    @ApiModelProperty(value = "行政区域城市名称")
    private String cityName;
    /**
     * 行政区域地区名称
     */
    @ApiModelProperty(value = "行政区域地区名称")
    private String areaName;
    /**
     * 行政区域街道名称
     */
    @ApiModelProperty(value = "行政区域街道名称")
    private String streetName;
    /**
     * 选择的单位
     */
    @ApiModelProperty(value = "选择的单位")
    private String unit;
    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
//    Cannot construct instance of `java.util.ArrayList` (although at least one Creator exists): no String-argument constructor/factory method to deserialize from String value (''); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `java.util.ArrayList` (although at least one Creator exists): no String-argument constructor/factory method to deserialize from String value ('')
//    @Valid
    private List<AttributeDTO> attributes;

}
