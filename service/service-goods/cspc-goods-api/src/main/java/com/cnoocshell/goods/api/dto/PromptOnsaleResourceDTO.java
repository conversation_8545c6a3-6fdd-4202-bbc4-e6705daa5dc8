package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("提示已挂牌资源")
public class PromptOnsaleResourceDTO {

    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    private String resourceId;

    /**
     * 品类商品ID
     */
    @ApiModelProperty(value = "品类商品ID")
    private String spuId;

    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private String goodsType;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDescribe;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    /**
     * 资源code
     */
    @ApiModelProperty(value = "资源code")
    private String resourceCode;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 计价方式，1 出厂价 2到位价
     */
    @ApiModelProperty(value = "计价方式")
    private String priceWay;

    /**
     * 价格
     */
    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String priceUnit;

    /**
     * 销售单位
     */
    @ApiModelProperty(value = "销售单位")
    private String saleUnit;

    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "仓库ID")
    private String storeId;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String storeName;

    /**
     * 仓库类型, 100卖家基地库 200平台中心仓
     */
    @ApiModelProperty(value = "仓库类型")
    private String storeType;

    /**
     * 仓库详细地址
     */
    @ApiModelProperty(value = "仓库详细地址")
    private String storeAddress;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer resourceVersion;

    /**
     * 资源状态, 100已挂牌 200已撤牌 300撤牌处理中 400未上架 500待审批
     */
    @ApiModelProperty(value = "资源状态")
    private String status;

    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;

    /**
     * 卖家姓名
     */
    @ApiModelProperty(value = "卖家姓名")
    private String sellerName;

    /**
     * 总销售数量
     */
    @ApiModelProperty(value = "总销售数量")
    private BigDecimal saleNum;

    /**
     * 可售数量
     */
    @ApiModelProperty(value = "可售数量")
    private BigDecimal cansaleNum;

    /**
     * 锁定数量
     */
    @ApiModelProperty(value = "锁定数量")
    private BigDecimal lockNum;

    /**
     * 区域定价层级
     */
    @ApiModelProperty(value = "区域定价层级")
    private Integer areaLevel;

    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private String saleArea;

    /**
     * 一级销售区域编码
     */
    @ApiModelProperty(value = "一级销售区域编码")
    private String saleAreaCode;

    /**
     * 二级销售区域编码
     */
    @ApiModelProperty(value = "二级销售区域编码")
    private String saleAreaCode2;

    /**
     * 三级销售区域编码
     */
    @ApiModelProperty(value = "三级销售区域编码")
    private String saleAreaCode3;

    /**
     * 四级销售区域编码
     */
    @ApiModelProperty(value = "四级销售区域编码")
    private String saleAreaCode4;

    /**
     * 五级销售区域编码
     */
    @ApiModelProperty(value = "五级销售区域编码")
    private String saleAreaCode5;

    /**
     * 一级销售区域名称
     */
    @ApiModelProperty(value = "一级销售区域名称")
    private String saleAreaName;

    /**
     * 二级销售区域名称
     */
    @ApiModelProperty(value = "二级销售区域名称")
    private String saleAreaName2;

    /**
     * 三级销售区域名称
     */
    @ApiModelProperty(value = "三级销售区域名称")
    private String saleAreaName3;

    /**
     * 四级销售区域名称
     */
    @ApiModelProperty(value = "四级销售区域名称")
    private String saleAreaName4;

    /**
     * 五级销售区域名称
     */
    @ApiModelProperty(value = "五级销售区域名称")
    private String saleAreaName5;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

}
