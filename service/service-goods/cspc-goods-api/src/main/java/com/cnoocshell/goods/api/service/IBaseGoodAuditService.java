package com.cnoocshell.goods.api.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.BaseGoodDetailAuditDto;
import com.cnoocshell.goods.api.dto.BaseGoodsAuditDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "cspc-service-goods", path = "/base-good/audit")
public interface IBaseGoodAuditService {
    @PostMapping("/apply")
    ItemResult<Boolean> applyGoodsAudit(@RequestBody BaseGoodsAuditDTO dto);

    @PostMapping("/audit")
    ItemResult<Boolean> doAudit(@RequestParam("goodId") String goodId, @RequestParam("pass") boolean pass, @RequestParam("msg") String msg, @RequestParam("operator") String operator);

    @GetMapping()
    ItemResult<BaseGoodDetailAuditDto> baseGoodDetails(@RequestParam("goodId") String goodId);

    @PostMapping( "apply/batchDelete")
    ItemResult<Boolean> batchDelete(@RequestBody List<String> goodIds, @RequestParam("operator") String operator);
}
