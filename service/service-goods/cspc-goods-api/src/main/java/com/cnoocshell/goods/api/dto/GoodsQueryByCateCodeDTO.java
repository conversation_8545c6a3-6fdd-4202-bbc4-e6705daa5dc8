package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("通过商品分类编码查询商品")
public class GoodsQueryByCateCodeDTO {
    /**
     * 商品分类编码
     */
    @ApiModelProperty(value = "商品分类编码")
    private String categoryCode;
    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
}
