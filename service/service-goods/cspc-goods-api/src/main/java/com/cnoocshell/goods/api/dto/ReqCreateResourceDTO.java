package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("请求新增资源DTO")
public class ReqCreateResourceDTO {
    /**
     * 卖家id
     */
    @ApiModelProperty(value = "卖家id")
    private String sellerId;

    /**
     * 卖家姓名
     */
    @ApiModelProperty(value = "卖家姓名")
    private String sellerName;

    /**
     * 卖家简称
     */
    @ApiModelProperty(value = "卖家简称")
    private String sellerNickName;

    /**
     * 销售员id
     */
    @ApiModelProperty(value = "销售员id")
    private String salesId;

    /**
     * 销售员姓名
     */
    @ApiModelProperty(value = "销售员姓名")
    private String salesName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 组织机构id
     */
    @ApiModelProperty(value = "组织机构id")
    private String orgId;

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 操作员
     */
    @ApiModelProperty(value = "操作员")
    private String operator;

    /**
     * 销售单位
     */
    @ApiModelProperty(value = "销售单位")
    private String unit;

    /**
     * 品类商品ID
     */
    @ApiModelProperty(value = "品类商品ID")
    private String spuId;

    /**
     * 商品基本信息
     */
    @ApiModelProperty(value = "商品基本信息")
    private List<GoodsBaseDTO> goodsBaseDTOs;

    /**
     * 商品价格信息
     */
    @ApiModelProperty(value = "商品价格信息")
    private GoodsPriceDTO goodsPriceDTO;

    /**
     * 商品数量信息
     */
    @ApiModelProperty(value = "商品数量信息")
    private GoodsQuantityDTO goodsQuantityDTO;

    /**
     * 商品交付信息
     */
    @ApiModelProperty(value = "商品交付信息")
    private GoodsConsignDTO goodsConsignDTO;

    /**
     * 商品其他信息
     */
    @ApiModelProperty(value = "商品其他信息")
    private GoodsOtherDTO goodsOtherDTO;
}
