package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午6:40 20/2/13
 */
@Data
@ApiModel("资源查询对象")
public class ResourceQueryDTO {
    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;

    /**
     * 卖家商品ID
     */
    @ApiModelProperty(value = "卖家商品ID")
    private String goodsId;

    /**
     * 资源状态列表
     */
    @ApiModelProperty(value = "资源状态列表")
    private List<String> statusList;
}
