package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午9:23 19/12/11
 */
@Data
@ApiModel("资源查询条件DTO")
public class QueryResourceConditionDTO {

    @ApiModelProperty(value = "资源ID列表")
    private List<String> resourceIdList;

    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "仓库ID")
    private String storeId;

    /**
     * 商品Id列表
     */
    @ApiModelProperty(value = "商品Id列表")
    private List<String> goodsIdList;

    /**
     * 合同Id列表
     */
    @ApiModelProperty(value = "合同Id列表")
    private List<String> contractIdList;

    /**
     * 买家ID
     */
    @ApiModelProperty(value = "买家ID")
    private String  sellerId;

    /**
     * 配送方式
     */
    @ApiModelProperty(value = "配送方式")
    private String  deliveryWay;

    /**
     * 所在省编码
     */
    @ApiModelProperty(value = "所在省编码")
    private String provinceCode;

    /**
     * 所在城市编码
     */
    @ApiModelProperty(value = "所在城市编码")
    private String cityCode;

    /**
     * 所在地区编码
     */
    @ApiModelProperty(value = "所在地区编码")
    private String areaCode;

    /**
     * 所在街道编码
     */
    @ApiModelProperty(value = "所在街道编码")
    private String streetCode;

    /**
     * 所在省编码
     */
    @ApiModelProperty(value = "所在省编码")
    private String provinceName;

    /**
     * 所在城市编码
     */
    @ApiModelProperty(value = "所在城市编码")
    private String cityName;

    /**
     * 所在地区编码
     */
    @ApiModelProperty(value = "所在地区编码")
    private String areaName;

    /**
     * 所在街道编码
     */
    @ApiModelProperty(value = "所在街道编码")
    private String streetName;

    /**
     * 运输类型
     */
    @ApiModelProperty(value = "运输类型")
    private String transportType;

    /**
     * 地址绑定的厂商销售区域ID
     */
    @ApiModelProperty(value = "地址绑定的厂商销售区域ID")
    private String bindingSaleRegionId;


}
