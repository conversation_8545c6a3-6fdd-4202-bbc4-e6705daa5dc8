package com.cnoocshell.goods.api.service;

import com.cnoocshell.goods.api.dto.GoodsSapMappingSimpleDTO;
import com.cnoocshell.goods.api.dto.QueryGoodsMappingSimpleDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name ="cspc-service-goods")
public interface IGoodsMappingSapService {

    @PostMapping("/goodsMappingSap/querySimple")
    @ApiOperation(value = "查询简单映射列表", notes = "查询简单映射列表")
    List<GoodsSapMappingSimpleDTO> querySimple(@RequestBody QueryGoodsMappingSimpleDTO param);
}
