package com.cnoocshell.goods.api.dto;

import com.cnoocshell.information.api.dto.announcement.AnnouncementAttachmentDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("卖家商品DTO")
public class SellerGoodsDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String goodsId;

    @ApiModelProperty(value = "spuID")
    private String spuId;

    /**
     * 物料商品编码
     */
    @ApiModelProperty(value = "物料商品编码")
    private String commodityCode;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品业务类型 1平台商品2卖家商品
     */
    @ApiModelProperty(value = "商品业务类型")
    private Integer goodsType;

    /**
     * 拼接属性值信息,便于模糊搜索
     */
    @ApiModelProperty(value = "拼接属性值信息,便于模糊搜索")
    private String searchKeywords;

    /**
     * 状态值，1草稿 2启用（审核通过）3禁用 4审核中 5审核失败
     */
    @ApiModelProperty(value = "状态值")
    private Integer goodsStatus;

    /**
     * 卖家id
     */
    @ApiModelProperty(value = "卖家id")
    private String sellerId;

    /**
     * 是否支持加价项
     */
    @ApiModelProperty(value = "是否支持加价项")
    private String supportAdditem;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String measureUnit;

    /**
     * 包装
     */
    @ApiModelProperty(value = "包装")
    private String pack;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specs;

    /**
     * 标号
     */
    @ApiModelProperty(value = "标号")
    private String mark;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String concreteClass;

    /**
     * 物流类型
     */
    @ApiModelProperty(value = "物流类型")
    private String logistics;

    /**
     * 搬运费规则
     */
    @ApiModelProperty(value = "搬运费规则")
    private String cartageRule;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式")
    private Byte pricingMode;

    /**
     * 配送方式 1自提，2厂家配送
     */
    @ApiModelProperty(value = "配送方式")
    private String deliveryMode;

    /**
     * 商品类型 1 2
     */
    @ApiModelProperty(value = "商品类型")
    private Integer categoryType;

    /**
     * 商品类型字符串
     */
    @ApiModelProperty(value = "商品类型字符串")
    private String categoryTypeStr;
    @ApiModelProperty("sku分类")
    private String skuCategories;

    private List<GoodCategoryRelationDto> skuRelations;
    @ApiModelProperty("spu分类")
    private String spuCategories;

    @ApiModelProperty(value = "重量")
    private BigDecimal weight;

    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String color;

    /**
     * 尺码
     */
    @ApiModelProperty(value = "尺码")
    private String size;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String imgs;

    //sap工厂编码
    private String sapFactoryCode;

    //sap物料编码
    private String sapMaterialCode;

    @ApiModelProperty(value = "配送收费标准附件")
    private String deliverCostStandard;

    @ApiModelProperty(value = "自提指南附件")
    private String selfPickupGuide;

    @ApiModelProperty(value = "自提承运商附件")
    private String selfPickupCarrier;

    @ApiModelProperty(value = "说明文件附近")
    private String documentIds;

    @ApiModelProperty(value = "pc应用领域")
    private String pcApplicationArea;

    @ApiModelProperty(value = "app应用领域")
    private String appApplicationArea;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;


    /**
     * 标识 0不是  1是
     */
    @ApiModelProperty(value = "标识")
    private Integer concreteFlag;

    /**
     * 分类属性
     */
    @ApiModelProperty(value = "分类属性")
    private List<CategoryAttributeDTO> categoryAttributeDTOS;

    /**
     * 扩展属性属性值
     */
    @ApiModelProperty(value = "扩展属性属性值")
    private List<GoodsAttributeDTO> goodsAttributeDTOS;

    /**
     * 商品分类值
     */
    @ApiModelProperty(value = "商品分类值")
    private List<GoodCategoryRelationDto> goodCategoryRelationDTOS;

    /**
     * 附件列表
     */
    @ApiModelProperty("附件列表")
    private List<AnnouncementAttachmentDTO> attachmentList;

}
