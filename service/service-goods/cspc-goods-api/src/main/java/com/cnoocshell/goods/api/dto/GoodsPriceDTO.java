package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("商品价格")
public class GoodsPriceDTO {
    /**
     * 区域定价层级
     */
    @ApiModelProperty(value = "区域定价层级")
    private Integer areaLevel;

    /**
     * 价格描述
     */
    @ApiModelProperty(value = "价格描述")
    private String priceDescribe;

    /**
     * 计价方式
     * ("1", "固定运价"),
     * ("2", "运费规则定价");
     */
    @ApiModelProperty(value = "计价方式")
    private String priceWay;

    /**
     * 是否可议价
     */
    @ApiModelProperty(value = "是否可议价")
    private Boolean ifProtocolPrice;

    /**
     * 区域定价定价详情
     */
    @ApiModelProperty(value = "区域定价定价详情")
    List<GoodsResourceStoreDTO> goodsResourceStoreDTOS;
}
