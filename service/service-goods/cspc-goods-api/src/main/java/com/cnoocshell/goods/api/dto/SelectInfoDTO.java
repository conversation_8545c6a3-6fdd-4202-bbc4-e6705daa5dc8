package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("查询信息DTO")
public class SelectInfoDTO {

    @ApiModelProperty(value = "选项1类型")
    private String option1Type;

    @ApiModelProperty(value = "选项1值")
    private String option1Value;

    @ApiModelProperty(value = "选项1名称")
    private String option1Name;

    @ApiModelProperty(value = "选项2类型")
    private String option2Type;

    @ApiModelProperty(value = "选项2值")
    private String option2Value;

    @ApiModelProperty(value = "选项2名称")
    private String option2Name;

    @ApiModelProperty(value = "选项3类型")
    private String option3Type;

    @ApiModelProperty(value = "选项3值")
    private String option3Value;

    @ApiModelProperty(value = "选项3名称")
    private String option3Name;

    @ApiModelProperty(value = "选项4类型")
    private String option4Type;

    @ApiModelProperty(value = "选项4值")
    private String option4Value;

    @ApiModelProperty(value = "选项4名称")
    private String option4Name;

    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "资源ID")
    private String resourceId;

    @ApiModelProperty(value = "列表索引")
    private Integer listIndex;
}
