package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("标准商品DTO")
public class BaseGoodsDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String goodsId;

    @ApiModelProperty(value = "spuID")
    private String spuId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品业务类型 1平台商品2卖家商品
     */
    @ApiModelProperty(value = "商品业务类型")
    private Integer goodsType;

    /**
     * 拼接属性值信息,便于模糊搜索
     */
    @ApiModelProperty(value = "拼接属性值信息,便于模糊搜索")
    private String searchKeywords;

    /**
     * 状态值， 1 草稿 2启用（审核通过）3禁用 4审核中 5审核失败 6 新增审核 7 修改审核 8 删除审核
     */
    @ApiModelProperty(value = "状态值")
    private Integer goodsStatus;

    @ApiModelProperty("审核信息")
    private String msg;
    /**
     * 卖家id
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String measureUnit;

    /**
     * 包装
     */
    @ApiModelProperty(value = "包装")
    private String pack;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specs;

    /**
     * 标号
     */
    @ApiModelProperty(value = "标号")
    private String mark;

    /**
     * 物流类型
     */
    @ApiModelProperty(value = "物流类型")
    private String logistics;

    /**
     * 搬运费规则
     */
    @ApiModelProperty(value = "搬运费规则")
    private String cartageRule;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式")
    private Byte pricingMode;

    /**
     * 配送方式 1自提，2厂家配送，3平台配送
     */
    @ApiModelProperty(value = "配送方式")
    private String deliveryMode;


    @ApiModelProperty(value = "商品分类关系")
    private List<GoodCategoryRelationDto> relationDtos;

    @ApiModelProperty(value = "重量")
    private BigDecimal weight;

    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String color;

    /**
     * 尺码
     */
    @ApiModelProperty(value = "尺码")
    private String size;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String imgs;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;

    /**
     * 分类属性
     */
    @ApiModelProperty(value = "分类属性")
    private List<CategoryAttributeDTO> categoryAttributeDTOS;

    /**
     * 分类属性值
     */
    @ApiModelProperty(value = "分类属性值")
    private List<GoodsAttributeValueDTO> goodsAttributeValueDTOS;
}
