package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("请求资源物流信息DTO")
public class ReqLogisticsResourceDTO {
    /**
     * 商品描述：关键字
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDescribe;
    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;
    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "仓库ID")
    private String storeId;
    /**
     * 仓库类型
     */
    @ApiModelProperty(value = "仓库类型")
    private String storeType;
}
