package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单位换算DTO
 * @Author: <EMAIL>
 * @Description  UnitConverDTO
 * @date   2018年8月29日 下午6:17:19
 */
@ApiModel("单位换算DTO")
public class UnitConverDTO implements Serializable {

	private static final long serialVersionUID = 8606765645380586294L;

	//基础单位
	@ApiModelProperty(value = "基础单位")
	private String unit1;
	@ApiModelProperty(value = "基础单位ID")
	private String unitId1;
	
	//需换算单位
	@ApiModelProperty(value = "需换算单位")
	private String unit2;
	@ApiModelProperty(value = "需换算单位ID")
	private String unitId2;
	
	//是否默认单位
	@ApiModelProperty(value = "是否默认单位")
	private boolean isDefault;
	
	//换算比率
	@ApiModelProperty(value = "换算比率")
	private BigDecimal ratio;
	
	public UnitConverDTO(){
	}
	
	public UnitConverDTO(String unit1, String unit2, BigDecimal ratio){
		this.unit1 = unit1;
		this.unit2 = unit2;
		this.ratio = ratio;
	}
	
	public String getDescription(){
		return "1" + unit1 + "=" + (ratio==null?null:ratio.doubleValue()) + unit2;
	}

	public String getUnit1() {
		return unit1;
	}

	public void setUnit1(String unit1) {
		this.unit1 = unit1;
	}

	public String getUnit2() {
		return unit2;
	}

	public void setUnit2(String unit2) {
		this.unit2 = unit2;
	}

	public BigDecimal getRatio() {
		return ratio;
	}

	public void setRatio(BigDecimal ratio) {
		this.ratio = ratio;
	}

	public String getUnitId1() {
		return unitId1;
	}

	public void setUnitId1(String unitId1) {
		this.unitId1 = unitId1;
	}

	public String getUnitId2() {
		return unitId2;
	}

	public void setUnitId2(String unitId2) {
		this.unitId2 = unitId2;
	}

	public boolean isDefault() {
		return isDefault;
	}

	public void setDefault(boolean isDefault) {
		this.isDefault = isDefault;
	}

}
