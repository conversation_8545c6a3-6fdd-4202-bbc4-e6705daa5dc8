package com.cnoocshell.goods.api.dto.GoodsShippingFee;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel("商品运费信息分页列表出参")
public class GoodsShippingFeeInfoDTO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "SAP物流编码")
    private String sapMaterialCode;

    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "市编码")
    private String cityCode;

    @ApiModelProperty(value = "市名称")
    private String cityName;

    @ApiModelProperty(value = "区编码")
    private String districtCode;

    @ApiModelProperty(value = "区名称")
    private String districtName;

    @ApiModelProperty(value = "包装方式")
    private String pack;

    @ApiModelProperty(value = "包装费")
    private BigDecimal packFee;

    @ApiModelProperty(value = "运费")
    private BigDecimal shippingFee;

    @ApiModelProperty(value = "有效开始日期")
    private LocalDate effectStartDate;

    @ApiModelProperty(value = "有效结束日期")
    private LocalDate effectEndDate;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("更新人id")
    private String updateUser;

    @ApiModelProperty("更新人名称")
    private String updateUserName;

    @ApiModelProperty("创建人id")
    private String createUser;
}
