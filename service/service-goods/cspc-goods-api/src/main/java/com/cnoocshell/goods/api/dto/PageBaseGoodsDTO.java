package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("标准商品分页DTO")
public class PageBaseGoodsDTO {

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 拼接属性值信息,便于模糊搜索
     */
    @ApiModelProperty(value = "拼接属性值信息,便于模糊搜索")
    private String searchKeywords;

    /**
     * 状态值，1草稿 2启用（审核通过）3禁用 4审核中 5审核失败
     */
    @ApiModelProperty(value = "状态值")
    private Integer goodsStatus;

    /**
     * 卖家id
     */
    @ApiModelProperty(value = "卖家id")
    private String sellerId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品类型")
    private Integer categoryType;

    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品类型")
    private String categoryCode;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @NotNull(message = "每页显示数量不能为空")
    private Integer pageSize;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;
}
