package com.cnoocshell.goods.api.enums;

/**
 * 
 * @Author: <EMAIL>
 * @Description  商品单位
 * @date   2018年8月16日 下午3:17:13
 */
public enum GoodsUnitEnum {
	
	T("0001", "吨"),
	PACK("0002", "袋"),
	M3("0003", "立方"),
	KG("0003", "kg");
	
    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    GoodsUnitEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}
	
	public static GoodsUnitEnum getByCode(String code) {
		for(GoodsUnitEnum _enum : GoodsUnitEnum.values()){
			if(_enum.code.equals(code)){
				return _enum;
			}
		}
		return null;
	}
}
