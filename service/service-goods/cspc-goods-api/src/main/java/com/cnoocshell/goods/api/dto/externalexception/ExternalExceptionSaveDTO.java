package com.cnoocshell.goods.api.dto.externalexception;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: ExternalExceptionSaveDTO
 * @Author: <EMAIL>
 * @Date: 2021-05-18 15:51
 */
@Data
@ApiModel("外部异常处理保存对象")
public class ExternalExceptionSaveDTO {

    @NotNull(message = "业务实体id不能为空")
    @ApiModelProperty("业务实体id")
    private String entryId;

    @NotNull(message = "方法类型不能为空")
    @ApiModelProperty("方法类型")
    private String methodType;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("异常原因")
    private String exceptionReason;

    @ApiModelProperty("重试次数")
    private Integer retryCount;

    @ApiModelProperty("发起卖家id")
    private String sellerId;

    @ApiModelProperty("操作人")
    private String operatorUserId;

    @ApiModelProperty("操作人名称")
    private String operatorUserName;
}
