package com.cnoocshell.goods.api.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 
 * @Author: <EMAIL>
 * @Description  商品基本属性：包装，规格，分类，标号，单位，等级强度 ，品牌，类型，塌落度（毫米）, 质量标准, 使用范围, 物流类型, 计价方式, 配送方式
 * @date   2018年8月16日 下午3:17:13
 */
public enum GoodsBaseAttrEnum {
	
	//	：标号、类型、包装、规格、单位、品牌、使用范围
	//	：等级强度、类型、单位、塌落度（毫米）、单位
	//	备注：物流类型、计价方式、配送方式 都不展示

	CATEGORY("0001", "分类", GoodsAttrValTypeEnum.SPECIAL, "category1", null,
			false, true, false, true, false, true, false, true, false),
	PACKAGE("0002", "包装", GoodsAttrValTypeEnum.INPUT, "pack", Lists.newArrayList(CategoryTypeEnum.CEMENT.getCode()),
			false, true, false, true, false, true, false, true, false),
	MARK("0003", "标号", GoodsAttrValTypeEnum.INPUT, "mark", Lists.newArrayList(CategoryTypeEnum.CEMENT.getCode(), CategoryTypeEnum.REBAR.getCode()),
			false, true, false, true, false, true, false, true, false),
	UNIT("0004", "单位", GoodsAttrValTypeEnum.OPTION, "unit", null,
			true, true, true, true, false, true, false, true, true),
	STRENGTH("0005", "等级强度", GoodsAttrValTypeEnum.INPUT, "strength", Lists.newArrayList(CategoryTypeEnum.CONCRETE.getCode()),
			false, true, false, true, false, true, false, true, false),
	
	SPECS("0006", "规格", GoodsAttrValTypeEnum.OPTION, "specs", Lists.newArrayList(CategoryTypeEnum.CEMENT.getCode(),CategoryTypeEnum.REBAR.getCode()),
			true, true, false, true, false, true, true, true, false),
	SPECS_TILE("0006", "规格", GoodsAttrValTypeEnum.OPTION, "specs", Lists.newArrayList(CategoryTypeEnum.TILE.getCode()),
			true, true, false, true, false, true, false, true, true),
	
	BRAND("0007", "品牌", GoodsAttrValTypeEnum.INPUT, "brand", Lists.newArrayList(CategoryTypeEnum.CEMENT.getCode(), CategoryTypeEnum.TILE.getCode()),
			true, true, false, true, false, true, false, true, false),
	
//	SLUMP("0008", "塌落度（毫米）", GoodsAttrValTypeEnum.INPUT, "slump", Lists.newArrayList(CategoryTypeEnum.CONCRETE.getCode()),
//			false, true, false, true, false, true, false, true, false),
	
	CONCRETE_CLASS("0009", "类型", GoodsAttrValTypeEnum.INPUT, "concreteClass", Lists.newArrayList(CategoryTypeEnum.CEMENT.getCode(),CategoryTypeEnum.CONCRETE.getCode(),CategoryTypeEnum.TILE.getCode()),
			false, true, true, true, false, true, false, true, false),
	
	QUALITY_STANDARD("0010", "质量标准", GoodsAttrValTypeEnum.OPTION, "qualityStandard", null,
			true, false, false, true, true, true, false, true, false),
	USE_RANGE("0011", "使用范围", GoodsAttrValTypeEnum.OPTION, "useRange", Lists.newArrayList(CategoryTypeEnum.CEMENT.getCode()),
			true, true, false, true, true, true, false, true, false),
	LOGISTICS("0012", "物流类型", GoodsAttrValTypeEnum.OPTION, "logistics", null,
			false, false, false, true, false, false, false, true, false),
	PRICING_MODE("0013", "计价方式", GoodsAttrValTypeEnum.INPUT, "pricingMode", null,
			false, false, false, true, false, false, false, true, false),
	DELIVERY_MODE("0014", "配送方式", GoodsAttrValTypeEnum.INPUT, "deliveryMode", null,
			false, false, false, true, false, false, false, true, false),
	
	COLOR("0015", "颜色", GoodsAttrValTypeEnum.OPTION, "color", Lists.newArrayList(CategoryTypeEnum.TILE.getCode()),
			false, true, true, true, false, true, true, false, true),
//	SIZE("0016", "尺寸", GoodsAttrValTypeEnum.OPTION, "size", Lists.newArrayList(CategoryTypeEnum.TILE.getCode()),
//			true, true, true, true, false, true, false, false, true),
	
	USE_SPACE("0017", "使用空间", GoodsAttrValTypeEnum.OPTION, "useRange", Lists.newArrayList(CategoryTypeEnum.TILE.getCode()),
			true, true, false, true, true, false, true, true, false),

	PATTERN_NAME("0018", "花纹/名称", GoodsAttrValTypeEnum.INPUT, "def4", Lists.newArrayList(CategoryTypeEnum.TILE.getCode()),
			true, true, true, true, false, false, false, true, false),
	STYLE("0019", "风格", GoodsAttrValTypeEnum.OPTION, "def1", Lists.newArrayList(CategoryTypeEnum.TILE.getCode()),
			true, true, false, true, true, false, true, true, false),
	CODE("0020", "编号", GoodsAttrValTypeEnum.INPUT, "def2", Lists.newArrayList(CategoryTypeEnum.TILE.getCode()),
			true, true, false, true, false, false, false, true, false),
	
	SUPPORT_ADDITEM("0021", "是否支持加价项", GoodsAttrValTypeEnum.OPTION, "supportAdditem", Lists.newArrayList(CategoryTypeEnum.CONCRETE.getCode()),
			true, true, false, true, false, false, false, true, false),
	
//			1-isEdit, 2-isShow, 3-detailShow, 4-effective, 5-multiSelect, 6-supportSearch, 7-valueAdded, 8-spu, 9-splitUp
	;
	
    /**
     * 编码
     */
    private String id;
    /**
     * 描述
     */
    private String description;
    /**
     * 对应商品表go_goods的VO字段
     */
    private String field;
    /**
     * 值类型
     */
    private GoodsAttrValTypeEnum valType;
    /**
     * 是否允许修改
     */
    private boolean isEdit;
    /**
     * 是否允许显示
     */
    private boolean isShow;
    
    /**
     * 是否生效
     */
    private Boolean effective;

    /**
     * 是否详情显示
     */
    private Boolean detailShow;

    /**
     * 是否多选
     */
    private Boolean multiSelect;

    /**
     * 是否支持搜索
     */
    private Boolean supportSearch;

    /**
     * 是否新增值
     */
    private Boolean valueAdded;

    /**
     * 是否spu
     */
    private Boolean spu;
    
    /**
     * 是否拆分
     */
    private Boolean splitUp;
    
    /**
     * 商品类型
     */
    private List<Integer> categoryTypes;
    
    GoodsBaseAttrEnum(String id, String description, GoodsAttrValTypeEnum valType, String field, List<Integer> categoryType,
                      boolean isEdit, boolean isShow, boolean detailShow, boolean effective, boolean multiSelect,
                      boolean supportSearch, boolean valueAdded, boolean spu, boolean splitUp) {
        this.id = id;
        this.description = description;
        this.valType = valType;
        this.field = field;
        this.isEdit = isEdit;
        this.isShow = isShow;
        this.categoryTypes = categoryType;
        this.detailShow = detailShow;
        this.effective = effective;
        this.multiSelect = multiSelect;
        this.supportSearch = supportSearch;
        this.valueAdded = valueAdded;
        this.spu = spu;
        this.splitUp = splitUp;
    }

    public static GoodsBaseAttrEnum getById(String id) {
		for(GoodsBaseAttrEnum _enum : GoodsBaseAttrEnum.values()){
			if(_enum.id.equals(id)){
				return _enum;
			}
		}
		return null;
	}
    
    public static String getById() {
		
		return "";
	}
    
	public String getId() {
		return id;
	}

	public String getDescription() {
		return description;
	}

	public GoodsAttrValTypeEnum getValType() {
		return valType;
	}

	public String getField() {
		return field;
	}

	public boolean isEdit() {
		return isEdit;
	}

	public List<Integer> getCategoryTypes() {
		return categoryTypes;
	}

	public boolean isShow() {
		return isShow;
	}

	public Boolean getEffective() {
		return effective;
	}

	public Boolean getDetailShow() {
		return detailShow;
	}

	public Boolean getMultiSelect() {
		return multiSelect;
	}

	public Boolean getSupportSearch() {
		return supportSearch;
	}

	public Boolean getValueAdded() {
		return valueAdded;
	}

	public Boolean getSpu() {
		return spu;
	}

	public Boolean getSplitUp() {
		return splitUp;
	}
	
}
