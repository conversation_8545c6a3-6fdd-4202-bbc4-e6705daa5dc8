package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("请求商品资源DTO")
public class ReqGoodsResourceDTO {
    /**
     * 所在国家编码
     */
    @ApiModelProperty(value = "所在国家编码")
    private String countryCode;

    /**
     * 所在省编码
     */
    @ApiModelProperty(value = "所在省编码")
    private String provinceCode;

    /**
     * 所在城市编码
     */
    @ApiModelProperty(value = "所在城市编码")
    private String cityCode;

    /**
     * 所在地区编码
     */
    @ApiModelProperty(value = "所在地区编码")
    private String areaCode;

    /**
     * 所在街道编码
     */
    @ApiModelProperty(value = "所在街道编码")
    private String streetCode;

    /**
     * 所在国家名称
     */
    @ApiModelProperty(value = "所在国家名称")
    private String countryName;

    /**
     * 所在省名称
     */
    @ApiModelProperty(value = "所在省名称")
    private String provinceName;

    /**
     * 所在城市名称
     */
    @ApiModelProperty(value = "所在城市名称")
    private String cityName;

    /**
     * 所在地区名称
     */
    @ApiModelProperty(value = "所在地区名称")
    private String areaName;

    /**
     * 所在街道名称
     */
    @ApiModelProperty(value = "所在街道名称")
    private String streetName;

    /**
     * 卖家编号
    */
    @ApiModelProperty(value = "卖家编号")
    private String sellerId;

    /**
     * 卖家编号集
     */
    @ApiModelProperty(value = "卖家编号集")
    private List<String> sellerIdList;

    /**
     * 卖家姓名
     */
    @ApiModelProperty(value = "卖家姓名")
    private String sellerName;

    /**
     * 台班费规则ID
     */
    @ApiModelProperty(value = "台班费规则ID")
    private String machineRuleId;


    /**
     * 空载费规则Id
     */
    @ApiModelProperty(value = "空载费规则Id")
    private String emptyLoadRuleId;


    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keywords;
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 关键字: 已失效
     */
    @ApiModelProperty(value = "关键字")
    private String searchKeywords;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;
    /**
     * 商品类型 ：已失效
     */
    @ApiModelProperty(value = "商品类型")
    private Integer categoryType;
    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private String categoryTypeStr;
    /**
     * 属性集
     */
    @ApiModelProperty(value = "属性集")
    private List<AttributeDTO> attributeDTOs;

    /**
     * 综合排序：0 不排序 1正序 2倒叙
     */
    @ApiModelProperty(value = "综合排序")
    private Integer totalSort;

    /**
     * 销量排序：0 不排序 1正序 2倒叙
     */
    @ApiModelProperty(value = "销量排序")
    private Integer salesVolumeSort;

    /**
     * 价格排序：0 不排序 1正序 2倒叙
     */
    @ApiModelProperty(value = "价格排序")
    private Integer priceSort;

    @ApiModelProperty(value = "购买频率排序开关: 0:不排序, 1:排序")
    private Integer purchaseFrequencySortFlag;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @NotNull(message = "每页显示数量不能为空")
    private Integer pageSize;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    /**
     * 是否允许自提
     */
    @ApiModelProperty(value = "是否允许自提")
    private Boolean ifTakeSelf;

    /**
     * 是否平台配送
     */
    @ApiModelProperty(value = "是否平台配送")
    private Boolean ifPlatformDelivery;

    /**
     * 是否卖家配送
     */
    @ApiModelProperty(value = "是否卖家配送")
    private Boolean ifSellerDelivery;
    /**
     * 仓库
     */
    @ApiModelProperty(value = "仓库")
    private String storeId;

    /**
     * 商品Id集
     */
    @ApiModelProperty(value = "商品Id集")
    private List<String> goodsIdList;

    @ApiModelProperty(value = "当前查询人会员id（用于查询收藏）")
    private String searchMemberId;
}
