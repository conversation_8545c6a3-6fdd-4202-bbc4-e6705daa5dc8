package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2019-11-19 14:32
 * @Description: SellerConfirmJudgeDTO
 */
@Data
@ApiModel("判定商品是否需要卖家确认实体")
public class SellerConfirmJudgeDTO {

    @ApiModelProperty("商品ID列表")
    private List<String> goodsIdList;

    @ApiModelProperty("是否需要旺季校验")
    private Boolean needCementBusySeasonApproval = Boolean.FALSE;

}
