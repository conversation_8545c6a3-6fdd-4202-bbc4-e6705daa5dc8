package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("定价模式")
public class PriceModeDTO {
    /**
     * 定价模式Code: 0,1,2,3,4,5
     */
    @ApiModelProperty(value = "定价模式Code")
    private Integer modeCode;
    /**
     * 定价模式Name:
     * 0 不按区域定价
     * 1 一级
     * 2 二级
     * 3 三级
     * 4 四级
     * 5 五级
     */
    @ApiModelProperty(value = "定价模式名称")
    private String modeName;
}
