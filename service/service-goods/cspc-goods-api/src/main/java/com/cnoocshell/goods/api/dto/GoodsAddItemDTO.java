package com.cnoocshell.goods.api.dto;

import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Auther: chenjun
 * @Description:
 * @Date: 15/08/2018 18:56
 */
@Slf4j
@Data
@ApiModel("商品加价项对象")
public class GoodsAddItemDTO implements Serializable {

    private static final long serialVersionUID = -9048352978268247460L;

    public static final String EMBELLISH_PIPE_MORTAR = "润管砂浆";
    public static final String SLUMP = "坍落度";
    public static final String PUMPING_WAY = "施工方式";//泵送方式

    /**
     * 资源id 主键
     */
    @ApiModelProperty(value = "资源id 主键")
    private String resourceId;

    /**
     * ERP加价项编码
     */
    @ApiModelProperty(value = "ERP加价项编码")
    private String erpAdditemCode;

    @ApiModelProperty(value = "ERP加价项名称")
    private String erpAdditemName;

    @ApiModelProperty(value = "ERP加价项类型名称")
    private String erpAdditemTypeName;

    @ApiModelProperty(value = "ERP加价项类型编码")
    private String erpAdditemTypeCode;

    @ApiModelProperty(value = "erp属性")
    private String erpAttribute;

    /**
     * 加价项id
     */
    @ApiModelProperty(value = "加价项id")
    private String additemId;

    /**
     * 会员id(商家公司的id）
     */
    @ApiModelProperty(value = "会员id(商家公司的id）")
    private String memberId;

    /**
     * 商品分类code(1，2)
     */
    @ApiModelProperty(value = "商品分类code(1，2)")
    private Integer goodstypeCode;

    /**
     * 加价项名称
     */
    @ApiModelProperty(value = "加价项名称")
    private String additemName;

    /**
     * 加价项价格
     */
    @ApiModelProperty(value = "加价项价格")
    private BigDecimal additemPrice;

    /**
     * 加价项单位
     */
    @ApiModelProperty(value = "加价项单位")
    private String additemUnit;

    /**
     * 价格单位
     */
    @ApiModelProperty(value = "价格单位")
    private String priceUnit;

    /**
     * 加价项父id，0表示父类
     */
    @ApiModelProperty(value = "加价项父id，0表示父类")
    private String parentId;

    /**
     * 商家加价项模板code，对应additem_code_description
     */
    @ApiModelProperty(value = "商家加价项模板code，对应additem_code_description")
    private String additemCode;

    /**
     * 加价项配置类型 0平台，1商家
     */
    @ApiModelProperty(value = "加价项配置类型 0平台，1商家")
    private Integer additemType;

    /**
     * 节点类型 0根节点/1叶节点
     */
    @ApiModelProperty(value = "节点类型 0根节点/1叶节点")
    private Integer nodeType;

    /**
     * 查询出的数据通过此字段排序
     */
    @ApiModelProperty(value = "查询出的数据通过此字段排序")
    private Integer numberOrder;

    /**
     * 是否支持议价(保留字段) 0 可议价，1不可议价
     */
    @ApiModelProperty(value = "是否支持议价(保留字段) 0 可议价，1不可议价")
    private Integer priceStatus;

    /**
     * 商家是否支持该项 0 支持，1不支持
     */
    @ApiModelProperty(value = "商家是否支持该项 0 支持，1不支持")
    private Integer disable;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;

    /**
     * 删除标记 1逻辑删除
     */
    @ApiModelProperty(value = "删除标记 1逻辑删除")
    private Integer delFlg;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 坍落度开关:只针对塌落度的加价项，0：没有关联其他加价项，1：关联其他加价项；
     */
    @ApiModelProperty(value = "坍落度开关:只针对塌落度的加价项，0：没有关联其他加价项，1：关联其他加价项；")
    private String slumpSwitch;

    /**
     * 计价方式（1:单价，2:商品，3:订单，4:不计价）
     */
    @ApiModelProperty(value = "计价方式（1:单价，2:商品，3:订单，4:不计价）")
    private Integer priceWay;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Boolean ifmust;

    /**
     * 加价项值类型（1：单选:2：多选:3：文本）
     */
    @ApiModelProperty(value = "加价项值类型（1：单选:2：多选:3：文本）")
    private Integer valueType;

    /**
     * 加价项版本
     */
    @ApiModelProperty(value = "加价项版本")
    private String itemVersion;

    /**
     * 子节点
     */
    @ApiModelProperty(value = "子节点")
    private List<GoodsAddItemDTO> children;

    
    /**
     * 独立数量，表示该加价项不是跟随商品数量，而是独立数量
     */
    @ApiModelProperty(value = "独立数量，表示该加价项不是跟随商品数量，而是独立数量")
    private BigDecimal independentAmount;

    @ApiModelProperty("是否加价项常规指标")
    private Boolean conventionalIndicatorsFlag;


    @ApiModelProperty("当前对象的map封装")
    private Map<String,String> map;

    public void attrPart2Map(){
        map = Maps.newHashMap();
        map.put("additemId",additemId);
        map.put("parentId",parentId);
        map.put("additemName",additemName);
        map.put("additemPrice",additemPrice == null ? "0" : additemPrice.setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString());
        //设置统一价格 前端需要的字段
        map.put("allPrice",additemPrice == null ? "0" : additemPrice.setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString());
        map.put("additemUnit",additemUnit);
        map.put("additemCode",additemCode);
        map.put("numberOrder",numberOrder ==null ? "" : numberOrder +"");
    }
}
