package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("卖家资源DTO")
public class ResourceSellerDTO {
    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id")
    private String resourceId;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String goodsId;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String[] imgs;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品分类
     * (1, ""),
     * (2, "");
     */
    @ApiModelProperty(value = "商品分类")
    private String goodsType;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDescribe;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    /**
     * 资源code
     */
    @ApiModelProperty(value = "资源code")
    private String resourceCode;

    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人")
    private String createUser;
    /**
     * 上架时间
     */
    @ApiModelProperty(value = "上架时间")
    private Date upTime;

    /**
     * 下架时间
     */
    @ApiModelProperty(value = "下架时间")
    private Date downTime;

    /**
     * 资源状态:
     * RES_STATUS100("100", "已挂牌"),
     * RES_STATUS200("200", "已撤牌"),
     * RES_STATUS300("300", "撤牌处理中"),
     * RES_STATUS400("400", "未上架"),
     * RES_STATUS500("500", "待审批");
     */
    @ApiModelProperty(value = "资源状态")
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
