package com.cnoocshell.goods.api.enums;

/**
 * @Author: <EMAIL>
 * @Description 商品状态： 1 草稿 2启用（审核通过）3禁用 4审核中 5审核失败 6 新增审核 7 修改审核 8 删除审核
 * @date 2018年8月16日 下午3:17:13
 */
public enum GoodsStatusEnum {

    DRAFT(1, "草稿"),
    ENABLE(2, "启用"),
    DISABLE(3, "禁用"),
    AUDIT(4, "审核中"),
    FAIL(5, "审核失败"),
    CREATE_AUDIT(6, "新增审核"),
    UPDATE_AUDIT(7, "修改审核"),
    DELETE_AUDIT(8, "删除审核"),
    ;

    /**
     * 编码
     */
    private final int code;
    /**
     * 描述
     */
    private final String description;

    GoodsStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static GoodsStatusEnum getByCode(int code) {
        for (GoodsStatusEnum _enum : GoodsStatusEnum.values()) {
            if (_enum.code == code) {
                return _enum;
            }
        }
        return null;
    }
}
