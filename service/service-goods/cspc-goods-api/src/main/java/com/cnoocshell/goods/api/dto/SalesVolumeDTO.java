package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("销售量DTO")
public class SalesVolumeDTO {
    /**
     * 起售量
     */
    @ApiModelProperty(value = "起售量")
    private BigDecimal salesVolume;
    /**
     * 销售单位
     */
    @ApiModelProperty(value = "销售单位")
    private String unit;
    /**
     * 销售单位编号
     */
    @ApiModelProperty(value = "销售单位编号")
    private String unitId;
    /**
     * 是否有货
     */
    @ApiModelProperty(value = "是否有货")
    private Integer isHaveGoods;
}
