package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("平台商品DTO")
public class GoodsPlatformDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String goodsId;

    @ApiModelProperty(value = "spuID")
    private String spuId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品业务类型 1平台商品2卖家商品3采购商品
     */
    @ApiModelProperty(value = "商品业务类型")
    private Integer goodsType;

    /**
     * 拼接属性值信息,便于模糊搜索
     */
    @ApiModelProperty(value = "拼接属性值信息,便于模糊搜索")
    private String searchKeywords;

    /**
     * 状态值，1草稿 2启用（审核通过）3禁用 4审核中 5审核失败
     */
    @ApiModelProperty(value = "状态值")
    private Integer goodsStatus;

    /**
     * 卖家id
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;

    /**
     * 卖家名称
     */
    @ApiModelProperty(value = "卖家名称")
    private String sellerName;

    /**
     * 是否支持加价项
     */
    @ApiModelProperty(value = "是否支持加价项")
    private String supportAdditem;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String measureUnit;

    /**
     * 包装
     */
    @ApiModelProperty(value = "包装")
    private String pack;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specs;

    /**
     * 标号
     */
    @ApiModelProperty(value = "标号")
    private String mark;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String concreteClass;

    /**
     * 等级强度
     */
    @ApiModelProperty(value = "等级强度")
    private String strength;

    /**
     * 塌落度（毫米）
     */
    @ApiModelProperty(value = "塌落度（毫米）")
    private String slump;

    /**
     * 质量标准
     */
    @ApiModelProperty(value = "质量标准")
    private String qualityStandard;

    /**
     * 使用范围
     */
    @ApiModelProperty(value = "使用范围")
    private String useRange;

    /**
     * 物流类型
     */
    @ApiModelProperty(value = "物流类型")
    private String logistics;

    /**
     * 搬运费规则
     */
    @ApiModelProperty(value = "搬运费规则")
    private String cartageRule;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式")
    private Byte pricingMode;

    /**
     * 配送方式 1自提，2厂家配送，3平台配送
     */
    @ApiModelProperty(value = "配送方式")
    private String deliveryMode;

    /**
     * 商品类型 1 2
     */
    @ApiModelProperty(value = "商品类型")
    private Integer categoryType;

    /**
     * 商品类型字符串
     */
    @ApiModelProperty(value = "商品类型字符串")
    private String categoryTypeStr;

    @ApiModelProperty(value = "重量")
    private BigDecimal weight;

    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String color;

    /**
     * 尺码
     */
    @ApiModelProperty(value = "尺码")
    private String size;

    /**
     * 分类1
     */
    @ApiModelProperty(value = "分类1")
    private String category1;

    /**
     * 分类2
     */
    @ApiModelProperty(value = "分类2")
    private String category2;

    /**
     * 分类3
     */
    @ApiModelProperty(value = "分类3")
    private String category3;

    /**
     * 分类4
     */
    @ApiModelProperty(value = "分类4")
    private String category4;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String imgs;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;

    /**
     * 商品特点
     */
    @ApiModelProperty(value = "商品特点")
    private String memo1;

    /**
     * 适用范围
     */
    @ApiModelProperty(value = "适用范围")
    private String memo2;

    /**
     * 性能指标
     */
    @ApiModelProperty(value = "性能指标")
    private String memo3;

    /**
     * 使用指南
     */
    @ApiModelProperty(value = "使用指南")
    private String memo4;

    /**
     * 备注5
     */
    @ApiModelProperty(value = "备注5")
    private String memo5;

    /**
     * 备注6
     */
    @ApiModelProperty(value = "备注6")
    private String memo6;

    /**
     * 商品特点
     */
    @ApiModelProperty(value = "app商品特点")
    private String appMemo1;

    /**
     * app适用范围
     */
    @ApiModelProperty(value = "app适用范围")
    private String appMemo2;

    /**
     * app性能指标
     */
    @ApiModelProperty(value = "app性能指标")
    private String appMemo3;

    /**
     * app使用指南
     */
    @ApiModelProperty(value = "app使用指南")
    private String appMemo4;

    /**
     * app备注5
     */
    @ApiModelProperty(value = "app备注5")
    private String appMemo5;

    /**
     * app备注6
     */
    @ApiModelProperty(value = "app备注6")
    private String appMemo6;

    /**
     * 自定义属性1
     */
    @ApiModelProperty(value = "自定义属性1")
    private String def1;

    /**
     * 自定义属性2
     */
    @ApiModelProperty(value = "自定义属性2")
    private String def2;

    /**
     * 自定义属性3
     */
    @ApiModelProperty(value = "自定义属性3")
    private String def3;

    /**
     * 自定义属性4
     */
    @ApiModelProperty(value = "自定义属性4")
    private String def4;

    /**
     * 自定义属性5
     */
    @ApiModelProperty(value = "自定义属性5")
    private String def5;

    /**
     * 自定义属性6
     */
    @ApiModelProperty(value = "自定义属性6")
    private String def6;

    /**
     * 自定义属性7
     */
    @ApiModelProperty(value = "自定义属性7")
    private String def7;

    /**
     * 自定义属性8
     */
    @ApiModelProperty(value = "自定义属性8")
    private String def8;

    /**
     * 分类属性
     */
    @ApiModelProperty(value = "分类属性")
    private List<CategoryAttributeDTO> categoryAttributeDTOS;

    /**
     * 扩展属性属性值
     */
    @ApiModelProperty(value = "扩展属性属性值")
    private List<GoodsAttributeDTO> goodsAttributeDTOS;
}
