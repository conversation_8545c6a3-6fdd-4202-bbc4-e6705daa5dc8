package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("商品分类属性DTO")
public class GoodsCategoryAttrDTO implements Serializable {

    @ApiModelProperty(value = "搜索值")
	private boolean searchValue = true;

    @ApiModelProperty(value = "值列表")
	private List<GoodsAttributeDTO> valueList;
	
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String categoryAttributeId;

    /**
     * 商品类型 1 2
     */
    @ApiModelProperty(value = "商品类型")
    private Integer categoryType;

    /**
     * 商品分类id
     */
    @ApiModelProperty(value = "商品分类id")
    private String categoryId;

    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private String categoryName;

    /**
     * 商品属性id
     */
    @ApiModelProperty(value = "商品属性id")
    private String goodsAttriId;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String attriName;
    
    /**
     * 商品属性值
     */
    @ApiModelProperty(value = "商品属性值")
    private String attriValue;
    
    /**
     * 商品属性值Id
     */
    @ApiModelProperty(value = "商品属性值Id")
    private String valueId;
    
    /**
     * 商品属性值Code
     */
    @ApiModelProperty(value = "商品属性值Code")
    private String valueCode;
    
    /**
     * 属性值类型,1-数字型，2-输入型，3-选项值，4-特殊值，5-日期
     */
    @ApiModelProperty(value = "属性值类型")
    private Integer valueType;
    
    /**
     * 是否生效
     */
    @ApiModelProperty(value = "是否生效")
    private Boolean effective;

    /**
     * 是否显示
     */
    @ApiModelProperty(value = "是否显示")
    private Boolean onShow;

    /**
     * 是否详情显示
     */
    @ApiModelProperty(value = "是否详情显示")
    private Boolean detailShow;

    /**
     * 是否可编辑
     */
    @ApiModelProperty(value = "是否可编辑")
    private Boolean editable;

    /**
     * 是否多选
     */
    @ApiModelProperty(value = "是否多选")
    private Boolean multiSelect;

    /**
     * 是否支持搜索
     */
    @ApiModelProperty(value = "是否支持搜索")
    private Boolean supportSearch;

    /**
     * 是否新增值
     */
    @ApiModelProperty(value = "是否新增值")
    private Boolean valueAdded;

    /**
     * 是否spu
     */
    @ApiModelProperty(value = "是否spu")
    private Boolean spu;

    /**
     * 是否拆分
     */
    @ApiModelProperty(value = "是否拆分")
    private Boolean splitUp;
    
    /**
     * 对应商品表字段
     */
    @ApiModelProperty(value = "对应商品表字段")
    private String tableField;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return category_attribute_id - 主键
     */
    public String getCategoryAttributeId() {
        return categoryAttributeId;
    }

    /**
     * 设置主键
     *
     * @param categoryAttributeId 主键
     */
    public void setCategoryAttributeId(String categoryAttributeId) {
        this.categoryAttributeId = categoryAttributeId == null ? null : categoryAttributeId.trim();
    }

    /**
     * 获取商品类型 1 2
     *
     * @return category_type - 商品类型 1 2
     */
    public Integer getCategoryType() {
        return categoryType;
    }

    /**
     * 设置商品类型 1 2
     *
     * @param categoryType 商品类型 1 2
     */
    public void setCategoryType(Integer categoryType) {
        this.categoryType = categoryType;
    }

    /**
     * 获取主键
     *
     * @return category_id - 主键
     */
    public String getCategoryId() {
        return categoryId;
    }

    /**
     * 设置主键
     *
     * @param categoryId 主键
     */
    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId == null ? null : categoryId.trim();
    }

    /**
     * 获取商品分类
     *
     * @return category_name - 商品分类
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 设置商品分类
     *
     * @param categoryName 商品分类
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    /**
     * 获取主键
     *
     * @return goods_attri_id - 主键
     */
    public String getGoodsAttriId() {
        return goodsAttriId;
    }

    /**
     * 设置主键
     *
     * @param goodsAttriId 主键
     */
    public void setGoodsAttriId(String goodsAttriId) {
        this.goodsAttriId = goodsAttriId == null ? null : goodsAttriId.trim();
    }

    /**
     * 获取商品属性
     *
     * @return attri_name - 商品属性
     */
    public String getAttriName() {
        return attriName;
    }

    /**
     * 设置商品属性
     *
     * @param attriName 商品属性
     */
    public void setAttriName(String attriName) {
        this.attriName = attriName == null ? null : attriName.trim();
    }

    /**
     * 获取是否生效
     *
     * @return effective - 是否生效
     */
    public Boolean getEffective() {
        return effective;
    }

    /**
     * 设置是否生效
     *
     * @param effective 是否生效
     */
    public void setEffective(Boolean effective) {
        this.effective = effective;
    }

    /**
     * 获取是否显示
     *
     * @return on_show - 是否显示
     */
    public Boolean getOnShow() {
        return onShow;
    }

    /**
     * 设置是否显示
     *
     * @param onShow 是否显示
     */
    public void setOnShow(Boolean onShow) {
        this.onShow = onShow;
    }

    /**
     * 获取是否详情显示
     *
     * @return detail_show - 是否详情显示
     */
    public Boolean getDetailShow() {
        return detailShow;
    }

    /**
     * 设置是否详情显示
     *
     * @param detailShow 是否详情显示
     */
    public void setDetailShow(Boolean detailShow) {
        this.detailShow = detailShow;
    }

    /**
     * 获取是否可编辑
     *
     * @return editable - 是否可编辑
     */
    public Boolean getEditable() {
        return editable;
    }

    /**
     * 设置是否可编辑
     *
     * @param editable 是否可编辑
     */
    public void setEditable(Boolean editable) {
        this.editable = editable;
    }

    /**
     * 获取是否多选
     *
     * @return multi_select - 是否多选
     */
    public Boolean getMultiSelect() {
        return multiSelect;
    }

    /**
     * 设置是否多选
     *
     * @param multiSelect 是否多选
     */
    public void setMultiSelect(Boolean multiSelect) {
        this.multiSelect = multiSelect;
    }

    /**
     * 获取是否支持搜索
     *
     * @return support_search - 是否支持搜索
     */
    public Boolean getSupportSearch() {
        return supportSearch;
    }

    /**
     * 设置是否支持搜索
     *
     * @param supportSearch 是否支持搜索
     */
    public void setSupportSearch(Boolean supportSearch) {
        this.supportSearch = supportSearch;
    }

    /**
     * 获取是否新增值
     *
     * @return value_added - 是否新增值
     */
    public Boolean getValueAdded() {
        return valueAdded;
    }

    /**
     * 设置是否新增值
     *
     * @param valueAdded 是否新增值
     */
    public void setValueAdded(Boolean valueAdded) {
        this.valueAdded = valueAdded;
    }

    /**
     * 获取是否spu
     *
     * @return spu - 是否spu
     */
    public Boolean getSpu() {
        return spu;
    }

    /**
     * 设置是否spu
     *
     * @param spu 是否spu
     */
    public void setSpu(Boolean spu) {
        this.spu = spu;
    }

    /**
     * 获取对应商品表字段
     *
     * @return table_field - 对应商品表字段
     */
    public String getTableField() {
        return tableField;
    }

    /**
     * 设置对应商品表字段
     *
     * @param tableField 对应商品表字段
     */
    public void setTableField(String tableField) {
        this.tableField = tableField == null ? null : tableField.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建者
     *
     * @return create_user - 创建者
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建者
     *
     * @param createUser 创建者
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取修改者
     *
     * @return update_user - 修改者
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改者
     *
     * @param updateUser 修改者
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取删除标志,1-删除，0-正常
     *
     * @return del_flg - 删除标志,1-删除，0-正常
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标志,1-删除，0-正常
     *
     * @param delFlg 删除标志,1-删除，0-正常
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

	public Integer getValueType() {
		return valueType;
	}

	public void setValueType(Integer valueType) {
		this.valueType = valueType;
	}
}