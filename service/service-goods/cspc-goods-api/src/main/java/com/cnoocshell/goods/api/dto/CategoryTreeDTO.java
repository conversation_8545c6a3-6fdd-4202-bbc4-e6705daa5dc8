package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("分类树")
public class CategoryTreeDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String categoryId;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private String parentId;

    /**
     * 父级名称
     */
    @ApiModelProperty(value = "父级名称")
    private String parentName;

    /**
     * 类别编码
     */
    @ApiModelProperty(value = "类别编码")
    private String categoryCode;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称")
    private String categoryName;


    /**
     * web图片
     */
    @ApiModelProperty(value = "web图片")
    private String imgs;

    /**
     * app图片
     */
    @ApiModelProperty(value = "app图片")
    private String appImgs;

    /**
     * 小程序图片
     */
    @ApiModelProperty(value = "小程序图片")
    private String miniImgs;


    /**
     * 是否备件,1-是，0-否
     */
    @ApiModelProperty(value = "是否备件")
    private Boolean ifSpareParts;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;

    //sap编码
    private String sapCode;

    /**
     * 子级分类
     */
    @ApiModelProperty(value = "子级分类")
    private List<CategoryTreeDTO> childs;

    /**
     * 子级分类
     */
    @ApiModelProperty(value = "子级分类")
    private List<CategoryTreeDTO> child;

    /**
     * 分类商品
     */
    @ApiModelProperty(value = "分类商品")
    private List<GoodsDTO> goodsDTOS;

    /**
     * 分类和账户关系
     */
    private List<CategoryAccountRelationDTO> categoryAccountRelationDTOS;

}
