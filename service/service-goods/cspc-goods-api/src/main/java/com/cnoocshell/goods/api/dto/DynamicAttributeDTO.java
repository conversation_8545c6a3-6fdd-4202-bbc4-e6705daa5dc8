package com.cnoocshell.goods.api.dto;

import com.cnoocshell.goods.api.enums.BooleanEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

@Data
@Slf4j
@ApiModel("动态属性DTO")
public class DynamicAttributeDTO implements Serializable {

	@ApiModelProperty(value = "商品属性值list")
	private List<GoodsAttributeValueDTO> goodsAttributeValueDTOList;

	public boolean check(String code) {
		boolean checkRes = false;
		if (goodsAttributeValueDTOList == null || goodsAttributeValueDTOList.isEmpty()) {
			return false;
		}
		for (GoodsAttributeValueDTO dto : goodsAttributeValueDTOList) {
			if (code.equals(dto.getGoodsAttriId()) && BooleanEnum.YES.message().equals(dto.getAttriValue())) {
				checkRes = true;
				return checkRes;
			}
		}
		return checkRes;
	}
	 
	public String getValue(String code) {
		if (goodsAttributeValueDTOList == null || goodsAttributeValueDTOList.isEmpty()) {
			return "";
		}

		for (GoodsAttributeValueDTO dto : goodsAttributeValueDTOList) {
			if (code.equals(dto.getGoodsAttriId())) {
				return dto.getAttriValue();
			}
		}
		return "";
	}

	public String getValueCode(String code) {
		if (goodsAttributeValueDTOList == null || goodsAttributeValueDTOList.isEmpty()) {
			return "";
		}

		for (GoodsAttributeValueDTO dto : goodsAttributeValueDTOList) {
			if (code.equals(dto.getGoodsAttriId())) {
				return dto.getAttriValueCode();
			}
		}
		return "";
	}

}
