package com.cnoocshell.goods.api.enums.contract;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum ContractStatusEnum {
    DRAFT("DRAFT", "草稿"),
    NOTEFFECTIVE("NOTEFFECTIVE", "未启用"),//创建合同时间未到合同生效时间
    NOTENABLED("NOTENABLED", "已中止"), //已中止
    INEFFECT("INEFFECT", "已生效"), //已生效
    CLOSED("CLOSED","已终止"); //已终止


    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String message;

    ContractStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ContractStatusEnum from(String code){
        for (ContractStatusEnum value : values()) {
            if (Objects.equals(value.code,code)){
                return value;
            }
        }
        return null;
    }
}
