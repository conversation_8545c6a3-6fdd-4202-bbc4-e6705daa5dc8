package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("确认仓库信息请求DTO")
public class ReqConfirmResourceStoreDTO {
    /**
     * 配送方式,BUYER_TAKE("030060100", "买家自提"),SELLER_DELIVERY("030060200", "卖家配送"),PLATFORM_DELIVERY("030060300", "平台配送")
     */
    @ApiModelProperty(value = "配送方式")
    private String deliveryWay;

    /**
     * 买家ID
     */
    @ApiModelProperty(value = "买家ID")
    private String buyerId;

    /**
     * 门店优惠码
     */
    @ApiModelProperty(value = "门店优惠码")
    private String shopCode;

    /**
     * 所在国家编码
     */
    @ApiModelProperty(value = "所在国家编码")
    private String countryCode;

    /**
     * 所在省编码
     */
    @ApiModelProperty(value = "所在省编码")
    private String provinceCode;

    /**
     * 所在城市编码
     */
    @ApiModelProperty(value = "所在城市编码")
    private String cityCode;

    /**
     * 所在地区编码
     */
    @ApiModelProperty(value = "所在地区编码")
    private String areaCode;

    /**
     * 所在街道编码
     */
    @ApiModelProperty(value = "所在街道编码")
    private String streetCode;
    /**
     * 所在国家编码
     */
    @ApiModelProperty(value = "所在国家编码")
    private String countryName;

    /**
     * 所在省编码
     */
    @ApiModelProperty(value = "所在省编码")
    private String provinceName;

    /**
     * 所在城市编码
     */
    @ApiModelProperty(value = "所在城市编码")
    private String cityName;

    /**
     * 所在地区编码
     */
    @ApiModelProperty(value = "所在地区编码")
    private String areaName;

    /**
     * 所在街道编码
     */
    @ApiModelProperty(value = "所在街道编码")
    private String streetName;

    /**
     * 所在地址ID
     */
    @ApiModelProperty(value = "所在地址ID")
    private String addressId;


}
