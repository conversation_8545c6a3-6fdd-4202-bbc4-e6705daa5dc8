package com.cnoocshell.goods.api.enums.contract;

import lombok.Getter;

@Getter
public enum ContractAdjustPriceNameEnum {
    CEMENT(1, "调价函通知"),
    CONCRETE(2, "混泥土调价函通知");

    /**
     * 编码
     */
    private Integer code;

    /**
     * 描述
     */
    private String message;

    ContractAdjustPriceNameEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMsgByCode(Integer code) {
        ContractAdjustPriceNameEnum[] enumConstants = values();
        for (ContractAdjustPriceNameEnum enumConstant : enumConstants) {
            if (enumConstant.code.equals(code)) {
                return enumConstant.getMessage();
            }
        }
        return null;
    }
}
