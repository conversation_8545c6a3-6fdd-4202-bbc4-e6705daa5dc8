package com.cnoocshell.goods.api.dto;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("卖家商品分页DTO")
public class PageSellerGoodsDTO {
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
    /**
     * 商品CODE
     */
    @ApiModelProperty(value = "商品CODE")
    private String goodsCode;
    /**
     * 商品状态 1草稿 2启用（审核通过）3禁用 4审核中 5审核失败
     */
    @ApiModelProperty(value = "商品状态")
    private Integer goodsStatus;
    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品分类类型")
    private Integer categoryType;
    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品分类Code")
    private String categoryCode;
    /**
     * 商品业务类型 1平台商品2卖家商品3采购商品
     */
    @ApiModelProperty(value = "商品业务类型")
    private Integer goodsType;
    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private String category;
    /**
     * 卖家
     */
    @ApiModelProperty(value = "卖家")
    private String sellerId;
    /**
     * 商品描述（关键字）
     */
    @ApiModelProperty(value = "商品描述（关键字）")
    private String searchKeywords;
    /**
     * 是否模糊查询商品名称
     */
    @ApiModelProperty(value = "是否模糊查询商品名称")
    private boolean isLikeName;

    @ApiModelProperty(value = "是否引用(背靠背)商品")
    private Boolean refFlg;
    /**
     * 分页条数
     */
    @ApiModelProperty(value = "分页条数")
    private Integer pageSize;
    /**
     * 分页页码
     */
    @ApiModelProperty(value = "分页页码")
    private Integer pageNum;

    @ApiModelProperty(value = "品牌字段排序标识 0升序 1降序")
    private Integer brandOrderFlag;

    //查询code
    private String categoryCodeTwo;

    private String currentMemberId;

    private List<String> goodsIds;

    @ApiModelProperty("主要意向商品ID")
    private List<String> mainIntentionGoodsIds = CollUtil.newArrayList("-1");

    @ApiModelProperty("所有意向商品ID")
    private List<String> allIntentionGoodsIds = CollUtil.newArrayList("-1");

}
