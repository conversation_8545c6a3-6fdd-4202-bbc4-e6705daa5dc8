package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("购买数量限制参数")
public class GoodsQuantityDTO {
    /**
     * 单笔最大购买量
     */
    @ApiModelProperty(value = "单笔最大购买量")
    private BigDecimal ordermaxNum;
    /**
     * 单笔最小购买量
     */
    @ApiModelProperty(value = "单笔最小购买量")
    private BigDecimal orderminNum;
    /**
     * 单笔最小变动量
     */
    @ApiModelProperty(value = "单笔最小变动量")
    private BigDecimal orderminchangeNum;
    /**
     * 单日最大购买量
     */
    @ApiModelProperty(value = "单日最大购买量")
    private BigDecimal daymaxNum;
    /**
     * 相对容差
     */
    @ApiModelProperty(value = "相对容差")
    private BigDecimal relativeTolerance;
}
