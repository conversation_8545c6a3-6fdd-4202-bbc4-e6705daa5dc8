package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("交易价格")
public class TransactionPriceDTO  implements Comparable<TransactionPriceDTO>{
    /**
     * KEY
     */
    @ApiModelProperty(value = "KEY")
    private String key;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private BigDecimal value;

    @Override
    public int compareTo(TransactionPriceDTO o) {
        return this.getValue().compareTo(o.getValue());
    }
}
