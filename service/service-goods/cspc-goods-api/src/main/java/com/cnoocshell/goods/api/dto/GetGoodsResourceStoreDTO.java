package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("获取商品资源仓库DTO")
public class GetGoodsResourceStoreDTO {
    /**
     * 区域层级
     */
    @ApiModelProperty(value = "区域层级")
    private Integer level;
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;
    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String accountId;

    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private List<String> filterIdList;
}
