package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("商品DTO")
public class GoodsDTO implements Serializable {
    /**
     * 基本商品ID
     */
    @ApiModelProperty(value = "基本商品ID")
	private String baseGoodsId;
	
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String goodsId;

    /**
     * spuId
     */
    @ApiModelProperty(value = "spuID")
    private String spuId;
    
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品业务类型 1平台商品2卖家商品
     */
    @ApiModelProperty(value = "商品业务类型")
    private Integer goodsType;

    /**
     * 拼接属性值信息,便于模糊搜索
     */
    @ApiModelProperty(value = "拼接属性值信息,便于模糊搜索")
    private String searchKeywords;

    /**
     * 状态值，1草稿 2启用（审核通过）3禁用 4审核中 5审核失败 
     */
    @ApiModelProperty(value = "状态值")
    private Integer goodsStatus;

    /**
     * 卖家id
     */
    @ApiModelProperty(value = "卖家id")
    private String sellerId;

    /**
     * 是否支持加价项
     */
    @ApiModelProperty(value = "是否支持加价项")
    private String supportAdditem;
    
    /**
     * 物流类型
     */
    @ApiModelProperty(value = "物流类型")
    private String logistics;

    /**
     * 搬运费规则
     */
    @ApiModelProperty(value = "搬运费规则")
    private String cartageRule;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式")
    private Integer pricingMode;
    
    /**
     * 配送方式 1自提，2厂家配送，3平台配送
     */
    @ApiModelProperty(value = "配送方式")
    private String deliveryMode;
    
    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String measureUnit;
    
    /**
     * 包装
     */
    @ApiModelProperty(value = "包装")
    private String pack;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specs;

    /**
     * 标号
     */
    @ApiModelProperty(value = "标号")
    private String mark;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String concreteClass;

    /**
     * 等级强度
     */
    @ApiModelProperty(value = "等级强度")
    private String strength;

    /**
     * 塌落度（毫米）
     */
    @ApiModelProperty(value = "塌落度（毫米）")
    private String slump;

    /**
     * 商品物料编码
     */
    @ApiModelProperty(value = "商品物料编码")
    private String commodityCode;

    /**
     * 质量标准
     */
    @ApiModelProperty(value = "质量标准")
    private String qualityStandard;

    /**
     * 使用范围
     */
    @ApiModelProperty(value = "使用范围")
    private String useRange;

    @ApiModelProperty(value = "重量")
    private BigDecimal weight;
    
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String color;
    
    /**
     * 尺码
     */
    @ApiModelProperty(value = "尺码")
    private String size;
    
    /**
     * 商品类型 1 2
     */
    @ApiModelProperty(value = "商品类型")
    private Integer categoryType;

    /**
     * 商品类型 1 2
     */
    @ApiModelProperty(value = "商品类型Code")
    private String categoryCode;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 分类1
     */
    @ApiModelProperty(value = "分类1")
    private String category1;
    @ApiModelProperty(value = "分类1")
    private String categoryNm1;

    /**
     * 分类2
     */
    @ApiModelProperty(value = "分类2")
    private String category2;
    @ApiModelProperty(value = "分类2")
    private String categoryNm2;

    /**
     * 分类3
     */
    @ApiModelProperty(value = "分类3")
    private String category3;
    @ApiModelProperty(value = "分类3")
    private String categoryNm3;
    
    /**
     * 分类4
     */
    @ApiModelProperty(value = "分类4")
    private String category4;
    @ApiModelProperty(value = "分类4")
    private String categoryNm4;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String[] imgs;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;



    /**
     * 签收选项 0001无  0002卖家签收 0003买家签收 0004司机签收
     */
    @ApiModelProperty(value = "签收选项")
    private String signType;

    /**
     * 搬运标识 0不需搬运  1需要搬运
     */
    @ApiModelProperty(value = "搬运标识")
    private Byte carryFlag;

    /**
     * 标识 0不是  1是
     */
    @ApiModelProperty(value = "标识")
    private Integer concreteFlag;

    /**
     * 加价项标识 0没有  1有
     */
    @ApiModelProperty(value = "加价项标识")
    private Integer addItemFlag;

    /**
     * 支持搬运标识
     */
    @ApiModelProperty("支持搬运标识")
    private Integer supportCarryFlag;

    @ApiModelProperty("引用的厂家商品id（经销商背靠背商品特有）")
    private String refGoodsId;

    @ApiModelProperty("引用的厂家商品的会员id（经销商背靠背商品特有）")
    private String refSellerId;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private List<GoodsAttributeDTO> attrList = null;

    @ApiModelProperty(value = "商品动态属性")
    private DynamicAttributeDTO dynamicAttributeDTO;

    //配送收费标准附件
    private String deliverCostStandard;

    //自提指南附件
    private String selfPickupGuide;

    //自提承运商附件
    private String selfPickupCarrier;

    //物料编码
    private String sapMaterialCode;
    
    private static final long serialVersionUID = 1L;

}