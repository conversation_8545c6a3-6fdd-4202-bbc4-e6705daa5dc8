package com.cnoocshell.goods.api.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 分类和账户关系表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CategoryAccountRelationDTO{

    private String categoryId;
    private String categoryCode;
    private String categoryName;
    private String accountId;
    private String accountRealName;
    private String accountRole;

}
