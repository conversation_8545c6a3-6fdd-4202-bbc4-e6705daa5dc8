package com.cnoocshell.goods.api.dto;

import com.cnoocshell.goods.api.enums.GoodsTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("背靠背厂家商品查询对象及结果DTO")
public class SellerGoodsIdAndNameDTO implements Serializable {

    private static final long serialVersionUID = 6152119378122466334L;
    @ApiModelProperty(value = "商品id(返回结果)")
    private String goodsId;

    @ApiModelProperty(value = "商品名称(查询条件/返回结果)")
    private String goodsName;

    @ApiModelProperty(value = "厂商id(查询条件)")
    private String sellerId;

    @ApiModelProperty(value = "商品类型(查询条件)")
    private Integer goodsType = GoodsTypeEnum.SELLER.getCode();

    @ApiModelProperty(value = "商品分类code(查询条件)")
    private String categoryCode;
}
