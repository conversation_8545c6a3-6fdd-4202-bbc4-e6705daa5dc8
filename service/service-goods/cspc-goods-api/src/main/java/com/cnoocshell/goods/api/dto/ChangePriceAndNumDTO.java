package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("改量改价")
public class ChangePriceAndNumDTO {
    /**
     * 资源Id
     */
    @ApiModelProperty(value = "资源ID")
    private String resourceId;
    /**
     * 商品Id
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;
    /**
     * 卖家Id
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;
    /**
     * 资源编号
     */
    @ApiModelProperty(value = "资源编号")
    private String resourceCode;
    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDescribe;
    /**
     * 出货地
     */
    @ApiModelProperty(value = "出货地")
    private String storeName;
    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private String saleArea;
    /**
     * 当前可售数量
     */
    @ApiModelProperty(value = "当前可售数量")
    private BigDecimal cansaleNum;
    /**
     * 当前价格
     */
    @ApiModelProperty(value = "当前价格")
    private BigDecimal price;
    /**
     * 出厂价
     */
    @ApiModelProperty(value = "出厂价")
    private BigDecimal factoryPrice;

    /**
     * 到位价
     */
    @ApiModelProperty(value = "到位价")
    private BigDecimal arrivePrice;
    /**
     * 计价单位
     */
    @ApiModelProperty(value = "计价单位")
    private String priceUnit;
    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 修改后可售数量
     */
    @ApiModelProperty(value = "修改后可售数量")
    private BigDecimal uptCansaleNum;
    /**
     * 修改后价格
     */
    @ApiModelProperty(value = "修改后价格")
    private BigDecimal uptPrice;

    /**
     * 修改后出厂价
     */
    @ApiModelProperty(value = "修改后出厂价")
    private BigDecimal upFactoryPrice;

    /**
     * 修改后到位价
     */
    @ApiModelProperty(value = "修改后到位价")
    private BigDecimal upArrivePrice;

    /**
     * 计价方式，1 出厂价 2到位价
     */
    @ApiModelProperty(value = "计价方式")
    private String priceWay;

}

