package com.cnoocshell.goods.api.enums;

public enum MergeRegionPrefixEnum {
	HASSUB("-Y_", "-Y_"),
	NOSUB("-N_", "-N_"),
	ENDSUB("_", "_");

	/**
	 * 编码
	 */
	private String code;
	/**
	 * 描述
	 */
	private String description;

	MergeRegionPrefixEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public static MergeRegionPrefixEnum getByCode(String code) {
		for (MergeRegionPrefixEnum _enum : MergeRegionPrefixEnum.values()) {
			if (_enum.code.equals(code)) {
				return _enum;
			}
		}
		return null;
	}
}
