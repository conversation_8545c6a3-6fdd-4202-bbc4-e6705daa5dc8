package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("商品属性DTO")
public class GoodsAttributeDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String goodsAttriId;
    /**
     *商品属性值ID
     */
    @ApiModelProperty(value = "商品属性值ID")
    private String valueId;
    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    private String parentId;
    
    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String attriName;

    /**
     * 属性类型,1-主表属性 ，2-扩展属性
     */
    @ApiModelProperty(value = "属性类型")
    private Integer attriType;

    /**
     * 属性值类型,1-数字型，2-输入型，3-选项值，4-特殊值，5-日期
     */
    @ApiModelProperty(value = "属性值类型")
    private Integer valueType;

    /**
     * 支持商品类型 1 2 ，多类型以','分开
     */
    @ApiModelProperty(value = "支持商品类型,多类型以','分开")
    private String categoryType;
    
    /**
     * 商品分类id，多id以','分开
     */
    @ApiModelProperty(value = "商品分类id,多id以','分开")
    private String category;
    
    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
    private String defalut;

    /**
     * 属性值code
     */
    @ApiModelProperty(value = "属性值code")
    private String attriValueCode;
    
    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attriValue;
    
    /**
     * VO
     */
    @ApiModelProperty(value = "VO")
    private String field;
    
    /**
     * 是否可编辑
     */
    @ApiModelProperty(value = "是否可编辑")
    private String isEdit;
    
    /**
     * 是否显示
     */
    @ApiModelProperty(value = "是否显示")
     private String isShow;
     
    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;
}