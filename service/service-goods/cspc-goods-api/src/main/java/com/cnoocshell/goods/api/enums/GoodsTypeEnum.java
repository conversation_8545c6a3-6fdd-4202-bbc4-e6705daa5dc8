package com.cnoocshell.goods.api.enums;

/**
 * 
 * @Author: <EMAIL>
 * @Description  商品类型 1平台商品2卖家商品
 * @date   2018年8月16日 下午3:17:13
 */
public enum GoodsTypeEnum {
	
	BASE(1, "平台商品"),
	SELLER(2, "卖家商品"),
	PURCHASE(3, "采购商品");
	
    /**
     * 编码
     */
    private int code;
    /**
     * 描述
     */
    private String description;

    GoodsTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

	public int getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}
	
	public static GoodsTypeEnum getByCode(int code) {
		for(GoodsTypeEnum _enum : GoodsTypeEnum.values()){
			if(_enum.code == code){
				return _enum;
			}
		}
		return null;
	}
}
