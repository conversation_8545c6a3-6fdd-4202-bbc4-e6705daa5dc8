package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("资源订单准备DTO")
public class ResourceOrderPrepareDTO {
    /**
     * 缓存KEY
     */
    @ApiModelProperty(value = "缓存KEY")
    private String key;
    /**
     * 是否可以购买商品
     */
    @ApiModelProperty(value = "是否可以购买商品")
    private Boolean isCanBuy;
    /**
     * 配送方式
     * BUYER_TAKE("030060100", "买家自提"),
     * SELLER_DELIVERY("030060200", "卖家配送"),
     * PLATFORM_DELIVERY("030060300", "平台配送")
     */
    @ApiModelProperty(value = "配送方式")
    private List<String> deliveryWays;
    /**
     * 买家ID
     */
    @ApiModelProperty(value = "买家ID")
    private String buyerId;
    /**
     * 商品自提点
     */
    @ApiModelProperty(value = "商品自提点")
    private List<StoreDTO> storeDTOS;

    @ApiModelProperty("是否买家采购人员 1:是")
    private Integer buyerPurchase;
}
