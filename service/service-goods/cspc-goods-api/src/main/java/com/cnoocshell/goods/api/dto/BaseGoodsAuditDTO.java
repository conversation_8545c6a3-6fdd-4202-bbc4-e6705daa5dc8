package com.cnoocshell.goods.api.dto;

import com.cnoocshell.goods.api.enums.AuditAction;

import java.util.List;

public class BaseGoodsAuditDTO {

    private AuditAction action;
    private String operator;
    private String goodsId;

    private String goodsName;

    private List<CategoryAttributeDTO> categoryAttributeDTOS;


    public AuditAction getAction() {
        return action;
    }

    public void setAction(AuditAction action) {
        this.action = action;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public List<CategoryAttributeDTO> getCategoryAttributeDTOS() {
        return categoryAttributeDTOS;
    }

    public void setCategoryAttributeDTOS(List<CategoryAttributeDTO> categoryAttributeDTOS) {
        this.categoryAttributeDTOS = categoryAttributeDTOS;
    }

    @Override
    public String toString() {
        return "BaseGoodsAuditDTO{" +
                "action=" + action +
                ", operator='" + operator + '\'' +
                ", goodsId='" + goodsId + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", categoryAttributeDTOS=" + categoryAttributeDTOS +
                '}';
    }
}
