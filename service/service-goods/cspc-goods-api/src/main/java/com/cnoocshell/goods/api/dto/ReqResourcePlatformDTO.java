package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("平台资源请求")
public class ReqResourcePlatformDTO {
    /**
     * 商品分类
     * (1, ""),
     * (2, "");
     */
    @ApiModelProperty(value = "商品分类")
    private String goodsType;
    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品类型")
    private String categoryCode;
    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDescribe;
    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private String saleArea;
    /**
     * 一级销售区域CODE
     */
    @ApiModelProperty(value = "一级销售区域CODE")
    private String saleAreaCode;
    /**
     * 二级销售区域CODE
     */
    @ApiModelProperty(value = "二级销售区域CODE")
    private String saleAreaCode2;
    /**
     * 三级销售区域CODE
     */
    @ApiModelProperty(value = "三级销售区域CODE")
    private String saleAreaCode3;
    /**
     * 四级销售区域CODE
     */
    @ApiModelProperty(value = "四级销售区域CODE")
    private String saleAreaCode4;
    /**
     * 五级销售区域CODE
     */
    @ApiModelProperty(value = "五级销售区域CODE")
    private String saleAreaCode5;
    /**
     * 资源编号
     */
    @ApiModelProperty(value = "资源编号")
    private String resourceCode;
    /**
     * 是否可议价 : true 可议价 | false不可议价
     */
    @ApiModelProperty(value = "是否可议价")
    private Boolean ifProtocolPrice;
    /**
     * 资源状态:
     * ("100", "已挂牌"),
     * ("200", "已撤牌"),
     * ("300", "撤牌处理中"),
     * ("400", "未上架"),
     * ("500", "待审批");
     */
    @ApiModelProperty(value = "资源状态")
    private String status;
    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;
    /**
     * 卖家名称
     */
    @ApiModelProperty(value = "卖家名称")
    private String sellerName;
    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @NotNull(message = "每页显示数量不能为空")
    private Integer pageSize;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;
}
