package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("商品其他信息DTO")
public class GoodsOtherDTO {
    /**
     * 是否定时上架 true立即上架 false定时上架
     */
    @ApiModelProperty(value = "是否定时上架")
    private Boolean ifup;
    /**
     * 定时上架时间
     */
    @ApiModelProperty(value = "定时上架时间")
    private Date fixUptime;
    /**
     * 是否定时下架 true立即下架 false定时下架
     */
    @ApiModelProperty(value = "是否定时下架")
    private Boolean ifdown;
    /**
     * 定时下架时间
     */
    @ApiModelProperty(value = "定时下架时间")
    private Date fixDowntime;
    /**
     * 交易开始时间
     */
    @ApiModelProperty(value = "交易开始时间")
    private Date tradeStarttime;
    /**
     * 交易结束时间
     */
    @ApiModelProperty(value = "交易结束时间")
    private Date tradeEndtime;
    /**
     * 支付有效期类型
     * (100, "不限"),
     * (200, "订单确认后");
     */
    @ApiModelProperty(value = "支付有效期类型")
    private String paydateType;
    /**
     * 支付有效期--小时
     */
    @ApiModelProperty(value = "支付有效期--小时")
    private Integer paydateLimit;
    /**
     * 提货有效期类型
     * (100, "不限"),
     * (200, "订单支付后");
     */
    @ApiModelProperty(value = "提货有效期类型")
    private String takedateType;
    /**
     * 提货有效期--天
     */
    @ApiModelProperty(value = "提货有效期--天")
    private Integer takedateLimit;
    /**
     * 提货有效期点--小时（不选择传 -1）
     */
    @ApiModelProperty(value = "提货有效期点--小时")
    private Integer takedateHour;
}
