package com.cnoocshell.goods.api.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.GoodsShippingFee.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name ="cspc-service-goods")
public interface IGoodsShippingFeeService {

    @ApiOperation("商品运费列表")
    @PostMapping(value = "/goodsShippingFee/findAllByCondition", consumes = "application/json")
    PageInfo<GoodsShippingFeeInfoDTO> findAllByCondition(@RequestBody GoodsShippingFeePageReqDTO reqDTO);

    @ApiOperation("商品运费列表-买家")
    @PostMapping("/goodsShippingFee/findAllByConditionOfBuyer")
    PageInfo<GoodsShippingFeeInfoDTO> findAllByConditionOfBuyer(@RequestBody GoodsShippingFeePagOfBuyerReqDTO reqDTO);

    @ApiOperation("删除商品运费信息")
    @GetMapping(value = "/goodsShippingFee/deleteGoodsShippingFee", consumes = "application/json")
    Boolean deleteGoodsShippingFee(@RequestParam String id, @RequestParam String accountId, @RequestParam String userName);

    @ApiOperation("商品运费信息详情")
    @GetMapping(value = "/goodsShippingFee/findDetailById", consumes = "application/json")
    GoodsShippingFeeInfoDTO findDetailById(@RequestParam String id);

    @ApiOperation("更新商品运费")
    @GetMapping(value = "/goodsShippingFee/updateGoodsShippingFee", consumes = "application/json")
    Boolean updateGoodsShippingFee(@RequestBody GoodsShippingFeeCreateDTO reqDTO);

    @ApiOperation("新增商品运费")
    @GetMapping(value = "/goodsShippingFee/insertGoodsShippingFee", consumes = "application/json")
    Boolean insertGoodsShippingFee(@RequestBody GoodsShippingFeeCreateDTO reqDTO);

    @ApiOperation("导入商品运费")
    @PostMapping(value = "/goodsShippingFee/importExcel", consumes = "application/json")
    ItemResult<String> importExcel(GoodsShippingFeeImportExcelInfoDTO reqDTO);
}
