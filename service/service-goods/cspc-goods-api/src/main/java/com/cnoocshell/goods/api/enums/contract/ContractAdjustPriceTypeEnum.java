package com.cnoocshell.goods.api.enums.contract;

import lombok.Getter;

@Getter
public enum ContractAdjustPriceTypeEnum {

    ABSOLUTE_PRICE("1", "固定价格"),
    RELATIVE_PRICE("2", "相对价格");

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String message;

    ContractAdjustPriceTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
