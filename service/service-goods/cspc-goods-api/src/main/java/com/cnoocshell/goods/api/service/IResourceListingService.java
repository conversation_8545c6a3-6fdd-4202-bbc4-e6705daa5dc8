package com.cnoocshell.goods.api.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.BatchUpdateResourceDTO;
import com.cnoocshell.goods.api.dto.CreateResourceDTO;
import com.cnoocshell.goods.api.dto.ManualOnSaleDTO;
import com.cnoocshell.goods.api.dto.UpdateResourceDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Created锛�Sat Sep 08 17:30:59 CST 2018
 * @Author: <EMAIL>
 * @Version:2
 * @Description::null
 */

@FeignClient(name ="cspc-service-goods")
public interface IResourceListingService {


   @PostMapping(value = "/resourceListing/createResource", consumes = "application/json")
   public void createResource(@RequestBody CreateResourceDTO arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resourceListing/batchUpdateResource", consumes = "application/json")
   public void batchUpdateResource(@RequestBody BatchUpdateResourceDTO batchUpdateResourceDTO);

   @PostMapping(value = "/resourceListing/updateResource", consumes = "application/json")
   public void updateResource(@RequestBody UpdateResourceDTO arg0, @RequestParam("arg1") String arg1);


   @PostMapping(value = "/resourceListing/deleteResource", consumes = "application/json")
   public void deleteResource(@RequestBody List<String> arg0, @RequestParam("arg1") String arg1);


   @PostMapping(value = "/resourceListing/manualOffSale", consumes = "application/json")
   ItemResult<String> manualOffSale(@RequestBody List<String> arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resourceListing/manualOnSale", consumes = "application/json")
   public void manualOnSale(@RequestBody ManualOnSaleDTO manualOnSaleDTO);

   @PostMapping(value = "/resourceListing/mandatoryOffSale", consumes = "application/json")
   public void mandatoryOffSale(@RequestBody List<String> arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resourceListing/mandatoryOnSale", consumes = "application/json")
   public void mandatoryOnSale(@RequestBody ManualOnSaleDTO manualOnSaleDTO);

   @PostMapping(value = "/resourceListing/refreshResourceElasticsearch", consumes = "application/json")
   public void refreshResourceElasticsearch();

   @PostMapping(value = "/resourceListing/aggregationResourceListing", consumes = "application/json")
   public void aggregationResourceListing();

   @PostMapping(value = "/resourceListing/offSaleSaleRegionResource", consumes = "application/json")
   public void offSaleSaleRegionResource(@RequestParam("saleRegionId") String saleRegionId);

}
