package com.cnoocshell.goods.api.dto.goods;

import com.cnoocshell.goods.api.dto.GoodsDTO;
import com.cnoocshell.goods.api.dto.GoodsSimpleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("商品分类树和商品分类list")
public class GoodsListDTO {

    @ApiModelProperty(value = "商品分类下商品goodsCode列表")
    public List<String> goodsCodeList;

    @ApiModelProperty(value = "商品列表")
    public List<GoodsSimpleDTO> goodsList;
}
