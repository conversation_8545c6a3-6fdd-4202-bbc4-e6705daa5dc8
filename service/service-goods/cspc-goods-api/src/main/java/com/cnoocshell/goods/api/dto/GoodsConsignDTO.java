package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("商品配送DTO")
public class GoodsConsignDTO {
    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    private List<String> payWay;

    /**
     * 是否允许自提
     */
    @ApiModelProperty(value = "是否允许自提")
    private Boolean ifTakeSelf;

    /**
     * 是否平台配送
     */
    @ApiModelProperty(value = "是否平台配送")
    private Boolean ifPlatformDelivery;

    /**
     * 是否卖家配送
     */
    @ApiModelProperty(value = "是否卖家配送")
    private Boolean ifSellerDelivery;

    /**
     * 自提优惠单价
     */
    @ApiModelProperty(value = "自提优惠单价")
    private BigDecimal takeSelfDiscounts;

    /**
     * 免物流费重量
     */
    @ApiModelProperty(value = "免物流费重量")
    private BigDecimal logisticsWeight;

    /**
     * 免物流费计算类型
     */
    @ApiModelProperty(value = "免物流费计算类型")
    private String logisticsType;

    /**
     * 物流费计算单位
     */
    @ApiModelProperty(value = "物流费计算单位")
    private String logisticsUnit;

    /**
     * 物流费单价
     */
    @ApiModelProperty(value = "物流费单价")
    private BigDecimal logisticsPrice;

    /**
     * 搬运费类型
     */
    @ApiModelProperty(value = "搬运费类型")
    private String cartageRuleId;

    /**
     * 台班费规则ID
     */
    @ApiModelProperty(value = "台班费规则ID")
    private String machineRuleId;

    /**
     * 空载费规则ID
     */
    @ApiModelProperty(value = "空载费规则ID")
    private String emptyLoadRuleId;
}
