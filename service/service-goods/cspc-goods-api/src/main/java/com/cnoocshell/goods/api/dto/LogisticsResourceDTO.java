package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("资源物流信息DTO")
public class LogisticsResourceDTO {
    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id")
    private String resourceId;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String goodsId;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private Integer goodsType;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String imgs;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDescribe;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    /**
     * 资源code
     */
    @ApiModelProperty(value = "资源code")
    private String resourceCode;
    /**
     * 币种
     * CNY("CNY", "人民币"),元
     * USD("USD", "美元"),
     * HKD("HKD", "港币");
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 计价方式
     * (1, "到位价"),
     * (2, "出厂价");
     */
    @ApiModelProperty(value = "计价方式")
    private Integer priceWay;

    /**
     * 价格
     */
    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    /**
     * 计价单位
     */
    @ApiModelProperty(value = "计价单位")
    private String unit;

    /**
     * 销售单位
     */
    @ApiModelProperty(value = "销售单位")
    private String saleUnit;

    /**
     * 运输品类Id
     */
    @ApiModelProperty(value = "运输品类Id")
    private String logistics;

    /**
     * 是否特殊商品
     */
    @ApiModelProperty(value = "是否特殊商品")
    private Integer specialFlag;
}
