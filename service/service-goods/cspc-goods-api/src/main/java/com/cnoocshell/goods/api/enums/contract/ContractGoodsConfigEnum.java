package com.cnoocshell.goods.api.enums.contract;

import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @created 20:56 21/11/2019
 */
@Getter
public enum ContractGoodsConfigEnum {

    ORDER_MIN_NUM("cg_orderminNum", "最小购买量"),

    ORDER_MAX_NUM("cg_ordermaxNum", "最大购买量"),

    ORDER_MIN_CHANGE_NUM("cg_orderminchangeNum", "最小变动量"),

    FLOW_MONITOR("cg_flowMonitor", "流向管控"),
    EC_FLOW_MONITOR("ec_flowMonitor", "电商流向管控"),

    ;
    private String code;

    private String msg;

    ContractGoodsConfigEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
