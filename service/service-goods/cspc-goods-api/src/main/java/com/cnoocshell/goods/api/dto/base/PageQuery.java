package com.cnoocshell.goods.api.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2018-08-01 14:40
 * @Description:
 */
@Data
@NoArgsConstructor
@ApiModel("分页查询对象")
public class PageQuery<T> {
    /**
     * 当前页数
     */
    @ApiModelProperty(value="当前页数", required = false, example="1")
    private Integer pageNum;

    /**
     * 分页大小
     */
    @ApiModelProperty(value="分页大小", required = false, example="20")
    private Integer pageSize;

    /**
     * 查询参数对象
     */
    @ApiModelProperty(value="查询参数对象", required = false)
    private T queryDTO;
}
