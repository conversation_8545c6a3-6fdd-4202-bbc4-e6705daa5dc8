package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GoodsSimpleDTO {
    @ApiModelProperty(value = "主键")
    private String goodsId;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty("SAP 物料编码")
    private String sapMaterialCode;
    @ApiModelProperty("SAP 工厂编码")
    private String sapFactoryCode;
    @ApiModelProperty("销售组")
    private String salesGroup;


    /**
     *{@link com.cnoocshell.goods.api.enums.GoodsStatusEnum}
     */
    @ApiModelProperty(value = "商品状态")
    private Integer goodsStatus;


    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品业务类型")
    private Integer goodsType;

    @ApiModelProperty("销售单位")
    private String unit;

    @ApiModelProperty("计量单位")
    private String measureUnit;

    @ApiModelProperty("包装方式")
    private String pack;

    @ApiModelProperty("配送方式")
    private String deliveryMode;

    @ApiModelProperty("配送收费标准（附件）")
    private String deliverCostStandard;

    @ApiModelProperty("自提指南（附件）")
    private String selfPickupGuide;

    @ApiModelProperty("自提承运商（附件）")
    private String selfPickupCarrier;
}
