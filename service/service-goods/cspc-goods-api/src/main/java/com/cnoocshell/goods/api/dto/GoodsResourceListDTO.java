package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel("商品资源列表DTO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoodsResourceListDTO {

    /**
     * 商品资源聚合ID
     */
    @ApiModelProperty(value = "商品资源聚合ID")
    private String goodsResourceId;

    /**
     * 品类ID
     */
    @ApiModelProperty(value = "品类ID")
    private String spuId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 商品类型 1 2
     */
    @ApiModelProperty(value = "商品类型")
    private Integer categoryType;

    /**
     * 是否有货 1有 0无
     */
    @ApiModelProperty(value = "是否有货")
    private Integer isHaveGoods;

    /**
     * 起售数量
     */
    @ApiModelProperty(value = "起售数量")
    private String salesVolume;

    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    private String resourceId;

    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private String goodsType;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDescribe;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    /**
     * 资源code
     */
    @ApiModelProperty(value = "资源code")
    private String resourceCode;

    /**
     * 台班费规则ID
     */
    @ApiModelProperty(value = "台班费规则ID")
    private String machineRuleId;

    /**
     * 空载费规则Id
     */
    @ApiModelProperty(value = "空载费规则Id")
    private String emptyLoadRuleId;


    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 币种符号
     */
    @ApiModelProperty(value = "币种符号")
    private String currencySymbol;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    /**
     * 价格描述
     */
    @ApiModelProperty(value = "价格描述")
    private String priceDescribe;

    /**
     * 计价方式，1 出厂价 2到位价
     */
    @ApiModelProperty(value = "计价方式")
    private String priceWay;

    /**
     * 价格
     */
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    /**
     * 最低价格
     */
    @ApiModelProperty(value = "最低价格")
    private BigDecimal minPrice;
    /**
     * 最高价格
     */
    @ApiModelProperty(value = "最高价格")
    private BigDecimal maxPrice;

    /**
     * 出厂价区间
     */
    @ApiModelProperty(value = "出厂价区间")
    private BigDecimal factoryPrice;
    /**
     * 最小出厂价
     */
    @ApiModelProperty(value = "最小出厂价")
    private BigDecimal minFactoryPrice;
    /**
     * 最大出厂价
     */
    @ApiModelProperty(value = "最大出厂价")
    private BigDecimal maxFactoryPrice;

    /**
     * 到位价区间
     */
    @ApiModelProperty(value = "到位价区间")
    private BigDecimal arrivePrice;
    /**
     * 最小到位价
     */
    @ApiModelProperty(value = "最小到位价")
    private BigDecimal minArrivePrice;
    /**
     * 最大到位价
     */
    @ApiModelProperty(value = "最大到位价")
    private BigDecimal maxArrivePrice;

    /**
     * 是否可议价
     */
    @ApiModelProperty(value = "是否可议价")
    private Boolean ifProtocolPrice;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String priceUnit;

    /**
     * 销售单位
     */
    @ApiModelProperty(value = "销售单位")
    private String saleUnit;

    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "仓库ID")
    private String storeId;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String storeName;

    /**
     * 仓库类型, 100卖家基地库 200平台中心仓
     */
    @ApiModelProperty(value = "仓库类型")
    private String storeType;

    /**
     * 仓库详细地址
     */
    @ApiModelProperty(value = "仓库详细地址")
    private String storeAddress;

    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;

    /**
     * 卖家姓名
     */
    @ApiModelProperty(value = "卖家姓名")
    private String sellerName;

    /**
     * 卖家简称
     */
    @ApiModelProperty(value = "卖家简称")
    private String sellerNickName;

    /**
     * 销售员ID
     */
    @ApiModelProperty(value = "销售员ID")
    private String salesId;

    /**
     * 销售员姓名
     */
    @ApiModelProperty(value = "销售员姓名")
    private String salesName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 组织机构ID
     */
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 总销售数量
     */
    @ApiModelProperty(value = "总销售数量")
    private BigDecimal saleNum;

    /**
     * 可售数量
     */
    @ApiModelProperty(value = "可售数量")
    private BigDecimal cansaleNum;

    /**
     * 锁定数量
     */
    @ApiModelProperty(value = "锁定数量")
    private BigDecimal lockNum;

    /**
     * 起售数量
     */
    @ApiModelProperty(value = "起售数量")
    private BigDecimal volumeNum;

    /**
     * 区域定价层级
     */
    @ApiModelProperty(value = "区域定价层级")
    private Integer areaLevel;

    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private String saleArea;

    /**
     * 交易状态 100, 可交易 200, 不可交易
     */
    @ApiModelProperty(value = "交易状态")
    private String tradeStatus;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String[] imgs;

    /**
     * 搜索得分
     */
    @ApiModelProperty(value = "搜索得分")
    private BigDecimal score;

    /**
     * 单笔最大购买量
     */
    @ApiModelProperty(value = "单笔最大购买量")
    private BigDecimal ordermaxNum;

    /**
     * 单笔最小购买量
     */
    @ApiModelProperty(value = "单笔最小购买量")
    private BigDecimal orderminNum;

    /**
     * 单笔最小变动量
     */
    @ApiModelProperty(value = "单笔最小变动量")
    private BigDecimal orderminchangeNum;

    /**
     * 单日最大购买量
     */
    @ApiModelProperty(value = "单日最大购买量")
    private BigDecimal daymaxNum;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specs;

    @ApiModelProperty(value = "是否被收藏(true 是)")
    private Boolean favoriteFlag;
}
