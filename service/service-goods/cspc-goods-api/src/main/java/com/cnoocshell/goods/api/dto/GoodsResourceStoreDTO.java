package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
@ApiModel("商品资源仓库DTO")
public class GoodsResourceStoreDTO implements Comparable<GoodsResourceStoreDTO>{
    /**
     * 资源Id
     */
    @ApiModelProperty(value = "资源Id")
    private String resourceId;
    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private String storeId;
    /**
     * 仓库类型
     */
    @ApiModelProperty(value = "仓库类型")
    private String storeType;
    /**
     * 出货地、仓库名称
     */
    @ApiModelProperty(value = "出货地、仓库名称")
    private String storeName;
    /**
     * 仓库详细地址
     */
    @ApiModelProperty(value = "仓库详细地址")
    private String storeAddress;
    /**
     * 真实销售区域
     */
    @ApiModelProperty(value = "真实销售区域编码")
    private String saleAreaRealCode;
    /**
     * 销售区域编码
     */
    @ApiModelProperty(value = "销售区域编码")
    private String saleAreaCodes;
    /**
     * 一级销售区域编码
     */
    @ApiModelProperty(value = "一级销售区域编码")
    private String saleAreaCode;

    /**
     * 二级销售区域编码
     */
    @ApiModelProperty(value = "二级销售区域编码")
    private String saleAreaCode2;

    /**
     * 三级销售区域编码
     */
    @ApiModelProperty(value = "三级销售区域编码")
    private String saleAreaCode3;

    /**
     * 四级销售区域编码
     */
    @ApiModelProperty(value = "四级销售区域编码")
    private String saleAreaCode4;

    /**
     * 五级销售区域编码
     */
    @ApiModelProperty(value = "五级销售区域编码")
    private String saleAreaCode5;

    /**
     * 一级销售区域名称
     */
    @ApiModelProperty(value = "一级销售区域名称")
    private String saleAreaName;

    /**
     * 二级销售区域名称
     */
    @ApiModelProperty(value = "二级销售区域名称")
    private String saleAreaName2;

    /**
     * 三级销售区域名称
     */
    @ApiModelProperty(value = "三级销售区域名称")
    private String saleAreaName3;

    /**
     * 四级销售区域名称
     */
    @ApiModelProperty(value = "四级销售区域名称")
    private String saleAreaName4;

    /**
     * 五级销售区域名称
     */
    @ApiModelProperty(value = "五级销售区域名称")
    private String saleAreaName5;
    /**
     * 行政区域编码列表--中心仓包含配送费信息
     */
    @ApiModelProperty(value = "行政区域编码列表")
    private List<AdministrativeRegionDTO> administrativeRegions;
    /**
     * 销售数量
     */
    @ApiModelProperty(value = "销售数量")
    private BigDecimal saleNum;
    /**
     * 上架数量
     */
    @ApiModelProperty(value = "上架数量")
    private BigDecimal cansaleNum;
    /**
     * 计价方式
     * ("1", "固定运价"),
     * ("2", "运费规则定价");
     */
    @ApiModelProperty(value = "计价方式")
    private String priceWay;
    /**
     * 价格
     */
    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    /**
     * 出厂价
     */
    @ApiModelProperty(value = "出厂价")
    private BigDecimal factoryPrice;

    /**
     * 到位价
     */
    @ApiModelProperty(value = "到位价")
    private BigDecimal arrivePrice;

    /**
     * 计价单位
     */
    @ApiModelProperty(value = "计价单位")
    private String priceUnit;
    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;
    /**
     * 操作
     * ("100", "已启用"),
     * ("200", "已禁用"),
     * ("300", "已锁住");
     */
    @ApiModelProperty(value = "操作")
    private String operateStatus;

    @Override
    public int compareTo(GoodsResourceStoreDTO o) {
        return this.getSaleAreaCodes().compareTo(o.getSaleAreaCodes());
    }
}
