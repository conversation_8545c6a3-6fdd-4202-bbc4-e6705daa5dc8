package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel("商品分类DTO")
public class GoodsCategoryDTO implements Serializable {
	
	/**
     * 分类属性
     */
	@ApiModelProperty(value = "分类属性")
	private List<GoodsCategoryAttrDTO> categoryAttrs;
	
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String categoryId;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private String parentId;

    /**
     * 父级名称
     */
    @ApiModelProperty(value = "父级名称")
    private String parentName;

    /**
     * 类别编码
     */
    @ApiModelProperty(value = "类别编码")
    private String categoryCode;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称")
    private String categoryName;

    /**
     * 商品类型 1 2
     */
    @ApiModelProperty(value = "商品类型")
    private Integer categoryType;
    
    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    private String[] imgs;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Integer delFlg;
    
    private static final long serialVersionUID = 1L;

    /**
     * 子级分类
     */
    @ApiModelProperty(value = "子级分类")
    private List<GoodsCategoryDTO> childs;
    
    /**
     * 获取主键
     *
     * @return category_id - 主键
     */
    public String getCategoryId() {
        return categoryId;
    }

    /**
     * 设置主键
     *
     * @param categoryId 主键
     */
    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId == null ? null : categoryId.trim();
    }

    /**
     * 获取父级id
     *
     * @return parent_id - 父级id
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * 设置父级id
     *
     * @param parentId 父级id
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    /**
     * 获取类别编码
     *
     * @return category_code - 类别编码
     */
    public String getCategoryCode() {
        return categoryCode;
    }

    /**
     * 设置类别编码
     *
     * @param categoryCode 类别编码
     */
    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode == null ? null : categoryCode.trim();
    }

    /**
     * 获取类别名称
     *
     * @return category_name - 类别名称
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 设置类别名称
     *
     * @param categoryName 类别名称
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    /**
     * 获取图片
     *
     * @return imgs - 图片
     */
    public String[] getImgs() {
        return imgs;
    }

    /**
     * 设置图片
     *
     * @param imgs 图片
     */
    public void setImgs(String[] imgs) {
        this.imgs = imgs;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建者
     *
     * @return create_user - 创建者
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建者
     *
     * @param createUser 创建者
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取修改者
     *
     * @return update_user - 修改者
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改者
     *
     * @param updateUser 修改者
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取删除标志,1-删除，0-正常
     *
     * @return del_flg - 删除标志,1-删除，0-正常
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标志,1-删除，0-正常
     *
     * @param delFlg 删除标志,1-删除，0-正常
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

	public List<GoodsCategoryDTO> getChilds() {
		return childs;
	}

	public void setChilds(List<GoodsCategoryDTO> childs) {
		this.childs = childs;
	}

	public Integer getCategoryType() {
		return categoryType;
	}

	public void setCategoryType(Integer categoryType) {
		this.categoryType = categoryType;
	}

	public List<GoodsCategoryAttrDTO> getCategoryAttrs() {
		return categoryAttrs;
	}

	public void setCategoryAttrs(List<GoodsCategoryAttrDTO> categoryAttrs) {
		this.categoryAttrs = categoryAttrs;
	}
	
}