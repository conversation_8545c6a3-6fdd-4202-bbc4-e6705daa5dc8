package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("合同资源")
public class ContractResourceDTO {
    /**
     * 卖家id
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;
    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源ID")
    private String resourceId;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private String goodsType;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDescribe;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;
    /**
     * 资源code
     */
    @ApiModelProperty(value = "资源code")
    private String resourceCode;
    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;
    /**
     * 价格
     */
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    /**
     * 价格描述
     */
    @ApiModelProperty(value = "价格描述")
    private String priceDescribe;
    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式")
    private String priceWay;
    /**
     * 价格
     */
    @ApiModelProperty(value = "价格")
    private BigDecimal discountPrice;
    /**
     * 计价单位
     */
    @ApiModelProperty(value = "计价单位")
    private String unit;
    /**
     * 计价单位编号
     */
    @ApiModelProperty(value = "计价单位编号")
    private String unitId;
    /**
     * 是否有货 1有 0无
     */
    @ApiModelProperty(value = "是否有货")
    private Integer isHaveGoods;
    /**
     * 可售数量
     */
    @ApiModelProperty(value = "可售数量")
    private BigDecimal cansaleNum;

}
