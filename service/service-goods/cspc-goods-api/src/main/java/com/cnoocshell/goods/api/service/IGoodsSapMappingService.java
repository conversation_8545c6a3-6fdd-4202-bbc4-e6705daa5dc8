package com.cnoocshell.goods.api.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

@FeignClient(name ="cspc-service-goods")
public interface IGoodsSapMappingService {

   @PostMapping("/goodsMappingSap/selectSapGoodsMappingList")
   ItemResult<PageInfo<GoodsSapMappingDTO>> selectSapGoodsMappingList(@RequestBody GoodsSapMappingDTO goodsSapMappingDTO);


   @PostMapping("/goodsMappingSap/createSapGoodsMapping")
   ItemResult<String> createSapGoodsMapping(@RequestBody GoodsSapMappingDTO goodsSapMappingDTO);


   @PostMapping("/goodsMappingSap/updateSapGoodsMapping")
   ItemResult<String> updateSapGoodsMapping(@RequestBody GoodsSapMappingDTO goodsSapMappingDTO);

   @PostMapping("/goodsMappingSap/deleteSapGoodsMapping")
   ItemResult<String> deleteSapGoodsMapping(@RequestBody GoodsSapMappingDTO goodsSapMappingDTO);


}
