package com.cnoocshell.goods.api.enums;

/**
 * 
 * @Author: <EMAIL>
 * @Description  商品类型
 * @date   2018年8月16日 下午3:17:13
 */
public enum CategoryTypeEnum {
	
	CEMENT(1, ""),
	CONCRETE(2, ""),
	REBAR(3, "钢筋"),
	<PERSON>IL<PERSON>(4, "瓷砖");
	
    /**
     * 编码
     */
    private int code;
    /**
     * 描述
     */
    private String description;

    CategoryTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

	public int getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}
	
	public static CategoryTypeEnum getByCode(int code) {
		for(CategoryTypeEnum _enum : CategoryTypeEnum.values()){
			if(_enum.code == code){
				return _enum;
			}
		}
		return null;
	}
}
