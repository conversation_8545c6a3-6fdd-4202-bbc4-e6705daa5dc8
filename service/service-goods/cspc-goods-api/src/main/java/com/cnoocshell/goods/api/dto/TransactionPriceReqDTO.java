package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("成交价请求DTO")
public class TransactionPriceReqDTO {
    /**
     * 开始时间 必传（时分秒精确到00:00:00）
     */
    @ApiModelProperty(value = "开始时间", required = true)
    private Date startDate;
    /**
     * 结束时间 必传（时分秒精确到59:59:59）
     */
    @ApiModelProperty(value = "结束时间")
    private Date endDate;
    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;
    /**
     * 分类编码
     *  002001001
     *  002001003
     * 熟料 002001002
     */
    @ApiModelProperty(value = "分类编码")
    private String categoryCode;
}
