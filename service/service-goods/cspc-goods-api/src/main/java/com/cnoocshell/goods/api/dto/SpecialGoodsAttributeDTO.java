package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author: lehu
 * Description:
 * Date: Create in 上午9:46 20/3/9
 */
@Data
@ApiModel("特殊商品属性")
public class SpecialGoodsAttributeDTO {
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    /**
     * 标识 0不是  1是
     */
    @ApiModelProperty(value = "标识")
    private Integer concreteFlag;

    /**
     * 加价项标识 0没有  1有
     */
    @ApiModelProperty(value = "加价项标识")
    private Integer addItemFlag;

    /**
     * 支持搬运标识 0 不支持 1 支持
     */
    @ApiModelProperty("支持搬运标识")
    private Integer supportCarryFlag;
}
