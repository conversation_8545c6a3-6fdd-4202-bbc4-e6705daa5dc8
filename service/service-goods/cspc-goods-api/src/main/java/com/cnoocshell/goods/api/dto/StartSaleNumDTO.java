package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("起售量")
public class StartSaleNumDTO {

    /**
     * 起售量管理主键
     */
    @ApiModelProperty(value = "起售量管理主键")
    private String startSaleId;

    /**
     * 起售数量
     */
    @ApiModelProperty(value = "起售数量")
    private BigDecimal saleNum;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 关联卖家id
     */
    @ApiModelProperty(value = "关联卖家id")
    private String sellerId;

    /**
     * 仓库类型（1：中心仓  2：门店  3：中转库 4：can）
     */
    @ApiModelProperty(value = "仓库类型")
    private String warehouseType;

    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private String warehouseId;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 商品类型id
     */
    @ApiModelProperty(value = "商品类型id")
    private String goodsTypeId;

    /**
     * 商品类型名称
     */
    @ApiModelProperty(value = "商品类型名称")
    private String goodsTypeName;

    /**
     * 商品类型编码
     */
    @ApiModelProperty(value = "商品类型编码")
    private String goodsTypeCode;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String goodsId;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Integer delFlg;

}
