package com.cnoocshell.goods.api.enums.contract;

import lombok.Getter;

@Getter
public enum TemplateTypeEnum {
    CONTRACT(1,"合同模板"),
    ADJUSTPRICE(2,"调价函模板");
    /** 枚举值 */
    private final Integer code;

    /** 枚举描述 */
    private final String message;
    private TemplateTypeEnum(Integer code,String message){
        this.code = code;
        this.message = message;
    }

    public static TemplateTypeEnum getByCode(Integer code) {
        for (TemplateTypeEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }
}
