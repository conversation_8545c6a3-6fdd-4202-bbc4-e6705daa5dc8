package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2019-12-13 15:48
 * @Description: GoodsQueryCondDTO
 */
@Data
@ApiModel("商品查询条件实例")
public class GoodsQueryCondDTO {

    @ApiModelProperty(value = "卖家ID")
    private String sellerId;

    @ApiModelProperty(value = "商品名关键字")
    private String goodsNameLike;

    @ApiModelProperty(value = "商品业务类型 1平台商品 2卖家商品 3采购商品")
    private Integer goodsType;

    @ApiModelProperty(value = "商品分类 1 2")
    private Integer categoryType;

    @ApiModelProperty(value = "商品分类编码")
    private String categoryCode;

    @ApiModelProperty(value = "商品分类编码不以此开头")
    private List<String> categoryCodeNotLike;

    @ApiModelProperty(value = "商品状态：1草稿 2启用（审核通过）3禁用 4审核中 5审核失败")
    private Integer goodsStatus;

    @ApiModelProperty(value = "物流运输品类id")
    private String logistics;

}
