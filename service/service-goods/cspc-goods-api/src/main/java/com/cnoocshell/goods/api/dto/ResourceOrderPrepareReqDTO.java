package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("挂牌下单准备请求DTO")
public class ResourceOrderPrepareReqDTO {
    /**
     * 门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String shopCode;
    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "仓库ID")
    private String storeId;
    /**
     * 配送方式
     * BUYER_TAKE("030060100", "买家自提"),
     * SELLER_DELIVERY("030060200", "卖家配送"),
     * PLATFORM_DELIVERY("030060300", "平台配送")
     */
    @ApiModelProperty(value = "配送方式")
    private String deliveryWay;
    /**
     * 买家ID
     */
    @ApiModelProperty(value = "买家ID")
    private String buyerId;
    /**
     * 所在国家编码
     */
    @ApiModelProperty(value = "所在国家编码")
    private String countryCode;
    /**
     * 所在省编码
     */
    @ApiModelProperty(value = "所在省编码")
    private String provinceCode;
    /**
     * 所在城市编码
     */
    @ApiModelProperty(value = "所在城市编码")
    private String cityCode;
    /**
     * 所在地区编码
     */
    @ApiModelProperty(value = "所在地区编码")
    private String areaCode;
    /**
     * 所在街道编码
     */
    @ApiModelProperty(value = "所在街道编码")
    private String streetCode;
    /**
     * 所在国家编码
     */
    @ApiModelProperty(value = "所在国家编码")
    private String countryName;
    /**
     * 所在省编码
     */
    @ApiModelProperty(value = "所在省编码")
    private String provinceName;
    /**
     * 所在城市编码
     */
    @ApiModelProperty(value = "所在城市编码")
    private String cityName;
    /**
     * 所在地区编码
     */
    @ApiModelProperty(value = "所在地区编码")
    private String areaName;
    /**
     * 所在街道编码
     */
    @ApiModelProperty(value = "所在街道编码")
    private String streetName;
    /**
     * 所在地址ID
     */
    @ApiModelProperty(value = "所在地址ID")
    private String addressId;

    /**
     * 所在地址坐标
     */
    @ApiModelProperty(value = "所在地址坐标")
    private String addressLocation;
    /**
     * 请求平台
     */
    @ApiModelProperty("请求平台 buyer_app:买家App mini_apps:小程序")
    private String platform;
    /**
     * 选择的商品
     */
    @ApiModelProperty(value = "选择的商品")
    private List<SelectResourceDTO> selectResourceDTOS;
}
