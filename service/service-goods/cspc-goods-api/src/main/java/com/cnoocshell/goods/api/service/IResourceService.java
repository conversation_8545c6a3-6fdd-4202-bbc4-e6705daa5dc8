package com.cnoocshell.goods.api.service;

import com.cnoocshell.goods.api.dto.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Created锛�Sat Sep 08 17:30:59 CST 2018
 * @Author: <EMAIL>
 * @Version:2
 * @Description::null
 */

@FeignClient(name ="cspc-service-goods")
public interface  IResourceService {

   @PostMapping(value = "/resource/manualResourceToElasticsearch", consumes = "application/json")
   public void manualResourceToElasticsearch();

   @PostMapping(value = "/resource/offSaleResourceBatch", consumes = "application/json")
   public void offSaleResourceBatch(@RequestBody List<String> arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resource/autoEndTradeCheck", consumes = "application/json")
   public void autoEndTradeCheck();

   @GetMapping(value = "/resource/ifCanSelectAreaPrice", consumes = "application/json")
   public Boolean ifCanSelectAreaPrice(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1,
                                       @RequestParam("arg2") Integer arg2);

   @PostMapping(value = "/resource/getRelationResources", consumes = "application/json")
   public List<ResourceDTO> getRelationResources(@RequestBody ResourceDTO arg0);

   @PostMapping(value = "/resource/getResourcePriceChangeHistory", consumes = "application/json")
   public List<ResourceDTO> getResourcePriceChangeHistory(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/searchResourceDetailBuyer", consumes = "application/json")
   public ResourceDetailDTO searchResourceDetailBuyer(@RequestBody ReqResourceDetailDTO arg0);

   @GetMapping(value = "/resource/getResourceDetail", consumes = "application/json")
   public ResourceDTO getResourceDetailById(@RequestParam("arg0") String arg0);

   @GetMapping(value = "/resource/getChangePriceAndNumList", consumes = "application/json")
   public List<ChangePriceAndNumDTO> getChangePriceAndNumList(@RequestParam("arg0") List<String> arg0,
                                                              @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resource/getIfNeedSellerCheck", consumes = "application/json")
   public boolean getIfNeedSellerCheck(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/autoStartTradeCheck", consumes = "application/json")
   public void autoStartTradeCheck();

   @PostMapping(value = "/resource/getGoodsResourceStore", consumes = "application/json")
   public GoodsPriceDTO getGoodsResourceStore(@RequestBody GetGoodsResourceStoreDTO arg0);

   @PostMapping(value = "/resource/isResourceOnTrade", consumes = "application/json")
   public boolean isResourceOnTrade(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/searchGoodsResourceBuyer", consumes = "application/json")
   public PageInfo<GoodsResourceListDTO> searchGoodsResourceBuyer(@RequestBody ReqGoodsResourceDTO arg0);

   @PostMapping(value = "/resource/searchGoodsResourceEmall", consumes = "application/json")
   public PageInfo<GoodsResourceListDTO> searchGoodsResourceEmall(@RequestBody ReqGoodsResourceDTO arg0);

   @PostMapping(value = "/resource/pageResourceSeller", consumes = "application/json")
   public PageInfo<ResourceSellerDTO> pageResourceSeller(@RequestBody ReqResourceSellerDTO arg0);

   @PostMapping(value = "/resource/getResourceByBuyerLocation", consumes = "application/json")
   public ResourceDTO getResourceByBuyerLocation(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1,
                                                 @RequestParam("arg2") String arg2, @RequestParam("arg3") String arg3, @RequestParam("arg4") String arg4);

   @PostMapping(value = "/resource/pageResourcePlatform", consumes = "application/json")
   public PageInfo<ResourcePlatformDTO> pageResourcePlatform(@RequestBody ReqResourcePlatformDTO arg0);

   @PostMapping(value = "/resource/getOrderResourceDetail", consumes = "application/json")
   public ResourceDTO getOrderResourceDetail(@RequestParam("arg0") String arg0, @RequestBody int arg1);

   @GetMapping(value = "/resource/searchGoodsResourceDetailBuyer", consumes = "application/json")
   public GoodsResourceDTO searchGoodsResourceDetailBuyer(@RequestParam("arg0") String arg0);

   @GetMapping(value = "/resource/searchGoodsByResource", consumes = "application/json")
   public GoodsResourceDTO searchGoodsByResource(@RequestParam("arg0") String arg0);

   @GetMapping(value = "/resource/searchGoodsResourceListBuyer", consumes = "application/json")
   public List<GoodsResourceDTO> searchGoodsResourceListBuyer(@RequestParam("arg0") List<String> arg0);

   @PostMapping(value = "/resource/changeResourcesUnit", consumes = "application/json")
   public ResourceDTO changeResourcesUnit(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resource/repealResourceUpdate", consumes = "application/json")
   public void repealResourceUpdate(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resource/getResourceSaleNumChangeHistory", consumes = "application/json")
   public List<ResourceDTO> getResourceSaleNumChangeHistory(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/getUneffectResource", consumes = "application/json")
   public List<ResourceDTO> getUneffectResource(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/offSaleResourcePlatform", consumes = "application/json")
   public void offSaleResourcePlatform(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

   @GetMapping(value = "/resource/getResourceDetail", consumes = "application/json")
   public ResourceDTO getResourceDetail(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/offSaleResourceBatchPlatform", consumes = "application/json")
   public void offSaleResourceBatchPlatform(@RequestBody List<String> arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resource/autoEffectCheck", consumes = "application/json")
   public void autoEffectCheck();

   @PostMapping(value = "/resource/updateResource", consumes = "application/json")
   public void updateResource(@RequestBody ReqUpdateResourceDTO arg0);

   @PostMapping(value = "/resource/offSaleResource", consumes = "application/json")
   public void offSaleResource(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

   @GetMapping(value = "/resource/getPriceMode", consumes = "application/json")
   public List<PriceModeDTO> getPriceMode(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/ifCanOnSale", consumes = "application/json")
   public boolean ifCanOnSale(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/autoOnsaleCheck", consumes = "application/json")
   public void autoOnsaleCheck();

   @PostMapping(value = "/resource/autoOnsaleCheck", consumes = "application/json")
   public void autoOffsalingCheck();

   @PostMapping(value = "/resource/createResource", consumes = "application/json")
   public void createResource(@RequestBody ReqCreateResourceDTO arg0);

   @PostMapping(value = "/resource/autoOffsaleCheck", consumes = "application/json")
   public void autoOffsaleCheck();

   @PostMapping(value = "/resource/deleteResource", consumes = "application/json")
   public void deleteResource(@RequestBody List<String> arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resource/onSaleResourceBatch", consumes = "application/json")
   public void onSaleResourceBatch(@RequestBody List<String> arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resource/onSaleResourcePlatform", consumes = "application/json")
   public void onSaleResourcePlatform(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resource/onSaleResourceBatchPlatform", consumes = "application/json")
   public void onSaleResourceBatchPlatform(@RequestBody List<String> arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resource/onSaleResource", consumes = "application/json")
   public void onSaleResource(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resource/searchResourceByDescribe", consumes = "application/json")
   public List<LogisticsResourceDTO> searchResourceByDescribe(@RequestBody ReqLogisticsResourceDTO arg0);

   @PostMapping(value = "/resource/confirmContractResource", consumes = "application/json")
   public ContractResourceDTO confirmContractResource(@RequestBody ReqContractResourceDTO arg0);

   @PostMapping(value = "/resource/ifCanGoodsUnShelve", consumes = "application/json")
   public Boolean ifCanGoodsUnShelve(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/getGoodsSalesVolume", consumes = "application/json")
   public SalesVolumeDTO getGoodsSalesVolume(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/queryEffectResourceIds", consumes = "application/json")
   public List<String> queryEffectResourceIds(@RequestBody List<String> arg0);

   @PostMapping(value = "/resource/searchGoodsCollectionList", consumes = "application/json")
   public List<GoodsCollecttionDTO> searchGoodsCollectionList(@RequestBody List<ReqGoodsCollecttionDTO> arg0);

   @PostMapping(value = "/resource/searchGoodsResourceAttributes", consumes = "application/json")
   public List<AttributeDTO> searchGoodsResourceAttributes(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resource/searchResourceDetailWithAttr", consumes = "application/json")
   public ResourceDetailDTO searchResourceDetailWithAttr(@RequestBody ResourceDetailSearchDTO arg0);

   @PostMapping(value = "/resource/checkResourceLogisticRule", consumes = "application/json")
   public boolean checkResourceLogisticRule(@RequestBody List<String> arg0);

   @PostMapping(value = "/resource/querySellerShopByGoodsType", consumes = "application/json")
   public List<SellerShopDTO> querySellerShopByGoodsType(@RequestBody ReqSellerShopDTO arg0);

   @PostMapping(value = "/resource/promptOnsaleResource", consumes = "application/json")
   public List<PromptOnsaleResourceDTO> promptOnsaleResource(@RequestBody ReqPromptOnsaleResourceDTO arg0);

   @PostMapping(value = "/resource/confirmResourceStore", consumes = "application/json")
   public ConfirmResourceStoreDTO confirmResourceStore(@RequestBody ReqConfirmResourceStoreDTO arg0);

   @PostMapping(value = "/resource/getDeliveryWays", consumes = "application/json")
   public List<String> getDeliveryWays(@RequestBody List<String> arg0);

   @PostMapping(value = "/resource/todayTransactionPrice", consumes = "application/json")
   public List<TransactionPriceDTO> todayTransactionPrice(@RequestBody TransactionPriceReqDTO arg0);

   @PostMapping(value = "/resource/queryEmallResourceDetail", consumes = "application/json")
   public EmallResourceDetailDTO queryEmallResourceDetail(@RequestBody EmallResourceDetailReqDTO arg0);

   @PostMapping(value = "/resource/resourceOrderPrepare", consumes = "application/json")
   public ResourceOrderPrepareDTO resourceOrderPrepare(@RequestBody ResourceOrderPrepareReqDTO arg0);

   @PostMapping(value = "/resource/resourceOrderPrepareDetail", consumes = "application/json")
   public List<ResourceDTO> resourceOrderPrepareDetail(@RequestBody ResourceOrderPrepareReqDTO arg0);

   @PostMapping(value = "/resource/queryResourceListByCondition", consumes = "application/json")
   public List<ResourceDTO> queryResourceListByCondition(@RequestBody QueryResourceConditionDTO queryResourceConditionDTO);

   @ApiOperation("通过资源id查询支持的支付方式id集合")
   @PostMapping(value = "/resource/findSupportPayWay", consumes = "application/json")
   public List<String> findSupportPayWay(@RequestBody Collection<String> resourceIds);
}
