package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("分类对象")
public class CategoryDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String categoryId;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id", required = true)
    @NotBlank(message = "请选择上级分类")
    private String parentId;

    /**
     * 类别编码
     */
    @ApiModelProperty(value = "类别编码")
    private String categoryCode;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称", required = true)
    @NotBlank(message = "请填写分类名称")
    private String categoryName;

    /**
     * web图片
     */
    @ApiModelProperty(value = "web图片")
    private String imgs;

    /**
     * app图片
     */
    @ApiModelProperty(value = "app图片")
    private String appImgs;

    /**
     * 小程序图片
     */
    @ApiModelProperty(value = "小程序图片")
    private String miniImgs;

    //sap编码
    private String sapCode;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;

    /**
     * 分类属性
     */
    @ApiModelProperty(value = "分类属性")
    private List<CategoryAttributeDTO> categoryAttributeDTOS;

    /**
     * 分类和账户关系
     */
    private List<CategoryAccountRelationDTO> categoryAccountRelationDTOS;

}
