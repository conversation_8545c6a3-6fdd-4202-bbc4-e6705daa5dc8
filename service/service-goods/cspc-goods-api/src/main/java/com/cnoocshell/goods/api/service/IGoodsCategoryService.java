package com.cnoocshell.goods.api.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * @Created锛�Sat Sep 08 17:24:36 CST 2018
 * @Author: <EMAIL>
 * @Version:2
 * @Description:GoodsCategoryController
 */

@FeignClient(name = "cspc-service-goods")
public interface IGoodsCategoryService {

    @PostMapping(value = "/goodsCategory/update", consumes = "application/json")
    ItemResult<GoodsCategoryDTO> update(@RequestParam("categoryId") String categoryId,
                                        @RequestParam("categoryCode") String categoryCode, @RequestParam("categoryName") String categoryName,
                                        @RequestParam("operator") String operator);

    @PostMapping(value = "/goodsCategory/delete", consumes = "application/json")
    ItemResult<Boolean> delete(@RequestParam("categoryId") String categoryId,
                               @RequestParam("operator") String operator);

    @PostMapping(value = "/goodsCategory/checkCementByCategoryCode", consumes = "application/json")
    ItemResult<Boolean> checkCementByCategoryCode(@RequestParam("categoryCode") String categoryCode);

    @PostMapping(value = "/goodsCategory/create", consumes = "application/json")
    ItemResult<GoodsCategoryDTO> create(@RequestParam("categoryCode") String categoryCode,
                                        @RequestParam("categoryName") String categoryName, @RequestParam("parentId") String parentId,
                                        @RequestParam("operator") String operator);

    @GetMapping("/goodsCategory/getAllList")
    ItemResult<List<GoodsCategoryDTO>> getAllList();

    @GetMapping(  "/goodsCategory/getAllTree")
    ItemResult<List<GoodsCategoryDTO>> getAllTree();

    @GetMapping("/goodsCategory/getTreeByParentId")
    ItemResult<List<GoodsCategoryDTO>> getTreeByParentId(@RequestParam("parentId") String parentId);

    @GetMapping( "/goodsCategory/getTreeViewByParentId")
    ItemResult getTreeViewByParentId(@RequestParam("parentId") String parentId);

    @PostMapping("/goodsCategory/getByLikeName")
    ItemResult<List<GoodsCategoryDTO>> getByLikeName(@RequestParam("name") String name);

    @PostMapping(value = "/goodsCategory/findCategoryAttrList", consumes = "application/json")
    ItemResult<List<GoodsCategoryDTO>> findCategoryAttrList(@RequestBody GoodsCategoryAttrDTO queryDTO);

    @PostMapping(value = "/goodsCategory/addCategory", consumes = "application/json")
    void addCategory(@RequestBody CategoryDTO arg0, @RequestParam("arg1") String arg1);

    @PostMapping(value = "/goodsCategory/uptCategory", consumes = "application/json")
    void uptCategory(@RequestBody CategoryDTO arg0, @RequestParam("arg1") String arg1);

    @PostMapping(value = "/goodsCategory/getCategory", consumes = "application/json")
    CategoryDTO getCategory(@RequestParam("arg0") String arg0);

    @PostMapping(value = "/goodsCategory/delCategory", consumes = "application/json")
    void delCategory(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

    @PostMapping(value = "/goodsCategory/findCategoryTree", consumes = "application/json")
    List<CategoryTreeDTO> findCategoryTree(String arg0);

    @PostMapping(value = "/goodsCategory/findCategoryTreeByMp", consumes = "application/json")
    List<CategoryTreeDTO> findCategoryTreeByMp(String arg0);

    @PostMapping(value = "/goodsCategory/findCategoryTreeGoods", consumes = "application/json")
    List<CategoryTreeDTO> findCategoryTreeGoods(String arg0);

    @PostMapping(value = "/goodsCategory/findCategoryTreeByCode", consumes = "application/json")
    List<CategoryTreeDTO> findCategoryTreeByCode(@RequestParam("arg0") String arg0);

    @PostMapping(value = "/goodsCategory/homeMenuCategoryTree", consumes = "application/json")
    HomeMenuCategoryTreeDTO homeMenuCategoryTree(@RequestParam("arg0") String arg0);

    @PostMapping(value = "/goodsCategory/goodsCategoryTree", consumes = "application/json")
    List<CategoryTreeDTO> goodsCategoryTree(@RequestParam("arg0") String arg0);

    @PostMapping(value = "/goodsCategory/purchaseCategoryTree", consumes = "application/json")
    List<CategoryTreeDTO> purchaseCategoryTree(@RequestParam("arg0") String arg0);

    @PostMapping(value = "/goodsCategory/getSimpleList", consumes = "application/json")
    List<GoodsCategorySimpleDTO> getSimpleList(@RequestBody Set<String> categoryCodeSet);

    @PostMapping(value = "/goodsCategory/getGoodsCategoryByGoodsId", consumes = "application/json")
    GoodsCategoryDTO getGoodsCategoryByGoodsId(@RequestParam("arg0") String arg0);

    @PostMapping(value = "/goodsCategory/findCategorySimpleByIds", consumes = "application/json")
    List<GoodsCategorySimpleDTO> findCategorySimpleByIds(@RequestBody List<String> categoryIds);

    @PostMapping(value = "/goodsCategory/findCategoryByCountId", consumes = "application/json")
    List<GoodsCategorySimpleDTO> findCategoryByCountId(@RequestBody GoodsCategorySimpleDTO categorySimpleDTO);

    @GetMapping(value = "/goods/findGoodsSimpleByCategoryId")
    ItemResult<List<GoodsSimpleDTO>> findGoodsSimpleByCategoryId(@RequestParam String categoryId);

    @PostMapping(value = "/goodsCategory/findGoodsByCategoryAccountId", consumes = "application/json")
    List<GoodsSimpleDTO> findGoodsByCategoryAccountId(@RequestBody GoodsCategorySimpleDTO categorySimpleDTO);

    @GetMapping(value = "/goodsCategory/findCategoryTreeGoodsByRole")
    List<CategoryTreeDTO> findCategoryTreeGoodsByRole(@RequestParam String memberId,@RequestParam String accountId);

    @ApiOperation("查询分类名称与其上一级的父级分类名称信息")
    @PostMapping("/goodsCategory/queryCategoryAndParentCategory")
    List<CategoryAndParentCategoryDTO> queryCategoryAndParentCategory(@RequestBody List<String> goodsCategoryCodes);


    @ApiOperation("查询分类名称与其上一级的父级分类名称信息")
    @PostMapping("/goodsCategory/queryCategoryAndParentByCategoryId")
    List<CategoryAndParentCategoryDTO> queryCategoryAndParentByCategoryId(@RequestBody List<String> categoryIds);

}
