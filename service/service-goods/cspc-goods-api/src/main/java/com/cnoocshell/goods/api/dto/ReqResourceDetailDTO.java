package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("请求资源详情DTO")
public class ReqResourceDetailDTO {
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String goodsId;
    /**
     * 买家ID
     */
    @ApiModelProperty(value = "买家ID")
    private String buyerId;
    /**
     * 买家姓名
     */
    @ApiModelProperty(value = "买家姓名")
    private String buyerName;
    /**
     * 卖家id
     */
    @ApiModelProperty(value = "卖家id")
    private String sellerId;

    /**
     * 下单数量
     */
    @ApiModelProperty(value = "下单数量")
    private BigDecimal buyNum;

    /**
     * 购买单位
     */
    @ApiModelProperty(value = "购买单位")
    private String unit;

    /**
     * 所在国家名称
     */
    @ApiModelProperty(value = "所在国家名称")
    private String countryName;

    /**
     * 所在省名称
     */
    @ApiModelProperty(value = "所在省名称")
    private String provinceName;

    /**
     * 所在城市名称
     */
    @ApiModelProperty(value = "所在城市名称")
    private String cityName;

    /**
     * 所在地区名称
     */
    @ApiModelProperty(value = "所在地区名称")
    private String areaName;

    /**
     * 所在街道名称
     */
    @ApiModelProperty(value = "所在街道名称")
    private String streetName;

    /**
     * 所在国家Code
     */
    @ApiModelProperty(value = "所在国家Code")
    private String countryCode;

    /**
     * 所在省Code
     */
    @ApiModelProperty(value = "所在省Code")
    private String provinceCode;

    /**
     * 所在城市Code
     */
    @ApiModelProperty(value = "所在城市Code")
    private String cityCode;

    /**
     * 所在地区Code
     */
    @ApiModelProperty(value = "所在地区Code")
    private String areaCode;

    /**
     * 所在街道Code
     */
    @ApiModelProperty(value = "所在街道Code")
    private String streetCode;

}
