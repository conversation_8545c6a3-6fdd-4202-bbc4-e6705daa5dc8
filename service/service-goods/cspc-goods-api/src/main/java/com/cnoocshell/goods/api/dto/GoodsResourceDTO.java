package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("商品资源DTO")
public class GoodsResourceDTO {

    /**
     * 商品资源聚合ID
     */
    @ApiModelProperty(value = "商品资源聚合ID")
    private String goodsResourceId;

    /**
     * 商品属性聚合
     */
    @ApiModelProperty(value = "商品属性聚合")
    private String goodsAttributes;

    /**
     * 资源区域聚合
     */
    @ApiModelProperty(value = "资源区域聚合")
    private String goodsRegions;

    /**
     * 资源聚合
     */
    @ApiModelProperty(value = "资源聚合")
    private String goodsResources;

    /**
     * 品类ID
     */
    @ApiModelProperty(value = "品类ID")
    private String spuId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 台班费规则ID
     */
    @ApiModelProperty(value = "台班费规则ID")
    private String machineRuleId;

    /**
     * 空载费规则Id
     */
    @ApiModelProperty(value = "空载费规则Id")
    private String emptyLoadRuleId;

    /**
     * 商品业务类型 1平台商品2卖家商品
     */
    @ApiModelProperty(value = "商品业务类型")
    private Integer goodsType;

    /**
     * 模糊搜索关键字
     */
    @ApiModelProperty(value = "模糊搜索关键字")
    private String searchKeywords;

    /**
     * 状态值，1草稿 2启用（审核通过）3禁用 4审核中 5审核失败
     */
    @ApiModelProperty(value = "状态值")
    private Integer goodsStatus;

    /**
     * 卖家id
     */
    @ApiModelProperty(value = "卖家id")
    private String sellerId;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String measureUnit;

    /**
     * 包装
     */
    @ApiModelProperty(value = "包装")
    private String pack;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specs;

    /**
     * 标号
     */
    @ApiModelProperty(value = "标号")
    private String mark;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String concreteClass;

    /**
     * 等级强度
     */
    @ApiModelProperty(value = "等级强度")
    private String strength;

    /**
     * 塌落度（毫米）
     */
    @ApiModelProperty(value = "塌落度（毫米）")
    private String slump;

    /**
     * 质量标准
     */
    @ApiModelProperty(value = "质量标准")
    private String qualityStandard;

    /**
     * 使用范围
     */
    @ApiModelProperty(value = "使用范围")
    private String useRange;

    /**
     * 物流类型
     */
    @ApiModelProperty(value = "物流类型")
    private String logistics;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式")
    private Integer pricingMode;

    /**
     * 配送方式 1自提，2厂家配送，3平台配送
     */
    @ApiModelProperty(value = "配送方式")
    private String deliveryMode;

    /**
     * 商品类型 1 2
     */
    @ApiModelProperty(value = "商品类型")
    private Integer categoryType;

    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    private BigDecimal weight;

    /**
     * 分类1
     */
    @ApiModelProperty(value = "分类1")
    private String category1;

    /**
     * 分类2
     */
    @ApiModelProperty(value = "分类2")
    private String category2;

    /**
     * 分类3
     */
    @ApiModelProperty(value = "分类3")
    private String category3;

    /**
     * 分类4
     */
    @ApiModelProperty(value = "分类4")
    private String category4;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String imgs;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;

    /**
     * 商品特点
     */
    @ApiModelProperty(value = "商品特点")
    private String memo1;

    /**
     * 适用范围
     */
    @ApiModelProperty(value = "适用范围")
    private String memo2;

    /**
     * 性能指标
     */
    @ApiModelProperty(value = "性能指标")
    private String memo3;

    /**
     * 使用指南
     */
    @ApiModelProperty(value = "使用指南")
    private String memo4;

    /**
     * 备注5
     */
    @ApiModelProperty(value = "备注5")
    private String memo5;

    /**
     * 备注6
     */
    @ApiModelProperty(value = "备注6")
    private String memo6;

    /**
     * app商品特点
     */
    @ApiModelProperty(value = "app商品特点")
    private String appMemo1;

    /**
     * app适用范围
     */
    @ApiModelProperty(value = "app适用范围")
    private String appMemo2;

    /**
     * app性能指标
     */
    @ApiModelProperty(value = "app性能指标")
    private String appMemo3;

    /**
     * app使用指南
     */
    @ApiModelProperty(value = "app使用指南")
    private String appMemo4;

    /**
     * app备注5
     */
    @ApiModelProperty(value = "app备注5")
    private String appMemo5;

    /**
     * app备注6
     */
    @ApiModelProperty(value = "app备注6")
    private String appMemo6;

    /**
     * 资源
     */
    @ApiModelProperty(value = "资源")
    private ResourceDTO resourceDTO;

}
