package com.cnoocshell.goods.api.enums;

/**
 * 
 * @Author: <EMAIL>
 * @Description  属性值类型,1-数字型，2-输入型，3-选项值，4-特殊值，5-日期
 * @date   2018年8月16日 下午3:17:13
 */
public enum GoodsAttrValTypeEnum {
	
	DIGITAL(1, "数字型"),
	INPUT(2, "输入型"),
	OPTION(3, "选项值"),
	SPECIAL(4, "特殊值"),
	DATE(5, "日期");
	
    /**
     * 编码
     */
    private int code;
    /**
     * 描述
     */
    private String description;

    GoodsAttrValTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

	public int getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}
}
