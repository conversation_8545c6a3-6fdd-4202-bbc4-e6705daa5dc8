package com.cnoocshell.goods.api.enums;

public enum MergeAttrPrefixEnum {
	INSTIDE(":", ":"),
	OUTSIDE(";", ";"),
	GROUPSIDE(",", ","),
	LIKESIDE("%", "%");

	/**
	 * 编码
	 */
	private String code;
	/**
	 * 描述
	 */
	private String description;

	MergeAttrPrefixEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public static MergeAttrPrefixEnum getByCode(String code) {
		for (MergeAttrPrefixEnum _enum : MergeAttrPrefixEnum.values()) {
			if (_enum.code.equals(code)) {
				return _enum;
			}
		}
		return null;
	}
}
