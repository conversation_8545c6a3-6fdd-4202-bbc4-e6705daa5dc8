package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CategoryAndParentCategoryDTO {
    @ApiModelProperty(value = "产品一级分类ID", notes = "ID")
    private String categoryIdLevelOne;
    @ApiModelProperty(value = "产品一级分类", notes = "编码")
    private String categoryCodeLevelOne;
    @ApiModelProperty(value = "产品一级分类", notes = "名称")
    private String categoryNameLevelOne;

    @ApiModelProperty(value = "产品二级分类ID", notes = "ID")
    private String categoryIdLevelTwo;
    @ApiModelProperty(value = "产品二级分类", notes = "编码")
    private String categoryCodeLevelTwo;
    @ApiModelProperty(value = "产品二级分类", notes = "名称")
    private String categoryNameLevelTwo;
}
