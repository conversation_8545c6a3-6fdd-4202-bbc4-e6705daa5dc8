package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode
@Data
@ApiModel("资源DTO")
public class ResourceDTO {
	/**
	 * 商品信息
	 */
	@ApiModelProperty(value = "商品信息")
	private GoodsDTO goodsDTO;

	/**
	 * 商品信息
	 */
	@ApiModelProperty(value = "商品信息")
	private List<AttributeDTO> searchAttributes;

    /**
     * 是否有货 1有 0无
     */
	@ApiModelProperty(value = "是否有货")
    private Integer isHaveGoods;

	/**
	 * 起售数量
	 */
	@ApiModelProperty(value = "起售数量")
	private String salesVolume;

	/**
	 * 台班费规则ID
	 */
	@ApiModelProperty(value = "台班费规则ID")
	private String machineRuleId;

	/**
	 * 空载费规则Id
	 */
	@ApiModelProperty(value = "空载费规则Id")
	private String emptyLoadRuleId;

	/**
	 * 资源ID
	 */
	@ApiModelProperty(value = "资源ID")
	private String resourceId;

	/**
	 * SKU属性聚合
	 */
	@ApiModelProperty(value = "SKU属性聚合")
	private String resourceAttributes;

	/**
	 * 资源区域聚合
	 */
	@ApiModelProperty(value = "资源区域聚合")
	private String resourceRegions;

	/**
	 * 商品资源聚合ID
	 */
	@ApiModelProperty(value = "商品资源聚合ID")
	private String goodsResourceId;

	/**
	 * 品类商品ID
	 */
	@ApiModelProperty(value = "品类商品ID")
	private String spuId;

	/**
	 * 商品ID
	 */
	@ApiModelProperty(value = "商品ID")
	private String goodsId;

	/**
	 * 商品名称
	 */
	@ApiModelProperty(value = "商品名称")
	private String goodsName;

	/**
	 * 商品分类
	 */
	@ApiModelProperty(value = "商品分类")
	private String goodsType;

	/**
	 * 商品分类
	 */
	@ApiModelProperty(value = "分类Code")
	private String categoryCode;

	/**
	 * 商品描述
	 */
	@ApiModelProperty(value = "商品描述")
	private String goodsDescribe;

	/**
	 * 资源名称
	 */
	@ApiModelProperty(value = "资源名称")
	private String resourceName;

	/**
	 * 资源code
	 */
	@ApiModelProperty(value = "资源code")
	private String resourceCode;

	/**
	 * 生效时间
	 */
	@ApiModelProperty(value = "生效时间")
	private Date effectTime;

	/**
	 * 币种
	 */
	@ApiModelProperty(value = "币种")
	private String currency;

	/**
	 * 币种符号
	 */
	@ApiModelProperty(value = "币种符号")
	private String currencySymbol;

	/**
	 * 币种名称
	 */
	@ApiModelProperty(value = "币种名称")
	private String currencyName;

	/**
	 * 价格描述
	 */
	@ApiModelProperty(value = "价格描述")
	private String priceDescribe;

	/**
	 * 计价方式，1 出厂价 2到位价
	 */
	@ApiModelProperty(value = "计价方式")
	private String priceWay;

	/**
	 * 价格
	 */
	@ApiModelProperty(value = "价格")
	private BigDecimal price;

	/**
	 * 换算率
	 */
	@ApiModelProperty(value = "换算率")
	private BigDecimal convertRate;

	/**
	 * 出厂价
	 */
	@ApiModelProperty(value = "出厂价")
	private BigDecimal factoryPrice;

	/**
	 * 到位价
	 */
	@ApiModelProperty(value = "到位价")
	private BigDecimal arrivePrice;

	/**
	 * 是否可议价
	 */
	@ApiModelProperty(value = "是否可议价")
	private Boolean ifProtocolPrice;

	/**
	 * 计量单位
	 */
	@ApiModelProperty(value = "计量单位")
	private String priceUnit;

	/**
	 * 销售单位
	 */
	@ApiModelProperty(value = "销售单位")
	private String saleUnit;

	/**
	 * 仓库ID
	 */
	@ApiModelProperty(value = "仓库ID")
	private String storeId;

	/**
	 * 仓库名称
	 */
	@ApiModelProperty(value = "仓库名称")
	private String storeName;

	/**
	 * 仓库类型, 100卖家基地库 200平台中心仓
	 */
	@ApiModelProperty(value = "仓库类型")
	private String storeType;

	/**
	 * 仓库详细地址
	 */
	@ApiModelProperty(value = "仓库详细地址")
	private String storeAddress;

	/**
	 * 版本号
	 */
	@ApiModelProperty(value = "版本号")
	private Integer resourceVersion;

	/**
	 * 资源状态, 100已挂牌 200已撤牌 300撤牌处理中 400未上架 500待审批
	 */
	@ApiModelProperty(value = "资源状态")
	private String status;

	/**
	 * 修改原因
	 */
	@ApiModelProperty(value = "修改原因")
	private String approvalMessage;

	/**
	 * 卖家ID
	 */
	@ApiModelProperty(value = "卖家ID")
	private String sellerId;

	/**
	 * 卖家姓名
	 */
	@ApiModelProperty(value = "卖家姓名")
	private String sellerName;

	/**
	 * 卖家简称
	 */
	@ApiModelProperty(value = "卖家简称")
	private String sellerNickName;

	/**
	 * 销售员ID
	 */
	@ApiModelProperty(value = "销售员ID")
	private String salesId;

	/**
	 * 销售员姓名
	 */
	@ApiModelProperty(value = "销售员姓名")
	private String salesName;

	/**
	 * 联系电话
	 */
	@ApiModelProperty(value = "联系电话")
	private String contactPhone;

	/**
	 * 组织机构ID
	 */
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 总销售数量
	 */
	@ApiModelProperty(value = "总销售数量")
	private BigDecimal saleNum;

	/**
	 * 可售数量
	 */
	@ApiModelProperty(value = "可售数量")
	private BigDecimal cansaleNum;

	/**
	 * 锁定数量
	 */
	@ApiModelProperty(value = "锁定数量")
	private BigDecimal lockNum;

	/**
	 * 起售数量
	 */
	@ApiModelProperty(value = "起售数量")
	private BigDecimal volumeNum;

	/**
	 * 容差类型， 100绝对容差 200相对容差
	 */
	@ApiModelProperty(value = "容差类型")
	private String toleranceType;

	/**
	 * 容差
	 */
	@ApiModelProperty(value = "容差")
	private BigDecimal tolerance;

	/**
	 * 单笔最大购买量
	 */
	@ApiModelProperty(value = "单笔最大购买量")
	private BigDecimal ordermaxNum;

	/**
	 * 单笔最小购买量
	 */
	@ApiModelProperty(value = "单笔最小购买量")
	private BigDecimal orderminNum;

	/**
	 * 单笔最小变动量
	 */
	@ApiModelProperty(value = "单笔最小变动量")
	private BigDecimal orderminchangeNum;

	/**
	 * 单日最大购买量
	 */
	@ApiModelProperty(value = "单日最大购买量")
	private BigDecimal daymaxNum;

	/**
	 * 区域定价层级
	 */
	@ApiModelProperty(value = "区域定价层级")
	private Integer areaLevel;

	/**
	 * 是否允许自提
	 */
	@ApiModelProperty(value = "是否允许自提")
	private Boolean ifTakeSelf;

	/**
	 * 是否平台配送
	 */
	@ApiModelProperty(value = "是否平台配送")
	private Boolean ifPlatformDelivery;

	/**
	 * 是否卖家配送
	 */
	@ApiModelProperty(value = "是否卖家配送")
	private Boolean ifSellerDelivery;

	/**
	 * 自提优惠单价
	 */
	@ApiModelProperty(value = "自提优惠单价")
	private BigDecimal takeSelfDiscounts;

	/**
	 * 免物流费重量
	 */
	@ApiModelProperty(value = "免物流费重量")
	private BigDecimal logisticsWeight;

	/**
	 * 免物流费计算类型
	 */
	@ApiModelProperty(value = "免物流费计算类型")
	private String logisticsType;

	/**
	 * 物流费计算单位
	 */
	@ApiModelProperty(value = "物流费计算单位")
	private String logisticsUnit;

	/**
	 * 物流费单价
	 */
	@ApiModelProperty(value = "物流费单价")
	private BigDecimal logisticsPrice;

	/**
	 * 搬运费类型
	 */
	@ApiModelProperty(value = "搬运费类型")
	private String cartageRuleId;

	/**
	 * 支付方式
	 */
	@ApiModelProperty(value = "支付方式")
	private List<String> payWay;

	/**
	 * 是否立即上架
	 */
	@ApiModelProperty(value = "是否立即上架")
	private Boolean ifup;

	/**
	 * 定时上架时间
	 */
	@ApiModelProperty(value = "定时上架时间")
	private Date fixUptime;

	/**
	 * 是否立即下架
	 */
	@ApiModelProperty(value = "是否立即下架")
	private Boolean ifdown;

	/**
	 * 定时下架时间
	 */
	@ApiModelProperty(value = "定时下架时间")
	private Date fixDowntime;

	/**
	 * 上架时间
	 */
	@ApiModelProperty(value = "上架时间")
	private Date upTime;

	/**
	 * 下架时间
	 */
	@ApiModelProperty(value = "下架时间")
	private Date downTime;

	/**
	 * 交易开始时间
	 */
	@ApiModelProperty(value = "交易开始时间")
	private Date tradeStarttime;

	/**
	 * 交易结束时间
	 */
	@ApiModelProperty(value = "交易结束时间")
	private Date tradeEndtime;

	/**
	 * 是否流量管控
	 */
	@ApiModelProperty(value = "是否流量管控")
	private Boolean ifFlowcontrol;

	/**
	 * 是否允许分批发货
	 */
	@ApiModelProperty(value = "是否允许分批发货")
	private Boolean allowPartial;

	/**
	 * 销售区域
	 */
	@ApiModelProperty(value = "销售区域")
	private String saleArea;

	/**
	 * 真实销售区域
	 */
	@ApiModelProperty(value = "真实销售区域")
	private String saleAreaRealCode;

	/**
	 * 一级销售区域编码
	 */
	@ApiModelProperty(value = "一级销售区域编码")
	private String saleAreaCode;

	/**
	 * 二级销售区域编码
	 */
	@ApiModelProperty(value = "二级销售区域编码")
	private String saleAreaCode2;

	/**
	 * 三级销售区域编码
	 */
	@ApiModelProperty(value = "三级销售区域编码")
	private String saleAreaCode3;

	/**
	 * 四级销售区域编码
	 */
	@ApiModelProperty(value = "四级销售区域编码")
	private String saleAreaCode4;

	/**
	 * 五级销售区域编码
	 */
	@ApiModelProperty(value = "五级销售区域编码")
	private String saleAreaCode5;

	/**
	 * 一级销售区域名称
	 */
	@ApiModelProperty(value = "一级销售区域名称")
	private String saleAreaName;

	/**
	 * 二级销售区域名称
	 */
	@ApiModelProperty(value = "二级销售区域名称")
	private String saleAreaName2;

	/**
	 * 三级销售区域名称
	 */
	@ApiModelProperty(value = "三级销售区域名称")
	private String saleAreaName3;

	/**
	 * 四级销售区域名称
	 */
	@ApiModelProperty(value = "四级销售区域名称")
	private String saleAreaName4;

	/**
	 * 五级销售区域名称
	 */
	@ApiModelProperty(value = "五级销售区域名称")
	private String saleAreaName5;

	/**
	 * 支付有效期类型
	 */
	@ApiModelProperty(value = "支付有效期类型")
	private String paydateType;

	/**
	 * 支付有效期
	 */
	@ApiModelProperty(value = "支付有效期")
	private Long paydateLimit;

	/**
	 * 提货有效期类型
	 */
	@ApiModelProperty(value = "提货有效期类型")
	private String takedateType;

	/**
	 * 提货有效期
	 */
	@ApiModelProperty(value = "提货有效期")
	private Long takedateLimit;

	/**
	 * 提货有效期 小时
	 */
	@ApiModelProperty(value = "提货有效期 小时")
	private Integer takedateHour;

	/**
	 * 删除标记
	 */
	@ApiModelProperty(value = "删除标记")
	private Boolean delFlg;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUser;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private String updateUser;

	/**
	 * 交易状态 100, 可交易 200, 不可交易
	 */
	@ApiModelProperty(value = "交易状态")
	private String tradeStatus;

	/**
	 * 订单触发自动完成类型 floatScale:浮动比例;fixedNumerical:固定数值
	 */
	@ApiModelProperty(value = "订单触发自动完成类型")
	private String autoCompleteType;

	/**
	 * 触发自动完成阀值
	 */
	@ApiModelProperty(value = "触发自动完成阀值")
	private BigDecimal autoCompleteThreshold;

}
