package com.cnoocshell.goods.api.enums.contract;

public enum ContractGoodsPriceStatusEnum {
    ABSOLUTE_PRICE("1","固定运价"),
    RELATIVE_PRICE("2","卖家运费规则");
    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    ContractGoodsPriceStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
