package com.cnoocshell.goods.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class GoodsSapMappingDTO {
    private String id;
    private String pack;
    private String goodsName;
    private String goodsCode;
    private String sapMaterialCode;
    private String mappingSapMaterialCode;
    private Integer pageNum;
    private Integer pageSize;
    private String operator;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private String createUser;
    private String createUserName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    private String updateUser;
    private String updateUserName;
}
