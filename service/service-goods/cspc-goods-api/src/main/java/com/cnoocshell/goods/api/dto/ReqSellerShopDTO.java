package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("请求卖家店铺DTO")
public class ReqSellerShopDTO {

    @ApiModelProperty(value = "当前查询人买家ID")
    private String searchBuyerId;

    /**
     * 商品分类 1 2 3钢筋 4瓷砖
     */
    @ApiModelProperty(value = "商品分类")
    private String goodsType;
    /**
     * 商品分类Id
     */
    @ApiModelProperty(value = "商品分类Id")
    private String categoryId;
    /**
     * 所在国家编码
     */
    @ApiModelProperty(value = "所在国家编码")
    private String countryCode;

    /**
     * 所在省编码
     */
    @ApiModelProperty(value = "所在省编码")
    private String provinceCode;

    /**
     * 所在城市编码
     */
    @ApiModelProperty(value = "所在城市编码")
    private String cityCode;

    /**
     * 所在地区编码
     */
    @ApiModelProperty(value = "所在地区编码")
    private String areaCode;

    /**
     * 所在街道编码
     */
    @ApiModelProperty(value = "所在街道编码")
    private String streetCode;

    /**
     * 所在国家名称
     */
    @ApiModelProperty(value = "所在国家名称")
    private String countryName;

    /**
     * 所在省名称
     */
    @ApiModelProperty(value = "所在省名称")
    private String provinceName;

    /**
     * 所在城市名称
     */
    @ApiModelProperty(value = "所在城市名称")
    private String cityName;

    /**
     * 所在地区名称
     */
    @ApiModelProperty(value = "所在地区名称")
    private String areaName;

    /**
     * 所在街道名称
     */
    @ApiModelProperty(value = "所在街道名称")
    private String streetName;
}
