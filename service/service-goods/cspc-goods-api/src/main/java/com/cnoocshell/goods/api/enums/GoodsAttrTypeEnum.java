package com.cnoocshell.goods.api.enums;

/**
 * 
 * @Author: <EMAIL>
 * @Description  属性类型,1-基本属性 ，2-扩展属性
 * @date   2018年8月16日 下午3:17:13
 */
public enum GoodsAttrTypeEnum {
	
	BASE(1, "主表属性"),
	EXTEND(2, "扩展属性");
	
    /**
     * 编码
     */
    private int code;
    /**
     * 描述
     */
    private String description;

    GoodsAttrTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

	public int getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}
	
	public static GoodsAttrTypeEnum getByCode(int code) {
		for(GoodsAttrTypeEnum _enum : GoodsAttrTypeEnum.values()){
			if(_enum.code == code){
				return _enum;
			}
		}
		return null;
	}
}
