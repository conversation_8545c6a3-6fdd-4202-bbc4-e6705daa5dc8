package com.cnoocshell.goods.api.service;

import com.cnoocshell.goods.api.dto.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * @Created锛�Sat Sep 08 17:30:59 CST 2018
 * @Author: <EMAIL>
 * @Version:2
 * @Description::null
 */

@FeignClient(name ="cspc-service-goods")
public interface IResourceQueryService {


   @PostMapping(value = "/resourceQuery/pageResourceSeller", consumes = "application/json")
   public PageInfo<ResourceSellerDTO> pageResourceSeller(@RequestBody ReqResourceSellerDTO arg0);

   @PostMapping(value = "/resourceQuery/pageResourcePlatform", consumes = "application/json")
   public PageInfo<ResourcePlatformDTO> pageResourcePlatform(@RequestBody ReqResourcePlatformDTO arg0);

   @GetMapping( "/resourceQuery/getSimpleResourceDetail")
   public ResourceDTO getSimpleResourceDetail(@RequestParam("arg0") String arg0);

   @GetMapping( "/resourceQuery/getComplexResourceDetail")
   public ResourceDTO getComplexResourceDetail(@RequestParam("arg0") String arg0);

   @PostMapping(value = "/resourceQuery/queryEffectResourceIds", consumes = "application/json")
   public List<String> queryEffectResourceIds(@RequestBody List<String> arg0);

   @PostMapping(value = "/resourceQuery/searchResourceByDescribe", consumes = "application/json")
   public List<LogisticsResourceDTO> searchResourceByDescribe(@RequestBody ReqLogisticsResourceDTO arg0);

   @PostMapping(value = "/resourceQuery/searchGoodsCollectionList", consumes = "application/json")
   public List<GoodsCollecttionDTO> searchGoodsCollectionList(@RequestBody List<ReqGoodsCollecttionDTO> arg0);

   @PostMapping(value = "/resourceQuery/querySellerShopByGoodsType", consumes = "application/json")
   public List<SellerShopDTO> querySellerShopByGoodsType(@RequestBody ReqSellerShopDTO arg0);

   @PostMapping(value = "/resourceQuery/getDeliveryWays", consumes = "application/json")
   public List<String> getDeliveryWays(@RequestBody List<String> arg0);

   @PostMapping(value = "/resourceQuery/searchGoodsResourceEmall", consumes = "application/json")
   public PageInfo<GoodsResourceListDTO> searchGoodsResourceEmall(@RequestBody ReqGoodsResourceDTO arg0);

   @PostMapping(value = "/resourceQuery/searchGoodsResourceBuyer", consumes = "application/json")
   public PageInfo<GoodsResourceListDTO> searchGoodsResourceBuyer(@RequestBody ReqGoodsResourceDTO arg0);

   @PostMapping(value = "/resourceQuery/queryEmallResourceDetail", consumes = "application/json")
   public EmallResourceDetailDTO queryEmallResourceDetail(@RequestBody EmallResourceDetailReqDTO arg0);

   @PostMapping(value = "/resourceQuery/resourceOrderPrepareDetail", consumes = "application/json")
   public List<ResourceDTO> resourceOrderPrepareDetail(@RequestBody ResourceOrderPrepareReqDTO arg0);

   @PostMapping(value = "/resourceQuery/todayTransactionPrice", consumes = "application/json")
   public List<TransactionPriceDTO> todayTransactionPrice(@RequestBody TransactionPriceReqDTO arg0);

   @PostMapping(value = "/resourceQuery/checkResourceLogisticRule", consumes = "application/json")
   public boolean checkResourceLogisticRule(@RequestBody List<String> arg0);

   @PostMapping(value = "/resourceQuery/confirmContractResource", consumes = "application/json")
   public ContractResourceDTO confirmContractResource(@RequestBody ReqContractResourceDTO arg0);

   @PostMapping(value = "/resourceQuery/searchGoodsResourceAttributes", consumes = "application/json")
   public List<AttributeDTO> searchGoodsResourceAttributes(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

   @PostMapping(value = "/resourceQuery/getEffectiveResourceList", consumes = "application/json")
   public List<ResourceDTO> getEffectiveResourceList(@RequestBody ResourceQueryDTO resourceQueryDTO);

   @ApiOperation("根据资源id集合查询资源表数据")
   @PostMapping(value = "/resourceQuery/findByResourceIds", consumes = "application/json")
   List<ResourceDTO> findByResourceIds(@RequestBody Collection<String> ids);

   @ApiOperation("根据资源id查询资源支持的支付方式")
   @PostMapping(value = "/resourceQuery/findPayWayByResourceIds", consumes = "application/json")
   List<String> findPayWayByResourceIds(@RequestBody Collection<String> ids);
}
