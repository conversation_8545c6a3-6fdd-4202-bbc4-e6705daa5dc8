package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("商品收藏对象")
public class GoodsCollecttionDTO {
    /**
     * 收藏对象ID
     */
    @ApiModelProperty(value = "收藏对象ID")
    private String objectId;
    /**
     * 收藏对象类型
     * list 商品
     * detail 资源
     */
    @ApiModelProperty(value = "收藏对象类型")
    private String objectType;
    /**
     * 商品详情
     */
    @ApiModelProperty(value = "商品详情")
    private GoodsResourceDTO goodsResourceDTO;
    /**
     * 资源详情
     */
    @ApiModelProperty(value = "资源详情")
    private ResourceDTO resourceDTO;
}
