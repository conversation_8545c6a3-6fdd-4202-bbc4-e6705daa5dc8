package com.cnoocshell.goods.api.dto.GoodsShippingFee;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("商品运费信息分页列表入参")
public class GoodsShippingFeePageReqDTO {

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "SAP物料编码")
    private String sapMaterialCode;

    @ApiModelProperty(value = "包装方式")
    private String pack;

    @ApiModelProperty(value = "用户id")
    private String accountId;

    @ApiModelProperty("页码")
    private int pageNum;

    @ApiModelProperty("单页条数")
    private int pageSize;
}
