package com.cnoocshell.goods.api.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.goods.api.dto.GoodCategoryRelationDto;
import com.cnoocshell.goods.api.dto.relation.GoodsCategoryRelationSimpleDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "cspc-service-goods", path = "/base-good/category/relation")
public interface IGoodCategoryRelationService {

    @PostMapping(value = "save", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ItemResult<Boolean> save(@RequestBody List<GoodCategoryRelationDto> dtos);

    @PostMapping("/queryRelationByCategoryCodes")
    List<GoodsCategoryRelationSimpleDTO> queryRelationByCategoryCodes(@RequestBody List<String> categoryCodes);
}
