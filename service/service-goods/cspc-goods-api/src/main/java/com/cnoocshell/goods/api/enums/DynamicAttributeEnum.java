package com.cnoocshell.goods.api.enums;

/**
 * 
 * @Author: <EMAIL>
 * @Description  商品动态属性列表
 * @date   2018年8月16日 下午3:17:13
 */
public enum DynamicAttributeEnum {
    MANUFACTURER("0109", "厂商"),
    SALESMAN("0110", "业务员确认"),
    STEEL_BAR("0111","支持钢筋选项"),
    DELIVERY_TIME_TYPE("0112","配置时间类型"), //12h,30m
    STORE_DISCOUNT("0113","门店优惠"),
    TRANSACTION_TERMS("0114","交易条款"),
    CARRY("0115","可搬运"),
    HIDE_PRICE("0117","是否屏蔽价格"),
    SIGN_TYPE("0118","签收选项");

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    DynamicAttributeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}
	
}
