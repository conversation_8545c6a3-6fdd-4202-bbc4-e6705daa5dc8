package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("首页菜单分类")
public class HomeMenuCategoryTreeDTO {

    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private List<CategoryTreeDTO> goodsCategorys;

    /**
     * 采购分类
     */
    @ApiModelProperty(value = "采购分类")
    private List<CategoryTreeDTO> purchaseCategorys;
}
