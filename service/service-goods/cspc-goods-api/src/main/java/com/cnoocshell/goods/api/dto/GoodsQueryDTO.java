package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("商品查询")
public class GoodsQueryDTO {

	@ApiModelProperty(value = "spuID")
	private String spuId;

	@ApiModelProperty(value = "卖家ID")
	private String sellerId;

	@ApiModelProperty(value = "商品属性列表")
	private List<GoodsAttributeDTO> attrList;
}