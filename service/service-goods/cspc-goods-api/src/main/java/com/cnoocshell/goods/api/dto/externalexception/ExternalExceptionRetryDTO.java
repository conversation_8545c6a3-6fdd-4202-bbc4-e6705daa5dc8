package com.cnoocshell.goods.api.dto.externalexception;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: ExternalExceptionRetryDTO
 * @Author: <EMAIL>
 * @Date: 2021-05-20 14:14
 */
@Data
@ApiModel("外部异常处理重试对象")
public class ExternalExceptionRetryDTO {

    @ApiModelProperty(value = "外部异常处理ID")
    private String externalExceptionHandleId;

    @ApiModelProperty(value="操作人Id", required = true,  example = "xxx")
    private String operatorUserId;

    @ApiModelProperty(value="操作人姓名", required = true,  example = "王大锤")
    private String operatorUserName;

}
