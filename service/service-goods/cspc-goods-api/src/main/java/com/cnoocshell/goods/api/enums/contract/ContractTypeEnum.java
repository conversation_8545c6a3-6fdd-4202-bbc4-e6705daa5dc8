package com.cnoocshell.goods.api.enums.contract;

import lombok.Getter;

@Getter
public enum ContractTypeEnum {

    CEMENT(1, ""),
    CONCRETE(2, ""),
    CASH(10, "现金合同"),
    CREDIT(20, "授信合同");


    /**
     * 编码
     */
    private Integer code;

    /**
     * 描述
     */
    private String message;

    ContractTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMsgByCode(int code) {
        for (ContractTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.message;
            }
        }
        return null;
    }
}
