package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午2:20 19/11/20
 */
@Data
@ApiModel("批量更新挂牌资源信息")
public class BatchUpdateResourceDTO {
    /**
     * 品类商品ID
     */
    @ApiModelProperty(value = "品类商品ID")
    private String spuId;

    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    /**
     * 挂牌规则JSON字符串
     */
    @ApiModelProperty(value = "挂牌规则JSON字符串")
    private String priceRule;

    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;

    /**
     * 是否定时上架
     */
    @ApiModelProperty(value = "是否定时上架")
    private Boolean ifUp;

    /**
     * 定时上架时间
     */
    @ApiModelProperty(value = "定时上架时间")
    private Date fixUptime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;
}
