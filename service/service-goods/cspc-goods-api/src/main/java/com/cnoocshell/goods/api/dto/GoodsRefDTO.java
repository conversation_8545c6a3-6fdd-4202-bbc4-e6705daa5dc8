package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("商品与上级商品DTO")
public class GoodsRefDTO implements Serializable {

    private static final long serialVersionUID = -8098021631160147514L;
    @ApiModelProperty(value = "商品id")
    private String goodsId;

    @ApiModelProperty(value = "上级商品(厂家商品)")
    private String refGoodsId;

}