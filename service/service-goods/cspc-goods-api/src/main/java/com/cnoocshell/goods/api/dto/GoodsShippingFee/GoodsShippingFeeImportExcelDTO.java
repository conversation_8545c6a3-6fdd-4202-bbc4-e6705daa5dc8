package com.cnoocshell.goods.api.dto.GoodsShippingFee;

import cn.hutool.core.annotation.Alias;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class GoodsShippingFeeImportExcelDTO {

    @Alias(value = "SAP物料编号")
    private String sapMaterialCode;

    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    @Alias(value = "省名称")
    private String provinceName;

    @ApiModelProperty(value = "市编码")
    private String cityCode;

    @Alias(value = "市名称")
    private String cityName;

    @ApiModelProperty(value = "区编码")
    private String districtCode;

    @Alias(value = "区/县/镇/街道名称")
    private String districtName;

    @Alias(value = "包装方式")
    private String pack;

    @Alias(value = "包装费")
    private String packFee;

    @Alias(value = "运费")
    private String shippingFee;

    @Alias(value = "有效开始日期")
    private String effectStartDate;

    @Alias(value = "有效截止日期")
    private String effectEndDate;
}
