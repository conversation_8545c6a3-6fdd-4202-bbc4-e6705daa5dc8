package com.cnoocshell.goods.api.service;

import com.cnoocshell.common.result.ItemResult;
import com.cnoocshell.common.result.PageData;
import com.cnoocshell.goods.api.dto.*;
import com.cnoocshell.goods.api.dto.base.PageQuery;
import com.cnoocshell.goods.api.dto.goods.GoodCodesListDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsAndCategoryInfoResultDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsDataListDTO;
import com.cnoocshell.goods.api.dto.goods.GoodsListDTO;
import com.cnoocshell.goods.api.enums.GoodsStatusEnum;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * @Created锛�Sat Sep 08 17:24:36 CST 2018
 * @Author: <EMAIL>
 * @Version:2
 * @Description:GoodsController
 */
@Api(tags = {"IGoodsService"}, description = "商品服务")
@FeignClient(name ="cspc-service-goods")
public interface IGoodsService {

	@PostMapping(value = "/goods/update", consumes = "application/json")
	ItemResult<GoodsDTO> update(@RequestBody GoodsDTO goodsDTO, @RequestParam("operator") String operator);

	@PostMapping(value = "/goods/delete", consumes = "application/json")
	ItemResult<Boolean> delete(@RequestParam("goodsIds") String goodsIds,
			@RequestParam("operator") String operator);

	@PostMapping(value = "/goods/create", consumes = "application/json")
	ItemResult<GoodsDTO> create(@RequestBody GoodsDTO goodsDTO, @RequestParam("operator") String operator);

	@PostMapping(value = "/goods/createList", consumes = "application/json")
	ItemResult<List<GoodsDTO>> createList(@RequestBody GoodsDTO goodsDTO, @RequestParam("operator") String operator);
	
	@PostMapping(value = "/goods/updateStatus", consumes = "application/json")
	ItemResult<String> updateStatus(@RequestParam("goodsId") String goodsId,
			@RequestParam("goodsStatus") Integer goodsStatus, @RequestParam("operator") String operator);

	@GetMapping( "/goods/getGoodsInfo")
	ItemResult<GoodsDTO> getGoodsInfo(@RequestParam("goodsId") String goodsId);

	@GetMapping( "/goods/findBaseGoodsLikeName")
	ItemResult<List<GoodsDTO>> findBaseGoodsLikeName(@RequestParam("name") String name, @RequestParam("categoryType") Integer categoryType);

	@GetMapping( "/goods/findBaseGoodsLikeName")
	ItemResult<List<GoodsDTO>> findBaseGoodsLikeName(@RequestParam("name") String name, @RequestParam("categoryType") Integer categoryType,@RequestParam("seller") String seller);

	@PostMapping(value = "/goods/goodsCommonQuery", consumes = "application/json")
	ItemResult<PageData<GoodsDTO>> goodsCommonQuery(@RequestBody PageQuery<GoodsQueryCondDTO> pageQuery);

	/**
	 * 查询有效的用户商品
	 * 
	 * @param keywords
	 * @return
	 */
	@GetMapping( "/goods/findUserGoods")
	ItemResult<List<GoodsDTO>> findUserGoods(@RequestParam("name") String name,
			@RequestParam("keywords") String keywords, @RequestParam("sellerId") String sellerId,
			@RequestParam("categoryType") Integer categoryType);

	/**
	 * 查询有效的用户商品 Like Name
	 * @return
	 */
	@GetMapping( "/goods/findUserGoodsLikeName")
	ItemResult<List<GoodsDTO>> findUserGoodsLikeName(@RequestParam("name") String name,
			@RequestParam("sellerId") String sellerId, @RequestParam("categoryType") Integer categoryType);

	@GetMapping( "/goods/findGoods")
	ItemResult<PageInfo<GoodsDTO>> findGoods(@RequestParam("name") String name,
			@RequestParam("goodsStatus") Integer goodsStatus, @RequestParam("categoryType") Integer categoryType,
			@RequestParam("goodsType") Integer goodsType, @RequestParam("category") String category,
			@RequestParam("sellerId") String sellerId, @RequestParam("keywords") String keywords,
			@RequestParam("pageSize") int pageSize, @RequestParam("pageNum") int pageNum);

	/**
	 * 获取商品属性
	 * 
	 * @param goodsId
	 * @param attriType
	 *            属性类型,1-基本表属性 ，2-扩展表属性， null-全部
	 * @return
	 */
	@GetMapping( "/goods/getAttrValByGoodsId")
	ItemResult<List<GoodsAttributeDTO>> getAttrValByGoodsId(@RequestParam("goodsId") String goodsId,
			@RequestParam("attriType") Integer attriType);

	/**
	 * 获取商品（SPU,SKU）分类属性
	 * @param goodsId
	 * @return
	 */
	@GetMapping( "/goods/getCategoryAttrByGoodsId")
	ItemResult<List<GoodsCategoryAttrDTO>> getCategoryAttrByGoodsId(@RequestParam("goodsId") String goodsId);

	/**
	 * 通过spu获取sku
	 * @param goodsQueryDTO
	 * @return
	 */
    @PostMapping(value = "/goods/findSkuBySpuAndAttr", consumes = "application/json")
	ItemResult<List<GoodsDTO>> findSkuBySpuAndAttr(@RequestBody GoodsQueryDTO goodsQueryDTO);
    
	/**
	 * 根据属性名称查询属性值
	 * 
	 * @param attrName
	 * @param categoryType
	 * @return
	 */
	@GetMapping( "/goods/getAttrValByName")
	ItemResult<List<GoodsAttributeDTO>> getAttrValByName(@RequestParam("attrName") String attrName,
			@RequestParam("categoryType") Integer categoryType);

	/**
	 * 查询商品单位换算信息
	 * 
	 * @param goodsId
	 * @return
	 */
	@GetMapping( "/goods/getUnitConverInfo")
	ItemResult<List<UnitConverDTO>> getUnitConverInfo(@RequestParam("goodsId") String goodsId);

	/**
	 * 获取分类的属性值
	 * 
	 * @param category
	 * @param categoryType
	 * @return
	 */
	@GetMapping( "/goods/getCategoryAttrVals")
	ItemResult<Map<String, List<GoodsAttributeDTO>>> getCategoryAttrVals(
			@RequestParam(name="category",required=false) String category, @RequestParam(name="categoryType",required=false)Integer categoryType);
	
	
	/**
	 * 查询有效的用户和平台商品
	 * @return
	 */
	@GetMapping( "/goods/findUserAndBaseGoodsLikeName")
	ItemResult<List<GoodsDTO>> findUserAndBaseGoodsLikeName(@RequestParam("name") String name,  @RequestParam("sellerId") String sellerId );

	@PostMapping(value = "/goods/enableBaseGoods", consumes = "application/json")
	ItemResult<List<String>> enableBaseGoods(@RequestBody List<String> arg0,@RequestParam("arg1") String arg1);

	@PostMapping(value = "/goods/disableBaseGoods", consumes = "application/json")
	ItemResult<List<String>> disableBaseGoods(@RequestBody List<String> arg0,@RequestParam("arg1") String arg1);

	@PostMapping(value = "/goods/pageBaseGoods", consumes = "application/json")
	ItemResult<PageInfo<BaseGoodsDTO>> pageBaseGoods(@RequestBody PageBaseGoodsDTO arg0);

	@PostMapping(value = "/goods/getGoodsDetail", consumes = "application/json")
	ItemResult<GoodsPlatformDTO> getGoodsDetail(@RequestParam("arg0") String arg0);

	@PostMapping(value = "/goods/deleteGoodsPlatform", consumes = "application/json")
	ItemResult<List<String>> deleteGoodsPlatform(@RequestBody List<String> arg0,@RequestParam("arg1") String arg1);

	@PostMapping(value = "/goods/enableGoodsPlatform", consumes = "application/json")
	ItemResult<List<String>> enableGoodsPlatform(@RequestBody List<String> arg0,@RequestParam("arg1") String arg1);

	@PostMapping(value = "/goods/disableGoodsPlatform", consumes = "application/json")
	ItemResult<List<String>> disableGoodsPlatform(@RequestBody List<String> arg0,@RequestParam("arg1") String arg1);

	@PostMapping(value = "/goods/pageGoodsPlatform", consumes = "application/json")
	ItemResult<PageInfo<GoodsPlatformDTO>> pageGoodsPlatform(@RequestBody PageGoodsPlatformDTO arg0);

	@PostMapping(value = "/goods/createSellerGoods", consumes = "application/json")
	ItemResult<Boolean> createSellerGoods(@RequestBody SellerGoodsDTO arg0, @RequestParam("arg1") String arg1);

	@PostMapping(value = "/goods/updateSellerGoods", consumes = "application/json")
	ItemResult<Boolean> updateSellerGoods(@RequestBody SellerGoodsDTO arg0,@RequestParam("arg1") String arg1);

	@PostMapping(value = "/goods/getSellerGoodsDetail", consumes = "application/json")
	ItemResult<SellerGoodsDTO> getSellerGoodsDetail(@RequestParam("arg0") String arg0);

	@PostMapping(value = "/goods/deleteSellerGoods", consumes = "application/json")
	ItemResult<List<String>> deleteSellerGoods(@RequestBody List<String> arg0,@RequestParam("arg1") String arg1);

	@PostMapping(value = "/goods/pageSellerGoods", consumes = "application/json")
	ItemResult<PageInfo<SellerGoodsDTO>> pageSellerGoods(@RequestBody PageSellerGoodsDTO arg0);

	@PostMapping(value = "/goods/pageGoods", consumes = "application/json")
	ItemResult<PageInfo<SellerGoodsDTO>> pageGoods(@RequestBody PageSellerGoodsDTO arg0);

	@PostMapping(value = "/goods/findBaseGoodsAttribute", consumes = "application/json")
	ItemResult<List<CategoryAttributeDTO>> findBaseGoodsAttribute(@RequestParam("arg0") String arg0);

	@PostMapping(value = "/goods/findBaseGoodsLikeCategoryCode", consumes = "application/json")
	ItemResult<List<GoodsDTO>> findBaseGoodsLikeCategoryCode(@RequestBody GoodsQueryByCateCodeDTO arg0);

	@PostMapping(value= "/goods/findSellerGoodsLikeCategoryCode", consumes = "application/json")
	ItemResult<List<GoodsDTO>> findSellerGoodsLikeCategoryCode(@RequestBody GoodsQueryByCateCodeDTO arg0);

	@PostMapping(value= "/goods/findPurchaseGoodsLikeCategoryCode", consumes = "application/json")
	ItemResult<List<GoodsDTO>> findPurchaseGoodsLikeCategoryCode(@RequestBody GoodsQueryByCateCodeDTO arg0);

	@GetMapping( "/goods/findRefferGoodsLikeName")
	ItemResult<List<GoodsDTO>> findRefferGoodsLikeName(@RequestParam("arg0") String arg0,@RequestParam("arg1") String arg1);

	@PostMapping(value= "/goods/findGoodsSimpleByIds", consumes = "application/json")
	ItemResult<List<GoodsSimpleDTO>> findGoodsSimpleByIds(@RequestBody List<String> goodsIds);

	@GetMapping( "/goods/getGoodsInfoByGoodsCode")
	ItemResult<GoodsDTO> getGoodsInfoByGoodsCode(@RequestParam("goodsCode") String goodsCode);

	@PostMapping(value= "/goods/findGoodsSimpleByCodes", consumes = "application/json")
	ItemResult<List<GoodsSimpleDTO>> findGoodsSimpleByCodes(@RequestBody List<String> goodsCodes);

	@PostMapping(value= "/goods/queryGoodsNameByGoodsCode", consumes = "application/json")
	List<GoodsNameInfoDTO> queryGoodsNameByGoodsCode(@RequestBody GoodsCodeInfoDTO goodsCodeInfoDTO);

	@PostMapping(value= "/goods/getGoodsListByGoodsCode")
	ItemResult<GoodsListDTO> getGoodsListByGoodsCode(@RequestBody GoodsListDTO dto);

	@PostMapping(value= "/goods/getPackSalesGroupByGoodsCode")
	ItemResult<List<GoodsDataListDTO>> getPackSalesGroupByGoodsCode(@RequestBody GoodCodesListDTO dto);

	@GetMapping(value= "/goods/findGoodsSimpleByLikeSapMaterialCode")
	ItemResult<List<GoodsSimpleDTO>> findGoodsSimpleByLikeSapMaterialCode(@RequestParam("sapMaterialCode")String sapMaterialCode);


	/**
	 * {@link GoodsStatusEnum}
	 */
	@ApiOperation(value = "根据商品名称模糊匹配商品信息",notes = "goodsStatus 商品状态 为空则不匹配商品状态 参考GoodsStatusEnum")
	@GetMapping("/goods/queryGoodsByLikeName")
	List<GoodsSimpleDTO> queryGoodsByLikeName(@RequestParam String name,@RequestParam(required = false) Integer goodsStatus);

	@PostMapping("/goods/queryGoodsAndCategoryInfoByGoodsCodes")
	List<GoodsAndCategoryInfoResultDTO> queryGoodsAndCategoryInfoByGoodsCodes(@RequestBody List<String> goodsCodes);
}
