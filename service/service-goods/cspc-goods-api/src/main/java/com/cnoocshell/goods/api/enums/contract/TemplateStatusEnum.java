package com.cnoocshell.goods.api.enums.contract;

import lombok.Getter;

@Getter
public enum TemplateStatusEnum {
    ENABLE(1,"启用"),
    DISABLE(2,"禁用");
    /** 枚举值 */
    private final Integer code;

    /** 枚举描述 */
    private final String message;
    private TemplateStatusEnum(Integer code,String message){
        this.code = code;
        this.message = message;
    }

    public static TemplateStatusEnum getByCode(Integer code) {
        for (TemplateStatusEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }
}
