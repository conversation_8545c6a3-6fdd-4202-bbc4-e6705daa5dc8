package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("确认资源仓库信息")
public class ConfirmResourceStoreDTO {
    /**
     * 是否有货 1有 0无
     */
    @ApiModelProperty(value = "是否有货")
    private Integer isHaveGoods;

    /**
     * 仓库信息
     */
    @ApiModelProperty(value = "仓库信息")
    List<StoreDTO> storeDTOList;
}
