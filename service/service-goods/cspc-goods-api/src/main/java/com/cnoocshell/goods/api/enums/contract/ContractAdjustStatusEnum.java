package com.cnoocshell.goods.api.enums.contract;

import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @created 17:25 20/08/2019
 */
@Getter
public enum ContractAdjustStatusEnum {

    DRAFT(-1, "草稿"),

    UN_EXECUTE(0, "未执行"),

    EXECUTED(1, "已执行"),

    REVOKE(2, "已撤销"),
    // 2020.6.23 合同批量调价需要
    EXECUTE_ERROR(3, "执行出错"),
    // 2020.7.9 执行中
    EXECUTE_DOING(5, "执行中")
    ;
    private Integer code;

    private String message;

    ContractAdjustStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
