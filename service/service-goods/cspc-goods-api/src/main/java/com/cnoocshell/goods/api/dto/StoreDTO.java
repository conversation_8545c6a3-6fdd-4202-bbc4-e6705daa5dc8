package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("仓库信息")
public class StoreDTO {
    /**
     * 资源Id
     */
    @ApiModelProperty(value = "资源ID")
    private String resourceId;

    /**
     * 仓库Id
     */
    @ApiModelProperty(value = "仓库ID")
    private String storeId;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String storeName;

    /**
     * 仓库类型:
     * CENTRAL_WAREHOUSE("0140100", "中心仓"),
     * STORE("0140200", "门店"),
     * TRANSFER_WAREHOUSE("0140300", "中转库"),
     * FACTORY_BASE("0140400", "厂家基地")
     */
    @ApiModelProperty(value = "仓库类型")
    private String storeType;

    /**
     * 仓库详细地址
     */
    @ApiModelProperty(value = "仓库详细地址")
    private String storeAddress;

    /**
     * 仓库所在省地址
     */
    @ApiModelProperty(value = "仓库所在省地址")
    private String province;

    /**
     * 仓库所在市地址
     */
    @ApiModelProperty(value = "仓库所在市地址")
    private String city;

    /**
     * 仓库所在区地址
     */
    @ApiModelProperty(value = "仓库所在区地址")
    private String district;

    /**
     * 仓库管理员
     */
    @ApiModelProperty(value = "仓库管理员")
    private String administrator;

    /**
     * 仓库管理员联系电话
     */
    @ApiModelProperty(value = "仓库管理员联系电话")
    private String administratorPhone;

    /**
     * 是否默认仓库
     */
    @ApiModelProperty(value = "是否默认仓库")
    private Integer storeDefault;

}
