package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.util.List;

@Data
@ApiModel("属性")
public class AttributeDTO  implements Comparable<AttributeDTO>{
    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String attributeName;

    /**
     * 属性编号
     */
    @ApiModelProperty(value = "性编号")
    @Pattern(regexp = "^\\d+$",message = "查询条件有误")
    private String attributeNo;

    /**
     * 属性值集
     */
    @ApiModelProperty(value = "属性值集")
//    @Valid
    private List<AttributeValueDTO> attributeValueDTOs;

    @Override
    public int compareTo(AttributeDTO o) {
        return this.getAttributeNo().compareTo(o.getAttributeNo());
    }

}
