package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;

@Data
@ApiModel("属性值")
public class AttributeValueDTO {
    /**
     * 属性值编号
     */
    @ApiModelProperty(value = "属性值编号")
	@Pattern(regexp = "^\\d*$",message = "查询条件有误")
    private String attributeValueNo;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attributeValue;

    /**
     * 是否选中
     */
    @ApiModelProperty(value = "是否选中")
    private Boolean selected;
}
