package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel("上架操作对象")
@Data
public class ManualOnSaleDTO implements Serializable {
    private static final long serialVersionUID = -7969804294291917933L;

    @ApiModelProperty("上架资源id")
    private List<String> resourceIds;

    @ApiModelProperty("资源对应的卖家id")
    private String sellerId;

    @ApiModelProperty("操作人id")
    private String operator;
}
