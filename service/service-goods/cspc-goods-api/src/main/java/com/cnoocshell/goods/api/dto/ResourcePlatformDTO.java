package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("平台资源DTO")
public class ResourcePlatformDTO {
    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id")
    private String resourceId;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String goodsId;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private String goodsType;

    /**
     * 商品分类字符串
     */
    @ApiModelProperty(value = "商品分类字符串")
    private String goodsTypeStr;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDescribe;

    /**
     * 台班费规则ID
     */
    @ApiModelProperty(value = "台班费规则ID")
    private String machineRuleId;


    /**
     * 空载费规则Id
     */
    @ApiModelProperty(value = "空载费规则Id")
    private String emptyLoadRuleId;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    /**
     * 资源code
     */
    @ApiModelProperty(value = "资源code")
    private String resourceCode;

    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人")
    private String createUser;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String storeName;

    /**
     * 仓库类型
     */
    @ApiModelProperty(value = "仓库类型")
    private String storeType;

    /**
     * 仓库详细地址
     */
    @ApiModelProperty(value = "仓库详细地址")
    private String storeAddress;

    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private String saleArea;

    /**
     * 总销售数量(上架数量)
     */
    @ApiModelProperty(value = "总销售数量")
    private BigDecimal saleNum;

    /**
     * 可售数量
     */
    @ApiModelProperty(value = "可售数量")
    private BigDecimal cansaleNum;

    /**
     * 锁定数量
     */
    @ApiModelProperty(value = "锁定数量")
    private BigDecimal orderLockNum;

    /**
     * 到位价
     */
    @ApiModelProperty(value = "到位价")
    private BigDecimal arrivePrice;

    /**
     * 出厂价
     */
    @ApiModelProperty(value = "出厂价")
    private BigDecimal factoryPrice;

    /**
     * 价格
     */
    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    /**
     * 计价单位
     */
    @ApiModelProperty(value = "计价单位")
    private String priceUnit;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;

    /**
     * 卖家名称
     */
    @ApiModelProperty(value = "卖家名称")
    private String sellerName;

    /**
     * 币种
     * CNY("CNY", "人民币"),
     * USD("USD", "美元"),
     * HKD("HKD", "港币");
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 计价方式
     * (1, "到位价"),
     * (2, "出厂价");
     */
    @ApiModelProperty(value = "计价方式")
    private String priceWay;

    /**
     * 定时上架时间
     */
    @ApiModelProperty(value = "定时上架时间")
    private Date fixUptime;

    /**
     * 定时下架时间
     */
    @ApiModelProperty(value = "定时下架时间")
    private Date fixDowntime;

    /**
     * 上架时间
     */
    @ApiModelProperty(value = "上架时间")
    private Date upTime;

    /**
     * 下架时间
     */
    @ApiModelProperty(value = "下架时间")
    private Date downTime;

    /**
     * 资源状态:
     * RES_STATUS100("100", "已挂牌"),
     * RES_STATUS200("200", "已撤牌"),
     * RES_STATUS300("300", "撤牌处理中"),
     * RES_STATUS400("400", "未上架"),
     * RES_STATUS500("500", "待审批");
     */
    @ApiModelProperty(value = "资源状态")
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
