package com.cnoocshell.goods.api.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum OperateTypeEnum {
    CREATE("create", "创建"),
    UPDATE("update", "修改"),
    DELETE("delete", "删除"),
    QUERY("query", "查询");

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>OperateTypeEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    private OperateTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public String code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return OperateTypeEnum
     */
    public static OperateTypeEnum getByCode(String code) {
        for (OperateTypeEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }

    /**
     * 获取全部枚举
     *
     * @return List<OperateTypeEnum>
     */
    public List<OperateTypeEnum> getAllEnum() {
        return new ArrayList<>(Arrays.asList(values()));
    }

    /**
     * 获取全部枚举值
     *
     * @return List<String>
     */
    public List<String> getAllEnumCode() {
        List<String> list = new ArrayList<>();
        for (OperateTypeEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }
}
