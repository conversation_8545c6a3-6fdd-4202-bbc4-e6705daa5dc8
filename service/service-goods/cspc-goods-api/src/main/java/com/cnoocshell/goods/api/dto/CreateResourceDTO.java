package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Data
@ApiModel("创建资源")
public class CreateResourceDTO {

    /**
     * 品类商品ID
     */
    @ApiModelProperty(value = "品类商品ID")
    private String spuId;
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;
    /**
     * 挂牌规则JSON字符串
     */
    @ApiModelProperty(value = "挂牌规则JSON字符串")
    private String priceRule;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    private List<String> payWays;
    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;
    /**
     * 价格描述
     */
    @ApiModelProperty(value = "价格描述")
    private String priceDescribe;
    /**
     * 优先定价方式，1：卖家配送优先 2：平台配送优先
     */
    @ApiModelProperty(value = "优先定价方式")
    private String priceWay;
    /**
     * 是否可议价
     */
    @ApiModelProperty(value = "是否可议价")
    private Boolean ifProtocolPrice;

    /**
     * 卖家ID
     */
    @ApiModelProperty(value = "卖家ID")
    private String sellerId;
    /**
     * 销售员ID
     */
    @ApiModelProperty(value = "销售员ID")
    private String salesId;
    /**
     * 组织机构ID
     */
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 容差类型， 100绝对容差 200相对容差
     */
    @ApiModelProperty(value = "容差类型")
    private String toleranceType;
    /**
     * 容差
     */
    @ApiModelProperty(value = "容差")
    private BigDecimal tolerance;
    /**
     * 单笔最大购买量
     */
    @ApiModelProperty(value = "单笔最大购买量")
    private BigDecimal ordermaxNum;
    /**
     * 单笔最小购买量
     */
    @ApiModelProperty(value = "单笔最小购买量")
    private BigDecimal orderminNum;
    /**
     * 单笔最小变动量
     */
    @ApiModelProperty(value = "单笔最小变动量")
    private BigDecimal orderminchangeNum;
    /**
     * 单日最大购买量
     */
    @ApiModelProperty(value = "单日最大购买量")
    private BigDecimal daymaxNum;

    /**
     * 是否允许自提
     */
    @ApiModelProperty(value = "是否允许自提")
    private Boolean ifTakeSelf;
    /**
     * 是否平台配送
     */
    @ApiModelProperty(value = "是否平台配送")
    private Boolean ifPlatformDelivery;
    /**
     * 是否卖家配送
     */
    @ApiModelProperty(value = "是否卖家配送")
    private Boolean ifSellerDelivery;
    /**
     * 搬运费类型
     */
    @ApiModelProperty(value = "搬运费类型")
    private String cartageRuleId;
    /**
     * 是否流量管控
     */
    @ApiModelProperty(value = "是否流量管控")
    private Boolean ifFlowcontrol;

    /**
     * 是否定时上架
     */
    @ApiModelProperty(value = "是否定时上架")
    private Boolean ifUp;
    /**
     * 定时上架时间
     */
    @ApiModelProperty(value = "定时上架时间")
    private Date fixUptime;
    /**
     * 是否定时下架
     */
    @ApiModelProperty(value = "是否定时下架")
    private Boolean ifDown;
    /**
     * 定时下架时间
     */
    @ApiModelProperty(value = "定时下架时间")
    private Date fixDowntime;
    /**
     * 交易开始时间
     */
    @ApiModelProperty(value = "交易开始时间")
    private Date tradeStarttime;
    /**
     * 交易结束时间
     */
    @ApiModelProperty(value = "交易结束时间")
    private Date tradeEndtime;
    /**
     * 支付有效期类型
     * (100, "不限"),
     * (200, "订单确认后");
     */
    @ApiModelProperty(value = "支付有效期类型")
    private String paydateType;
    /**
     * 支付有效期--小时
     */
    @ApiModelProperty(value = "支付有效期--小时")
    private Integer paydateLimit;
    /**
     * 提货有效期类型
     * (100, "不限"),
     * (200, "订单支付后");
     */
    @ApiModelProperty(value = "提货有效期类型")
    private String takedateType;
    /**
     * 提货有效期--天
     */
    @ApiModelProperty(value = "提货有效期--天")
    private Integer takedateLimit;
    /**
     * 提货有效期点--小时
     * （不选择传 -1）
     */
    @ApiModelProperty(value = "提货有效期点--小时")
    private Integer takedateHour;
    /**
     * 是否定时生效
     */
    @ApiModelProperty(value = "是否定时生效")
    private Boolean ifEffect;
    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    private Date effectTime;

    /**
     * 台班费规则ID
     */
    @ApiModelProperty(value = "台班费规则ID")
    private String machineRuleId;
    /**
     * 空载费规则ID
     */
    @ApiModelProperty(value = "空载费规则ID")
    private String emptyLoadRuleId;

    /**
     *  自动完成类型
     *  floatScale:浮动比例(百分比) fixedNumerical:固定数值(按计量单位计算)
     */
    @ApiModelProperty("自动完成类型")
    private String autoCompleteType;

    /**
     *  自动完成阀值
     */
    @ApiModelProperty("自动完成阀值")
    private BigDecimal autoCompleteThreshold;

    public Boolean getIfPlatformDelivery() {
        return Optional.ofNullable(ifPlatformDelivery).orElse(Boolean.FALSE);
    }

    public Boolean getIfSellerDelivery() {
        return Optional.ofNullable(ifSellerDelivery).orElse(Boolean.FALSE);
    }
}
