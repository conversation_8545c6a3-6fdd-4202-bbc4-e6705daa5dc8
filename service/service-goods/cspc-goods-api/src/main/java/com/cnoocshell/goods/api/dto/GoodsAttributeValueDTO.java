package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("商品属性值DTO")
public class GoodsAttributeValueDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String valueId;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String goodsId;

    /**
     * 商品属性id
     */
    @ApiModelProperty(value = "商品属性id")
    private String goodsAttriId;

    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String attriName;

    /**
     * 属性值编码
     */
    @ApiModelProperty(value = "属性值编码")
    private String attriValueCode;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attriValue;

    /**
     * 商品分类id，多id以','分开
     */
    @ApiModelProperty(value = "商品分类id，多id以','分开")
    private String category;

    /**
     * 支持商品类型 1 2 ，多类型以','分开
     */
    @ApiModelProperty(value = "支持商品类型,多类型以','分开")
    private String categoryType;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateUser;

    /**
     * 删除标志,1-删除，0-正常
     */
    @ApiModelProperty(value = "删除标志")
    private Boolean delFlg;

    /**
     * 对应商品表字段
     */
    @ApiModelProperty(value = "对应商品表字段")
    private String tableField;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Boolean isRequire;

    /**
     * 属性信息
     */
    @ApiModelProperty(value = "属性信息")
    private GoodsCategoryAttrDTO goodsCategoryAttrDTO;
}
