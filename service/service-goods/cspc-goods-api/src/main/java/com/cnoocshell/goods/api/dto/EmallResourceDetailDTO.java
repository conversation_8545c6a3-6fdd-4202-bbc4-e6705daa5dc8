package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("Emall资源详情")
public class EmallResourceDetailDTO {

    /**
     * 商品状态 200: 正常, 404: 找不到, 403: 下架
     */
    @ApiModelProperty(value = "商品状态")
    private String status;

    /**
     * 卖家id
     */
    @ApiModelProperty(value = "卖家id")
    private String sellerId;
    /**
     * 卖家名称
     */
    @ApiModelProperty(value = "卖家名称")
    private String sellerName;
    /**
     * 卖家简称
     */
    @ApiModelProperty(value = "卖家简称")
    private String sellerNickName;
    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id")
    private String resourceId;
    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;
    /**
     * 资源code
     */
    @ApiModelProperty(value = "资源code")
    private String resourceCode;
    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private String storeId;
    /**
     * 销售区域id
     */
    @ApiModelProperty(value = "销售区域id")
    private String saleAreaCode;
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String goodsId;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
    /**
     * 聚合商品id
     */
    @ApiModelProperty(value = "聚合商品id")
    private String goodsResourceId;
    /**
     * 运输品类id
     */
    @ApiModelProperty(value = "运输品类id")
    private String logisticsId;
    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private Integer goodsType;
    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private Integer goodsTypeStr;
    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDescribe;
    /**
     * 台班费规则ID
     */
    @ApiModelProperty(value = "台班费规则ID")
    private String machineRuleId;
    /**
     * 空载费规则Id
     */
    @ApiModelProperty(value = "空载费规则Id")
    private String emptyLoadRuleId;
    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 价格区间
     */
    @ApiModelProperty(value = "价格区间")
    private BigDecimal price;
    /**
     * 最小价格
     */
    @ApiModelProperty(value = "最小价格")
    private BigDecimal minPrice;
    /**
     * 最大价格
     */
    @ApiModelProperty(value = "最大价格")
    private BigDecimal maxPrice;

    /**
     * 出厂价区间
     */
    @ApiModelProperty(value = "出厂价区间")
    private BigDecimal factoryPrice;
    /**
     * 最小出厂价
     */
    @ApiModelProperty(value = "最小出厂价")
    private BigDecimal minFactoryPrice;
    /**
     * 最大出厂价
     */
    @ApiModelProperty(value = "最大出厂价")
    private BigDecimal maxFactoryPrice;

    /**
     * 到位价区间
     */
    @ApiModelProperty(value = "到位价区间")
    private BigDecimal arrivePrice;
    /**
     * 最小到位价
     */
    @ApiModelProperty(value = "最小到位价")
    private BigDecimal minArrivePrice;
    /**
     * 最大到位价
     */
    @ApiModelProperty(value = "最大到位价")
    private BigDecimal maxArrivePrice;

    /**
     * 是否可议价
     */
    @ApiModelProperty(value = "是否可议价")
    private Boolean ifProtocolPrice;
    /**
     * 价格描述
     */
    @ApiModelProperty(value = "价格描述")
    private String priceDescribe;
    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式")
    private Integer priceWay;
    /**
     * 优惠价格
     */
    @ApiModelProperty(value = "优惠价格")
    private BigDecimal discountPrice;
    /**
     * 计价单位
     */
    @ApiModelProperty(value = "计价单位")
    private String unit;
    /**
     * 计价单位编号
     */
    @ApiModelProperty(value = "计价单位编号")
    private String unitId;
    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String measureUnit;
    /**
     * 是否有货 1有 0无
     */
    @ApiModelProperty(value = "是否有货")
    private Integer isHaveGoods;
    /**
     * 可售数量
     */
    @ApiModelProperty(value = "可售数量")
    private BigDecimal cansaleNum;
    /**
     * 总销售数量
     */
    @ApiModelProperty(value = "总销售数量")
    private BigDecimal saleNum;

    /**
     * 单笔最大购买量
     */
    @ApiModelProperty(value = "单笔最大购买量")
    private BigDecimal ordermaxNum;

    /**
     * 单笔最小购买量
     */
    @ApiModelProperty(value = "单笔最小购买量")
    private BigDecimal orderminNum;

    /**
     * 单笔最小变动量
     */
    @ApiModelProperty(value = "单笔最小变动量")
    private BigDecimal orderminchangeNum;

    /**
     * 单日最大购买量
     */
    @ApiModelProperty(value = "单日最大购买量")
    private BigDecimal daymaxNum;
    /**
     * 配送方式
     */
    @ApiModelProperty(value = "配送方式")
    private List<String> deliveryWay;

    /**
     * 支持的支付方式
     */
    @ApiModelProperty(value = "支持的支付方式")
    private List<String> payWay;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片")
    private String imgs;
    /**
     * 商品信息
     */
    @ApiModelProperty(value = "商品信息")
    private List<AttributeDTO> searchAttributes;

    /**
     * 起售数量
     */
    @ApiModelProperty(value = "起售数量")
    private BigDecimal salesVolume;
    /**
     * 商品特点
     */
    @ApiModelProperty(value = "商品特点")
    private String memo1;
    /**
     * 适用范围
     */
    @ApiModelProperty(value = "适用范围")
    private String memo2;
    /**
     * 性能指标
     */
    @ApiModelProperty(value = "性能指标")
    private String memo3;
    /**
     * 使用指南
     */
    @ApiModelProperty(value = "使用指南")
    private String memo4;
    /**
     * 备注5
     */
    @ApiModelProperty(value = "备注5")
    private String memo5;
    /**
     * 备注6
     */
    @ApiModelProperty(value = "备注6")
    private String memo6;
    /**
     * app商品特点
     */
    @ApiModelProperty(value = "app商品特点")
    private String appMemo1;
    /**
     * app适用范围
     */
    @ApiModelProperty(value = "app适用范围")
    private String appMemo2;
    /**
     * app性能指标
     */
    @ApiModelProperty(value = "app性能指标")
    private String appMemo3;
    /**
     * app使用指南
     */
    @ApiModelProperty(value = "app使用指南")
    private String appMemo4;
    /**
     * app备注5
     */
    @ApiModelProperty(value = "app备注5")
    private String appMemo5;
    /**
     * app备注6
     */
    @ApiModelProperty(value = "app备注6")
    private String appMemo6;
    /**
     * 商品分类类型
     */
    @ApiModelProperty(value = "商品分类类型")
    private Integer categoryType;
    /**
     * 是否允许自提
     */
    @ApiModelProperty(value = "是否允许自提")
    private Boolean ifTakeSelf;
    /**
     * 是否平台配送
     */
    @ApiModelProperty(value = "是否平台配送")
    private Boolean ifPlatformDelivery;
    /**
     * 是否卖家配送
     */
    @ApiModelProperty(value = "是否卖家配送")
    private Boolean ifSellerDelivery;
    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;
    /**
     * 包装
     */
    @ApiModelProperty(value = "包装")
    private String pack;
    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specs;
    /**
     * 标号
     */
    @ApiModelProperty(value = "标号")
    private String mark;
    /**
     * 厂商
     */
    @ApiModelProperty(value = "厂商")
    private String manufacturer;
    /**
     * 是否有搬运规则
     */
    @ApiModelProperty(value = "是否有搬运规则")
    private Boolean isHaveCartageRule;
    /**
     * 是否有加价项
     */
    @ApiModelProperty(value = "是否有加价项")
    private Boolean isSupportAddItem = false;
    /**
     * 是否有钢筋选项
     */
    @ApiModelProperty(value = "是否有钢筋选项")
    private Boolean isSteelBar = false;
    /**
     * 是否有交易条款
     */
    @ApiModelProperty(value = "是否有交易条款")
    private Boolean isTransactionTerms = false;
    /**
     * 是否可以搬运
     */
    @ApiModelProperty(value = "是否可以搬运")
    private Boolean isCarry = false;
    /**
     * 是否可以选择业务员
     */
    @ApiModelProperty(value = "是否可以选择业务员")
    private Boolean isNeedSalesman = false;
    /**
     * 配送时间段类型 12h,30m
     */
    @ApiModelProperty(value = "配送时间段类型")
    private String deliveryTimeType = "12h";
    /**
     * 门店优惠
     */
    @ApiModelProperty(value = "门店优惠")
    private Boolean  isStoreDiscount = false;
    /**
     * 是否屏蔽价格
     */
    @ApiModelProperty(value = "是否屏蔽价格")
    private Boolean isHidePrice = false;
    /**
     * 可选择的仓库
     */
    @ApiModelProperty(value = "可选择的仓库")
    private List<StoreDTO> storeDTOS;
    /**
     * 可选择的销售单位
     */
    @ApiModelProperty(value = "可选择的销售单位")
    private List<UnitDTO> units;

    /**
     * 标识 0不是  1是
     */
    @ApiModelProperty(value = "标识")
    private Integer concreteFlag;

    /**
     * 加价项标识 0没有  1有
     */
    @ApiModelProperty(value = "加价项标识")
    private Integer addItemFlag;

    /**
     * 支持搬运标识 0 不支持 1 支持
     */
    @ApiModelProperty("支持搬运标识")
    private Integer supportCarryFlag;

    /**
     * 分类属性
     */
    @ApiModelProperty(value = "分类属性")
    private List<CategoryAttributeDTO> categoryAttributeDTOS;

    /**
     * 扩展属性属性值
     */
    @ApiModelProperty(value = "扩展属性属性值")
    private List<GoodsAttributeDTO> goodsAttributeDTOS;

    @ApiModelProperty(value = "是否被收藏(true 是)")
    private Boolean favoriteFlag;
}
