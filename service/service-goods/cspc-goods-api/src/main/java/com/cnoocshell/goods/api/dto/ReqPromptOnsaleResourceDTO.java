package com.cnoocshell.goods.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("提示已挂牌资源请求DTO")
public class ReqPromptOnsaleResourceDTO {
    /**
     * 商品品类ID
     */
    @ApiModelProperty(value = "商品品类ID")
    private String spuId;
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    /**
     * 用户查询区域列表
     */
    @ApiModelProperty(value = "用户查询区域列表")
    private List<String> regionList;

}
