package com.cnoocshell.common.config.url;

import com.cnoocshell.common.filter.LogParameterFilter;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Objects;

@Component
public class LogParameterRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {

        ServletRequestAttributes requestAttributes = getServletRequestAttributes();
        if (Objects.isNull(requestAttributes)) {
            return;
        }
        String userId = (String) requestAttributes.getAttribute(LogParameterFilter.USER_ID, 1);
        String username = (String) requestAttributes.getAttribute(LogParameterFilter.USER_NAME, 1);
        requestTemplate.header(LogParameterFilter.USER_ID, userId);
        requestTemplate.header(LogParameterFilter.USER_NAME, username);
    }

    private ServletRequestAttributes getServletRequestAttributes() {
        try {
            // 这种方式获取的HttpServletRequest是线程安全的
            return (ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes());
        } catch (Exception e) {
            return null;
        }
    }
}
