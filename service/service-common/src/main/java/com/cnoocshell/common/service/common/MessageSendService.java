package com.cnoocshell.common.service.common;

import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.dto.MessageDTO;
import com.cnoocshell.common.service.IMessageSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class MessageSendService implements IMessageSendService {

    private final KafkaTemplate<String,String> kafkaTemplate;

    @Override
    public void sendMessage(MessageDTO messageDTO) {
        log.info("kafka发送站内信 >>>>{}<<<<<",messageDTO);
        try{
            kafkaTemplate.send(MqTopicConstant.SEND_MESSAGE_TOPIC, JSONUtil.toJsonStr(messageDTO));
        }catch (Exception e){
            log.error("kafka发送消息异常： ",e);
        }
    }

}
