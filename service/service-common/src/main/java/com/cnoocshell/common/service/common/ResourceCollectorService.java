package com.cnoocshell.common.service.common;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.digest.MD5;
import com.cnoocshell.common.dto.ApiResourceDTO;
import com.cnoocshell.common.enums.AppNames;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import org.apache.commons.lang.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.mvc.condition.PatternsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.io.IOException;
import java.security.MessageDigest;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * api资源自动收集，后续授权优化可以使用
 */
@Slf4j
@Service
public class ResourceCollectorService implements CommandLineRunner {

    @Autowired
    private WebApplicationContext appContext;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private UUIDGenerator uuidGenerator;
    @Autowired
    private ObjectMapper objectMapper;
    @Value("${spring.application.name}")
    private String applicationName;
    @Value("${spring.profiles.active}")
    private String profile;

    private static final String HTTP_URL = "http://";
    private static final String URL_LOG = "url:{} 请求失败:{}";
    private static final String SEARCH_URL = HTTP_URL + AppNames.MICRO_SERVICE_BASE.getCode() + "/serveiceManager/findByPlatform";
    private static final String UPDATE_URL = HTTP_URL + AppNames.MICRO_SERVICE_BASE.getCode() + "/serveiceManager/updateService";
    private static final String INSERT_URL = HTTP_URL + AppNames.MICRO_SERVICE_BASE.getCode() + "/serveiceManager/createService";
    private static final String DELETE_URL = HTTP_URL + AppNames.MICRO_SERVICE_BASE.getCode() + "/serveiceManager/delService";
    private static final String MEMBER_PLATFORM_PLATFORM_ACCOUNT_ID = "37a1mc4ah82kxxqx1578vkff7";

    /**
     * 系统启动后自动收集api资源
     */
    @Override
    public void run(String... args) {
        //只收集web层controller
        if (AppNames.isMicroService(applicationName) || "prod".equals(profile)) {
            log.info("ignore micro service:{}", applicationName);
            return;
        }
        //只在本地或dev环境执行
        if (!"dev".equals(profile) && !"test".equals(profile)) {
            log.info("ignore env profile:{}", profile);
            return;
        }
        CompletableFuture.runAsync(() -> {
            try {
                // todo
                //collectionRequestMapping();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    protected void collectionRequestMapping() throws IOException {
        //获取所有的RequestMapping
        Map<String, HandlerMapping> allRequestMappings = BeanFactoryUtils.beansOfTypeIncludingAncestors(appContext,
                HandlerMapping.class, true, false);

        Map<String, ApiResourceDTO> resourceMap = getResourceMap();
        Set<String> ignoreClassSet = getIgnoreClassName();
        int existSize = resourceMap.size();
        log.info("exists resource:{}", existSize);
        AppNames appName = AppNames.getByCode(applicationName);
        Date now = new Date();
        List<ApiResourceDTO> addList = Lists.newArrayList();
        List<ApiResourceDTO> updateList = Lists.newArrayList();
        List<ApiResourceDTO> delList = Lists.newArrayList();
        Set<String> deleteIdSet = Sets.newHashSet(resourceMap.keySet());

        for (HandlerMapping handlerMapping : allRequestMappings.values()) {
            //本项目只需要RequestMappingHandlerMapping中的URL映射
            if (handlerMapping instanceof RequestMappingHandlerMapping) {
                RequestMappingHandlerMapping requestMappingHandlerMapping = (RequestMappingHandlerMapping) handlerMapping;
                Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
                extracted(handlerMethods, ignoreClassSet, appName, now, resourceMap, addList, updateList, deleteIdSet);
            }
        }

        if (!CollectionUtils.isEmpty(deleteIdSet)) {
            resourceMap.forEach((k, v) -> {
                if (deleteIdSet.contains(k)) {
                    log.debug("delete:{}.{} url:{}", v.getServiceClass(), v.getServiceFunction(), v.getUrl());
                    delList.add(v);
                }
            });
        }
        log.info("Api资源自动收集结果 addList:{},updateList:{},delList:{}", addList.size(), updateList.size(), deleteIdSet.size());
        printInsertSQL(addList);
        printUpdateSQL(updateList);
        printDelSQL(delList);
    }

    private static void extracted(Map<RequestMappingInfo, HandlerMethod> handlerMethods, Set<String> ignoreClassSet, AppNames appName, Date now, Map<String, ApiResourceDTO> resourceMap, List<ApiResourceDTO> addList, List<ApiResourceDTO> updateList, Set<String> deleteIdSet) {
        for (Map.Entry<RequestMappingInfo, HandlerMethod> requestMappingInfoHandlerMethodEntry : handlerMethods.entrySet()) {
            RequestMappingInfo requestMappingInfo = requestMappingInfoHandlerMethodEntry.getKey();
            HandlerMethod mappingInfoValue = requestMappingInfoHandlerMethodEntry.getValue();
            PatternsRequestCondition patternsCondition = requestMappingInfo.getPatternsCondition();
            String controllerName = mappingInfoValue.getBeanType().getSimpleName();
            String requestMethodName = mappingInfoValue.getMethod().getName();
            ApiOperation methodAnnotation = mappingInfoValue.getMethodAnnotation(ApiOperation.class);
            if (patternsCondition == null || CollectionUtils.isEmpty(patternsCondition.getPatterns())) {
                log.warn("{}.{} requestMapping is empty", controllerName, requestMethodName);
                continue;
            }
            for (String requestUrl : patternsCondition.getPatterns()) {
                if (StringUtils.isBlank(requestUrl) ||
                        requestUrl.contains("anon") ||
                        requestUrl.contains("refresh") ||
                        "ApiResourceController".equals(controllerName) ||
                        "BasicErrorController".equals(controllerName) ||
                        "KaptchaController".equals(controllerName) ||
                        ignoreClassSet.contains(controllerName)
                ) {
                    log.info("{}.{} requestMapping:[{}] ignore", controllerName, requestMethodName, requestUrl);
                    continue;
                }
                ApiResourceDTO resource = getApiResourceDTO(appName, now, requestUrl, methodAnnotation, controllerName, requestMethodName);
                byte[] bytes = CharSequenceUtil.bytes(resource.getUrlApp() + resource.getUrl());
                String id = MD5.create().digestHex(bytes);
                ApiResourceDTO exists = resourceMap.get(id);
                extracted(resourceMap, addList, updateList, deleteIdSet, requestUrl, exists, controllerName, requestMethodName, resource, id);
            }
        }
    }

    @NotNull
    private static ApiResourceDTO getApiResourceDTO(AppNames appName, Date now, String requestUrl, ApiOperation methodAnnotation, String controllerName, String requestMethodName) {
        if (methodAnnotation == null || StringUtils.isBlank(methodAnnotation.value())) {
            log.error("{}.{} ApiOperation is empty.", controllerName, requestMethodName);
        }
        ApiResourceDTO resource = new ApiResourceDTO();
        resource.setServiceClass(controllerName);
        resource.setServiceFunction(requestMethodName);
        resource.setServiceName(requestMethodName);
        resource.setServiceCode("自己获取数据库递增值");
        resource.setUrl(requestUrl.startsWith("/") ? appName.getApiRoutePrefix() + requestUrl : appName.getApiRoutePrefix() + "/" + requestUrl);
        resource.setNeedConfusion("0");
        resource.setNeedLogin("1");
        resource.setUrlApp(appName.getPlatform());
        resource.setCreateUser(MEMBER_PLATFORM_PLATFORM_ACCOUNT_ID);
        resource.setUpdateUser(MEMBER_PLATFORM_PLATFORM_ACCOUNT_ID);
        resource.setCreateTime(now);
        resource.setUpdateTime(now);
        resource.setVersion(0L);
        resource.setDelFlg(false);
        resource.setUrlRemark(methodAnnotation == null ? "null" : methodAnnotation.value());
        return resource;
    }

    private static void extracted(Map<String, ApiResourceDTO> resourceMap, List<ApiResourceDTO> addList, List<ApiResourceDTO> updateList, Set<String> deleteIdSet, String requestUrl, ApiResourceDTO exists, String controllerName, String requestMethodName, ApiResourceDTO resource, String id) {
        if (exists == null) {
            log.debug("add:{}.{} url:{}", controllerName, requestMethodName, requestUrl);
            //如果不存在，则添加
            resource.setServiceId(id);
            addList.add(resource);
            resourceMap.put(id, resource);
        } else {
            if (StringUtils.isNotBlank(exists.getServiceCode()) &&
                    !StringUtils.equals(exists.getUrlRemark(), resource.getUrlRemark()) &&
                    StringUtils.isNotBlank(resource.getUrlRemark()) &&
                    !"null".equalsIgnoreCase(resource.getUrlRemark())
            ) {
                //如果存在 且挨批描述有更新
                exists.setUrlRemark(resource.getUrlRemark());
                updateList.add(exists);
            }
            //剔除存在的，保留下来的用于删除判断
            deleteIdSet.remove(id);
        }
    }

    protected void printInsertSQL(List<ApiResourceDTO> list) {
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

                HttpEntity<ApiResourceDTO> request = new HttpEntity<>(item, headers);
                ApiResourceDTO result = null;
                try {
                    result = restTemplate.postForObject(INSERT_URL, request, ApiResourceDTO.class);
                } catch (Exception e) {
                    log.warn(URL_LOG, INSERT_URL, e.getMessage(), e);
                    log.info("自动注册资源失败,请手工执行: INSERT INTO `ba_res_service` (`service_id`, `service_code`, `service_name`, `url`, `service_class`, `service_function`, `url_remark`, `need_confusion`, `need_login`, `url_app`, `mudule_l1`, `mudule_l2`, `del_flg`, `create_user`, `update_user`, `create_time`, `update_time`, `version`) VALUES ('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', NULL, NULL, '0', '{}', '{}', '{}', '{}', '0');",
                            item.getServiceId(), item.getServiceCode(), item.getServiceName(), item.getUrl(), item.getServiceClass(), item.getServiceFunction(), item.getUrlRemark(), item.getNeedConfusion(), item.getNeedLogin(), item.getUrlApp(), item.getCreateUser(), item.getCreateUser(), item.getCreateTime(), item.getUpdateTime());
                }
                if (result != null) {
                    log.info("成功自动注册资源: INSERT INTO `ba_res_service` (`service_id`, `service_code`, `service_name`, `url`, `service_class`, `service_function`, `url_remark`, `need_confusion`, `need_login`, `url_app`, `mudule_l1`, `mudule_l2`, `del_flg`, `create_user`, `update_user`, `create_time`, `update_time`, `version`) VALUES ('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', NULL, NULL, '0', '{}', '{}', '{}', '{}', '0');",
                            result.getServiceId(), result.getServiceCode(), result.getServiceName(), result.getUrl(), result.getServiceClass(), result.getServiceFunction(), result.getUrlRemark(), result.getNeedConfusion(), result.getNeedLogin(), result.getUrlApp(), result.getCreateUser(), result.getCreateUser(), result.getCreateTime(), result.getUpdateTime());
                } else {
                    log.info("自动注册资源失败,请手工执行: INSERT INTO `ba_res_service` (`service_id`, `service_code`, `service_name`, `url`, `service_class`, `service_function`, `url_remark`, `need_confusion`, `need_login`, `url_app`, `mudule_l1`, `mudule_l2`, `del_flg`, `create_user`, `update_user`, `create_time`, `update_time`, `version`) VALUES ('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', NULL, NULL, '0', '{}', '{}', '{}', '{}', '0');",
                            item.getServiceId(), item.getServiceCode(), item.getServiceName(), item.getUrl(), item.getServiceClass(), item.getServiceFunction(), item.getUrlRemark(), item.getNeedConfusion(), item.getNeedLogin(), item.getUrlApp(), item.getCreateUser(), item.getCreateUser(), item.getCreateTime(), item.getUpdateTime());
                }
            });
        }
    }

    protected void printUpdateSQL(List<ApiResourceDTO> list) {
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

                ApiResourceDTO apiResourceDTO = new ApiResourceDTO();
                apiResourceDTO.setServiceId(item.getServiceId());
                apiResourceDTO.setUpdateUser(MEMBER_PLATFORM_PLATFORM_ACCOUNT_ID);
                apiResourceDTO.setUrlRemark(item.getUrlRemark());
                HttpEntity<ApiResourceDTO> request = new HttpEntity<>(apiResourceDTO, headers);
                Integer result = null;
                try {
                    result = restTemplate.postForObject(UPDATE_URL, request, Integer.class);
                } catch (Exception e) {
                    log.warn(URL_LOG, UPDATE_URL, e.getMessage(), e);
                    log.info("自动更新资源备注失败,请手工执行: UPDATE ba_res_service SET url_remark='{}' WHERE service_code='{}';", item.getUrlRemark(), item.getServiceCode());
                }
                if (result != null && result > 0) {
                    log.info("成功更新资源备注: UPDATE ba_res_service SET url_remark='{}' WHERE service_code='{}';", item.getUrlRemark(), item.getServiceCode());
                } else {
                    log.info("自动更新资源备注失败,请手工执行: UPDATE ba_res_service SET url_remark='{}' WHERE service_code='{}';", item.getUrlRemark(), item.getServiceCode());
                }
            });
        }
    }

    protected void printDelSQL(List<ApiResourceDTO> list) {
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

                MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
                map.add("id", item.getServiceId());
                map.add("operator", MEMBER_PLATFORM_PLATFORM_ACCOUNT_ID);

                HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);

                log.info("自动自动清理资源失败,请手工执行: UPDATE ba_res_service SET del_flg=1 WHERE service_code='{}';", item.getServiceCode());
                log.info("自动自动清理资源失败,请手工执行: UPDATE ba_res_call_relation SET del_flg=1 WHERE service_code='{}';", item.getServiceCode());
            });
        }
    }

    protected Set<String> getIgnoreClassName() {
        return Sets.newHashSet();
    }

    protected Map<String, ApiResourceDTO> getResourceMap() throws IOException {
        String platform = AppNames.getByCode(applicationName).getPlatform();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("platform", platform);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
        try {
            String urls = restTemplate.postForObject(SEARCH_URL, request, String.class);
            List<ApiResourceDTO> list = objectMapper.readValue(urls, new TypeReference<List<ApiResourceDTO>>() {
            });
            if (CollectionUtils.isEmpty(list)) {
                log.info(SEARCH_URL + "?platform=" + platform + " 查询结果为空");
                return Maps.newHashMap();
            }
            return list.stream().collect(Collectors.toMap(item -> getKey(platform, item), Function.identity(), (k1, k2) -> k2));
        } catch (IOException e) {
            log.warn(URL_LOG, SEARCH_URL, e.getMessage());
            throw e;
        }
    }

    protected String getKey(String platform, ApiResourceDTO item) {
        byte[] bytes = CharSequenceUtil.bytes(platform + item.getUrl());
        return MD5.create().digestHex(bytes);
    }
}
