package com.cnoocshell.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.File;
import java.util.List;
import java.util.Map;

@Data
public class EmailDTO {
    @ApiModelProperty(value = "邮件模板编码",notes = "参考com.cnoocshell.integration.enums.EmailTemplateEnum")
    private String emailTemplateCode;

    @ApiModelProperty("收件人邮件地址")
    private List<String> tos;

    @ApiModelProperty("抄送人邮件地址")
    private List<String> toCcs;

    @ApiModelProperty("附件")
    private Map<String, File> attachments;

    @ApiModelProperty("邮件模板参数")
    private Map<String,Object> templateParam;
}
