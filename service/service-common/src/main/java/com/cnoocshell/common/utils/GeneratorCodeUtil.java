package com.cnoocshell.common.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * @description GeneratorCodeUtil...
 */
@Slf4j
@Component
public class GeneratorCodeUtil {
    @Autowired
    @Qualifier("bizRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    private static final String CODE_KEY = "generate:sequence:code:";


    /**
     * 生成以天为组 自增编码 自增序列当天有效
     *
     * @param prefix 编码前缀 区别类型
     * @reutrn prefix + yyyyMMdd + 0001
     */
    public String generate(String prefix, int length) {
        String now = DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMATTER);
        String key = CharSequenceUtil.format("{}:{}", prefix, now);
        return prefix + now + this.incr(key, length);
    }

    /**
     * redis生成 length位自增流水号
     * 若未超出指定长度则前缀补0处理，若超出指定长度不做补0处理
     * @param key 自己设置，保存当前自增值
     * @return
     */
    public String incr(String key, int length) {
        RedisAtomicLong entityIdCounter = new RedisAtomicLong(CODE_KEY + key,
                Objects.requireNonNull(redisTemplate.getConnectionFactory()));
        Long increment = entityIdCounter.getAndIncrement();

        //初始设置过期时间
        if (Objects.equals(increment, 0L)) {
            //设置自增值过期时间, 当天晚上24点过期
            increment = 0L;
            entityIdCounter.expireAt(DateUtil.endOfDay(new Date()));
        }

        increment++;

        String incrementStr = String.valueOf(increment);

        if(CharSequenceUtil.length(incrementStr) > length)
            return incrementStr;

        return CharSequenceUtil.padPre(incrementStr, length, "0");
    }

}
