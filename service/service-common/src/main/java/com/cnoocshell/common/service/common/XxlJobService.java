package com.cnoocshell.common.service.common;

import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.dto.StopJobDTO;
import com.cnoocshell.common.dto.XxlJobDTO;
import com.cnoocshell.common.service.IXxlJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class XxlJobService implements IXxlJobService {
    private final KafkaTemplate<String, String> kafkaTemplate;


    @Override
    public void createXxlJob(XxlJobDTO param) {
        log.info("kafka发送创建自动任务消息开始 {}", param);
        try {
            kafkaTemplate.send(MqTopicConstant.CREATE_XXL_JOB_TOPIC, JSONUtil.toJsonStr(param));
        } catch (Exception e) {
            log.error("kafka发送创建自动任务消息异常 {}", param, e);
        }
        log.info("kafka发送创建自动任务消息结束 {}", param);
    }

    @Override
    public void stopXxlJob(Long jobId) {
        log.info("kafka发送停止自动任务消息开始 {}", jobId);
        try {
            kafkaTemplate.send(MqTopicConstant.STOP_XXL_JOB_TOPIC, String.valueOf(jobId));
        } catch (Exception e) {
            log.error("kafka发送停止自动任务消息异常 {}", jobId, e);
        }
        log.info("kafka发送停止自动任务消息结束 {}", jobId);
    }

    @Override
    public void destroyXxlJob(Long jobId) {
        log.info("kafka发送销毁自动任务消息开始 {}", jobId);
        try {
            kafkaTemplate.send(MqTopicConstant.DESTROY_XXL_JOB_TOPIC, String.valueOf(jobId));
        } catch (Exception e) {
            log.error("kafka发送销毁自动任务消息异常 {}", jobId, e);
        }
        log.info("kafka发送销毁自动任务消息结束 {}", jobId);
    }

    @Override
    public void stopXxlJob(StopJobDTO param) {
        log.info("kafka发送根据参数停止自动任务消息开始 {}", param);
        try {
            kafkaTemplate.send(MqTopicConstant.STOP_XXL_JOB_BY_PARAM_TOPIC, JSONUtil.toJsonStr(param));
        } catch (Exception e) {
            log.error("kafka发送根据参数停止自动任务消息异常 {}", param, e);
        }
        log.info("kafka发送根据参数停止自动任务消息结束 {}", param);
    }
}
