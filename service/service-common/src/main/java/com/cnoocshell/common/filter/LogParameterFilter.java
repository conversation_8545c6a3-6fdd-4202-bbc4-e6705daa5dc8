package com.cnoocshell.common.filter;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@Component
@WebFilter(urlPatterns = "/*", filterName = "LogParameterFilter")
public class LogParameterFilter extends HttpFilter {

    public final static String USER_NAME = "login_user_name";

    public final static String USER_ID = "login_user_id";

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("LogParameterFilter init");
    }

    @Override
    protected void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {

        String userId = request.getHeader(USER_ID);
        String username = request.getHeader(USER_NAME);

        if (username != null && username.trim().length() > 0) {
            MDC.put(USER_ID, userId);
            MDC.put(USER_NAME, username);

            ServletRequestAttributes requestAttributes = new ServletRequestAttributes(request);
            requestAttributes.setAttribute(LogParameterFilter.USER_ID, userId, 1);
            requestAttributes.setAttribute(LogParameterFilter.USER_NAME, username, 1);
            RequestContextHolder.setRequestAttributes(requestAttributes);
        }

        try {
            chain.doFilter(request, response);
        } finally {
            MDC.remove(USER_ID);
            MDC.remove(USER_NAME);
        }
    }

    @Override
    public void destroy() {
        log.info("destroy");
    }
}
