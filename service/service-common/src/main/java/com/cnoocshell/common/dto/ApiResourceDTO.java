package com.cnoocshell.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ApiResourceDTO implements Serializable {

    @ApiModelProperty("服务id")
    private String serviceId;
    /**
     * 服务code
     */
    @ApiModelProperty("服务code(后端生成)")
    private String serviceCode;
    /**
     * 服务名称
     */
    @ApiModelProperty("服务名称")
    private String serviceName;
    /**
     * 服务地址
     */
    @ApiModelProperty("服务地址")
    private String url;

    /**
     * 服务类型
     */
    @ApiModelProperty("服务类型")
    private String serviceClass;
    /**
     * 服务功能
     */
    @ApiModelProperty("服务功能")
    private String serviceFunction;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String urlRemark;

    /**
     * 是否需要混淆
     */
    @ApiModelProperty("是否需要混淆")
    private String needConfusion;

    /**
     * 是/否都支持
     */
    @ApiModelProperty("是/否都支持")
    private String needLogin;

    /**
     * 所属模块
     */
    @ApiModelProperty("所属模块(取值集JZ0001)")
    private String urlApp;

    /**
     * 数据版本
     */
    @ApiModelProperty("数据版本")
    private Long version;

    /**
     *
     */
    @ApiModelProperty("一级模块")
    private String muduleL1;

    /**
     *
     */
    @ApiModelProperty("二级模块")
    private String muduleL2;

    /**
     * 关联表查询使用(页面code)
     */
    @ApiModelProperty("关联表查询使用(页面code)")
    private String pageCode;

    private String createUser;

    private Date createTime;

    /**
     * 更新用户
     */
    @ApiModelProperty("更新用户")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("删除标识")
    private Boolean delFlg;

    @ApiModelProperty("当前页数")
    private Integer pageNum = 1;
    @ApiModelProperty("分页大小")
    private Integer pageSize = 10000;
}
