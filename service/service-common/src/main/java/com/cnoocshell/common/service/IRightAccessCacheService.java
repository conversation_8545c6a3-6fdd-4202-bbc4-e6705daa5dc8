package com.cnoocshell.common.service;


import com.cnoocshell.common.service.access.RightAccessCache;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 权限缓存接口
 */
public interface IRightAccessCacheService {

    /**
     * 取到一条权限角色数据
     * @param url
     * @return
     */
    RightAccessCache getRightAccess(String url);

    /**
     * 取得所有权限列表
     * @return
     */
    List<RightAccessCache> getAllRightAccess();

    /**
     * 链接是否存在
     * @param url
     * @return
     */
    boolean hasPermission(String url);

    /**
     * 判断是否包含此角色
     * @return
     */
    boolean hasRightAccess(String url, int role);

    boolean hasRightAccess(String url, int role,long[] roles);

    /**
     * 设置一条权限缓存
     */
    void setRightAccess(RightAccessCache cache);

    /**
     * 设置一条权限缓存
     *      权限组
     */
    void setRightAccess(String rul, int[] roleArray);
    void setRightAccess(Map<String,Set<Integer>> urlAndRoleMap);
    /**
     * 移除一条权限数据
     * @param role
     */
    void removePermission(String role);

}
