package com.cnoocshell.common.service;

import org.apache.ibatis.annotations.InsertProvider;
import tk.mybatis.mapper.annotation.RegisterMapper;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 17:17 12/09/2018
 */
@RegisterMapper
public interface MyInsertListMapper<T> {
    @InsertProvider(
            type = MySpecialProvider.class,
            method = "dynamicSQL"
    )
    int insertList(List<T> var1);
}