package com.cnoocshell.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("Xxl job 内部集成模型")
public class XxlJobDTO {
    @ApiModelProperty("执行器名称")
    private String appName;

    @ApiModelProperty("任务描述")
    @Size(max = 255, message = "限制字符长度不超过255")
    private String jobDesc;

    @ApiModelProperty("作者")
    private String author;

    @ApiModelProperty("调度类型 默认CRON")
    private String scheduleType = "CRON";

    @ApiModelProperty("调度配置 若调度类型未cron 则该字段未cron表达式")
    private String scheduleConf;

    @ApiModelProperty("调度过期策略")
    private String misfireStrategy = "DO_NOTHING";

    @ApiModelProperty("路由策略 默认第一个")
    private String executorRouteStrategy = "FIRST";

    @ApiModelProperty("执行器任务handler")
    private String executorHandler;

    @ApiModelProperty("执行器任务参数")
    private String executorParam;

    @ApiModelProperty("阻塞处理策略 默认SERIAL_EXECUTION单机串行")
    private String executorBlockStrategy = "SERIAL_EXECUTION";

    @ApiModelProperty("任务执行超时时间 单位秒")
    private Integer executorTimeout = 0;

    @ApiModelProperty("失败重试次数")
    private Integer executorFailRetryCount = 0;

    @ApiModelProperty("运行模式 默认 BEAN")
    private String glueType = "BEAN";

    @ApiModelProperty("glue 源代码")
    private String glueSource = "";

    private String glueRemark = "GLUE代码初始化";

    @ApiModelProperty("任务状态 1：启用 0停止 默认启用")
    private Integer triggerStatus = 1;

    public XxlJobDTO(String appName,String jobDesc,String cron,String executorHandler,String executorParam){
        this.appName = appName;
        this.jobDesc = jobDesc;
        this.executorHandler = executorHandler;
        this.executorParam = executorParam;
        this.scheduleConf = cron;

        this.author = "SYSTEM";
        this.scheduleType = "CRON";
        this.executorBlockStrategy = "SERIAL_EXECUTION";
        this.misfireStrategy = "DO_NOTHING";
        this.executorRouteStrategy = "FIRST";
        this.executorTimeout = 0;
        this.executorFailRetryCount = 0;

        this.glueType = "BEAN";
        this.glueSource = "";
        this.glueRemark = "GLUE代码初始化";
        this.triggerStatus = 1;
    }
}
