package com.cnoocshell.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Author: <EMAIL>
 * @Date: 26/12/2018 14:52
 * @DESCRIPTION:
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface AddLog {
    /**
     * 模块名称
     * @return
     */
    String moduleName() default "";

    /**
     * 操作方法
     * @return
     */
    String operMethod() default "";

    /**
     * 操作动作子方法
     * @return
     */
    String operSubMethod() default "";
    /**
     * sessionId所在参数列表中的位置
     * @return
     */
    int sessionIdIndex() default -1;

    /**
     * sessionId在对象的属性名称
     * @return
     */
    String sessionIdFieldName() default "";
    /**
     * 业务编号所在参数列表中的位置
     * @return
     */
    int businessNumberIndex() default -1;

    /**
     * 业务编号在对象的属性名称
     * @return
     */
    String businessNumberFieldName() default "";
    /**
     * 操作人所在参数列表中的位置
     * @return
     */
    int operatorIndex() default -1;

    /**
     * 操作人所在对象的属性名称
     * @return
     */
    String operatorFieldName() default "";

    /**
     * 操作人ip所在参数列表中的位置
     * @return
     */
    int operatorIPIndex() default -1;

    /**
     * 操作人ip所在对象的属性名称
     * @return
     */
    String operatorIPFieldName() default "";

    /**
     * 要加密字处理的参数
     * @return
     */
    int[] encryptedArgIndex() default {};
    /**
     * 要加密字处理的属性(不区分是哪个参数)
     * @return
     */
    String[] encryptedFieldName() default {};

    /**
     * 备注
     * @return
     */
    String remark() default "";
}
