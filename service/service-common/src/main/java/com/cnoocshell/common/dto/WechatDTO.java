package com.cnoocshell.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

// 订阅消息请求体（字段要求严格匹配微信文档）

/**
 * 官方请求的消息体示例
 * {
 *   "touser": "OPENID",
 *   "template_id": "TEMPLATEID",
 *   "page": "mp.weixin.qq.com",
 *   "miniprogram":{
 *              "appid":"APPID",
 *              "pagepath":"index?foo=bar"
 *   },
 *   "data": {
 *       "name1": {
 *           "value": "广州腾讯科技有限公司"
 *       },
 *       "thing8": {
 *           "value": "广州腾讯科技有限公司"
 *       },
 *        "time7": {
 *           "value": "2019年8月8日"
 *       }
 *      }
 * }
 *
 * 例如，模板的内容为
 *
 * 姓名: {{name01.DATA}}
 * 金额: {{amount01.DATA}}
 * 行程: {{thing01.DATA}}
 * 日期: {{date01.DATA}}
 * 则对应的json为
 *
 * {
 *   "touser": "OPENID",
 *   "template_id": "TEMPLATE_ID",
 *   "page": "index",
 *   "data": {
 *       "name01": {
 *           "value": "某某"
 *       },
 *       "amount01": {
 *           "value": "￥100"
 *       },
 *       "thing01": {
 *           "value": "广州至北京"
 *       },
 *       "date01": {
 *           "value": "2018-01-01"
 *       }
 *   }
 * }
 */
@Data
public class WechatDTO {

    @ApiModelProperty(value = "接收者openid")
    private String toUser;

    @ApiModelProperty(value = "订阅模板ID")
    private String templateId;

    @ApiModelProperty(value = "点击消息跳转的页面路径（支持带参数）。示例：index?foo=bar")
    private String page;

    @ApiModelProperty(value = "跳转小程序配置")
    private MiniProgram miniProgram;

    @ApiModelProperty(value = "模板内容字段。格式要求：key对应模板中的标识，value为具体内容")
    private Map<String, DataItem> data;

    @Data
    public static class MiniProgram {
        @ApiModelProperty(value = "小程序appid（若跳转小程序则必填）")
        private String appid;

        @ApiModelProperty(value = "所需跳转到的小程序页面路径")
        private String pagepath;
    }

    @Data
    public static class DataItem {
        @ApiModelProperty(value = "字段内容")
        private String value;

        public DataItem(String value) {
            this.value = value;
        }
    }
}
