package com.cnoocshell.common.service;

import com.cnoocshell.common.dto.InterfacePermissionDTO;

import java.util.List;

public interface IAuthorizeService {

    /**
     * 权限认证
     * @param url
     * @param roleCodes
     * @return
     */
    Boolean authorize(String url, List<String> roleCodes);

    boolean verifyPublic(String url, List<InterfacePermissionDTO> publicList);

    String getAuthKey(String requestUrl,List<String> roleCodes);

    boolean verifyPublic(String url);
}
