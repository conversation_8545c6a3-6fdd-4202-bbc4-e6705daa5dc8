package com.cnoocshell.common.config.redis;

import lombok.Data;

@Data
public class ObjectExpire  {

    private String key;
    private Long expire;

    public ObjectExpire(String key, Long expire){
        this.key = key;
        this.expire = expire;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ObjectExpire{");
        sb.append("key='").append(key).append('\'');
        sb.append(", expire=").append(expire);
        sb.append('}');
        return sb.toString();
    }
}
