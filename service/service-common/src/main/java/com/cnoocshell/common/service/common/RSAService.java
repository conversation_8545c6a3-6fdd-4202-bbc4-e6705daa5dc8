package com.cnoocshell.common.service.common;

import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.config.rsa.RSAConfig;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.Key;
import java.security.KeyFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;


@Service
@Slf4j
public class RSAService {

    @Autowired
    private RSAConfig rSAConfig;

    //加密
    public String getRsaEncrypt(String data) throws Exception {
        // 对公钥解密
        byte[] keyBytes = decryptBASE64(rSAConfig.getPublicKeyStr());
        // 取得公钥
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSAConfig.KEY_ALGORITHM);
        Key publicKey = keyFactory.generatePublic(x509KeySpec);
        // 对数据加密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] str = cipher.doFinal(data.getBytes());
        String result = encryptBASE64(str);
        return result;
    }

    //解密
    public String getRsaDecrypt(String str) throws Exception {
    	if(StringUtils.isBlank(rSAConfig.getPrivateKeyStr())) {
    		throw new BizException(BasicCode.DATA_NOT_EXIST,"RSA私钥");
    	}
    	byte[] data = this.decryptBASE64(str);
		// 对密钥解密
		byte[] keyBytes = this.decryptBASE64(rSAConfig.getPrivateKeyStr());
		// 取得私钥
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
		KeyFactory keyFactory = KeyFactory.getInstance(RSAConfig.KEY_ALGORITHM);
		Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
		// 对数据解密
		Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
		cipher.init(Cipher.DECRYPT_MODE, privateKey);
		byte[] doFinal = cipher.doFinal(data);
		String result = new String(doFinal);
		return result;
    }


    //获取公钥
    public String getPublicKey() {
        return this.rSAConfig.getPublicKeyStr();

    }

    @PostConstruct
    public void init() {
        try {
            String publicKeyUrl = rSAConfig.getPublicKeyUrl();
            String privateKeyUrl = rSAConfig.getPrivateKeyUrl();

	    	if(StringUtils.isBlank(publicKeyUrl) || StringUtils.isBlank(privateKeyUrl)) {
	    		return;
	    	}

            privateKeyRun(privateKeyUrl);

            publicKeyRun(publicKeyUrl);

        } catch (Exception e1) {
            log.error("初始化异常", e1);
        }
    }

    private void publicKeyRun(String publicKeyUrl) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                //定时读取配置公钥
                while (true) {
                    String publicKey = "";
                    if (publicKeyUrl.startsWith("http")) {
                        //网络地址
                        publicKey = readToStringByUrl(publicKeyUrl);
                    } else {
                        //文件系统
                        publicKey = readToStringByFilePath(publicKeyUrl);
                    }

                    if (StringUtils.isBlank(publicKey)) {
                        break;
                    }

                    rSAConfig.setPublicKeyStr(publicKey);
                    break;
                }
            }
        }, "init-publickey-thread").start();
    }

    private void privateKeyRun(String privateKeyUrl) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                //定时读取配置私钥
                while (true) {
                    String privateKey = "";
                    if (privateKeyUrl.startsWith("http")) {
                        //网络地址
                        privateKey = readToStringByUrl(privateKeyUrl);
                    } else {
                        //文件系统
                        privateKey = readToStringByFilePath(privateKeyUrl);
                    }

                    rSAConfig.setPrivateKeyStr(privateKey);
                    break;
                }
            }
        }, "init-privateKey-thread").start();
    }

    private byte[] decryptBASE64(String key) {
        return Base64Utils.decodeFromString(key);
    }

    private String encryptBASE64(byte[] bytes) {
        return Base64Utils.encodeToString(bytes);
    }


    public static String readToStringByUrl(String url) {
        try {
            URL fileUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) fileUrl.openConnection();
            //设置超时间为5秒
            conn.setConnectTimeout(5 * 1000);

            try(InputStream in = conn.getInputStream();InputStreamReader inReader = new InputStreamReader(in)){

                BufferedReader bReader = new BufferedReader(inReader);
                StringBuilder str = new StringBuilder("");
                String lineTxt = null;
                while ((lineTxt = bReader.readLine()) != null) {
                    str.append(lineTxt);
                }
                return str.toString().replace("-----BEGIN PUBLIC KEY-----", "")
                        .replace("-----END PUBLIC KEY-----", "")
                        .replace("-----BEGIN PRIVATE KEY-----", "")
                        .replace("-----END PRIVATE KEY-----", "")
                        .trim();
            }
        } catch (FileNotFoundException e) {
            log.error("没有找到文件：{}", url);
        } catch (Exception e) {
            log.error("读取秘钥文件异常,{}", e.getMessage());
        }
        return null;
    }


    public static String readToStringByFilePath(String filePath) {
    	if(StringUtils.isBlank(filePath)) {
    		return "";
    	}

        File file = new File(filePath);
        if (!file.exists()) {
            log.error("没有找到文件：" + filePath);
            return "";
        }
        try(FileInputStream in = new FileInputStream(file);
            InputStreamReader inReader = new InputStreamReader(in);
            BufferedReader bReader = new BufferedReader(inReader)){
            StringBuilder str = new StringBuilder("");
            String lineTxt = null;
            if (bReader != null) {
                while ((lineTxt = bReader.readLine()) != null) {
                    str.append(lineTxt);
                }
            }
            return str.toString().replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .trim();
        } catch (Exception e) {
            log.error("读取秘钥文件异常", e);
        }
        return null;
    }


    public boolean isHavePrivateKey() {
    	if(StringUtils.isNotBlank(rSAConfig.getPrivateKeyStr())) {
    		return true;
    	} else {
    		return false;
    	}

    }


    public boolean isHavePublicKey() {
    	if(StringUtils.isNotBlank(rSAConfig.getPublicKeyStr())) {
    		return true;
    	} else {
    		return false;
    	}
    }
}
