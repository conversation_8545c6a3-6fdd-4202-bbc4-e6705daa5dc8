package com.cnoocshell.common.redis;

public class InterfacePermissionKey {
    
    private InterfacePermissionKey(){
        throw new IllegalStateException("InterfacePermissionKey class");
    }

    /**
     *接口权限  相关redis key
     */
    public static final String BASE_CACHE_INTERFACE_PERMISSIONS = "base:cache:interface:permissions:";
    public static final String BASE_CACHE_INTERFACE_PERMISSIONS_PRIVATE = BASE_CACHE_INTERFACE_PERMISSIONS+"private";
    public static final String BASE_CACHE_INTERFACE_PERMISSIONS_PUBLIC = BASE_CACHE_INTERFACE_PERMISSIONS+"public";
    public static final String BASE_CACHE_INTERFACE_PERMISSIONS_REQUEST = BASE_CACHE_INTERFACE_PERMISSIONS+"request:";

}
