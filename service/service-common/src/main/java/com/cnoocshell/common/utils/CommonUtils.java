package com.cnoocshell.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.cnoocshell.common.base.BaseEntity;
import com.cnoocshell.common.result.ItemResult;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class CommonUtils {

    public static final String PERCENTAGE_SIGN = "%";
    public static final String BIAS = "/";
    public static final Character QUESTION_MARK = '?';

    private CommonUtils() {
        throw new IllegalStateException("CommonUtils class");
    }

    public static <K, T> T getByKey(Map<K, T> map, K key) {
        if (Objects.isNull(map) || Objects.isNull(key))
            return null;
        return map.containsKey(key) ? map.get(key) : null;
    }

    public static <K, T> T allowNullGetByKey(Map<K, T> map, K key) {
        if (Objects.isNull(map))
            return null;
        return map.containsKey(key) ? map.get(key) : null;
    }

    public static <K, T> T getOrDefaultIfNull(Map<K, T> map, K key, T defaultValue) {
        if (Objects.isNull(map) || Objects.isNull(key))
            return defaultValue;
        return map.containsKey(key) && Objects.nonNull(map.get(key)) ? map.get(key) : defaultValue;
    }

    public static <K, T> List<T> getListByKey(Map<K, T> map, List<K> keys) {
        if (Objects.isNull(map) || CollUtil.isEmpty(keys))
            return null;
        return keys.stream()
                .map(v -> CommonUtils.getByKey(map, v))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static <T> void andEqualToIfNotNull(Example.Criteria criteria, String name, T value) {
        if (checkNotNull(criteria, name, value))
            criteria.andEqualTo(name, value);
    }

    public static <T> void andEqualToIfNotNBank(Example.Criteria criteria, String name, String value) {
        if (checkNotNull(criteria, name) && CharSequenceUtil.isNotBlank(value))
            criteria.andEqualTo(name, value);
    }

    public static <T> void andInIfNotEmpty(Example.Criteria criteria, String name, Collection<T> values) {
        if (checkNotNull(criteria, name) && CollUtil.isNotEmpty(values))
            criteria.andIn(name, values);
    }

    public static void andLikeIfNotBank(Example.Criteria criteria, String name, String value) {
        if (checkNotNull(criteria, name) && CharSequenceUtil.isNotBlank(value))
            criteria.andLike(name, getLikeValue(Boolean.TRUE, Boolean.TRUE, value));
    }

    public static <T> void andLikeLeftIfNotBank(Example.Criteria criteria, String name, String value) {
        if (checkNotNull(criteria, name) && CharSequenceUtil.isNotBlank(value))
            criteria.andLike(name, getLikeValue(Boolean.TRUE, Boolean.FALSE, value));
    }

    public static <T> void andLikeRightIfNotBank(Example.Criteria criteria, String name, String value) {
        if (checkNotNull(criteria, name) && CharSequenceUtil.isNotBlank(value))
            criteria.andLike(name, getLikeValue(Boolean.FALSE, Boolean.TRUE, value));
    }

    public static void orderByDesc(Condition condition, String name) {
        if (checkNotNull(condition, name))
            condition.orderBy(name).desc();
    }

    public static void orderByAsc(Condition condition, String name) {
        if (checkNotNull(condition, name))
            condition.orderBy(name).asc();
    }

    public static void setOperator(BaseEntity entity, String operatorId, boolean isCreate) {
        if (isCreate) {
            entity.setDelFlg(Boolean.FALSE);
            entity.setCreateUser(operatorId);
            entity.setCreateTime(new Date());
        } else {
            entity.setUpdateUser(operatorId);
            entity.setUpdateTime(new Date());
        }
    }

    public static boolean equalsSize(Collection<?> collection1, Collection<?> collection2) {
        return Objects.equals(CollUtil.size(collection1), CollUtil.size(collection2));
    }

    public static <T> boolean containsIgnoreCase(Collection<T> collection1, T value) {
        if (CollUtil.isEmpty(collection1) || Objects.isNull(value))
            return false;
        return collection1.stream()
                .anyMatch(v -> CharSequenceUtil.equalsIgnoreCase(StrUtil.toString(v), StrUtil.toString(value)));
    }

    public static <K, V> Map<K, V> getMap(ItemResult<List<V>> result, Function<? super V, ? extends K> keyMapper) {
        if (Objects.isNull(result))
            return null;
        return getMap(result.getData(), keyMapper);
    }

    public static <K, V> Map<K, V> getMap(List<V> list, Function<? super V, ? extends K> keyMapper) {
        if (CollUtil.isEmpty(list) || Objects.isNull(keyMapper))
            return null;
        return list.stream()
                .collect(Collectors.toMap(keyMapper, Function.identity(), (v1, v2) -> v1));
    }

    public static <K, V> Map<K, List<V>> group(List<V> list, Function<? super V, ? extends K> keyMapper) {
        if (CollUtil.isEmpty(list) || Objects.isNull(keyMapper))
            return null;
        return list.stream()
                .collect(Collectors.groupingBy(keyMapper));
    }

    public static <K, V> Map<K, List<V>> allowNullGroupByKey(List<V> list, Function<? super V, ? extends K> key) {
        if (checkNotNull(list, key)) {
            Map<K, List<V>> group = new HashMap<>();

            List<V> nonNullByKey = list.stream()
                    .filter(v -> Objects.nonNull(key.apply(v)))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(nonNullByKey)) {
                group.put(null, list);
                return group;
            }
            List<V> nullByKey = list.stream()
                    .filter(v -> Objects.isNull(key.apply(v)))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nullByKey)) {
                group.put(null, nullByKey);
            }
            group.putAll(nonNullByKey.stream().collect(Collectors.groupingBy(key)));
            return group;
        }
        return null;
    }


    /**
     * 分段进行路径匹配
     *
     * @param requestUrl    请求路径
     * @param permissionUrl 权限路径
     */
    public static boolean equalsByUrl(String requestUrl, String permissionUrl) {
        if (CharSequenceUtil.equals(requestUrl, permissionUrl))
            return true;
        requestUrl = CharSequenceUtil.trim(CharSequenceUtil.removePrefix(requestUrl, BIAS));
        permissionUrl = CharSequenceUtil.trim(CharSequenceUtil.removePrefix(permissionUrl, BIAS));

        if (CharSequenceUtil.equals(requestUrl, permissionUrl))
            return true;

        List<String> split1 = getSplitUrl(requestUrl);
        List<String> split2 = getSplitUrl(permissionUrl);
        if (!CommonUtils.equalsSize(split1, split2))
            return false;
        if (CollUtil.isEmpty(split1) || CollUtil.isEmpty(split2))
            return false;
        for (int i = 0; i < split1.size(); i++) {
            String requestUrlFragment = CollUtil.get(split1, i);
            String permissionUrlFragment = CollUtil.get(split2, i);
            if (CharSequenceUtil.equals(requestUrlFragment, permissionUrlFragment))
                continue;
            //校验是否url路径拼接参数
            if (!(CharSequenceUtil.startWith(permissionUrlFragment, "{")
                    && CharSequenceUtil.endWith(permissionUrlFragment, "}")))
                return false;
        }

        return true;
    }

    public static List<String> getSplitUrl(String url) {
        if (!CharSequenceUtil.contains(url, QUESTION_MARK))
            return CharSequenceUtil.split(url, BIAS);
        return CharSequenceUtil.split(CharSequenceUtil.sub(url, 0, CharSequenceUtil.indexOf(url, QUESTION_MARK)), BIAS);
    }

    /**
     * 处理url成统一格式
     * 去除前缀/
     * 去除地址参数拼接 ?param=xxx
     */
    public static String handleUrl(String requestUrl) {
        requestUrl = CharSequenceUtil.trim(CharSequenceUtil.removePrefix(requestUrl, BIAS));
        if (CharSequenceUtil.contains(requestUrl, QUESTION_MARK))
            requestUrl = CharSequenceUtil.sub(requestUrl, 0, CharSequenceUtil.indexOf(requestUrl, QUESTION_MARK));
        return requestUrl;
    }

    public static String toStr(BigDecimal value){
        return Objects.nonNull(value) ? NumberUtil.toStr(value) : null;
    }

    /**
     * 获取去重后的非空值列表。
     *
     * @param list   原始列表
     * @param mapper 映射函数，用于将列表中的元素转换为另一种类型
     * @param <T>    原始列表的元素类型
     * @param <V>    转换后的元素类型
     * @return 去重后的非空值列表，如果输入列表为空或映射函数为空，则返回 null
     */
    public static <T, V> List<V> getListValueByDistinctAndFilterNull(List<T> list, Function<? super T, ? extends V> mapper) {
        if (CollUtil.isEmpty(list) || Objects.isNull(mapper))
            return null;
        return list.stream().map(mapper).distinct().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取去重后的非空字符串列表。
     *
     * @param list   原始列表
     * @param mapper 映射函数，用于将列表中的元素转换为字符串
     * @param <T>    原始列表的元素类型
     * @return 去重后的非空字符串列表，如果输入列表为空或映射函数为空，则返回 null
     */
    public static <T> List<String> getListValueByDistinctAndFilterBank(List<T> list, Function<? super T, ? extends String> mapper) {
        if (CollUtil.isEmpty(list) || Objects.isNull(mapper))
            return null;
        return list.stream().map(mapper).distinct().filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 合并两个列表。
     *
     * @param list1 第一个列表
     * @param list2 第二个列表
     * @param <T>   列表元素类型
     * @return 合并后的列表
     */
    public static <T> List<T> union(List<T> list1, List<T> list2) {
        List<T> result = new ArrayList<>();

        if (CollUtil.isNotEmpty(list1))
            result.addAll(list1);

        if (CollUtil.isNotEmpty(list2))
            result.addAll(list2);
        return result;
    }

    /**
     * 合并两个列表并去重。
     *
     * @param list1 第一个列表
     * @param list2 第二个列表
     * @param <T>   列表元素类型
     * @return 合并并去重后的列表
     */
    public static <T> List<T> unionDistinct(List<T> list1, List<T> list2) {
        List<T> result = union(list1, list2);
        if (CollUtil.isNotEmpty(result))
            result = result.stream().distinct().collect(Collectors.toList());
        return result;
    }

    /**
     * 合并两个列表并根据指定的键映射函数去重。
     *
     * @param list1     第一个列表
     * @param list2     第二个列表
     * @param keyMapper 键映射函数，用于从列表元素中提取键值以进行去重
     * @param <T>       列表元素类型
     * @param <K>       键的类型
     * @return 合并并去重后的列表，如果输入列表为空或映射函数为空，则返回 null
     */
    public static <T, K> List<T> unionDistinct(List<T> list1, List<T> list2, Function<? super T, ? extends K> keyMapper) {
        return distinctList(unionDistinct(list1, list2), keyMapper);
    }

    /**
     * 根据指定的键映射函数对列表进行去重。
     *
     * @param list      原始列表
     * @param keyMapper 键映射函数，用于从列表元素中提取键值
     * @param <T>       列表元素类型
     * @param <K>       键的类型
     * @return 去重后的列表，如果输入列表为空或映射函数为空，则返回 null
     */
    public static <T, K> List<T> distinctList(List<T> list, Function<? super T, ? extends K> keyMapper) {
        if (CollUtil.isEmpty(list))
            return null;
        Map<K, T> map = getMap(list, keyMapper);
        if (CollUtil.isEmpty(map))
            return null;
        return map.values().stream().collect(Collectors.toList());
    }

    /**
     * 合并多个列表到源列表中。
     *
     * @param sourceList 源列表，如果为 null，则会创建一个新的 ArrayList
     * @param values     要合并的多个列表
     * @param <T>        列表元素类型
     * @return 合并后的源列表
     */
    public static <T> List<T> addAll(List<T> sourceList, List<T>... values) {
        if (Objects.isNull(sourceList))
            sourceList = new ArrayList<>();
        if (!checkNotNull(values))
            return sourceList;
        for (List<T> value : values) {
            if(CollUtil.isNotEmpty(value))
                sourceList.addAll(value);
        }
        return sourceList;
    }
    


    private static <T> Boolean checkNotNull(Object... values) {
        return values != null && Arrays.stream(values).allMatch(v -> Objects.nonNull(v));
    }

    private static String getLikeValue(Boolean likeLeft, Boolean likeRight, String value) {
        if (BooleanUtil.isTrue(likeLeft) && BooleanUtil.isTrue(likeRight))
            return CharSequenceUtil.format("{}{}{}", PERCENTAGE_SIGN, value, PERCENTAGE_SIGN);
        else if (BooleanUtil.isTrue(likeLeft))
            return CharSequenceUtil.format("{}{}", value, PERCENTAGE_SIGN);
        else if (BooleanUtil.isTrue(likeRight))
            return CharSequenceUtil.format("{}{}", PERCENTAGE_SIGN, value);
        return value;
    }
}
