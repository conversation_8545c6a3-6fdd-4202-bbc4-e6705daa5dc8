package com.cnoocshell.common.service;


import com.cnoocshell.common.service.reference.ReferenceUrlCache;

import java.util.List;

/**
 * 全链路缓存接口
 */
public interface IUrlReferenceCacheService {

    /**
     * 取到一条全链路数据
     * @param url
     * @return
     */
    ReferenceUrlCache getUrlReference(String url);

    /**
     * 查询所有链路信息
     * @return
     */
    List getAllReference();

    /**
     * 链接是否存在
     * @param url
     * @return
     */
    boolean hasExist(String url);


    /**
     * 设置链路缓存
     */
    void setUrlReference(ReferenceUrlCache cache);
}
