package com.cnoocshell.common.config.url;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.stereotype.Component;

@Component
@EnableCaching
public class URLConfig {
    @Value("${web.server.protocol:http}")
    private String webProtocol;

    @Value("${web.server.port:8080}")
    private String webPort;

    @Value("${web.server.host:localhost}")
    private String webServer;


    public String getWebServer() {
        // todo
        return webProtocol + "://" + webServer + ":" + webPort;
    }
}
