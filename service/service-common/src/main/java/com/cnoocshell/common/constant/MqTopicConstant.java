package com.cnoocshell.common.constant;

public class MqTopicConstant {

    private MqTopicConstant(){
        throw new IllegalStateException("MqTopicConstant class");
    }

    /**
     *系统操作日志
     */
    public static final String SYS_LOG_TOPIC = "sysLogQueue";

    public static final String SEND_EMAIL_TOPIC = "SEND_EMAIL_TOPIC";

    public static final String SEND_MESSAGE_TOPIC = "SEND_MESSAGE_TOPIC";

    public static final String SEND_SMS_TOPIC = "SEND_SMS_TOPIC";

    public static final String SEND_WECHAT_TOPIC = "SEND_WECHAT_TOPIC";

    public static final String CREATE_XXL_JOB_TOPIC = "CREATE_XXL_JOB_TOPIC";
    public static final String STOP_XXL_JOB_TOPIC = "STOP_XXL_JOB_TOPIC";
    public static final String DESTROY_XXL_JOB_TOPIC = "DESTROY_XXL_JOB_TOPIC";
    public static final String STOP_XXL_JOB_BY_PARAM_TOPIC = "STOP_XXL_JOB_BY_PARAM_TOPIC";

    /**
     *创 建SAP合同
     */
    public static final String CREATE_SAP_CONTRACT_TOPIC = "CREATE_SAP_CONTRACT_TOPIC";

    /**
     *创建SAP合同 通知(失败、成功都通知)
     */
    public static final String CREATE_SAP_CONTRACT_SUCCESS_TOPIC = "CREATE_SAP_CONTRACT_SUCCESS_TOPIC";

    public static final String INTERFACE_LOG_INFO = "INTERFACE_LOG_INFO";

    /**
     *保存协议
     */
    public static final String SAVE_AGREEMENT_TOPIC = "SAVE_AGREEMENT_TOPIC";


    /**
     *会员信息变更
     */
    public static final String MEMBER_CHANGE_TOPIC = "MEMBER_CHANGE_TOPIC";

    /**
     *账户信息变更
     */
    public static final String ACCOUNT_CHANGE_TOPIC = "ACCOUNT_CHANGE_TOPIC";

    /**
     *商品名称变更
     */
    public static final String GOODS_NAME_CHANGE_TOPIC = "GOODS_NAME_CHANGE_TOPIC";


    /**
     *账户解绑
     */
    public static final String ACCOUNT_UNBIND_TOPIC = "ACCOUNT_UNBIND_TOPIC";


    /**
     * 会员意向变更
     */
    public static final String MEMBER_INTENTION_CHANGE_TOPIC = "MEMBER_INTENTION_CHANGE_TOPIC";
}



