package com.cnoocshell.common.config.redis;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 11:27 10/09/2018
 */
@Configuration
@EnableCaching
public class DefaultRedisConfig extends RedisConfig{

    /**
     * 配置redis连接工厂
     *
     * @return
     */
    @Primary
    @Bean(name = "defaultRedisConnectionFactory")
    public RedisConnectionFactory defaultRedisConnectionFactory() {
        return createJedisConnectionFactory();
    }


    /**
     * @Author: <EMAIL>
     * @discription: fastjosn序列化redistemplate
     * @creat_date: 2018/1/10
     * @creat_time: 11:01
     **/
    @Bean(name = "defaultRedisTemplate")
    public RedisTemplate redisTemplate(){
        RedisTemplate template = new RedisTemplate();
        template.setConnectionFactory(defaultRedisConnectionFactory());
        setSerializer(template);
        template.afterPropertiesSet();
        return template;

    }
}
