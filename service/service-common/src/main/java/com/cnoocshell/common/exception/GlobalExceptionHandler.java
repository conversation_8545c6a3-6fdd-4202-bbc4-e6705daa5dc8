package com.cnoocshell.common.exception;

import cn.hutool.core.text.CharSequenceUtil;
import com.cnoocshell.common.result.ItemResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理
 */
@Slf4j
//@Component("CommonGlobalExceptionHandler")
//@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 业务异常
     */
    @ResponseBody
    @ExceptionHandler({BizException.class})
    public static ItemResult bizException(BizException ex) {
        log.error("业务异常", ex);
        return new ItemResult(ex.getErrorCode().getCode(), ex.getMessage());
    }

    /**
     * 业务异常
     */
    @ResponseBody
    @ExceptionHandler({BasicRuntimeException.class})
    public static ItemResult basicRuntimeException(BasicRuntimeException ex) {
        log.error("基础运行异常", ex);
        return new ItemResult(BasicCode.CUSTOM_ERROR.getCode(), ex.getMessage());
    }

    /**
     * 未知异常
     */
    @ResponseBody
    @ExceptionHandler({Exception.class})
    public static ItemResult otherException(Exception ex) {
        log.error("未知异常", ex);
        return new ItemResult(BasicCode.UNKNOWN_ERROR.getCode(), "系统未知异常，请联系管理员");
    }

    /**
     * 分布式锁异常
     */
    @ResponseBody
    @ExceptionHandler({DistributeLockException.class})
    public static ItemResult bizException(DistributeLockException ex) {
        log.error("分布式锁异常", ex);
        return new ItemResult(BasicCode.CUSTOM_ERROR.getCode(), ex.getMessage());
    }

    /**
     * 登录失败异常
     */
    @ResponseBody
    @ExceptionHandler({FailLoginException.class})
    public static ItemResult failLoginException(FailLoginException ex) {
        log.error("登录失败异常", ex);
        return new ItemResult(BasicCode.CUSTOM_ERROR.getCode(), ex.getMessage());
    }

    /**
     * 用户被挤下线异常
     */
    @ResponseBody
    @ExceptionHandler({ForcedLogoutException.class})
    public static ItemResult forcedLogoutException(ForcedLogoutException ex) {
        log.error("用户被挤下线异常", ex);
        return new ItemResult(BasicCode.CUSTOM_ERROR.getCode(), ex.getMessage());
    }

    /**
     * 用户未登录异常
     */
    @ResponseBody
    @ExceptionHandler({NoLoginException.class})
    public static ItemResult noLoginException(NoLoginException ex) {
        log.error("用户未登录异常", ex);
        return new ItemResult(BasicCode.CUSTOM_ERROR.getCode(), ex.getMessage());
    }

    /**
     * 用户无权限异常
     */
    @ResponseBody
    @ExceptionHandler({NoPermissionException.class})
    public static ItemResult noPermissionException(NoPermissionException ex) {
        log.error("用户无权限异常", ex);
        return new ItemResult(BasicCode.NO_PERMISSION.getCode(), BasicCode.NO_PERMISSION.getMsg());
    }

    /**
     * http链接异常
     */
    @ResponseBody
    @ExceptionHandler({OkHttpConnectException.class})
    public static ItemResult okHttpConnectException(OkHttpConnectException ex) {
        log.error("http链接异常", ex);
        return new ItemResult(BasicCode.HTTP_CONNECT_ERROR.getCode(),  ex.getMessage());
    }
}
