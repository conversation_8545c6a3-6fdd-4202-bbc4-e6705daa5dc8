package com.cnoocshell.common.service.access;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.dto.InterfacePermissionDTO;
import com.cnoocshell.common.redis.InterfacePermissionKey;
import com.cnoocshell.common.service.IAuthorizeService;
import com.cnoocshell.common.service.common.BizRedisService;
import com.cnoocshell.common.utils.CommonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthorizeService implements IAuthorizeService {
    private final BizRedisService bizRedisService;

    @Override
    public Boolean authorize(String url, List<String> roleCodes) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        //先校验是否是公共接口
        List<InterfacePermissionDTO> publicList = bizRedisService.get(InterfacePermissionKey.BASE_CACHE_INTERFACE_PERMISSIONS_PUBLIC);
        if(this.verifyPublic(url,publicList))
            return true;
        //不在公共接口范围
        if (CollUtil.isEmpty(roleCodes))
            return false;

        String requestPermissionKey = this.getAuthKey(url,roleCodes);
        if (bizRedisService.hasKey(requestPermissionKey))
            return bizRedisService.get(requestPermissionKey, Boolean.class);

        return null;
    }

    @Override
    public boolean verifyPublic(String url, List<InterfacePermissionDTO> publicList) {
        if (CollUtil.isEmpty(publicList))
            return false;
        for (InterfacePermissionDTO v : publicList) {
            if (CommonUtils.equalsByUrl(url, v.getInterfaceUrl())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String getAuthKey(String requestUrl, List<String> roleCodes) {
        roleCodes = roleCodes.stream().distinct().sorted().collect(Collectors.toList());

        String baseRequestUrl = Base64.encodeUrlSafe(CommonUtils.handleUrl(requestUrl), StandardCharsets.UTF_8);
        String baseRoleCode = Base64.encode(JSONUtil.toJsonStr(roleCodes));
        String requestPermissionKey = CharSequenceUtil.format("{}{}:{}", InterfacePermissionKey.BASE_CACHE_INTERFACE_PERMISSIONS_REQUEST, baseRequestUrl, baseRoleCode);

        return requestPermissionKey;
    }

    @Override
    public boolean verifyPublic(String url) {
        List<InterfacePermissionDTO> publicList = bizRedisService.get(InterfacePermissionKey.BASE_CACHE_INTERFACE_PERMISSIONS_PUBLIC);
        return this.verifyPublic(url,publicList);
    }
}
