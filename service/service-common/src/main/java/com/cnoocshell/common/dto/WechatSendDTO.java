package com.cnoocshell.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

// 订阅消息请求体（字段要求严格匹配微信文档）

/**
 * 官方请求的消息体示例
 * {
 *   "touser": "OPENID",
 *   "template_id": "TEMPLATEID",
 *   "page": "mp.weixin.qq.com",
 *   "miniprogram":{
 *              "appid":"APPID",
 *              "pagepath":"index?foo=bar"
 *   },
 *   "data": {
 *       "name1": {
 *           "value": "广州腾讯科技有限公司"
 *       },
 *       "thing8": {
 *           "value": "广州腾讯科技有限公司"
 *       },
 *        "time7": {
 *           "value": "2019年8月8日"
 *       }
 *      }
 * }
 *
 * 例如，模板的内容为
 *
 * 姓名: {{name01.DATA}}
 * 金额: {{amount01.DATA}}
 * 行程: {{thing01.DATA}}
 * 日期: {{date01.DATA}}
 * 则对应的json为
 *
 * {
 *   "touser": "OPENID",
 *   "template_id": "TEMPLATE_ID",
 *   "page": "index",
 *   "data": {
 *       "name01": {
 *           "value": "某某"
 *       },
 *       "amount01": {
 *           "value": "￥100"
 *       },
 *       "thing01": {
 *           "value": "广州至北京"
 *       },
 *       "date01": {
 *           "value": "2018-01-01"
 *       }
 *   }
 * }
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WechatSendDTO {
    @ApiModelProperty(value = "接收者openid")
    private String touser;

    @ApiModelProperty(value = "订阅模板Code")
    private String templateCode;

    @ApiModelProperty(value = "订阅模板ID")
    private String template_id;

    @ApiModelProperty(value = "点击消息跳转的页面路径（支持带参数）。示例：index?foo=bar")
    private String page;

    private String miniprogram_state;

    @ApiModelProperty(value = "模板内容字段。格式要求：key对应模板中的标识，value为具体内容")
    private Map<String, WechatSendDTO.DataItem> data;

    @ApiModelProperty(value = "业务No")
    private String businessNo;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DataItem {
        @ApiModelProperty(value = "字段内容")
        private String value;

    }

}
