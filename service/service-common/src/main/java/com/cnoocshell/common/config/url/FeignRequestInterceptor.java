package com.cnoocshell.common.config.url;

import com.cnoocshell.common.service.IUrlReferenceCacheService;
import com.cnoocshell.common.service.reference.ReferenceUrlCache;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

@Component
public class FeignRequestInterceptor implements RequestInterceptor {
    public static String tag = "->";
    @Value("${spring.application.name}")
    private String serviceName;
    @Autowired
    private IUrlReferenceCacheService urlReferenceCacheService;
    @Override
    public void apply(RequestTemplate requestTemplate) {
        StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();
        String referenceUrl = "";
        int count = 0;
        for(StackTraceElement stackTraceElement : stackTraceElements){
            if(stackTraceElement.getClassName().contains("com.cnoocshell")){
                if(count==1){
                    referenceUrl = stackTraceElement.toString();
                    break;
                }else{
                    count++;
                }
            }
        }
        StringBuffer sf = new StringBuffer(serviceName);
        sf.append(tag);
        sf.append(referenceUrl);
        ReferenceUrlCache referenceUrlCache = new ReferenceUrlCache();
        referenceUrlCache.setUrl(requestTemplate.url());
        Set<String> list = new HashSet<String>();
        list.add(sf.toString());
        referenceUrlCache.setList(list);
        urlReferenceCacheService.setUrlReference(referenceUrlCache);
    }
}
