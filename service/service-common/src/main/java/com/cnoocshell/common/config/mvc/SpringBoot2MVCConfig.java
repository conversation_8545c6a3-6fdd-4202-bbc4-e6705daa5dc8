package com.cnoocshell.common.config.mvc;

import com.cnoocshell.common.enums.AppNames;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.optionals.OptionalDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.support.PageableSpringEncoder;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.PostConstruct;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
/**
 * 需求: 响应给前端的json,String类型数据 如果是空，则返回空字符串
 * 修改webService序列化规则,其它不动
 *
 * 前端 ---->反序列化 webService (Feign)序列化 -------->反序列化 microService
 * 前端 ---->[序列化(仅修改)] webService (Feign)反序列化 <--------序列化 microService
 *
 * microService2 (Feign)序列化 -------->反序列化 microService
 * microService2 (Feign)反序列化 <--------序列化 microService
 *
 */
@Slf4j
@Configuration
public class SpringBoot2MVCConfig implements WebMvcConfigurer {

    @Value("${spring.application.name}")
    private String applicationName;

    @PostConstruct
    public void init(){
        log.info("check applicationName");
        //如果都不是，则说明项目名称不正确
        for (AppNames value : AppNames.values()) {
            if( value.getCode().equals(applicationName)){
                return;
            }
        }
        throw new BizException(BasicCode.UNDEFINED_ERROR,"当前项目名称:"+applicationName+"没有对应的枚举:com.cnoocshell.common.enums.AppNames");
    }

    @LoadBalanced
    @ConditionalOnMissingBean
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    //通用设置
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = Jackson2ObjectMapperBuilder.json().build();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Boolean.class,new MyBooleanDeserializer(Boolean.class));
        module.addDeserializer(Integer.class,new MyIntegerDeserializer(Integer.class));
        module.addDeserializer(Date.class,new MyDateDeserializer(Date.class));
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        module.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
        module.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));
        module.addSerializer(LocalTime.class, new LocalTimeSerializer(timeFormatter));
        module.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));
        module.addDeserializer(LocalTime.class, new LocalTimeDeserializer(timeFormatter));
        module.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));
        mapper.registerModule(module);
        mapper.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        //忽略前端多余的和错误的字段
        if(AppNames.isWebService(applicationName)) {
            //忽略未知属性
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            //空字符串转空对象
            mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        }
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,true);
        mapper.configure(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE,false);
        //允许出现特殊字符和转义符
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true) ;
        //允许出现单引号
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true) ;

        return mapper;
    }

    //通用设置
    @Bean
    public MappingJackson2HttpMessageConverter mappingJacksonHttpMessageConverter() {
        final MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        ObjectMapper objectMapper = objectMapper();

        //webService序列化 String空值设置为空字符串
        if(AppNames.isWebService(applicationName)) {
            objectMapper.setSerializerFactory(objectMapper.getSerializerFactory().withSerializerModifier(new MyStringSerializerModifier()));
        }
        converter.setObjectMapper(objectMapper);
        setSupportedMediaTypes(converter);
        converter.setDefaultCharset(Charset.forName("UTF-8"));
        return converter;
    }


    /**
     * web才层初始化,与通用设置分开
     */
    @Bean("mappingJacksonHttpMessageConverter2")
    @ConditionalOnExpression("T(com.cnoocshell.common.enums.AppNames).isWebService('${spring.application.name}')")
    public MappingJackson2HttpMessageConverter mappingJacksonHttpMessageConverter2() {
        final MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        //web才层初始化,与通用设置分开
        converter.setObjectMapper(objectMapper());
        setSupportedMediaTypes(converter);
        converter.setDefaultCharset(Charset.forName("UTF-8"));
        return converter;
    }

    private void setSupportedMediaTypes(MappingJackson2HttpMessageConverter converter){
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        supportedMediaTypes.add(MediaType.APPLICATION_ATOM_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_FORM_URLENCODED);
        supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        supportedMediaTypes.add(MediaType.APPLICATION_PDF);
        supportedMediaTypes.add(MediaType.APPLICATION_RSS_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XHTML_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XML);
        supportedMediaTypes.add(MediaType.IMAGE_GIF);
        supportedMediaTypes.add(MediaType.IMAGE_JPEG);
        supportedMediaTypes.add(MediaType.IMAGE_PNG);
        supportedMediaTypes.add(MediaType.TEXT_EVENT_STREAM);
        supportedMediaTypes.add(MediaType.TEXT_HTML);
        supportedMediaTypes.add(MediaType.TEXT_MARKDOWN);
        supportedMediaTypes.add(MediaType.TEXT_XML);
        converter.setSupportedMediaTypes(supportedMediaTypes);
    }

    /**
     * web才层初始化
     */
    @Bean("messageConverters2")
    @DependsOn("mappingJacksonHttpMessageConverter2")
    @ConditionalOnExpression("T(com.cnoocshell.common.enums.AppNames).isWebService('${spring.application.name}')")
    public ObjectFactory<HttpMessageConverters> messageConverters(){
        final HttpMessageConverters httpMessageConverters =

                new HttpMessageConverters();

        return () -> httpMessageConverters;
    }

    //over FeignClientsConfiguration.class
    @Bean
    @DependsOn("messageConverters2")
    @ConditionalOnExpression("T(com.cnoocshell.common.enums.AppNames).isWebService('${spring.application.name}')")
    public Decoder feignDecoder(ObjectFactory<HttpMessageConverters> messageConverters2) {
        return new OptionalDecoder(new ResponseEntityDecoder(new SpringDecoder(messageConverters2)));
    }

    @Bean
    @DependsOn("messageConverters2")
    @ConditionalOnExpression("T(com.cnoocshell.common.enums.AppNames).isWebService('${spring.application.name}')")
    public Encoder feignEncoder(ObjectFactory<HttpMessageConverters> messageConverters2) {
        return new SpringEncoder(messageConverters2);
    }

    @Bean
    @ConditionalOnMissingBean
    @DependsOn("messageConverters2")
    @ConditionalOnClass(name = "org.springframework.data.domain.Pageable")
    @ConditionalOnExpression("T(com.cnoocshell.common.enums.AppNames).isWebService('${spring.application.name}')")
    public Encoder feignEncoderPageable(ObjectFactory<HttpMessageConverters> messageConverters2) {
        return new PageableSpringEncoder(new SpringEncoder(messageConverters2));
    }

}
