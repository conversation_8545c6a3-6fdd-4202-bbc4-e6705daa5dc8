package com.cnoocshell.common.service.common;

import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.common.service.IBaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;

public class BaseService<T> implements IBaseService<T> {
    private static final Logger log = LoggerFactory.getLogger(BaseService.class);
    private IBaseBiz<T> biz;

    protected void setBiz(IBaseBiz<T> biz) {
        this.biz = biz;
    }

    @Override
    public List<T> find(T t) {
        return this.biz.find(t);
    }

    @Override
    public List<T> findByCondition(Condition condition) {
        return this.biz.findByCondition(condition);
    }

    @Override
    public Page<T> page(Condition t, PageInfo pageInfo) {
        return this.biz.page(t, pageInfo);
    }

    public PageInfo<T> pageInfo(Condition t, PageInfo pageInfo) {
        return this.biz.pageInfo(t, pageInfo);
    }

    @Override
    public T get(String id) {
        return this.biz.get(id);
    }

    @Override
    public T updateSelective(T t) {
        return this.biz.updateSelective(t);
    }

    @Override
    public T save(T t) {
        return this.biz.save(t);
    }

    @Override
    public T save(T t, Object operUserId) {
        return this.biz.save(t, operUserId);
    }

    @Override
    public void delete(T t) {
        this.biz.delete(t);
    }
}
