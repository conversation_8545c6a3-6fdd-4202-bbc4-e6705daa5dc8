package com.cnoocshell.common.service.access;

import com.cnoocshell.common.service.IRightAccessCacheService;
import com.cnoocshell.common.service.common.BizRedisService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class RightAccessCacheService implements IRightAccessCacheService {
    private static final String RIGHT_ACCESS_CACHE_NAME = "right_access_cache";
    private static final int ARRAY_LENGTH = 10;
    @Autowired
    private BizRedisService redisService;

    @Override
    public RightAccessCache getRightAccess(String url) {
        return redisService.hget(RIGHT_ACCESS_CACHE_NAME, url, RightAccessCache.class);
    }

    @Override
    public List<RightAccessCache> getAllRightAccess() {
        return null;
    }

    @Override
    public boolean hasPermission(String url) {
        return redisService.hasField(RIGHT_ACCESS_CACHE_NAME, url);
    }

    @Override
    public boolean hasRightAccess(String url, int role) {
        return hasRightAccess(url, role, null);
    }

    @Override
    public boolean hasRightAccess(String url, int role, long[] roles) {
        if (roles == null || roles.length == 0) {
            roles = getRightAccess(url).getRoles();
        }
        long tmp = 1L << (role % 64 - 1);
        try {
            if ((roles[role / 65] & tmp) == tmp) {
                return true;
            }
        } catch (ArrayIndexOutOfBoundsException e) {
            log.info("url:{},role:{},roles:{},tmp:{}", url, role, roles, tmp);
            throw e;
        }
        return false;
    }

    @Override
    public void setRightAccess(RightAccessCache cache) {
        if (cache == null) {
            return;
        }
        redisService.hset(RIGHT_ACCESS_CACHE_NAME, cache.getKey(), cache);
    }

    @Override
    public void setRightAccess(String url, int[] roleArray) {
        long[] functions = new long[ARRAY_LENGTH];
        for (int n : roleArray) {
            if (n > 0 && n < (64 * ARRAY_LENGTH + 1)) {
                long tmp = 1L << (n % 64 - 1);
                functions[n / 65] |= tmp;
            }
        }
        RightAccessCache cache = new RightAccessCache();
        cache.setUrl(url);
        cache.setRoles(functions);
        setRightAccess(cache);
    }

    @Override
    public void setRightAccess(Map<String, Set<Integer>> urlAndRoleMap) {
        Map<String, RightAccessCache> rightAccessCacheMap = Maps.newHashMap();
        int count = 0;
        for (Map.Entry<String, Set<Integer>> entry : urlAndRoleMap.entrySet()) {
            String url = entry.getKey();
            Set<Integer> roleIdSet = entry.getValue();

            long[] functions = new long[ARRAY_LENGTH];
            for (int n : roleIdSet) {
                if (n > 0 && n < (64 * ARRAY_LENGTH + 1)) {
                    long tmp = 1L << (n % 64 - 1);
                    functions[n / 65] |= tmp;
                }
            }
            RightAccessCache cache = new RightAccessCache();
            cache.setUrl(url);
            cache.setRoles(functions);
            rightAccessCacheMap.put(url, cache);
            count++;
            log.debug("setRightAccess {} url:[{}],role:{},roleLong:{}", count, url, roleIdSet, functions);

            if (count % 1000 == 0) {
                redisService.getRedisTemplate().opsForHash().putAll(RIGHT_ACCESS_CACHE_NAME, rightAccessCacheMap);
                log.info("setRightAccess count: {}", count);
                rightAccessCacheMap = Maps.newHashMap();
            }
        }
        redisService.getRedisTemplate().opsForHash().putAll(RIGHT_ACCESS_CACHE_NAME, rightAccessCacheMap);
        log.info("setRightAccess end count: {}", count);
    }

    @Override
    public void removePermission(String url) {
        redisService.del(RIGHT_ACCESS_CACHE_NAME, url);
    }
}
