//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.cnoocshell.common.service.common;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import com.cnoocshell.common.base.BaseEntity;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.IBaseBiz;
import com.cnoocshell.common.service.IBaseMapper;
import com.cnoocshell.common.service.common.uuid.UUIDGenerator;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import javax.persistence.Id;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Date;
import java.util.List;

public class BaseBiz<T> implements IBaseBiz<T> {
    private static final Logger log = LoggerFactory.getLogger(BaseBiz.class);
    private IBaseMapper<T> mapper;
    @Autowired
    private UUIDGenerator uuidGenerator;

    public BaseBiz() {
        //add sonar note
    }

    @Autowired
    protected void setMapper(IBaseMapper<T> mapper) {
        this.mapper = mapper;
    }

    @Override
    public IBaseMapper<T> getMapper() {
        return mapper;
    }

    @Override
    public String getUuidGeneratorGain() {
        return uuidGenerator.gain();
    }

    private Type[] getGenericParams() {
        Type genType = this.getClass().getGenericSuperclass();
        return ((ParameterizedType) genType).getActualTypeArguments();
    }

    @Override
    public List<T> find(T t) {
        return this.mapper.select(t);
    }

    @Override
    public List<T> findByCondition(Condition condition) {
        return this.mapper.selectByCondition(condition);
    }

    @Override
    public Page<T> page(Condition t, PageInfo pageInfo) {
        Page<T> page = PageMethod.startPage(pageInfo.getPageNum(), pageInfo.getPageSize()).doSelectPage(() -> {
            this.mapper.selectByCondition(t);
        });
//        page.setTotal(page.size());
        return page;
    }

    @Override
    public PageInfo<T> pageInfo(Condition t, PageInfo pageInfo) {
       PageMethod.startPage(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal() <= 0);
        List<T> list = this.mapper.selectByCondition(t);

        return new PageInfo(list);
    }

    @Override
    public T get(String id) {
        return this.mapper.selectByPrimaryKey(id);
    }

    @Override
    public T updateSelective(T t) {
        this.mapper.updateByPrimaryKeySelective(t);
        return t;
    }

    @Override
    public T save(T t) {
        return this.save(t, (Object) null);
    }

    @Override
    public T save(T t, Object operUserId) {
        String id = null;
        boolean insertsign = false;
        Field idField = null;

        try {
            Field[] fields = t.getClass().getDeclaredFields();
            Field[] var7 = fields;
            int var8 = fields.length;

            for (int var9 = 0; var9 < var8; ++var9) {
                Field field = var7[var9];
                if (field.getAnnotation(Id.class) != null) {
                    idField = field;
                    break;
                }
            }

            if (idField == null) {
                throw new BizException(BasicCode.DB_TABLE_ID_NOT_FOUND, new Object[]{"id"});
            }

            if (id == null) {
                id = BeanUtils.getProperty(t, idField.getName());
            }

            insertsign = id == null;
            if (!insertsign) {
                insertsign = StringUtils.isEmpty(id);
            }

            if (insertsign) {
                BeanUtil.setProperty(t, idField.getName(), this.uuidGenerator.gain());
            }
        } catch (IllegalAccessException var11) {
            log.error("save IllegalAccessException 保存时发生错误", var11);
            throw new RuntimeException("保存失败", var11);
        } catch (InvocationTargetException var12) {
            log.error("save InvocationTargetException 保存时发生错误" + var12.getMessage(), var12);
            throw new RuntimeException("保存失败", var12);
        } catch (NoSuchMethodException var13) {
            log.error("save NoSuchMethodException 保存时发生错误" + var13.getMessage(), var13);
            throw new RuntimeException("保存失败", var13);
        }

        if (operUserId != null) {
            setOperInfo(t, operUserId, insertsign);
        }

        if (insertsign) {
            this.mapper.insert(t);
        } else {
            this.mapper.updateByPrimaryKeySelective(t);
        }

        return t;
    }

    @Override
    public int insert(T t) {
        return mapper.insert(t);
    }

    @Override
    public int insertList(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return mapper.insertList(list);
    }

    @Override
    public List<T> batchInsert(List<T> t) {
        return batchInsert(t, null);
    }

    @Override
    public List<T> batchInsert(List<T> list, Object operUserId) {
        Field idField = null;

        for (T t : list) {
            try {

                idField = getField(t, idField);

                if (idField == null) {
                    throw new BizException(BasicCode.DB_TABLE_ID_NOT_FOUND, new Object[]{"id"});
                }

                BeanUtil.setProperty(t, idField.getName(), this.uuidGenerator.gain());
            } catch (Exception var11) {
                log.error("batchInsert 保存时发生错误", var11);
                throw new RuntimeException("保存失败", var11);
            }

            if (operUserId != null) {
                setOperInfo(t, operUserId, true);
            }
        }

        this.mapper.insertList(list);
        return list;
    }

    private static <T> @Nullable Field getField(T t, Field idField) {
        if (idField == null) {
            Field[] fields = t.getClass().getDeclaredFields();
            Field[] var7 = fields;
            int var8 = fields.length;

            for (int var9 = 0; var9 < var8; ++var9) {
                Field field = var7[var9];
                if (field.getAnnotation(Id.class) != null) {
                    idField = field;
                    break;
                }
            }
        }
        return idField;
    }

    public static void setCreateOperInfo(Object t, Object operId) {
        setOperInfo(t, operId, true);
    }

    public static void setUpdateOperInfo(Object t, Object operId) {
        setOperInfo(t, operId, false);
    }

    public static void setOperInfo(Object t, Object operId, boolean buildCreateInfo) {
        try {
            if (buildCreateInfo) {
                BeanUtil.setProperty(t, "createUser", operId);
                BeanUtil.setProperty(t, "createTime", new Date());
                BeanUtil.setProperty(t, "delFlg", Integer.valueOf(0));
            }

            BeanUtil.setProperty(t, "updateUser", operId);
            BeanUtil.setProperty(t, "updateTime", new Date());
        } catch (Exception var5) {
            log.warn("设置操作日志信息时发生错误", var5);
        }

    }

    public static void setOperatorInfo(BaseEntity t , String operatorId, Boolean setCreator) {
        try {
            if (BooleanUtil.isTrue(setCreator)) {
                t.setCreateUser(operatorId);
                t.setCreateTime(new Date());
                t.setDelFlg(Boolean.FALSE);
            }

            t.setUpdateUser(operatorId);
            t.setUpdateTime(new Date());
        } catch (Exception var5) {
            log.warn("设置操作日志信息时发生错误", var5);
        }

    }

    @Override
    public void delete(T t) {
        try {
            BeanUtil.setProperty(t, "delFlg", Integer.valueOf(1));
        } catch (Exception var3) {
            log.error("逻辑删除失败：", var3);
            throw new BizException(BasicCode.DB_DELETE_FAILED, new Object[]{"逻辑删除"});
        }

        this.mapper.updateByPrimaryKeySelective(t);
    }

    @Override
    public Condition newCondition() {
        Class<T> tClass = getClz();
        if (tClass == null) {
            return null;
        }
        return new Condition(tClass);
    }

    /**
     * 创建一个Class的对象来获取泛型的class
     */
    private Class<T> clz;

    @SuppressWarnings("unchecked")
    public Class<T> getClz() {
        if (clz == null) {
            clz = (Class<T>) (((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[0]);
        }
        return clz;
    }

}
