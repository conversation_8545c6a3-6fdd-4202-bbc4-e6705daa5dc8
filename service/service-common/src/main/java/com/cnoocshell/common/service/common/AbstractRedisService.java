package com.cnoocshell.common.service.common;

import cn.hutool.core.collection.CollUtil;
import com.cnoocshell.common.config.redis.ObjectExpire;
import com.cnoocshell.common.service.RedisService;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Author: <EMAIL>
 * @discription: redis服务实现（spring getRedisTemplate()）
 * @date : 15:02 2018/1/10
 **/
public abstract class AbstractRedisService implements RedisService {

    public abstract RedisTemplate<String, Object> getRedisTemplate();

    @Override
    public <T> Set<T> sMembers(String key) {
        return (Set<T>) getRedisTemplate().opsForSet().members(key);
    }

    @Override
    public <T> boolean sRemove(String key, Set<T> value) {
        getRedisTemplate().opsForSet().remove(key, value.toArray());
        return true;
    }

    @Override
    public <T> boolean sAdd(String key, Set<T> value) {
        getRedisTemplate().opsForSet().add(key, value.toArray());
        return true;
    }

    @Override
    public <T> boolean set(String key, T value) {
        getRedisTemplate().opsForValue().set(key, value);
        return true;
    }

    @Override
    public <T> boolean hset(String key, String field, T value) {
        getRedisTemplate().opsForHash().put(key, field, value);
        return true;
    }

    @Override
    public <T> boolean sset(String key, T value) {
        getRedisTemplate().opsForSet().add(key, value);
        return true;
    }

    @Override
    public <T> boolean existSValue(String key, T value) {
        return getRedisTemplate().opsForSet().isMember(key, value);
    }

    @Override
    public Set sget(String key) {
        return getRedisTemplate().opsForSet().members(key);
    }


    @Override
    public boolean setIfAbsent(String key, int value) {
        return this.getRedisTemplate().opsForValue().setIfAbsent(key, value);
    }

    @Override
    public boolean expire(String key, long expire) {
        getRedisTemplate().expire(key, expire, TimeUnit.SECONDS);

        return true;
    }


    @Override
    public <T> boolean setex(String key, long expire, T value) {
        getRedisTemplate().opsForValue().set(key, value, expire, TimeUnit.SECONDS);

        return true;
    }

    @Override
    public <T> T get(String key, Class<T> t) {
        Object value = getRedisTemplate().opsForValue().get(key);
        return (T) value;
    }

    @Override
    public <T> T hget(String key, String field, Class<T> t) {
        HashOperations<String, String, String> hashOperations = getRedisTemplate().opsForHash();
        Object value = hashOperations.get(key, field);
        return (T) value;
    }

    @Override
    public <T> T get(String key) {
        Object value = getRedisTemplate().opsForValue().get(key);
        return (T) value;
    }

    @Override
    public <T> T hget(String key, String field) {
        HashOperations<String, String, String> hashOperations = getRedisTemplate().opsForHash();
        Object value = hashOperations.get(key, field);
        return (T) value;
    }

    @Override
    public Map<Object, Object> hgetall(String key) {
        return getRedisTemplate().opsForHash().entries(key);
    }


    @Override
    public boolean hasKey(String key) {
        return getRedisTemplate().hasKey(key);
    }

    @Override
    public boolean hasField(String key, String field) {
        return getRedisTemplate().opsForHash().hasKey(key, field);
    }

    @Override
    public void del(String key) {
        getRedisTemplate().delete(key);
    }

    @Override
    public void del(String key, String field) {
        getRedisTemplate().opsForHash().delete(key, field);
    }

    @Override
    public boolean expireat(String key, Date date) {
        return getRedisTemplate().expireAt(key, date);
    }

    @Override
    public long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return getRedisTemplate().opsForValue().increment(key, delta);
    }


    @Override
    public int batchExpire(List<ObjectExpire> expires) {
        return getRedisTemplate().execute(new RedisCallback<Integer>() {
            @Override
            public Integer doInRedis(RedisConnection redisConnection) throws DataAccessException {
                RedisSerializer<String> serializer = new StringRedisSerializer();
                redisConnection.openPipeline();
                for (ObjectExpire expire : expires) {
                    redisConnection.expire(serializer.serialize(expire.getKey()), expire.getExpire());
                }
                List<Object> list = redisConnection.closePipeline();
                return 1;
            }
        });
    }

    @Override
    public void batchDel(List<String> keys) {
        getRedisTemplate().delete(keys);
    }

    @Override
    public boolean existKey(String key) {
        return getRedisTemplate().hasKey(key);
    }

    @Override
    public boolean setTimeout(String key, long timeout, TimeUnit unit) {
        return getRedisTemplate().expire(key, timeout, unit);
    }

    public void deleteKeysByPrefix(String prefix) {
        Set<String> keys = getRedisTemplate().keys(prefix + "*");
        if (CollUtil.isNotEmpty(keys)) {
            getRedisTemplate().delete(keys);
        }
    }
}
