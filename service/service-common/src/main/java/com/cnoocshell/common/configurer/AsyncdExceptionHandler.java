package com.cnoocshell.common.configurer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;

import java.lang.reflect.Method;

public class AsyncdExceptionHandler implements AsyncUncaughtExceptionHandler
{
    private final static Logger logger = LoggerFactory.getLogger(AsyncdExceptionHandler.class);

    @Override
    public void handleUncaughtException(Throwable throwable, Method method, Object... objects)
    {
        logger.info("Exception message - " + throwable.getMessage());
        logger.info("Method name - " + method.getName());
        for (Object param : objects)
        {
            logger.info("Parameter value - " + param);
        }
    }
}
