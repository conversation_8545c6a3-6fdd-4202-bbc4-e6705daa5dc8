package com.cnoocshell.common.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.service.ISmsSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class SmsSendService implements ISmsSendService {
    private final KafkaTemplate<String, String> kafkaTemplate;

    //限制短信批量发送100条
    @Value("${sms.send.limit.size:100}")
    private Integer limitSize;

    @Override
    public void sendSms(SmsDTO message) {
        log.info("kafka发送短信 >>>>{}<<<<<", message);
        if (CollUtil.isEmpty(message.getMobiles())) {
            log.info("手机号为空 MQ发送结束");
            return;
        }
        if(CollUtil.isNotEmpty(message.getTemplateParams())){
            //处理模板参数为空的数据为 空字符 （传null发送短信会失败）
            message.setTemplateParams(message.getTemplateParams().stream()
                    .map(v-> ObjectUtil.defaultIfNull(v,""))
                    .collect(Collectors.toList()));
        }

        // 短信发送平台限制单次批量发送最多100条
        if (Objects.nonNull(limitSize) && limitSize > 0) {
            int size = CollUtil.size(message.getMobiles());
            int page = size / limitSize + (size % limitSize == 0 ? 0 : 1); // 计算总页数
            for (int i = 0; i < page; i++) {
                try {
                    SmsDTO newMessage = new SmsDTO(message.getTemplateCode(), null, message.getTemplateParams());
                    newMessage.setMobiles(CollUtil.sub(message.getMobiles(), i * limitSize, Math.min((i + 1) * limitSize, size)));
                    kafkaTemplate.send(MqTopicConstant.SEND_SMS_TOPIC, JSONUtil.toJsonStr(newMessage)); // 使用新创建的消息对象发送
                } catch (Exception e) {
                    log.error("kafka发送短信异常： ", e);
                }
            }
            return;
        }

        try {
            kafkaTemplate.send(MqTopicConstant.SEND_SMS_TOPIC, JSONUtil.toJsonStr(message));
        } catch (Exception e) {
            log.error("kafka发送短信异常： ", e);
        }
    }
}
