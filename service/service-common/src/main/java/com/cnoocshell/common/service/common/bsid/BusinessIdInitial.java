package com.cnoocshell.common.service.common.bsid;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.common.BizRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 11:39 28/08/2018
 */
@Slf4j
@Component
public class BusinessIdInitial implements InitializingBean {
    @Value("${spring.application.name}")
    private String appName;
    @Autowired
    BizRedisService redisService;
    public static final String PREFIX = "BSID";

    @Override
    public void afterPropertiesSet() throws Exception {
        initKey();
    }
    public String initKey(){
        String key = getIncrementKey();
        if(!redisService.existKey(key)){
            boolean success = redisService.setIfAbsent(key, 1);
            if(!success){
                log.warn("redis key "+key+" had exist");
            }else{
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
                Date end = null;
                SimpleDateFormat FORMAT_DAY = new SimpleDateFormat("yyyyMMdd");
                String timeStr = FORMAT_DAY.format(new Date());
                try {
                    end = sdf.parse(timeStr+" 23:59:59");
                } catch (ParseException e) {
                    // TODO Auto-generated catch block
                    log.error("时间转换出错：",e);
                    throw new BizException(BasicCode.TIME_CONVERT_ERROR);
                }
                Date start = new Date();
                long second = (end.getTime() - start.getTime())/1000;
                redisService.setTimeout(key, second, TimeUnit.SECONDS);
            }
        }
        return key;
    }
    public String getIncrementKey(){
        String timeStr = DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT);
        if (appName.isEmpty()) {
            throw new NullPointerException();
        }
        return PREFIX + ":" + appName + ":" + timeStr;
    }
}
