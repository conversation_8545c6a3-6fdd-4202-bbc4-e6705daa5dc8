package com.cnoocshell.common.config.rsa;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;

@Configuration
@PropertySources({@PropertySource(value = "classpath:bootstrap.yml", ignoreResourceNotFound = true)})
@ConfigurationProperties(prefix="ras")
public class RSAConfig {
	public static final String KEY_ALGORITHM = "RSA";
	public static final String SIGNATURE_ALGORITHM = "MD5withRSA";

	public static final String PUBLIC_KEY = "RSAPublicKey";
	public static final String PRIVATE_KEY = "RSAPrivateKey";
	
    private String publicKeyUrl;
    private String privateKeyUrl;
    
    private String publicKeyStr;
    private String privateKeyStr;
    
	public String getPublicKeyUrl() {
		return publicKeyUrl;
	}
	
	public void setPublicKeyUrl(String publicKeyUrl) {
		this.publicKeyUrl = publicKeyUrl;
	}
	
	public String getPrivateKeyUrl() {
		return privateKeyUrl;
	}
	
	public void setPrivateKeyUrl(String privateKeyUrl) {
		this.privateKeyUrl = privateKeyUrl;
	}

	public String getPublicKeyStr() {
		return publicKeyStr;
	}

	public void setPublicKeyStr(String publicKeyStr) {
		this.publicKeyStr = publicKeyStr;
	}

	public String getPrivateKeyStr() {
		return privateKeyStr;
	}

	public void setPrivateKeyStr(String privateKeyStr) {
		this.privateKeyStr = privateKeyStr;
	}
	
    
}
