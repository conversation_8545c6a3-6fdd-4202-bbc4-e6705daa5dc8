package com.cnoocshell.common.service.common.bsid;

import com.cnoocshell.common.enums.BusinessCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 20:37 27/08/2018
 */
@Slf4j
@Component
public class CommonBusinessIdGenerator extends AbstractIBusinessIdGenerator {
    @Override
    public String businessCodePrefix() {
        return "";
    }

    public String incrementPriceCode(){
        return incrementCode(BusinessCode.PRICE);
    }

    public String incrementOrderCode(){
        return getEnv() + incrementCode(BusinessCode.ORDER);
    }

    public String incrementGoodsCode(){
        return incrementCode(BusinessCode.GOODS);
    }

    public String incrementResourceCode(){
        return incrementCode(BusinessCode.RESOURCE);
    }

    public String incrementTakeCode(){
        return incrementCode(BusinessCode.TAKE);
    }

    public String incrementPickCode(){
        return incrementCode(BusinessCode.PICK);
    }

    public String incrementDispatcherCode(){
        return incrementCode(BusinessCode.DISPATCHER);
    }

    public String incrementTransportCode(){
        return incrementCode(BusinessCode.TRANSPORT);
    }

    public String incrementRepertoryCode(){
        return incrementCode(BusinessCode.REPERTORY);
    }

    public String incrementUnloadingCode(){
        return incrementCode(BusinessCode.UNLOADING);
    }

    public String incrementPayCode(){
        return incrementCode(BusinessCode.PAY);
    }

    public String incrementChannelCode(){
        return incrementCode(BusinessCode.CHANNEL);
    }

    public String incrementRefundCode(){
        return incrementCode(BusinessCode.REFUND);
    }

    public String incrementWithdrawalCode(){
        return incrementCode(BusinessCode.WITHDRAWAL);
    }

    public String incrementRechargeCode(){
        return incrementCode(BusinessCode.RECHARGE);
    }

    public String incrementPaySplitCode(){
        return incrementCode(BusinessCode.PAY_SPLIT);
    }

    public String incrementOrderPayInfoCode(){
        return incrementCode("P");
    }
}
