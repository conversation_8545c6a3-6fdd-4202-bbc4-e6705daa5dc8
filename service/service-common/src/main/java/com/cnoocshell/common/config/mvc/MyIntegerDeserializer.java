package com.cnoocshell.common.config.mvc;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.jsontype.TypeDeserializer;

import java.io.IOException;

public class MyIntegerDeserializer extends StdDeserializer<Integer> {

    protected MyIntegerDeserializer(Class<?> vc) {
        super(Integer.class);
    }

    @Override
    public Integer deserializeWithType(JsonParser p, DeserializationContext ctxt, TypeDeserializer typeDeserializer) throws IOException {
        if (p.hasToken(JsonToken.VALUE_NUMBER_INT)) {
            return p.getIntValue();
        }
        return deserialize(p, ctxt);
    }

    @Override
    public Integer deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        if (p.hasToken(JsonToken.VALUE_NUMBER_INT)) {
            return p.getIntValue();
        }
        try{
            return p.getIntValue();
        }catch (Exception e){
            try {
                String value = p.getValueAsString();
                if( value == null || "".equals(value.trim())){
                    return null;
                }
                value = value.trim().toLowerCase();
                if("1".equals(value) || "true".equals(value) || "y".equals(value)){
                    return 1;
                }
                if("0".equals(value) || "false".equals(value) || "n".equals(value)){
                    return 0;
                }
                return Integer.valueOf(value);
            }catch (Exception e1){

            }
        }
        return null;
    }
}
