package com.cnoocshell.common.config.session;

import com.cnoocshell.common.config.redis.RedisConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.session.data.redis.config.ConfigureRedisAction;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;
import org.springframework.session.web.http.HeaderHttpSessionIdResolver;


/**
 *http session 配置
 * 会话超时默认15分钟
 */
@Configuration
@EnableRedisHttpSession(maxInactiveIntervalInSeconds = 15 * 60)
@Slf4j
public class HttpSessionConfig extends RedisConfig {

    @Bean
    public static ConfigureRedisAction configureRedisAction() {
        return ConfigureRedisAction.NO_OP;
    }

    @Bean(name = "sessionRedisConnectionFactory")
    public RedisConnectionFactory connectionFactory() {
        return createJedisConnectionFactory();
    }

    @Bean
    public CookieSerializer cookieSerializer(){
        log.info("cookieSerializer init...");
        DefaultCookieSerializer cookieSerializer = new DefaultCookieSerializer();
        cookieSerializer.setCookieName("SESSIONID");
        //IllegalArgumentException: Invalid cookie domain: .test1.com
        cookieSerializer.setDomainName(".cnoocshell.com");
        cookieSerializer.setCookiePath("/");
        cookieSerializer.setUseSecureCookie(true);
        cookieSerializer.setSameSite("None");
      //  cookieSerializer.setSameSite(null);

        return cookieSerializer;
    }

    //spring boot 2 依赖spring-session-data-redis
    @Bean
    public HeaderHttpSessionIdResolver httpSessionStrategy() {
        return new HeaderHttpSessionIdResolver("x-auth-token");
    }
}
