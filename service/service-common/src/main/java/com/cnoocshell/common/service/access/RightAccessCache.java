package com.cnoocshell.common.service.access;


import com.cnoocshell.common.service.ICacheEntry;
import lombok.Data;

/**
 * 权限角色
 */
@Data
public class RightAccessCache implements ICacheEntry<String> {
    private String key;
    /**
     * 链接
     */
    private String url;
    /**
     * 链接对应的所有角色
     */
    private long[] roles;

    @Override
    public String getKey() {
        return getUrl();
    }

}
