package com.cnoocshell.common.service.lock;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * @Author: <EMAIL>
 * @Description: 分布式锁redis实现
 * （1）获取锁的时候，使用setnx加锁，并使用expire命令为锁添加一个超时时间，超过该时间则自动释放锁，锁的value值为一个随机生成的UUID，通过此在释放锁的时候进行判断。
 *（2）获取锁的时候还设置一个获取的超时时间，若超过这个时间则放弃获取锁。
 *（3）释放锁的时候，通过UUID判断是不是该锁，若是该锁，则执行delete进行锁释放。
 * @Date: Create in 14:33 13/09/2018
 */
@Slf4j
@Component
public class DistributedLock {

    @Autowired
    @Qualifier("bizRedisTemplate")
    private RedisTemplate redisTemplate;

    private static final String LOCK_PRE = "lock:";

    public DistributedLock() {

    }

    public DistributedLock(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    /**
     * 加锁
     * @param lockName       锁的key
     * @param acquireTimeout 获取超时时间
     * @param timeout        锁的超时时间
     * @return 锁标识
     */
    public String lockWithTimeout(String lockName, long acquireTimeout, long timeout) {
       return (String)redisTemplate.execute((RedisCallback) conn -> {
            String retIdentifier = null;
            try {
                // 随机生成一个value
                String identifier = UUID.randomUUID().toString();
                // 锁名，即key值
                String lockKey = LOCK_PRE + lockName;
                // 超时时间，上锁后超过此时间则自动释放锁
                int lockExpire = (int) (timeout/1000);

                // 获取锁的超时时间，超过这个时间则放弃获取锁
                long end = System.currentTimeMillis() + acquireTimeout;
                while (System.currentTimeMillis() < end) {
                    if (conn.setNX(lockKey.getBytes(), identifier.getBytes())) {
                        conn.expire(lockKey.getBytes(), lockExpire);
                        // 返回value值，用于释放锁时间确认
                        retIdentifier = identifier;
                        log.info(Thread.currentThread().getName()+":setNX success"+lockKey.getBytes());
                        return retIdentifier;
                    }
                    // 返回-1代表key没有设置超时时间，为key设置一个超时时间
                    setOutTimeAndSleep(conn, lockKey, lockExpire);
                }
                if(System.currentTimeMillis() >= end){
                    log.info(Thread.currentThread().getName()+":connect timeout");
                }
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                Thread.currentThread().interrupt();
            } finally {
                if (conn != null) {
                    conn.close();
                }
            }
            return retIdentifier;
        });

    }

    private static void setOutTimeAndSleep(RedisConnection conn, String lockKey, int lockExpire) {
        if (conn.ttl(lockKey.getBytes()) == -1) {
            log.info(Thread.currentThread().getName()+":expire fail");
            conn.expire(lockKey.getBytes(), lockExpire);
        }

        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 加锁
     * @param lockName       锁的key
     * @param timeout        锁的超时时间
     * @return 锁标识
     */
    public String lockWithTimeout(String lockName, long timeout) {
        return (String)redisTemplate.execute((RedisCallback) conn -> {
            String retIdentifier = null;
            try {
                // 随机生成一个value
                String identifier = UUID.randomUUID().toString();
                // 锁名，即key值
                String lockKey = LOCK_PRE + lockName;
                // 超时时间，上锁后超过此时间则自动释放锁
                int lockExpire = (int) (timeout/1000);
                if (conn.setNX(lockKey.getBytes(), identifier.getBytes())) {
                    conn.expire(lockKey.getBytes(), lockExpire);
                    // 返回value值，用于释放锁时间确认
                    retIdentifier = identifier;
                    log.info(Thread.currentThread().getName()+":setNX success"+lockKey.getBytes());
                    return retIdentifier;
                }
                Long remainTime = conn.ttl(lockKey.getBytes());
                log.info("lockName: {} setNX is false, exist key left time : {}",lockName,remainTime);
                // 返回-1代表key没有设置超时时间，为key设置一个超时时间
                if ( remainTime == -1) {
                    log.info(Thread.currentThread().getName()+":expire fail");
                    conn.expire(lockKey.getBytes(), lockExpire);
                }
            } catch (Exception e) {
                log.error("lockWithTimeout error:{}",e.getMessage(),e);
            } finally {
                if (conn != null) {
                    conn.close();
                }
            }
            return retIdentifier;
        });

    }

    /**
     * 释放锁
     * @param lockName   锁的key
     * @param identifier 释放锁的标识
     * @return
     */
    public boolean releaseLock(String lockName, String identifier) {
        if( identifier == null ){
            return true;
        }
        String lockKey = LOCK_PRE + lockName;

        return (boolean)redisTemplate.execute((RedisCallback) conn -> {
            boolean retFlag = false;
            try {
                while (true) {
                    // 监视lock，准备开始事务
                    conn.watch(lockKey.getBytes());
                    // 通过前面返回的value值判断是不是该锁，若是该锁，则删除，释放锁
                    String result = getResult(conn, lockKey);

                    log.info(Thread.currentThread().getName()+":get value for del:"+result);
                    if (identifier.equals(result)) {
                        conn.multi();
                        conn.del(lockKey.getBytes());
                        List<Object> results = conn.exec();
                        if (results == null) {
                            log.info(Thread.currentThread().getName()+":delete fail");
                            continue;
                        }
                        log.info(Thread.currentThread().getName()+":delete success");
                        retFlag = true;
                    }else{
                        log.info(Thread.currentThread().getName()+":identifier change");
                    }
                    conn.unwatch();
                    break;
                }
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            } finally {
                if (conn != null) {
                    conn.close();
                }
            }
            return retFlag;
        });

    }

    @NotNull
    private static String getResult(RedisConnection conn, String lockKey) {
        byte[] resultByte = conn.get(lockKey.getBytes());
        String result = "";
        if(resultByte!=null){
            result = new String(resultByte);
        }
        return result;
    }
}
