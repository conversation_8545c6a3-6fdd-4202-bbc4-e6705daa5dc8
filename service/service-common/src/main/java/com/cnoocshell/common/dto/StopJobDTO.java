package com.cnoocshell.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StopJobDTO {
    @ApiModelProperty("执行器名称")
    private String appName;
    @ApiModelProperty("定时任务名称")
    private List<String> executorHandlers;
    @ApiModelProperty("执行器参数")
    private String executorParam;
}
