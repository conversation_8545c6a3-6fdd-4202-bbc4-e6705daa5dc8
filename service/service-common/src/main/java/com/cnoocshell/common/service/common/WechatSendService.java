package com.cnoocshell.common.service.common;

import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.dto.SmsDTO;
import com.cnoocshell.common.dto.WechatDTO;
import com.cnoocshell.common.dto.WechatSendDTO;
import com.cnoocshell.common.service.ISmsSendService;
import com.cnoocshell.common.service.IWechatSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class WechatSendService implements IWechatSendService {
    private final KafkaTemplate<String,String> kafkaTemplate;

    @Override
    public void sendWechat(WechatSendDTO message) {
        log.info("kafka发送微信订阅消息 >>>>{}<<<<<",message);
        try{
            kafkaTemplate.send(MqTopicConstant.SEND_WECHAT_TOPIC, JSONUtil.toJsonStr(message));
        }catch (Exception e){
            log.error("kafka发送微信订阅消息： ",e);
        }
    }
}
