package com.cnoocshell.common.service;

import com.cnoocshell.common.config.redis.ObjectExpire;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Author: <EMAIL>
 * @discription: redis服务接口
 * @creat_date: 2018/1/10
 * @creat_time: 11:11
 **/
public interface RedisService {
    /**
     * @Author: <EMAIL>
     * @discription:
     * @date : 11:42 2018/1/10
     * @param  * @param null
     **/
    <T> boolean sAdd(String key, Set<T> value);

    <T> boolean sRemove(String key, Set<T> value);

    <T> Set<T> sMembers(String key);

   <T> boolean set(String key, T value);

   <T> boolean sset(String key, T value);

    <T> Set<T> sget(String key);

   <T>boolean existSValue(String key,T value);

   <T> boolean hset(String key,String field, T value);

    boolean setIfAbsent(String key, int value);

    boolean expire(String key, long expire);

    <T> boolean setex(String key, long expire, T value);

    <T> T get(String key, Class<T> t);

    <T> T hget(String key,String field, Class<T> t);

    <T> T get(String key);

    <T> T hget(String key,String field);

    Map<Object, Object> hgetall(String key);

    boolean hasKey(String key);

    boolean hasField(String key,String field);

    void del(String key);

    void del(String key,String field);

    boolean expireat(String key, Date date);

    long incr(String key, long delta);


    int batchExpire(List<ObjectExpire> expires);

    void batchDel(List<String> keys);

    boolean existKey(String key);

    boolean setTimeout(String key, long timeout, TimeUnit unit);
}
