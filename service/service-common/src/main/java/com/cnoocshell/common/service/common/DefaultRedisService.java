package com.cnoocshell.common.service.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 16:53 10/09/2018
 */
//@Component
public class DefaultRedisService extends AbstractRedisService {

    @Autowired
    @Qualifier("defaultRedisTemplate")
    private RedisTemplate<String,Object> redisTemplate;

    @Override
    public RedisTemplate<String, Object> getRedisTemplate() {
        return redisTemplate;
    }
}
