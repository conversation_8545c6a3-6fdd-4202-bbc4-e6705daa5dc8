package com.cnoocshell.common.service.token;

import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.exception.BizException;
import com.cnoocshell.common.service.ITokenManager;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class JwtITokenManager implements ITokenManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(JwtITokenManager.class);

    private static final String CLAIM_KEY_USERNAME = "sub";
    private static final String CLAIM_KEY_AUDIENCE = "audience";
    private static final String CLAIM_KEY_CREATED = "created";
    private static final String CLAIM_KEY_USERID = "userId";

    @Value("${jwt.secret:}")
    private String secret;

    @Value("${jwt.expiration:}")
    private Long expiration;

    @Override
    public String getUsernameFromToken(String token) {
        String username = null;
        try {
            final Claims claims = getClaimsFromToken(token);
            if(null != claims) username = claims.getSubject();
        } catch (Exception e) {
            LOGGER.error("解析token时异常,token={}", token);
            throw new BizException(BasicCode.TOKEN_ERROR,
                    "token 解析userName失败");
        }

        return username;
    }

    @Override
    public String getUserIdFromToken(String token){
        long start = System.currentTimeMillis();
        String userId = null;
        try {
            final Claims claims = getClaimsFromToken(token);
            if(null != claims) userId = String.valueOf(claims.get(CLAIM_KEY_USERID));
        } catch (Exception e) {
            LOGGER.error("解析token时异常,token=" + token, e);
            throw new BizException(BasicCode.TOKEN_ERROR,
                    "解析userId失败");
        }
        long end = System.currentTimeMillis();

        LOGGER.info("method getUserIdFromToken cast:"+(end-start)/1000+" s");
        return userId;
    }


    public Date getCreatedDateFromToken(String token) {
        Date created = null;
        try {
            final Claims claims = getClaimsFromToken(token);
            if(null != claims) created = new Date((Long) claims.get(CLAIM_KEY_CREATED));
        } catch (Exception e) {
            LOGGER.error("解析token时异常,token=" + token, e);
            throw new BizException(BasicCode.TOKEN_ERROR,
                    "解析Date失败");
        }
        return created;
    }

    public Date getExpirationDateFromToken(String token) {
        Date expiration = null;
        try {
            final Claims claims = getClaimsFromToken(token);
            if(null != claims) expiration = claims.getExpiration();
        } catch (Exception e) {
            expiration = null;
        }
        return expiration;
    }

    private Claims getClaimsFromToken(String token) {
        Claims claims;
        try {
            claims = Jwts.parser()
                    .setSigningKey(secret)
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            claims = null;
        }
        return claims;
    }

    private Date generateExpirationDate() {
        return new Date(System.currentTimeMillis() + expiration * 1000);
    }

    private Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    private Boolean isCreatedBeforeLastPasswordReset(Date created, Date lastPasswordReset) {
        return (lastPasswordReset != null && created.before(lastPasswordReset));
    }

    @Override
    public String genrateToken(String userId){
        Map<String, Object> claims = new HashMap<>();
//        claims.put(CLAIM_KEY_CREATED, System.currentTimeMillis());
        claims.put(CLAIM_KEY_USERID, userId);
        return generateToken(claims);
    }
    String generateToken(Map<String, Object> claims) {
        return Jwts.builder()
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    public Boolean canTokenBeRefreshed(String token, Date lastPasswordReset) {
        final Date created = getCreatedDateFromToken(token);
        return !isCreatedBeforeLastPasswordReset(created, lastPasswordReset)
                && (!isTokenExpired(token));
    }

    public String refreshToken(String token) {
        String refreshedToken = null;
        try {
            final Claims claims = getClaimsFromToken(token);
            if(null != claims){
                claims.put(CLAIM_KEY_CREATED, new Date());
                refreshedToken = generateToken(claims);
            }
        } catch (Exception e) {
            refreshedToken = null;
        }
        return refreshedToken;
    }

}