package com.cnoocshell.common.config.feign;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.exception.BasicCode;
import com.cnoocshell.common.result.ItemResult;
import feign.FeignException;
import feign.Request;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpStatus;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * 用于微服务之间的异常处理
 */
@Slf4j
public class ResultErrorDecoder implements ErrorDecoder {

    /**
     * 对于restful抛出的4xx的错误，也许大部分是业务异常，并不是服务提供方的异常
     * 因此在进行feign client调用的时候，需要进行errorDecoder去处理，
     * 适配为HystrixBadRequestException，好避开circuit breaker的统计，否则就容易误判
     */
    @SneakyThrows
    @Override
    public Exception decode(String methodKey, Response response) {
        String body = StrUtil.str(response.body().asInputStream().readAllBytes(),StandardCharsets.UTF_8);
        log.error("服务层异常传递：{}",body);
        if(CharSequenceUtil.isBlank(body)){
            body = JSONUtil.toJsonStr(ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"系统异常，请联系管理员"));
        }

        FeignException feignException = FeignException.errorStatus(methodKey, response);
        Request request = response.request();
        String message = feignException.contentUTF8();
        log.error("FeignException invoke exception,url:[" + request.url() + "],msg:[" + message + "],body:[" + body + "]");

        feignException.initCause(new ServiceException(body));
        return feignException;
    }

    private String getBody(Response response) {
        String body = null;
        try {
            InputStream stream = response.body().asInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BufferedInputStream inputStream = new BufferedInputStream(stream);

            // 一次读多个字节
            byte[] buf = new byte[8096];
            // 读入多个字节到字节数组中，size为一次读入的字节数
            int len = 0;
            while ((len = inputStream.read(buf)) != -1) {
                outputStream.write(buf, 0, len);
            }
            inputStream.close();
            stream.close();

            body = new String(outputStream.toByteArray(), StandardCharsets.UTF_8);
        } catch (Exception ignored) {
        }

        return body;
    }
}
