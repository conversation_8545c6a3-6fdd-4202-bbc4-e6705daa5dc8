package com.cnoocshell.common.service.common;

import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.cnoocshell.common.constant.MqTopicConstant;
import com.cnoocshell.common.dto.EmailDTO;
import com.cnoocshell.common.service.IEmailSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class EmailSendService implements IEmailSendService {
    private final KafkaTemplate<String,String> kafkaTemplate;

    @Override
    public void sendEmail(EmailDTO email) {
        log.info("kafka发送邮件 >>>>{}<<<<<",email);
        try{
            kafkaTemplate.send(MqTopicConstant.SEND_EMAIL_TOPIC, JSONUtil.toJsonStr(email, JSONConfig.create().setIgnoreNullValue(false)));
        }catch (Exception e){
            log.error("kafka发送消息异常： ",e);
        }
    }
}
