package com.cnoocshell.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <pre>
 * 添加注解：@ComponentScan({"com.cnoocshell.common.aop.log","com.cnoocshell.kafka"}),@EnableKafka
 * 添加依赖：
 * 		<dependency>
 * 			<groupId>com.cnoocshell</groupId>
 * 			<artifactId>kafka-spring-boot-starter</artifactId>
 * 		</dependency>
 * 添加配置：
 * spring:
 *   kafka:
 *     bootstrap-servers: ${kafka_bootstrap_servers:193.112.193.72:9092}
 *     producer:
 *       retries: 0
 *       batch-size: 16384
 *       buffer-memory: 33554432
 *       key-serializer: org.apache.kafka.common.serialization.StringSerializer
 *       value-serializer: org.apache.kafka.common.serialization.StringSerializer
 *
 * 消息发送到:
 * message.setExchange("sysLogQueue");
 *
 * 消息发送实现代码示例：
 *
 *     @Autowired
 *     @Qualifier("kafkaProducer")
 *     private IMQProducer producer;
 *
 *     @Value("${spring.application.name:unknown}")
 *     private String centerName;
 *     @Value("${spring.cloud.client.ipAddress:unknown}")
 *     private String centerIp;
 *
 *     public void successLog(String moduleName,String operMethod){
 *         sendSysLog(new SysLogAddDTO(moduleName,operMethod));
 *     }
 *     public void successLog(String moduleName,String operMethod,Long operStartTime){
 *         sendSysLog(new SysLogAddDTO(moduleName,operMethod,operStartTime));
 *     }
 *     public void successLog(String moduleName,String operMethod,Long operStartTime,Long operEndTime){
 *         sendSysLog(new SysLogAddDTO(moduleName,operMethod,operStartTime,operEndTime));
 *     }
 *
 *     public void sendSysLog(SysLogAddDTO sysLogAddDTO){
 *         if( sysLogAddDTO == null ){
 *             return;
 *         }
 *         if( StringUtils.isBlank(sysLogAddDTO.getCenterName()) ){
 *             sysLogAddDTO.setCenterName(centerName);
 *         }
 *         if( StringUtils.isBlank(sysLogAddDTO.getCerterIp()) ){
 *             sysLogAddDTO.setCerterIp(centerIp);
 *         }
 *         MQMessage<SysLogAddDTO> message = new MQMessage<>();
 *         message.setData(sysLogAddDTO);
 *         message.setMessageType("sysLog");
 *         message.setExchange("sysLogQueue");
 *
 *         message.setMsgId(UUID.randomUUID().toString());
 *         try {
 *             producer.send(message);
 *         } catch (Exception e) {
 *             log.error(e.getMessage(),e);
 *         }
 *     }
 *</pre>
 *
 * @Author: <EMAIL>
 * @Date: 24/12/2018 13:24
 * @DESCRIPTION:
 */
@ApiModel("系统日志添加DTO")
@Data
@AllArgsConstructor
public class LogAddDTO {

    @ApiModelProperty("操作结果：成功")
    public static final String OPER_RESULT_SUCCESS="success";
    @ApiModelProperty("操作结果：失败")
    public static final String OPER_RESULT_FAIL="fail";
    @ApiModelProperty("操作结果：异常")
    public static final String OPER_RESULT_EXCEPTION="exception";

    @ApiModelProperty("中心或web层应用名称(自动获取，不需要填写)")
    private String centerName;
    @ApiModelProperty("中心或web层应用所在ip(自动获取，不需要填写)")
    private String certerIp;
    @ApiModelProperty("操作模块(服务名称)")
    private String moduleName;
    @ApiModelProperty("操作动作(服务方法名称)")
    private String operMethod;

    @ApiModelProperty("操作动作子方法(服务内部调用具体类名+方法名称)")
    private String operSubMethod;

    @ApiModelProperty("请求id")
    private String sessionId;

    @ApiModelProperty("业务编号")
    private String businessNumber;

    @ApiModelProperty("操作结果(success/fail/exception)")
    private String operResult;

    @ApiModelProperty("异常名称(类名)")
    private String exception;

    @ApiModelProperty("异常代码")
    private String exceptionCode;

    @ApiModelProperty("操作开始时间(时间戳)")
    private Long operStartTime;

    @ApiModelProperty("操作结束时间(时间戳)")
    private Long operEndTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("操作耗时(毫秒)")
    private Long operCostTime;

    @ApiModelProperty("操作人所在会员id")
    private String operMemberId;

    @ApiModelProperty("操作人所在会员code")
    private String operMemberCode;

    @ApiModelProperty("操作人id")
    private String createUser;

    @ApiModelProperty("操作人ip")
    private String createUserIp;

    @ApiModelProperty("操作入参")
    private String operInputParams;

    @ApiModelProperty("操作出参(返回值)")
    private String operReturnValue;

    @ApiModelProperty("错误消息")
    private String errorMessage;

    @ApiModelProperty("异常堆栈")
    private String stackTraceInfo;

    public LogAddDTO(){

    }

    public LogAddDTO(String moduleName, String operMethod){
        this(moduleName,operMethod,System.currentTimeMillis());
    }

    public LogAddDTO(String moduleName, String operMethod, Long operStartTime){
        this(moduleName,operMethod,operStartTime,System.currentTimeMillis());
    }

    public LogAddDTO(String moduleName, String operMethod, Long operStartTime, Long operEndTime){
        this.moduleName = moduleName;
        this.operMethod = operMethod;
        this.operResult = OPER_RESULT_SUCCESS;
        this.operStartTime = operStartTime;
        this.operEndTime = operEndTime;
        if( operStartTime != null && operEndTime != null ) {
            this.operCostTime = operEndTime - operStartTime;
        }
    }

    public LogAddDTO(String moduleName, String operMethod, String operSubMethod, String operResult, Long operStartTime){
        this.moduleName = moduleName;
        this.operMethod = operMethod;
        this.operSubMethod = operSubMethod;
        this.operResult = operResult;
        this.operStartTime = operStartTime;
    }
}
