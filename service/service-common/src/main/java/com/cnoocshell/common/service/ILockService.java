package com.cnoocshell.common.service;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 15:12 13/09/2018
 */
public interface ILockService {

    /**
     * 枷锁
     * @param key
     * @return
     */
    String lock(String key);
    /**
     * 加锁
     * @param key       锁的key
     * @param acquireTimeout 获取超时时间
     * @param timeout        锁的超时时间
     * @return 锁标识
     */
    String lockWithTimeout(String key, Long acquireTimeout, Long timeout);
    /**
     * 枷锁 获取锁等待时间为0，一次没有成功则马上返回
     * @param key
     * @return
     */
    String lockFast(String key);
    /**
     * 枷锁 获取锁等待时间为0，超过重试次数没有成功则则返回
     */
    String lockFast(String key,int retryCount);

    /**
     * 解锁
     * @param identifier
     */
    void unlock(String key,String identifier);
}
