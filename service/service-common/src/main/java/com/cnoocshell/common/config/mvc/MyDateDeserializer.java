package com.cnoocshell.common.config.mvc;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.core.JsonTokenId;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.jsontype.TypeDeserializer;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class MyDateDeserializer extends StdDeserializer<Date> {


    public MyDateDeserializer(Class<?> vc) {
        super(Date.class);
    }



    @Override
    public Date deserializeWithType(JsonParser p, DeserializationContext ctxt, TypeDeserializer typeDeserializer) throws IOException {
        return deserialize(p, ctxt);
    }

    //格式不够再加
    private static final List<String> formatStringList = Lists.newArrayList("yyyy-MM-dd","yyyy-MM-dd HH:mm:ss","yyyy-MM-dd HH:mm:ss.SSS","yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    private static final Set<String> formatStringAdd8HourSet = Sets.newHashSet("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");


    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        if(p.getCurrentTokenId() == JsonTokenId.ID_NUMBER_INT){
            long ts;
            try {
                ts = p.getLongValue();
            } catch (JsonParseException e) {
                Number v = (Number) ctxt.handleWeirdNumberValue(_valueClass, p.getNumberValue(),
                        "not a valid 64-bit long for creating `java.util.Date`");
                ts = v.longValue();
            }
            return new java.util.Date(ts);
        }
        if (p.hasToken(JsonToken.VALUE_STRING)) {
            String str = p.getText().trim();
            int len = str.length();
            if ( len == 0) {
                return null;
            }
            try {
                return new Date(Long.parseLong(str));
            } catch (NumberFormatException e) {
            }
            for (String s : formatStringList) {
                int pattern = s.length();
                if(len == pattern || len + 4 == pattern || len + 2 == pattern){
                    return getDate(ctxt, s, str);
                }
            }
            return (java.util.Date) ctxt.handleWeirdStringValue(handledType(), str,
                    "expected format \"%s\"", formatStringList.stream().collect(Collectors.joining(",")));
        }
        return null;
    }

    private Date getDate(DeserializationContext ctxt, String s, String str) throws IOException {
        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat(s);
        synchronized(simpleDateFormat1) {
            try {
                return add8Hour(simpleDateFormat1.parse(str), s, str);
            } catch (ParseException e) {
                return (Date) ctxt.handleWeirdStringValue(handledType(), str,
                        "expected format \"%s\"", s);
            }
        }
    }

    private Date add8Hour(Date date,String formatStr,String dateStr){
        if( formatStringAdd8HourSet.contains(formatStr)){
            Date result = new Date(date.getTime() + 28800000L);
            log.info("source date:{},format:[{}] and add 8 hour : {}",dateStr,formatStr,result);
            return result;
        }
        return date;
    }
}
