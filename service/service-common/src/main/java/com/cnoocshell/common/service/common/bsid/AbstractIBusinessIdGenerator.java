package com.cnoocshell.common.service.common.bsid;

import com.cnoocshell.common.enums.BusinessCode;
import com.cnoocshell.common.service.common.BizRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 20:37 27/08/2018
 */
@Slf4j
public abstract class AbstractIBusinessIdGenerator implements IBusinessIdGenerator {
    @Autowired
    private BizRedisService redisService;
    @Autowired
    private BusinessIdInitial businessIdInitial;
    @Value("${spring.profiles.active:dev}")
    private String profile;

    public static String PATTERN = "yyyyMMdd";
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(PATTERN);

    @Override
    public Long gain() {
        Long num;
        String key = businessIdInitial.getIncrementKey();
        if (!redisService.existKey(key)) {
            //不存在就报错
            businessIdInitial.initKey();
            log.info("new business key:"+key);
            num = 1L;
        } else {
            num = redisService.incr(key, 1);
        }

        return num;
    }

    @Override
    public String gainString() {
        Long incrNum = gain();
        String value = incrNum.toString();
        StringBuffer result = new StringBuffer();
        if (value.length() < 6) {
            int len = 6 - value.length();
            for (int i = len; i > 0; i--) {
                result.append("0");
            }
        }
        result.append(value);
        return result.toString();
    }

    @Override
    public String gainDateString(){
        return LocalDateTime.now().format(DATE_TIME_FORMATTER);
    }

    @Override
    public String incrementCode() {
        return new StringBuilder(businessCodePrefix()).
                append(LocalDateTime.now().format(DATE_TIME_FORMATTER)).
                append(gainString()).toString();
    }

    public String incrementCode(BusinessCode businessCode){
        return incrementCode(businessCode.getCode());
    }

    public String incrementCode(String businessCode){
        return new StringBuilder(businessCode).
                append(LocalDateTime.now().format(DATE_TIME_FORMATTER)).
                append(gainString()).toString();
    }

    public String getEnv(){
        switch (profile.toLowerCase()){
            case "dev" :
            case "test" :
            case "sit" :
            case "uat" :
            case "newuat" : return profile.toUpperCase().substring(0,1);
            default: return "";
        }
    }
}
