package com.cnoocshell.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class InterfaceLogInfoDTO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("接口ID")
    private String interfaceId;

    @ApiModelProperty("消息ID")
    private String messageId;

    @ApiModelProperty("接口名称")
    private String interfaceName;

    @ApiModelProperty("传入的参数")
    private String params;

    @ApiModelProperty("返回的信息")
    private String returnMessage;

    @ApiModelProperty("返回编码")
    private String returnCode;

    @ApiModelProperty("错误消息")
    private String errorMessage;

    @ApiModelProperty("日志信息")
    private String logInfo;

    @ApiModelProperty("方法执行开始时间")
    private Date startTime;

    @ApiModelProperty("方法执行结束时间")
    private Date endTime;

    @ApiModelProperty("创建时间")
    private Date createTime;
}
