package com.cnoocshell.common.service.common.uuid;

import java.security.SecureRandom;

public final class RandomShortUUID implements UUIDGenerator {
    private static SecureRandom numberGenerator = new SecureRandom();
    static final char[] Digits = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x'};
    private static final int RADIX;

    static {
        RADIX = Digits.length;
    }

    private static StringBuilder getBuilder() {
        SecureRandom ng = numberGenerator;
        byte[] data = new byte[16];
        ng.nextBytes(data);
        StringBuilder sb = new StringBuilder(25);
        long bits = (long) (data[0] & 255);
        bits = bits << 8 | (long) (data[1] & 255);
        bits = bits << 8 | (long) (data[2] & 255);
        bits = bits << 8 | (long) (data[3] & 255);
        bits = bits << 8 | (long) (data[4] & 255);
        bits = bits << 8 | (long) (data[5] & 255);
        bits = bits << 8 | (long) (data[6] & 255);
        bits = bits << 6 | (long) (data[7] & 63);
        append(bits, sb);
        bits = (long) (data[8] & 255);
        bits = bits << 8 | (long) (data[9] & 255);
        bits = bits << 8 | (long) (data[10] & 255);
        bits = bits << 8 | (long) (data[11] & 255);
        bits = bits << 8 | (long) (data[12] & 255);
        bits = bits << 8 | (long) (data[13] & 255);
        bits = bits << 8 | (long) (data[14] & 255);
        bits = bits << 4 | (long) (data[15] & 15);
        append(bits, sb);
        return sb;
    }

    public static String getFixSize() {
        StringBuilder sb = getBuilder();
        int needAdd = 25 - sb.length();

        for (int i = 0; i < needAdd; ++i) {
            sb.append('0');
        }

        return sb.toString();
    }

    public static String get() {
        return getBuilder().toString();
    }

    @Override
    public String gain() {
        return get();
    }

    private static void append(long i, StringBuilder sb) {
        char[] buf = new char[13];
        int charPos = 12;

        for (i = -i; i <= (long) (-RADIX); i /= (long) RADIX) {
            buf[charPos--] = Digits[(int) (-(i % (long) RADIX))];
        }

        buf[charPos] = Digits[(int) (-i)];
        sb.append(buf, charPos, 13 - charPos);
    }
}
