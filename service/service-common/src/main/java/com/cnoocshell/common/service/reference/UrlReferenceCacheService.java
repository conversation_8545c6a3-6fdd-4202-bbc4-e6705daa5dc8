package com.cnoocshell.common.service.reference;

import com.cnoocshell.common.service.IUrlReferenceCacheService;
import com.cnoocshell.common.service.common.BizRedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UrlReferenceCacheService implements IUrlReferenceCacheService {
    private static final String REFERENCE_URL_CACHE_NAME = "referenceUrlCache";
    @Autowired
    private BizRedisService redisService;
    @Override
    public ReferenceUrlCache getUrlReference(String url) {
        return redisService.hget(REFERENCE_URL_CACHE_NAME,url,ReferenceUrlCache.class);
    }

    @Override
    public List getAllReference() {
        return (List)redisService.getRedisTemplate().opsForHash().values(REFERENCE_URL_CACHE_NAME);
    }

    @Override
    public boolean hasExist(String url) {
        return redisService.existSValue(REFERENCE_URL_CACHE_NAME,url);
    }

    @Override
    public void setUrlReference(ReferenceUrlCache value) {
        String url = value.getUrl();
        ReferenceUrlCache cache = this.getUrlReference(url);
        if(cache==null){
            redisService.hset(REFERENCE_URL_CACHE_NAME,url,value);
        }else{
            cache.getList().addAll(value.getList());
            redisService.hset(REFERENCE_URL_CACHE_NAME,url,cache);
        }
    }

}
