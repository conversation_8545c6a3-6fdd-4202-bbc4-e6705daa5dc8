package com.cnoocshell.common.config.mvc;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.jsontype.TypeDeserializer;

import java.io.IOException;

public class MyBooleanDeserializer extends StdDeserializer<Boolean> {

    protected MyBooleanDeserializer(Class<?> vc) {
        super(Boolean.class);
    }

    @Override
    public Object deserializeWithType(JsonParser p, DeserializationContext ctxt, TypeDeserializer typeDeserializer) throws IOException {
        JsonToken t = p.getCurrentToken();
        if (t == JsonToken.VALUE_TRUE) {
            return Boolean.TRUE;
        }
        if (t == JsonToken.VALUE_FALSE) {
            return Boolean.FALSE;
        }
        return deserialize(p, ctxt);
    }

    @Override
    public Boolean deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        JsonToken t = p.getCurrentToken();
        if (t == JsonToken.VALUE_TRUE) {
            return Boolean.TRUE;
        }
        if (t == JsonToken.VALUE_FALSE) {
            return Boolean.FALSE;
        }

        try{
            return p.getBooleanValue();
        }catch (Exception e){
            try {
                String value = p.getValueAsString();
                if(value == null || "".equals(value.trim())){
                    return false;
                }
                value = value.trim().toLowerCase();
                if("1".equals(value) || "true".equals(value) || "y".equals(value)){
                    return true;
                }
                if("0".equals(value) || "false".equals(value) || "n".equals(value)){
                    return false;
                }
                return Boolean.valueOf(value);
            }catch (Exception e1){

            }
        }
        return false;
    }
}
