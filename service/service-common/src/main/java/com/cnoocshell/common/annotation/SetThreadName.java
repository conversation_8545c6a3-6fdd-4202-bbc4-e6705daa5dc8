package com.cnoocshell.common.annotation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@ApiModel("给线程设置名称")
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface SetThreadName {

    @ApiModelProperty("用随机字符串作为线程的名称")
    boolean uuid() default true;

    @ApiModelProperty("第几个参数作为key")
    int index() default -1;

    @ApiModelProperty("第几个参数的哪些字段的值作为key")
    String[] fieldName() default {};

    @ApiModelProperty("多个位置的参数组合作为key")
    int[] indexs() default {};


}
