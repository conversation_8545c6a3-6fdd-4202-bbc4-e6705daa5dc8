package com.cnoocshell.common.config.feign;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.cnoocshell.common.enums.AppNames;
import com.cnoocshell.common.exception.*;
import com.cnoocshell.common.result.ItemResult;
import org.apache.commons.lang.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.support.WebExchangeBindException;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;


@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Value("${spring.application.name}")
    private String applicationName;

    @PostConstruct
    public void init(){
        log.info("GlobalExceptionHandler init success.");
    }

    @ExceptionHandler(value = BizException.class)
    @ResponseBody
    public ItemResult<String> bizExceptionHandle(BizException exception, HttpServletRequest req, HttpServletResponse response) throws Exception {
        log.error("---BizException Handler---Host {} invokes url {} Parameter {} ERROR: {}",
                req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception);

        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode(exception.getErrorCode().getCode());
        result.setDescription(getMessage(exception.getMessage()));
        log.info("BizExceptionHandle : {}", JSON.toJSONString(result));

        checkException(exception,result,response);

        return result;
    }

    @ExceptionHandler(value = FeignException.class)
    @ResponseBody
    public ItemResult<String> feignExceptionHandle(FeignException exception, HttpServletRequest req,HttpServletResponse response) throws Exception {
        log.error("---FeignException Handler---Host {} invokes url {} Parameter {} ERROR: {}",
                req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception);

        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setDescription(getMessage(exception.contentUTF8()));

        checkException(exception,result,response);

        if(exception.getCause() instanceof ServiceException){
            String serviceErrorMsg = exception.getCause().getMessage();
            if(CharSequenceUtil.isNotBlank(serviceErrorMsg) && JSONUtil.isTypeJSON(serviceErrorMsg)){
                ItemResult serviceRs = JSONUtil.toBean(serviceErrorMsg,ItemResult.class);
                if(Objects.nonNull(serviceRs) && CharSequenceUtil.isNotBlank(serviceRs.getDescription()))
                    return serviceRs;
            }else if(CharSequenceUtil.isNotBlank(serviceErrorMsg)){
                return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),serviceErrorMsg);
            }else{
                return ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"系统异常，请联系管理员");
            }
        }
        return result;
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public ItemResult<String> exceptionHandle(Exception exception, HttpServletRequest req,HttpServletResponse response) throws Exception {
        log.error("---Exception Handler---Host {} invokes url {} Parameter {} ERROR: {}",
                req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception == null ? "" : exception.getMessage(),exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setDescription("系统异常，请联系管理员");

        checkException(exception,result,response);
        return result;
    }

    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(WebExchangeBindException.class)
    public ItemResult<String> webExchangeBindException(WebExchangeBindException exception, HttpServletRequest req,HttpServletResponse response)throws Exception {
        log.error("---WebExchangeBindException Handler---Host {} invokes url {} Parameter {} ERROR: {}",
                req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        FieldError fieldError = exception.getBindingResult().getFieldError();
        result.setDescription("数据绑定异常:["+(null != fieldError ? fieldError.getDefaultMessage() : "")+"]");

        checkException(exception,result,response);
        return result;
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public ItemResult<String> validationHandle(MethodArgumentNotValidException exception, HttpServletRequest req,HttpServletResponse response) throws Exception {
        log.error("---MethodArgumentNotValidException Handler---Host {} invokes url {} Parameter {} ERROR: {}",
                req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        FieldError fieldError = exception.getBindingResult().getFieldError();
        result.setDescription(null != fieldError ? fieldError.getDefaultMessage() : "参数校验失败");

        checkException(exception,result,response);
        return result;
    }

    @ExceptionHandler(value = NoLoginException.class)
    @ResponseBody
    public ItemResult<String> noLoginExceptionHandle(NoLoginException exception, HttpServletRequest req,HttpServletResponse response) throws Exception {
        log.error("---NoLoginException Handler---Host {} invokes url {} Parameter {} ERROR: {}",
                req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode("401");
        result.setDescription("登录信息失效，请重新登陆");

        checkException(exception,result,response);
        return result;
    }

    @ExceptionHandler(value = NoPermissionException.class)
    @ResponseBody
    public ItemResult<String> noPermissionExceptionHandle(NoPermissionException exception, HttpServletRequest req,HttpServletResponse response) throws Exception {
        log.error("---NoPermissionException Handler---Host {} invokes url {} Parameter {} ERROR: {}",
                req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode("402");
        result.setDescription("用户无此操作权限");

        checkException(exception,result,response);
        
        return result;
    }

    @ExceptionHandler(value = ForcedLogoutException.class)
    @ResponseBody
    public ItemResult<String> forcedLogoutExceptionHandle(ForcedLogoutException exception, HttpServletRequest req,HttpServletResponse response) throws Exception {
        log.error("---ForcedLogoutException Handler---Host {} invokes url {} Parameter {} ERROR: {}",
                req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode("403");
        result.setDescription("该账号已在其它地方登陆");

        checkException(exception,result,response);
        
        return result;
    }

    private String getMessage(String message) {
        return StringUtils.isBlank(message) ? "操作失败" : message;
    }

    private ItemResult getResult(String message) {
        ItemResult itemResult;
        if(JSONUtil.isTypeJSON(message)){
            itemResult = JSONUtil.toBean(message,ItemResult.class);
            if(Objects.isNull(itemResult))
                itemResult = ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),"操作失败");
        }else{
            itemResult = ItemResult.fail(BasicCode.CUSTOM_ERROR.getCode(),StringUtils.isBlank(message) ? "操作失败" : message);
        }
        return itemResult;
    }

    private String toJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (IOException e) {
            log.warn("toJson exception:{}",e.getMessage(), e);
        }
        return "";
    }

    private void checkException(Exception e,ItemResult errorResult,HttpServletResponse response) throws Exception{
        if(AppNames.isMicroService(applicationName)){
            if(Objects.nonNull(response))
                response.setStatus(cn.hutool.http.HttpStatus.HTTP_INTERNAL_ERROR);
        }
    }

}
