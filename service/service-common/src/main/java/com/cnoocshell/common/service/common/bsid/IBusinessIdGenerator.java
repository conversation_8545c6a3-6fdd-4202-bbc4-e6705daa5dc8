package com.cnoocshell.common.service.common.bsid;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 13:57 28/08/2018
 */
public interface IBusinessIdGenerator {
    /**
     * @Author: <EMAIL>
     * @Description: 默认根据businessCodePrefix生成id
     * @Date: Create in 14:21 28/08/2018
     */
    String incrementCode();

    /**
     * @Author: <EMAIL>
     * @Description: 前缀自定义
     * @Date: Create in 14:16 28/08/2018
     */
    String businessCodePrefix();

    /**
     * @Author: <EMAIL>
     * @Description: 根据系统名称
     * @Date: Create in 14:16 28/08/2018
     */
    Long gain();

    /**
     * @Author: <EMAIL>
     * @Description: 默认六位，不满六位补0
     * @Date: Create in 14:24 28/08/2018
     */
    String gainString();

    /**
     * 年月日字符串：yymmdd
     * @return
     */
    String gainDateString();
}
