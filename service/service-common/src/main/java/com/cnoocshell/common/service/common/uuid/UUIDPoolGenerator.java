package com.cnoocshell.common.service.common.uuid;

import com.cnoocshell.common.utils.BlockingQueueLocate;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Component
public class UUIDPoolGenerator implements UUIDGenerator, InitializingBean, DisposableBean {
    private static final Log logger = LogFactory.getLog(UUIDPoolGenerator.class);
    private static final String EVENT_STRING_PLACE_HOLDER = "esp";
    private Lock lock = new ReentrantLock();
    private Condition worker;
    private BlockingQueue<String> queue;
    private Thread producer;
    private boolean fixedLength;
    private int capacity;
    private int threshold;

    public UUIDPoolGenerator() {
        this.worker = this.lock.newCondition();
        this.queue = BlockingQueueLocate.createBlockingQueue();
        this.fixedLength = false;
        this.capacity = 1000;
        this.threshold = 200;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (this.threshold > this.capacity) {
            throw new IllegalArgumentException("threshold can't great than capacity.");
        } else {
            this.producer = new Thread("UUIDGen") {
                @Override
                public void run() {
                    int times = UUIDPoolGenerator.this.capacity / UUIDPoolGenerator.this.threshold;

                    for (int i = 0; i < times; ++i) {
                        UUIDPoolGenerator.this.produce();
                    }

                    UUIDPoolGenerator.logger.debug("fill the queue with capacity:" + UUIDPoolGenerator.this.capacity);

                    while (!Thread.interrupted()) {
                        UUIDPoolGenerator.this.lock.lock();

                        try {
                            UUIDPoolGenerator.this.worker.await();
                            UUIDPoolGenerator.this.produce();
                        } catch (InterruptedException var6) {
                            Thread.currentThread().interrupt();
                            break;
                        } finally {
                            UUIDPoolGenerator.this.lock.unlock();
                        }
                    }

                }
            };
            this.producer.setDaemon(true);
            this.producer.start();
        }
    }

    @Override
    public void destroy() throws Exception {
        this.producer.interrupt();
    }

    private void produce() {
        for (int i = 0; i < this.threshold; ++i) {
            this.queue.add(this.fixedLength ? RandomShortUUID.getFixSize() : RandomShortUUID.get());
        }

        this.queue.add(EVENT_STRING_PLACE_HOLDER);
    }

    private void startProduce() {
        this.lock.lock();

        try {
            this.worker.signal();
        } finally {
            this.lock.unlock();
        }

    }

    @Override
    public String gain() {
        String one;
        try {
            one = (String) this.queue.take();
        } catch (InterruptedException var3) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(var3);
        }

        if (!one.equals(EVENT_STRING_PLACE_HOLDER)) {
            return one;
        } else {
            this.startProduce();
            return this.gain();
        }
    }
}